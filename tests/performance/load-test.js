/**
 * RAG系统负载测试
 * 使用Artillery.js进行性能和负载测试
 */

const { check } = require('k6');
const http = require('k6/http');

// 测试配置
export let options = {
  stages: [
    { duration: '2m', target: 10 }, // 预热阶段：2分钟内逐渐增加到10个用户
    { duration: '5m', target: 10 }, // 稳定阶段：保持10个用户5分钟
    { duration: '2m', target: 20 }, // 增压阶段：2分钟内增加到20个用户
    { duration: '5m', target: 20 }, // 高负载阶段：保持20个用户5分钟
    { duration: '2m', target: 50 }, // 峰值阶段：2分钟内增加到50个用户
    { duration: '5m', target: 50 }, // 峰值保持：保持50个用户5分钟
    { duration: '2m', target: 0 },  // 降压阶段：2分钟内降到0个用户
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95%的请求响应时间小于2秒
    http_req_failed: ['rate<0.05'],    // 错误率小于5%
    http_reqs: ['rate>10'],            // 每秒请求数大于10
  },
};

// 测试数据
const BASE_URL = __ENV.BASE_URL || 'http://localhost:3001';
const TEST_QUESTIONS = [
  '什么是人工智能？',
  '机器学习的主要类型有哪些？',
  '深度学习和传统机器学习的区别是什么？',
  '自然语言处理的应用场景有哪些？',
  '如何评估机器学习模型的性能？',
  '什么是过拟合和欠拟合？',
  '神经网络的基本结构是什么？',
  '强化学习的基本概念是什么？',
  '计算机视觉的主要任务有哪些？',
  '大数据处理的常用技术有哪些？'
];

// 获取随机测试问题
function getRandomQuestion() {
  return TEST_QUESTIONS[Math.floor(Math.random() * TEST_QUESTIONS.length)];
}

// 主测试函数
export default function() {
  // 测试1：健康检查
  testHealthCheck();
  
  // 测试2：文档上传
  testDocumentUpload();
  
  // 测试3：RAG问答
  testRAGGeneration();
  
  // 测试4：向量检索
  testVectorSearch();
  
  // 测试5：批量向量化
  testBatchVectorization();
}

// 健康检查测试
function testHealthCheck() {
  const response = http.get(`${BASE_URL}/health`);
  
  check(response, {
    '健康检查状态码为200': (r) => r.status === 200,
    '健康检查响应时间<100ms': (r) => r.timings.duration < 100,
  });
}

// 文档上传测试
function testDocumentUpload() {
  const testContent = `
    # 测试文档 ${Date.now()}
    
    这是一个用于性能测试的文档。
    
    ## 内容概述
    
    本文档包含了关于人工智能的基础知识，包括机器学习、深度学习等相关概念。
    
    ### 机器学习
    
    机器学习是人工智能的一个重要分支，它使计算机能够从数据中学习并做出预测。
    
    ### 深度学习
    
    深度学习是机器学习的一个子集，使用多层神经网络来处理复杂的数据模式。
  `;

  const formData = {
    file: http.file(Buffer.from(testContent), 'test-document.txt', 'text/plain'),
    title: `性能测试文档-${Date.now()}`,
    extract_metadata: 'true'
  };

  const response = http.post(`${BASE_URL}/api/v1/documents/parse`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  check(response, {
    '文档上传状态码为201': (r) => r.status === 201,
    '文档上传响应时间<5s': (r) => r.timings.duration < 5000,
    '返回文档ID': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.document_id !== undefined;
      } catch {
        return false;
      }
    },
  });
}

// RAG问答测试
function testRAGGeneration() {
  const question = getRandomQuestion();
  
  const payload = {
    question: question,
    retrievalTopK: 5,
    maxContextLength: 2000,
    model: 'gpt-3.5-turbo',
    temperature: 0.7
  };

  const response = http.post(
    `${BASE_URL}/api/v1/generation/rag/question`,
    JSON.stringify(payload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  check(response, {
    'RAG问答状态码为200': (r) => r.status === 200,
    'RAG问答响应时间<10s': (r) => r.timings.duration < 10000,
    '返回有效答案': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data.answer && body.data.answer.length > 10;
      } catch {
        return false;
      }
    },
    '置信度大于0': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.data.confidence > 0;
      } catch {
        return false;
      }
    },
  });
}

// 向量检索测试
function testVectorSearch() {
  const query = getRandomQuestion();
  
  const params = {
    query: query,
    top_k: 10,
    search_type: 'hybrid'
  };

  const response = http.get(`${BASE_URL}/api/v1/search`, { params });

  check(response, {
    '向量检索状态码为200': (r) => r.status === 200,
    '向量检索响应时间<2s': (r) => r.timings.duration < 2000,
    '返回检索结果': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && Array.isArray(body.data.results);
      } catch {
        return false;
      }
    },
  });
}

// 批量向量化测试
function testBatchVectorization() {
  const texts = [
    '人工智能是计算机科学的一个分支',
    '机器学习使计算机能够从数据中学习',
    '深度学习使用多层神经网络',
    '自然语言处理帮助计算机理解人类语言',
    '计算机视觉使机器能够理解图像'
  ];

  const payload = {
    texts: texts,
    model_name: 'paraphrase-multilingual-MiniLM-L12-v2',
    batch_size: 32,
    normalize: true
  };

  const response = http.post(
    `${BASE_URL}/api/v1/vectorize/batch`,
    JSON.stringify(payload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  check(response, {
    '批量向量化状态码为200': (r) => r.status === 200,
    '批量向量化响应时间<5s': (r) => r.timings.duration < 5000,
    '返回向量结果': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.success && body.data.success_count === texts.length;
      } catch {
        return false;
      }
    },
  });
}

// 测试场景：并发RAG问答
export function concurrentRAGTest() {
  const question = getRandomQuestion();
  
  const payload = {
    question: question,
    retrievalTopK: 3,
    maxContextLength: 1500
  };

  const response = http.post(
    `${BASE_URL}/api/v1/generation/rag/question`,
    JSON.stringify(payload),
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  check(response, {
    '并发RAG问答成功': (r) => r.status === 200,
    '并发RAG问答响应时间合理': (r) => r.timings.duration < 15000,
  });
}

// 测试场景：流式生成
export function streamGenerationTest() {
  const payload = {
    messages: [
      {
        role: 'user',
        content: getRandomQuestion()
      }
    ],
    stream: true,
    model: 'gpt-3.5-turbo'
  };

  const response = http.post(
    `${BASE_URL}/api/v1/generation/stream`,
    JSON.stringify(payload),
    {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
      },
    }
  );

  check(response, {
    '流式生成状态码为200': (r) => r.status === 200,
    '流式生成内容类型正确': (r) => r.headers['Content-Type'].includes('text/event-stream'),
  });
}

// 测试场景：大文档处理
export function largeDocumentTest() {
  // 生成大文档内容（约10KB）
  const largeContent = '人工智能技术正在快速发展。'.repeat(500);
  
  const formData = {
    file: http.file(Buffer.from(largeContent), 'large-document.txt', 'text/plain'),
    title: `大文档测试-${Date.now()}`,
    extract_metadata: 'true'
  };

  const response = http.post(`${BASE_URL}/api/v1/documents/parse`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  check(response, {
    '大文档处理状态码为201': (r) => r.status === 201,
    '大文档处理响应时间<30s': (r) => r.timings.duration < 30000,
  });
}

// 性能监控函数
export function handleSummary(data) {
  return {
    'performance-report.json': JSON.stringify(data, null, 2),
    stdout: `
    ========== 性能测试报告 ==========
    
    总请求数: ${data.metrics.http_reqs.values.count}
    平均响应时间: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms
    95%响应时间: ${data.metrics.http_req_duration.values['p(95)'].toFixed(2)}ms
    99%响应时间: ${data.metrics.http_req_duration.values['p(99)'].toFixed(2)}ms
    错误率: ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%
    吞吐量: ${data.metrics.http_reqs.values.rate.toFixed(2)} req/s
    
    ================================
    `,
  };
}
