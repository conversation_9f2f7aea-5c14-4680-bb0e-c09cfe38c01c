/**
 * RAG系统集成测试
 * 测试整个RAG流程的端到端功能
 */

const request = require('supertest');
const fs = require('fs');
const path = require('path');

// 测试配置
const config = {
  userService: process.env.USER_SERVICE_URL || 'http://localhost:3000',
  documentService: process.env.DOCUMENT_SERVICE_URL || 'http://localhost:8001',
  vectorizationService: process.env.VECTORIZATION_SERVICE_URL || 'http://localhost:8002',
  vectorDatabase: process.env.VECTOR_DATABASE_URL || 'http://localhost:8005',
  generationService: process.env.GENERATION_SERVICE_URL || 'http://localhost:3001',
  retrievalService: process.env.RETRIEVAL_SERVICE_URL || 'http://localhost:8003',
};

// 测试数据
let testUser = null;
let authToken = null;
let testDocumentId = null;
let testConversationId = null;

describe('RAG系统集成测试', () => {
  
  beforeAll(async () => {
    console.log('🚀 开始RAG系统集成测试');
    
    // 等待所有服务启动
    await waitForServices();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    console.log('✅ RAG系统集成测试完成');
  });

  describe('1. 用户认证流程', () => {
    test('用户注册', async () => {
      const userData = {
        email: `test-${Date.now()}@example.com`,
        password: 'TestPassword123!',
        name: '集成测试用户'
      };

      const response = await request(config.userService)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.token).toBeDefined();

      testUser = response.body.data.user;
      authToken = response.body.data.token;
    });

    test('用户登录', async () => {
      const loginData = {
        email: testUser.email,
        password: 'TestPassword123!'
      };

      const response = await request(config.userService)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
    });
  });

  describe('2. 文档处理流程', () => {
    test('文档上传', async () => {
      // 创建测试文档
      const testContent = `
        # 人工智能技术概述
        
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        
        ## 机器学习
        
        机器学习是人工智能的一个子领域，专注于算法的设计，这些算法可以从数据中学习并做出预测或决策。
        
        ### 深度学习
        
        深度学习是机器学习的一个子集，它使用多层神经网络来模拟人脑的工作方式。
        
        ## 自然语言处理
        
        自然语言处理（NLP）是人工智能的一个重要分支，旨在让计算机能够理解、解释和生成人类语言。
      `;

      const testFilePath = path.join(__dirname, '../fixtures/test-document.md');
      fs.writeFileSync(testFilePath, testContent);

      const response = await request(config.documentService)
        .post('/api/v1/documents/parse')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', testFilePath)
        .field('extract_metadata', 'true')
        .field('perform_ocr', 'false')
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.document_id).toBeDefined();
      expect(response.body.content).toContain('人工智能');

      testDocumentId = response.body.document_id;

      // 清理测试文件
      fs.unlinkSync(testFilePath);
    });

    test('文档分块', async () => {
      const chunkRequest = {
        strategy: 'semantic',
        chunk_size: 512,
        overlap: 64
      };

      const response = await request(config.documentService)
        .post(`/api/v1/documents/${testDocumentId}/chunk`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(chunkRequest)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.chunks).toBeInstanceOf(Array);
      expect(response.body.chunks.length).toBeGreaterThan(0);
      expect(response.body.total_chunks).toBeGreaterThan(0);
    });

    test('文档向量化', async () => {
      // 获取文档分块
      const chunksResponse = await request(config.documentService)
        .get(`/api/v1/documents/${testDocumentId}/chunks`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const chunks = chunksResponse.body.chunks;
      const texts = chunks.map(chunk => chunk.text);

      // 批量向量化
      const vectorizeRequest = {
        texts: texts,
        model_name: 'paraphrase-multilingual-MiniLM-L12-v2',
        batch_size: 32,
        normalize: true
      };

      const response = await request(config.vectorizationService)
        .post('/api/v1/vectorize/batch')
        .send(vectorizeRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toBeInstanceOf(Array);
      expect(response.body.data.success_count).toBe(texts.length);
      expect(response.body.data.error_count).toBe(0);
    });

    test('向量存储', async () => {
      // 创建测试集合
      const collectionName = `test_collection_${Date.now()}`;
      
      const createResponse = await request(config.vectorDatabase)
        .post(`/api/v1/collections/${collectionName}`)
        .send({
          dimension: 384,
          distance_function: 'cosine'
        })
        .expect(200);

      expect(createResponse.body.status).toBe('success');

      // 插入测试向量
      const insertRequest = {
        vectors: [
          [0.1, 0.2, 0.3, ...Array(381).fill(0)], // 384维向量
          [0.4, 0.5, 0.6, ...Array(381).fill(0)]
        ],
        documents: ['测试文档1', '测试文档2'],
        metadatas: [
          { type: 'test', source: 'integration_test' },
          { type: 'test', source: 'integration_test' }
        ],
        ids: ['test_1', 'test_2']
      };

      const insertResponse = await request(config.vectorDatabase)
        .post(`/api/v1/collections/${collectionName}/insert`)
        .send(insertRequest)
        .expect(200);

      expect(insertResponse.body.status).toBe('success');
      expect(insertResponse.body.inserted_count).toBe(2);
    });
  });

  describe('3. 检索功能测试', () => {
    test('向量检索', async () => {
      const collectionName = `test_collection_${Date.now()}`;
      
      // 先创建集合并插入数据
      await request(config.vectorDatabase)
        .post(`/api/v1/collections/${collectionName}`)
        .send({ dimension: 384, distance_function: 'cosine' });

      await request(config.vectorDatabase)
        .post(`/api/v1/collections/${collectionName}/insert`)
        .send({
          vectors: [[0.1, 0.2, 0.3, ...Array(381).fill(0)]],
          documents: ['人工智能是计算机科学的重要分支'],
          ids: ['ai_doc_1']
        });

      // 执行检索
      const searchRequest = {
        query_vector: [0.1, 0.2, 0.3, ...Array(381).fill(0)],
        top_k: 5
      };

      const response = await request(config.vectorDatabase)
        .post(`/api/v1/collections/${collectionName}/search`)
        .send(searchRequest)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.results).toBeInstanceOf(Array);
      expect(response.body.total_results).toBeGreaterThan(0);
    });

    test('混合检索', async () => {
      const searchRequest = {
        query: '人工智能和机器学习',
        search_type: 'hybrid',
        top_k: 10
      };

      const response = await request(config.retrievalService)
        .get('/api/v1/search')
        .query(searchRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.results).toBeInstanceOf(Array);
    });
  });

  describe('4. 生成功能测试', () => {
    test('基础文本生成', async () => {
      const generateRequest = {
        messages: [
          {
            role: 'user',
            content: '请简单介绍一下人工智能'
          }
        ],
        model: 'gpt-3.5-turbo',
        max_tokens: 200,
        temperature: 0.7
      };

      const response = await request(config.generationService)
        .post('/api/v1/generation/generate')
        .send(generateRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.content).toBeDefined();
      expect(response.body.data.content.length).toBeGreaterThan(10);
      expect(response.body.data.model).toBe('gpt-3.5-turbo');
    });

    test('RAG问答', async () => {
      const ragRequest = {
        question: '什么是深度学习？',
        retrievalTopK: 5,
        maxContextLength: 2000
      };

      const response = await request(config.generationService)
        .post('/api/v1/generation/rag/question')
        .send(ragRequest)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.answer).toBeDefined();
      expect(response.body.data.confidence).toBeGreaterThan(0);
      expect(response.body.data.sources).toBeInstanceOf(Array);
    });

    test('流式生成', async () => {
      const streamRequest = {
        messages: [
          {
            role: 'user',
            content: '写一首关于人工智能的短诗'
          }
        ],
        stream: true
      };

      const response = await request(config.generationService)
        .post('/api/v1/generation/stream')
        .send(streamRequest)
        .expect(200);

      expect(response.headers['content-type']).toContain('text/event-stream');
    });
  });

  describe('5. 端到端RAG流程测试', () => {
    test('完整RAG问答流程', async () => {
      // 1. 上传文档
      const testContent = '量子计算是一种基于量子力学原理的计算方式，具有巨大的计算潜力。';
      const testFilePath = path.join(__dirname, '../fixtures/quantum-computing.txt');
      fs.writeFileSync(testFilePath, testContent);

      const uploadResponse = await request(config.documentService)
        .post('/api/v1/documents/parse')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', testFilePath)
        .expect(201);

      const documentId = uploadResponse.body.document_id;

      // 2. 等待文档处理完成
      await waitForDocumentProcessing(documentId);

      // 3. 执行RAG问答
      const ragResponse = await request(config.generationService)
        .post('/api/v1/generation/rag/question')
        .send({
          question: '什么是量子计算？',
          documentIds: [documentId],
          retrievalTopK: 3
        })
        .expect(200);

      expect(ragResponse.body.success).toBe(true);
      expect(ragResponse.body.data.answer).toContain('量子');
      expect(ragResponse.body.data.confidence).toBeGreaterThan(0.5);

      // 清理
      fs.unlinkSync(testFilePath);
    });
  });

  describe('6. 性能测试', () => {
    test('并发请求测试', async () => {
      const concurrentRequests = 10;
      const requests = [];

      for (let i = 0; i < concurrentRequests; i++) {
        const request = fetch(`${config.generationService}/api/v1/generation/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            messages: [{ role: 'user', content: `测试请求 ${i}` }],
            max_tokens: 50
          })
        });
        requests.push(request);
      }

      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const endTime = Date.now();

      const successCount = responses.filter(r => r.ok).length;
      const avgResponseTime = (endTime - startTime) / concurrentRequests;

      expect(successCount).toBe(concurrentRequests);
      expect(avgResponseTime).toBeLessThan(5000); // 平均响应时间小于5秒
    });

    test('大文档处理性能', async () => {
      // 生成大文档内容
      const largeContent = '人工智能技术正在快速发展。'.repeat(1000);
      const testFilePath = path.join(__dirname, '../fixtures/large-document.txt');
      fs.writeFileSync(testFilePath, largeContent);

      const startTime = Date.now();
      
      const response = await request(config.documentService)
        .post('/api/v1/documents/parse')
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', testFilePath)
        .expect(201);

      const processingTime = Date.now() - startTime;

      expect(response.body.status).toBe('success');
      expect(processingTime).toBeLessThan(30000); // 处理时间小于30秒

      // 清理
      fs.unlinkSync(testFilePath);
    });
  });
});

// 辅助函数
async function waitForServices() {
  const services = Object.values(config);
  const maxRetries = 30;
  
  for (const serviceUrl of services) {
    let retries = 0;
    while (retries < maxRetries) {
      try {
        const response = await fetch(`${serviceUrl}/health`);
        if (response.ok) {
          console.log(`✅ 服务 ${serviceUrl} 已就绪`);
          break;
        }
      } catch (error) {
        // 服务未就绪，继续等待
      }
      
      retries++;
      if (retries >= maxRetries) {
        throw new Error(`服务 ${serviceUrl} 启动超时`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

async function waitForDocumentProcessing(documentId) {
  const maxAttempts = 30;
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await request(config.documentService)
        .get(`/api/v1/documents/${documentId}`)
        .set('Authorization', `Bearer ${authToken}`);

      if (response.body.status === 'completed') {
        return;
      }

      if (response.body.status === 'error') {
        throw new Error('文档处理失败');
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      throw new Error(`检查文档状态失败: ${error.message}`);
    }
  }

  throw new Error('文档处理超时');
}

async function cleanupTestData() {
  try {
    // 清理测试用户
    if (testUser && authToken) {
      await request(config.userService)
        .delete(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${authToken}`);
    }

    // 清理测试文档
    if (testDocumentId && authToken) {
      await request(config.documentService)
        .delete(`/api/v1/documents/${testDocumentId}`)
        .set('Authorization', `Bearer ${authToken}`);
    }

    console.log('🧹 测试数据清理完成');
  } catch (error) {
    console.warn('⚠️ 清理测试数据时出现错误:', error.message);
  }
}
