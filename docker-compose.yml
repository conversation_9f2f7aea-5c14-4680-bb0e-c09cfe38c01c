version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: rag-postgres
    environment:
      POSTGRES_DB: rag_system
      POSTGRES_USER: rag_user
      POSTGRES_PASSWORD: rag_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - rag-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rag-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: rag-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - rag-network
    restart: unless-stopped
    command: server /data --console-address ":9001"

  # 向量数据库 (Chroma)
  chroma:
    image: chromadb/chroma:latest
    container_name: rag-chroma
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    networks:
      - rag-network
    restart: unless-stopped
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000

  # 用户服务
  user-service:
    build:
      context: ./backend/services/user-service
      dockerfile: Dockerfile
    container_name: rag-user-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/rag_system
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
    ports:
      - "3001:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/user-service:/app
      - /app/node_modules

  # 文档服务
  document-service:
    build:
      context: ./backend/services/document-service
      dockerfile: Dockerfile
    container_name: rag-document-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/rag_system
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
    ports:
      - "3002:3000"
    depends_on:
      - postgres
      - redis
      - minio
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/document-service:/app
      - /app/node_modules

  # 向量化服务
  embedding-service:
    build:
      context: ./backend/services/embedding-service
      dockerfile: Dockerfile
    container_name: rag-embedding-service
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=************************************************/rag_system
      - REDIS_URL=redis://redis:6379
      - CHROMA_URL=http://chroma:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
      - chroma
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/embedding-service:/app

  # 检索服务
  retrieval-service:
    build:
      context: ./backend/services/retrieval-service
      dockerfile: Dockerfile
    container_name: rag-retrieval-service
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=************************************************/rag_system
      - REDIS_URL=redis://redis:6379
      - CHROMA_URL=http://chroma:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8000
    ports:
      - "8002:8000"
    depends_on:
      - postgres
      - redis
      - chroma
      - embedding-service
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/retrieval-service:/app

  # 生成服务
  generation-service:
    build:
      context: ./backend/services/generation-service
      dockerfile: Dockerfile
    container_name: rag-generation-service
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    ports:
      - "3003:3000"
    depends_on:
      - redis
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/generation-service:/app
      - /app/node_modules

  # 对话服务
  conversation-service:
    build:
      context: ./backend/services/conversation-service
      dockerfile: Dockerfile
    container_name: rag-conversation-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/rag_system
      - REDIS_URL=redis://redis:6379
      - RETRIEVAL_SERVICE_URL=http://retrieval-service:8000
      - GENERATION_SERVICE_URL=http://generation-service:3000
    ports:
      - "3004:3000"
    depends_on:
      - postgres
      - redis
      - retrieval-service
      - generation-service
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./backend/services/conversation-service:/app
      - /app/node_modules

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: rag-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8002
    ports:
      - "3100:3000"
    depends_on:
      - retrieval-service
    networks:
      - rag-network
    restart: unless-stopped
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next

volumes:
  postgres_data:
  redis_data:
  minio_data:
  chroma_data:

networks:
  rag-network:
    driver: bridge
