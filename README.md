# 🤖 RAG系统 - 检索增强生成平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![Python Version](https://img.shields.io/badge/python-%3E%3D3.9-blue)](https://www.python.org/)
[![Docker](https://img.shields.io/badge/docker-%3E%3D20.10-blue)](https://www.docker.com/)

一个完整的RAG（检索增强生成）系统，集成大语言模型接口，提供智能问答、文档管理和知识检索功能。

## 🚀 项目特性

- **📄 多格式文档支持**：PDF、Word、TXT、Markdown等格式
- **🔍 智能语义检索**：基于向量相似度的精准检索
- **🤖 多LLM集成**：支持OpenAI GPT、Claude等主流模型
- **💬 多轮对话**：上下文相关的连续对话体验
- **🎨 现代化界面**：React + Next.js响应式前端
- **🏗️ 微服务架构**：可扩展的分布式系统设计
- **🔒 企业级安全**：JWT认证、RBAC权限控制
- **📊 实时监控**：完善的系统监控和日志记录

## 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────┐
│                    前端应用层                            │
│              Web前端 + 管理后台                          │
├─────────────────────────────────────────────────────────┤
│                    API网关层                             │
│            路由、认证、限流、监控                        │
├─────────────────────────────────────────────────────────┤
│                   业务服务层                             │
│    用户服务 | 文档服务 | 向量服务 | 检索服务 | 生成服务    │
├─────────────────────────────────────────────────────────┤
│                   数据存储层                             │
│         PostgreSQL | 向量数据库 | Redis | 对象存储       │
└─────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **Node.js + TypeScript**：业务逻辑服务
- **Python + FastAPI**：AI相关服务
- **PostgreSQL**：关系数据存储
- **Redis**：缓存和会话管理
- **向量数据库**：Pinecone/Weaviate/Chroma
- **MinIO**：对象存储服务

### 前端技术
- **React 18**：用户界面框架
- **Next.js 14**：全栈React框架
- **TypeScript**：类型安全开发
- **Tailwind CSS**：原子化CSS框架

### 基础设施
- **Docker**：容器化部署
- **Kubernetes**：容器编排（可选）
- **Prometheus + Grafana**：监控告警
- **ELK Stack**：日志收集分析

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+

### 1. 克隆项目
```bash
git clone https://github.com/your-org/ai-rag-system.git
cd ai-rag-system
```

### 2. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量，配置API密钥等
vim .env
```

### 3. 启动服务
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者分别启动各个服务
npm run dev
```

### 4. 数据库初始化
```bash
# 运行数据库迁移
npm run db:migrate

# 插入种子数据
npm run db:seed
```

### 5. 访问应用
- **前端应用**：http://localhost:3100
- **API网关**：http://localhost:3000
- **管理后台**：http://localhost:3100/admin
- **API文档**：http://localhost:3000/docs

## 📋 开发进度

### ✅ 已完成模块

#### 第一阶段：MVP核心功能
- [x] **项目初始化和环境搭建** - 完整的项目结构和开发环境
- [x] **PostgreSQL数据库模块** - 关系数据库设计和配置
- [x] **用户服务模块** - 用户认证、授权、权限管理
- [x] **API网关模块** - 请求路由、负载均衡、认证授权
- [x] **文档服务模块** - 文档上传、解析、预处理、管理
- [x] **向量化服务模块** - 文本向量化、向量存储、向量管理
- [x] **向量数据库模块** - 向量数据库集成和相似度检索

#### 第二阶段：问答功能完善
- [x] **检索服务模块** - 语义检索、混合检索、结果排序
- [x] **生成服务模块** - LLM调用、提示词工程、内容生成
- [x] **对话服务模块** - 对话管理、历史记录、会话状态管理
- [x] **用户前端模块** - 用户界面、智能问答、文档浏览

#### 第三阶段：性能和管理优化
- [x] **Redis缓存模块** - 数据缓存、会话存储、消息队列
- [x] **管理后台模块** - 管理员界面、系统配置、监控面板
- [x] **监控服务模块** - 系统监控、性能监控、告警管理
- [x] **对象存储模块** - 文件存储、静态资源服务

#### 第四阶段：运维和完善
- [x] **配置中心模块** - 配置管理、配置分发、热更新
- [x] **日志服务模块** - 日志收集、日志分析、日志存储

### 🎯 项目状态
- **总模块数**: 17个
- **已完成**: 17个 (100%)
- **开发状态**: ✅ 全部完成

## 📁 项目结构

```
rag-system/
├── frontend/                    # 前端应用
│   ├── app/                    # Next.js应用目录
│   │   ├── (auth)/             # 认证相关页面
│   │   ├── admin/              # 管理后台
│   │   ├── chat/               # 聊天界面
│   │   └── documents/          # 文档管理
│   ├── components/             # 共享组件
│   ├── lib/                    # 工具库
│   └── public/                 # 静态资源
├── backend/                    # 后端服务
│   ├── services/               # 微服务
│   │   ├── api-gateway/        # API网关服务
│   │   ├── user-service/       # 用户管理服务
│   │   ├── document-service/   # 文档处理服务
│   │   ├── vectorization-service/ # 向量化服务
│   │   ├── retrieval-service/  # 检索服务
│   │   ├── generation-service/ # 生成服务
│   │   ├── conversation-service/ # 对话服务
│   │   ├── storage-service/    # 存储服务
│   │   └── config-service/     # 配置服务
│   ├── infrastructure/         # 基础设施
│   │   ├── database/           # 数据库配置
│   │   ├── redis/              # Redis配置
│   │   ├── monitoring/         # 监控配置
│   │   ├── logging/            # 日志配置
│   │   ├── storage/            # 存储配置
│   │   └── config-center/      # 配置中心
│   └── shared/                 # 共享代码
├── docs/                       # 项目文档
├── scripts/                    # 部署脚本
└── tests/                      # 测试代码
```

## 🔧 核心功能

### 智能问答系统
- **多轮对话**: 支持上下文相关的连续对话
- **文档问答**: 基于上传文档的知识问答
- **实时流式**: WebSocket实时流式响应
- **历史管理**: 完整的对话历史记录

### 文档管理系统
- **多格式支持**: PDF、Word、TXT、Markdown等
- **智能解析**: 自动文档内容提取和预处理
- **向量化处理**: 文档分块和向量化存储
- **搜索浏览**: 文档搜索和在线浏览

### 用户管理系统
- **用户认证**: 注册、登录、密码重置
- **权限控制**: 基于角色的访问控制(RBAC)
- **会话管理**: JWT令牌和会话状态管理
- **用户配置**: 个人设置和偏好管理

### 管理后台系统
- **用户管理**: 用户列表、权限分配、状态管理
- **文档管理**: 文档列表、批量操作、状态监控
- **系统监控**: 实时监控面板、性能指标
- **数据统计**: 使用统计、趋势分析

## 🚀 部署指南

### 开发环境部署
```bash
# 启动所有基础设施服务
docker-compose -f backend/infrastructure/database/docker-compose.yml up -d
docker-compose -f backend/infrastructure/redis/docker-compose.yml up -d
docker-compose -f backend/infrastructure/monitoring/docker-compose.yml up -d

# 启动应用服务
npm run dev:all
```

### 生产环境部署
```bash
# 使用Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# 或使用Kubernetes
kubectl apply -f k8s/
```

## 📊 监控和运维

### 监控服务
- **Prometheus**: 指标收集和存储
- **Grafana**: 可视化监控面板
- **AlertManager**: 告警规则和通知

### 日志服务
- **Elasticsearch**: 日志存储和搜索
- **Logstash**: 日志处理和转换
- **Kibana**: 日志分析和可视化

### 配置管理
- **Consul**: 服务发现和配置中心
- **热更新**: 配置实时更新
- **版本控制**: 配置变更历史

## 🧪 测试

```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 📖 API文档

API文档通过Swagger自动生成，启动服务后访问：
- **API文档**: http://localhost:3000/docs
- **API规范**: http://localhost:3000/api-docs.json

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: RAG Development Team
- **邮箱**: <EMAIL>
- **项目地址**: https://github.com/your-org/ai-rag-system
- **文档地址**: https://docs.rag-system.com

---

**🎉 恭喜！** 这是一个完整的企业级RAG系统，包含了从前端到后端、从开发到运维的完整技术栈。所有17个功能模块已全部完成，系统具备高可用性、高性能和可扩展性，适合中大型企业使用。

