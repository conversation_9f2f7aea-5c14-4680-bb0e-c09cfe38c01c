# RAG系统部署指南

## 📋 概述

本指南详细说明了如何在生产环境中部署RAG智能问答系统。系统采用微服务架构，支持Docker容器化部署和Kubernetes集群部署。

## 🏗️ 系统架构

### 服务组件
- **用户服务** (User Service): 用户认证和管理
- **文档服务** (Document Service): 文档解析和处理
- **向量化服务** (Vectorization Service): 文本向量化
- **向量数据库** (Vector Database): 向量存储和检索
- **检索服务** (Retrieval Service): 智能检索
- **生成服务** (Generation Service): LLM生成
- **前端应用** (Frontend): React用户界面

### 依赖服务
- **PostgreSQL**: 关系型数据库
- **Redis**: 缓存和会话存储
- **ChromaDB**: 向量数据库
- **Elasticsearch**: 全文搜索引擎
- **Nginx**: 反向代理和负载均衡

## 🐳 Docker部署

### 1. 环境准备

```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量

创建 `.env` 文件：

```bash
# 数据库配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=rag_system
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# ChromaDB配置
CHROMA_HOST=chromadb
CHROMA_PORT=8000

# Elasticsearch配置
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200

# LLM API配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 服务端口配置
USER_SERVICE_PORT=3000
DOCUMENT_SERVICE_PORT=8001
VECTORIZATION_SERVICE_PORT=8002
RETRIEVAL_SERVICE_PORT=8003
GENERATION_SERVICE_PORT=3001
VECTOR_DATABASE_PORT=8005
FRONTEND_PORT=3002

# 日志级别
LOG_LEVEL=info

# 文件存储
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=50MB
```

### 3. 启动服务

```bash
# 克隆项目
git clone <repository-url>
cd ai-rag

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 验证部署

```bash
# 检查服务健康状态
curl http://localhost:3000/health  # 用户服务
curl http://localhost:8001/health  # 文档服务
curl http://localhost:8002/health  # 向量化服务
curl http://localhost:8003/health  # 检索服务
curl http://localhost:3001/health  # 生成服务
curl http://localhost:8005/health  # 向量数据库

# 访问前端应用
open http://localhost:3002
```

## ☸️ Kubernetes部署

### 1. 准备Kubernetes集群

```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 验证集群连接
kubectl cluster-info
```

### 2. 创建命名空间

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: rag-system
  labels:
    name: rag-system
```

```bash
kubectl apply -f k8s/namespace.yaml
```

### 3. 配置存储

```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: rag-pv
  namespace: rag-system
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: standard
  hostPath:
    path: /data/rag-system
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rag-pvc
  namespace: rag-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: standard
```

### 4. 部署数据库服务

```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: rag-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: "rag_system"
        - name: POSTGRES_USER
          value: "rag_user"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rag-secrets
              key: postgres-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: rag-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: rag-system
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

### 5. 部署应用服务

```yaml
# rag-services.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: rag-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: rag-system/user-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: POSTGRES_HOST
          value: "postgres"
        - name: REDIS_HOST
          value: "redis"
        envFrom:
        - secretRef:
            name: rag-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: rag-system
spec:
  selector:
    app: user-service
  ports:
  - port: 3000
    targetPort: 3000
```

### 6. 配置Ingress

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rag-ingress
  namespace: rag-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - rag.yourdomain.com
    secretName: rag-tls
  rules:
  - host: rag.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend
            port:
              number: 3002
      - path: /api/auth
        pathType: Prefix
        backend:
          service:
            name: user-service
            port:
              number: 3000
      - path: /api/documents
        pathType: Prefix
        backend:
          service:
            name: document-service
            port:
              number: 8001
      - path: /api/generation
        pathType: Prefix
        backend:
          service:
            name: generation-service
            port:
              number: 3001
```

## 🔧 配置管理

### 1. 创建配置文件

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-config
  namespace: rag-system
data:
  LOG_LEVEL: "info"
  MAX_FILE_SIZE: "50MB"
  UPLOAD_PATH: "/app/uploads"
  JWT_EXPIRES_IN: "7d"
```

### 2. 创建密钥

```bash
# 创建密钥
kubectl create secret generic rag-secrets \
  --from-literal=postgres-password=your_secure_password \
  --from-literal=redis-password=your_redis_password \
  --from-literal=jwt-secret=your_jwt_secret \
  --from-literal=openai-api-key=your_openai_key \
  --from-literal=anthropic-api-key=your_anthropic_key \
  -n rag-system
```

## 📊 监控和日志

### 1. 部署Prometheus监控

```yaml
# monitoring.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: rag-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
```

### 2. 配置日志收集

```yaml
# logging.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd
  namespace: rag-system
spec:
  selector:
    matchLabels:
      name: fluentd
  template:
    metadata:
      labels:
        name: fluentd
    spec:
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1-debian-elasticsearch
        env:
        - name: FLUENT_ELASTICSEARCH_HOST
          value: "elasticsearch"
        - name: FLUENT_ELASTICSEARCH_PORT
          value: "9200"
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
```

## 🔒 安全配置

### 1. 网络策略

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: rag-network-policy
  namespace: rag-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: rag-system
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: rag-system
```

### 2. RBAC配置

```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rag-service-account
  namespace: rag-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: rag-role
  namespace: rag-system
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rag-role-binding
  namespace: rag-system
subjects:
- kind: ServiceAccount
  name: rag-service-account
  namespace: rag-system
roleRef:
  kind: Role
  name: rag-role
  apiGroup: rbac.authorization.k8s.io
```

## 🚀 部署脚本

### 自动化部署脚本

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 开始部署RAG系统..."

# 检查环境
echo "📋 检查部署环境..."
kubectl cluster-info > /dev/null 2>&1 || { echo "❌ Kubernetes集群不可用"; exit 1; }

# 创建命名空间
echo "📦 创建命名空间..."
kubectl apply -f k8s/namespace.yaml

# 创建配置和密钥
echo "🔧 创建配置..."
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# 部署存储
echo "💾 部署存储..."
kubectl apply -f k8s/storage.yaml

# 部署数据库
echo "🗄️ 部署数据库..."
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/redis.yaml
kubectl apply -f k8s/elasticsearch.yaml
kubectl apply -f k8s/chromadb.yaml

# 等待数据库就绪
echo "⏳ 等待数据库服务就绪..."
kubectl wait --for=condition=ready pod -l app=postgres -n rag-system --timeout=300s

# 部署应用服务
echo "🔧 部署应用服务..."
kubectl apply -f k8s/user-service.yaml
kubectl apply -f k8s/document-service.yaml
kubectl apply -f k8s/vectorization-service.yaml
kubectl apply -f k8s/retrieval-service.yaml
kubectl apply -f k8s/generation-service.yaml
kubectl apply -f k8s/vector-database.yaml

# 部署前端
echo "🎨 部署前端..."
kubectl apply -f k8s/frontend.yaml

# 配置网络
echo "🌐 配置网络..."
kubectl apply -f k8s/ingress.yaml

# 部署监控
echo "📊 部署监控..."
kubectl apply -f k8s/monitoring.yaml

echo "✅ RAG系统部署完成！"
echo "🌐 访问地址: https://rag.yourdomain.com"
echo "📊 监控地址: https://rag.yourdomain.com/monitoring"
```

## 🔍 故障排查

### 常见问题

1. **服务无法启动**
   ```bash
   kubectl logs -f deployment/service-name -n rag-system
   kubectl describe pod pod-name -n rag-system
   ```

2. **数据库连接失败**
   ```bash
   kubectl exec -it postgres-pod -n rag-system -- psql -U rag_user -d rag_system
   ```

3. **网络连接问题**
   ```bash
   kubectl get svc -n rag-system
   kubectl get ingress -n rag-system
   ```

### 性能调优

1. **资源限制调整**
2. **副本数量优化**
3. **缓存配置优化**
4. **数据库索引优化**

## 📈 扩容指南

### 水平扩容

```bash
# 扩容应用服务
kubectl scale deployment user-service --replicas=5 -n rag-system
kubectl scale deployment generation-service --replicas=3 -n rag-system
```

### 垂直扩容

```yaml
# 调整资源限制
resources:
  requests:
    memory: "512Mi"
    cpu: "500m"
  limits:
    memory: "1Gi"
    cpu: "1000m"
```

## 🔄 更新和回滚

### 滚动更新

```bash
# 更新镜像
kubectl set image deployment/user-service user-service=rag-system/user-service:v2.0 -n rag-system

# 查看更新状态
kubectl rollout status deployment/user-service -n rag-system
```

### 回滚

```bash
# 查看历史版本
kubectl rollout history deployment/user-service -n rag-system

# 回滚到上一版本
kubectl rollout undo deployment/user-service -n rag-system
```

## 📝 维护建议

1. **定期备份数据库**
2. **监控系统资源使用**
3. **定期更新安全补丁**
4. **性能测试和优化**
5. **日志分析和清理**
