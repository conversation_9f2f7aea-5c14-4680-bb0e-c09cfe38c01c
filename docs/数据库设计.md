# RAG系统数据库设计文档

## 📋 概述

本文档描述了RAG（检索增强生成）系统的数据库设计，包括表结构、关系、索引和约束等详细信息。

## 🗄️ 数据库信息

- **数据库类型**: PostgreSQL 15+
- **字符集**: UTF-8
- **时区**: UTC
- **扩展**: uuid-ossp, pg_trgm

## 📊 数据模型概览

```mermaid
erDiagram
    users ||--o{ user_preferences : has
    users ||--o{ user_sessions : has
    users ||--o{ documents : owns
    users ||--o{ conversations : participates
    users ||--o{ audit_logs : generates
    
    documents ||--o{ document_chunks : contains
    document_chunks ||--o{ retrieval_results : referenced_in
    
    conversations ||--o{ messages : contains
    messages ||--o{ retrieval_results : uses
    
    system_configs }o--|| config_types : categorized_by
    job_queue }o--|| job_types : typed_by
```

## 📋 表结构详细说明

### 1. 用户管理模块

#### users (用户表)
存储系统用户的基本信息和认证数据。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 用户唯一标识 |
| email | VARCHAR(255) | UNIQUE NOT NULL | 用户邮箱 |
| password_hash | VARCHAR(255) | NOT NULL | 密码哈希值 |
| name | VARCHAR(100) | NOT NULL | 用户姓名 |
| avatar_url | TEXT | | 头像URL |
| role | VARCHAR(50) | NOT NULL DEFAULT 'user' | 用户角色 |
| status | VARCHAR(20) | NOT NULL DEFAULT 'active' | 用户状态 |
| email_verified | BOOLEAN | DEFAULT FALSE | 邮箱验证状态 |
| last_login_at | TIMESTAMP WITH TIME ZONE | | 最后登录时间 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

**约束条件:**
- `users_email_check`: 邮箱格式验证
- `users_role_check`: 角色值限制 (admin, user, viewer)
- `users_status_check`: 状态值限制 (active, inactive, suspended)

#### user_preferences (用户偏好表)
存储用户的个性化设置和偏好。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 偏好设置唯一标识 |
| user_id | UUID | NOT NULL REFERENCES users(id) | 关联用户ID |
| language | VARCHAR(10) | DEFAULT 'zh-CN' | 界面语言 |
| timezone | VARCHAR(50) | DEFAULT 'Asia/Shanghai' | 时区设置 |
| theme | VARCHAR(20) | DEFAULT 'light' | 主题设置 |
| notifications | JSONB | DEFAULT '{}' | 通知设置 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

#### user_sessions (用户会话表)
管理用户的登录会话和刷新令牌。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 会话唯一标识 |
| user_id | UUID | NOT NULL REFERENCES users(id) | 关联用户ID |
| refresh_token_hash | VARCHAR(255) | NOT NULL UNIQUE | 刷新令牌哈希 |
| device_info | JSONB | | 设备信息 |
| ip_address | INET | | IP地址 |
| user_agent | TEXT | | 用户代理 |
| expires_at | TIMESTAMP WITH TIME ZONE | NOT NULL | 过期时间 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| last_used_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 最后使用时间 |

### 2. 文档管理模块

#### documents (文档表)
存储上传文档的元数据信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 文档唯一标识 |
| user_id | UUID | NOT NULL REFERENCES users(id) | 文档所有者 |
| filename | VARCHAR(255) | NOT NULL | 原始文件名 |
| title | VARCHAR(500) | NOT NULL | 文档标题 |
| description | TEXT | | 文档描述 |
| file_type | VARCHAR(50) | NOT NULL | 文件类型 |
| file_size | BIGINT | NOT NULL | 文件大小(字节) |
| file_hash | VARCHAR(64) | NOT NULL | 文件哈希值 |
| storage_path | TEXT | NOT NULL | 存储路径 |
| status | VARCHAR(20) | NOT NULL DEFAULT 'uploading' | 处理状态 |
| processing_progress | INTEGER | DEFAULT 0 | 处理进度(0-100) |
| processing_log | JSONB | DEFAULT '[]' | 处理日志 |
| tags | TEXT[] | DEFAULT '{}' | 标签数组 |
| category | VARCHAR(100) | | 文档分类 |
| metadata | JSONB | DEFAULT '{}' | 扩展元数据 |
| uploaded_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 上传时间 |
| processed_at | TIMESTAMP WITH TIME ZONE | | 处理完成时间 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

#### document_chunks (文档块表)
存储文档分块后的内容片段。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 文档块唯一标识 |
| document_id | UUID | NOT NULL REFERENCES documents(id) | 关联文档ID |
| chunk_index | INTEGER | NOT NULL | 块索引序号 |
| content | TEXT | NOT NULL | 块内容 |
| content_hash | VARCHAR(64) | NOT NULL | 内容哈希值 |
| start_position | INTEGER | NOT NULL | 起始位置 |
| end_position | INTEGER | NOT NULL | 结束位置 |
| token_count | INTEGER | | Token数量 |
| metadata | JSONB | DEFAULT '{}' | 块元数据 |
| vectorized | BOOLEAN | DEFAULT FALSE | 是否已向量化 |
| vector_id | VARCHAR(255) | | 向量数据库ID |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

### 3. 对话管理模块

#### conversations (对话表)
存储用户的对话会话信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 对话唯一标识 |
| user_id | UUID | NOT NULL REFERENCES users(id) | 对话参与者 |
| title | VARCHAR(500) | NOT NULL | 对话标题 |
| description | TEXT | | 对话描述 |
| system_prompt | TEXT | | 系统提示词 |
| status | VARCHAR(20) | NOT NULL DEFAULT 'active' | 对话状态 |
| message_count | INTEGER | DEFAULT 0 | 消息数量 |
| last_message_at | TIMESTAMP WITH TIME ZONE | | 最后消息时间 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

#### messages (消息表)
存储对话中的具体消息内容。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 消息唯一标识 |
| conversation_id | UUID | NOT NULL REFERENCES conversations(id) | 关联对话ID |
| role | VARCHAR(20) | NOT NULL | 消息角色 |
| content | TEXT | NOT NULL | 消息内容 |
| metadata | JSONB | DEFAULT '{}' | 消息元数据 |
| parent_message_id | UUID | REFERENCES messages(id) | 父消息ID |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### retrieval_results (检索结果表)
存储消息对应的文档检索结果。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 检索结果唯一标识 |
| message_id | UUID | NOT NULL REFERENCES messages(id) | 关联消息ID |
| chunk_id | UUID | NOT NULL REFERENCES document_chunks(id) | 关联文档块ID |
| score | DECIMAL(5,4) | NOT NULL | 相似度分数 |
| rank_position | INTEGER | NOT NULL | 排名位置 |
| retrieval_strategy | VARCHAR(50) | NOT NULL | 检索策略 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 4. 系统管理模块

#### system_configs (系统配置表)
存储系统的各种配置参数。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 配置唯一标识 |
| config_key | VARCHAR(100) | UNIQUE NOT NULL | 配置键名 |
| config_value | JSONB | NOT NULL | 配置值 |
| description | TEXT | | 配置描述 |
| config_type | VARCHAR(50) | NOT NULL | 配置类型 |
| is_encrypted | BOOLEAN | DEFAULT FALSE | 是否加密 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

#### job_queue (任务队列表)
管理系统的异步任务队列。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 任务唯一标识 |
| job_type | VARCHAR(50) | NOT NULL | 任务类型 |
| job_data | JSONB | NOT NULL | 任务数据 |
| status | VARCHAR(20) | NOT NULL DEFAULT 'pending' | 任务状态 |
| priority | INTEGER | DEFAULT 0 | 任务优先级 |
| attempts | INTEGER | DEFAULT 0 | 尝试次数 |
| max_attempts | INTEGER | DEFAULT 3 | 最大尝试次数 |
| scheduled_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 计划执行时间 |
| started_at | TIMESTAMP WITH TIME ZONE | | 开始执行时间 |
| completed_at | TIMESTAMP WITH TIME ZONE | | 完成时间 |
| error_message | TEXT | | 错误信息 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

#### audit_logs (审计日志表)
记录系统的操作审计日志。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | UUID | PRIMARY KEY | 日志唯一标识 |
| user_id | UUID | REFERENCES users(id) | 操作用户ID |
| action | VARCHAR(100) | NOT NULL | 操作动作 |
| resource_type | VARCHAR(50) | NOT NULL | 资源类型 |
| resource_id | VARCHAR(255) | | 资源ID |
| old_values | JSONB | | 变更前值 |
| new_values | JSONB | | 变更后值 |
| ip_address | INET | | IP地址 |
| user_agent | TEXT | | 用户代理 |
| created_at | TIMESTAMP WITH TIME ZONE | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 🔍 索引设计

### 主要索引

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 文档表索引
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_file_type ON documents(file_type);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);
CREATE INDEX idx_documents_title_search ON documents USING GIN(to_tsvector('english', title));

-- 文档块表索引
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_document_chunks_vectorized ON document_chunks(vectorized);
CREATE INDEX idx_document_chunks_content_search ON document_chunks USING GIN(to_tsvector('english', content));

-- 对话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_last_message_at ON conversations(last_message_at);

-- 消息表索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- 检索结果表索引
CREATE INDEX idx_retrieval_results_message_id ON retrieval_results(message_id);
CREATE INDEX idx_retrieval_results_score ON retrieval_results(score DESC);
```

## 🔧 触发器和函数

### 自动更新时间戳

```sql
-- 更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 应用到相关表
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 📈 性能优化建议

1. **分区策略**
   - 对 `audit_logs` 表按时间分区
   - 对 `messages` 表按对话ID分区

2. **缓存策略**
   - 用户会话信息缓存到Redis
   - 系统配置缓存到Redis
   - 热门文档块缓存到Redis

3. **查询优化**
   - 使用复合索引优化常用查询
   - 定期分析查询计划
   - 使用EXPLAIN分析慢查询

## 🔒 安全考虑

1. **数据加密**
   - 密码使用bcrypt哈希
   - 敏感配置项加密存储
   - 传输层使用TLS加密

2. **访问控制**
   - 基于角色的权限控制(RBAC)
   - 行级安全策略(RLS)
   - 审计日志记录所有操作

3. **数据备份**
   - 定期自动备份
   - 异地备份存储
   - 备份数据加密

## 📊 监控指标

1. **性能指标**
   - 查询响应时间
   - 连接池使用率
   - 缓存命中率

2. **业务指标**
   - 用户活跃度
   - 文档处理量
   - 对话消息数

3. **系统指标**
   - 数据库大小
   - 表空间使用率
   - 索引效率
