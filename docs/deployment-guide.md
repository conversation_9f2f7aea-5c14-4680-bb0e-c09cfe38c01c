# RAG系统部署指南

本文档提供RAG系统的完整部署指南，包括环境准备、服务部署、配置管理和运维监控。

## 📋 目录

- [系统要求](#系统要求)
- [环境准备](#环境准备)
- [快速部署](#快速部署)
- [生产环境部署](#生产环境部署)
- [配置管理](#配置管理)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)

## 🔧 系统要求

### 硬件要求

**最小配置（开发/测试环境）：**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB SSD
- 网络: 100Mbps

**推荐配置（生产环境）：**
- CPU: 8核心以上
- 内存: 32GB RAM以上
- 存储: 500GB SSD以上
- 网络: 1Gbps以上

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 12+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Node.js**: 18.0+ (如果本地开发)
- **Python**: 3.9+ (如果本地开发)

## 🚀 环境准备

### 1. 安装Docker和Docker Compose

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆项目代码

```bash
git clone https://github.com/your-org/rag-system.git
cd rag-system
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

## ⚡ 快速部署

### 使用Docker Compose一键部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 验证部署

```bash
# 运行健康检查
./scripts/health-check.sh

# 运行集成测试
./scripts/test-integration.sh
```

## 🏭 生产环境部署

### 1. 使用Kubernetes部署

```bash
# 创建命名空间
kubectl create namespace rag-system

# 部署配置文件
kubectl apply -f k8s/

# 检查部署状态
kubectl get pods -n rag-system
```

### 2. 配置负载均衡

```yaml
# nginx.conf
upstream api_gateway {
    server rag-api-1:3000;
    server rag-api-2:3000;
    server rag-api-3:3000;
}

server {
    listen 80;
    server_name api.rag-system.com;
    
    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 配置SSL证书

```bash
# 使用Let's Encrypt
certbot --nginx -d api.rag-system.com
```

## ⚙️ 配置管理

### 环境变量配置

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/rag_system
REDIS_URL=redis://localhost:6379

# API密钥配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 服务配置
NODE_ENV=production
LOG_LEVEL=info
```

### 服务配置文件

每个服务都有独立的配置文件：

- `backend/services/user-service/.env`
- `backend/services/document-service/.env`
- `backend/services/embedding-service/.env`
- 等等...

## 📊 监控和日志

### 1. 日志收集

```yaml
# docker-compose.yml 日志配置
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 2. 监控指标

使用Prometheus和Grafana进行监控：

```bash
# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 访问Grafana
open http://localhost:3000
```

### 3. 健康检查

```bash
# 自动健康检查脚本
./scripts/health-check.sh

# 手动检查各服务
curl http://localhost:3000/health
curl http://localhost:3001/api/v1/health
```

## 🔧 故障排除

### 常见问题

**1. 服务启动失败**
```bash
# 检查日志
docker-compose logs service-name

# 检查资源使用
docker stats
```

**2. 数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec postgres psql -U rag_user -d rag_system -c "SELECT 1;"
```

**3. 向量数据库连接失败**
```bash
# 检查Chroma服务
curl http://localhost:8000/api/v1/heartbeat
```

### 性能优化

**1. 数据库优化**
```sql
-- 创建索引
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
```

**2. 缓存优化**
```bash
# Redis内存优化
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

**3. 应用优化**
```javascript
// Node.js内存限制
node --max-old-space-size=4096 app.js
```

## 🔐 安全配置

### 1. 网络安全

```bash
# 防火墙配置
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

### 2. 数据库安全

```sql
-- 创建只读用户
CREATE USER rag_readonly WITH PASSWORD 'secure_password';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO rag_readonly;
```

### 3. API安全

```javascript
// 限流配置
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 最多100个请求
});
```

## 📈 扩展部署

### 水平扩展

```bash
# 扩展API服务
docker-compose up -d --scale api-gateway=3

# 扩展处理服务
docker-compose up -d --scale embedding-service=2
```

### 垂直扩展

```yaml
# docker-compose.yml
services:
  api-gateway:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
```

## 🔄 备份和恢复

### 数据库备份

```bash
# 自动备份脚本
./scripts/backup-database.sh

# 手动备份
pg_dump -h localhost -U rag_user rag_system > backup.sql
```

### 恢复数据

```bash
# 恢复数据库
psql -h localhost -U rag_user rag_system < backup.sql
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看[故障排除](#故障排除)部分
2. 检查[GitHub Issues](https://github.com/your-org/rag-system/issues)
3. 联系技术支持团队

---

**注意**: 本部署指南会根据系统更新持续完善，请定期查看最新版本。
