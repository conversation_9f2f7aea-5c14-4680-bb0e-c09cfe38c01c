# RAG系统开发指南

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [项目结构](#项目结构)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [部署指南](#部署指南)
- [故障排查](#故障排查)

## 🛠️ 开发环境搭建

### 系统要求

- **操作系统**: Linux, macOS, Windows (推荐 Linux/macOS)
- **Node.js**: >= 18.0.0
- **Python**: >= 3.9
- **Docker**: >= 20.10
- **Docker Compose**: >= 2.0
- **Git**: >= 2.30

### 快速启动

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ai-rag-system
   ```

2. **初始化项目**
   ```bash
   # 运行项目初始化脚本
   ./scripts/init-project.sh
   ```

3. **配置环境变量**
   ```bash
   # 编辑 .env 文件，填入必要的配置
   cp .env.example .env
   vim .env
   ```

4. **启动开发环境**
   ```bash
   # 启动所有服务
   ./scripts/dev-start.sh
   
   # 或者使用 Docker Compose
   docker-compose up -d
   ```

### 手动安装依赖

如果自动化脚本失败，可以手动安装：

```bash
# 安装根目录依赖
npm install

# 安装前端依赖
cd frontend && npm install && cd ..

# 安装各个后端服务依赖
cd backend/services/user-service && npm install && cd ../../..
cd backend/services/document-service && npm install && cd ../../..
cd backend/services/generation-service && npm install && cd ../../..
cd backend/services/conversation-service && npm install && cd ../../..
cd backend/api-gateway && npm install && cd ../..

# 安装 Python 服务依赖
cd backend/services/embedding-service && pip install -r requirements.txt && cd ../../..
cd backend/services/retrieval-service && pip install -r requirements.txt && cd ../../..
```

## 📁 项目结构

```
ai-rag-system/
├── backend/                    # 后端服务
│   ├── api-gateway/           # API 网关
│   ├── services/              # 微服务
│   │   ├── user-service/      # 用户服务 (Node.js)
│   │   ├── document-service/  # 文档服务 (Node.js)
│   │   ├── embedding-service/ # 向量化服务 (Python)
│   │   ├── retrieval-service/ # 检索服务 (Python)
│   │   ├── generation-service/# 生成服务 (Node.js)
│   │   └── conversation-service/ # 对话服务 (Node.js)
│   └── shared/               # 共享代码
├── frontend/                  # 前端应用
│   ├── app/                  # Next.js 应用
│   ├── components/           # React 组件
│   ├── hooks/               # 自定义 Hooks
│   ├── lib/                 # 工具库
│   └── styles/              # 样式文件
├── database/                 # 数据库相关
│   └── init/                # 初始化脚本
├── docs/                    # 项目文档
├── scripts/                 # 脚本文件
├── shared/                  # 跨项目共享代码
├── docker-compose.yml       # Docker 编排文件
├── .env.example            # 环境变量示例
└── README.md               # 项目说明
```

## 🔄 开发流程

### Git 工作流

我们使用 Git Flow 工作流：

1. **主分支**
   - `main`: 生产环境分支
   - `develop`: 开发环境分支

2. **功能分支**
   ```bash
   # 创建功能分支
   git checkout -b feature/用户认证功能
   
   # 开发完成后合并到 develop
   git checkout develop
   git merge feature/用户认证功能
   ```

3. **发布分支**
   ```bash
   # 创建发布分支
   git checkout -b release/v1.0.0
   
   # 测试完成后合并到 main 和 develop
   git checkout main
   git merge release/v1.0.0
   git tag v1.0.0
   ```

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能开发
git commit -m "feat(用户服务): 添加用户注册功能"

# 问题修复
git commit -m "fix(文档服务): 修复文件上传失败问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor(检索服务): 优化检索算法性能"

# 测试相关
git commit -m "test(用户服务): 添加用户认证单元测试"
```

## 📝 代码规范

### TypeScript/JavaScript 规范

- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Airbnb JavaScript 风格指南
- 使用 TypeScript 严格模式

```typescript
// 良好的示例
interface UserCreateRequest {
  email: string;
  password: string;
  name: string;
}

export class UserService {
  async createUser(userData: UserCreateRequest): Promise<User> {
    // 实现逻辑
  }
}
```

### Python 规范

- 遵循 PEP 8 风格指南
- 使用 Black 进行代码格式化
- 使用 mypy 进行类型检查

```python
# 良好的示例
from typing import List, Optional
from pydantic import BaseModel

class EmbeddingRequest(BaseModel):
    text: str
    model: str = "text-embedding-ada-002"

class EmbeddingService:
    async def create_embedding(
        self, 
        request: EmbeddingRequest
    ) -> List[float]:
        # 实现逻辑
        pass
```

### 命名规范

- **文件名**: kebab-case (user-service.ts)
- **类名**: PascalCase (UserService)
- **函数名**: camelCase (createUser)
- **常量**: UPPER_SNAKE_CASE (MAX_FILE_SIZE)
- **变量**: camelCase (userData)

## 🧪 测试指南

### 测试策略

1. **单元测试**: 测试单个函数/方法
2. **集成测试**: 测试服务间交互
3. **端到端测试**: 测试完整用户流程

### 运行测试

```bash
# 运行所有测试
npm run test

# 运行特定服务测试
cd backend/services/user-service
npm run test

# 运行前端测试
cd frontend
npm run test

# 运行 Python 服务测试
cd backend/services/embedding-service
pytest
```

### 测试覆盖率

- 目标覆盖率: >= 80%
- 关键业务逻辑: >= 90%

```bash
# 生成覆盖率报告
npm run test:coverage
```

## 🚀 部署指南

### 开发环境部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service-name]
```

### 生产环境部署

1. **构建镜像**
   ```bash
   docker-compose -f docker-compose.prod.yml build
   ```

2. **推送到镜像仓库**
   ```bash
   docker-compose -f docker-compose.prod.yml push
   ```

3. **部署到服务器**
   ```bash
   # 使用 Kubernetes
   kubectl apply -f k8s/

   # 或使用 Docker Swarm
   docker stack deploy -c docker-compose.prod.yml rag-system
   ```

## 🔧 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看服务日志
   docker-compose logs [service-name]
   
   # 检查端口占用
   netstat -tulpn | grep [port]
   
   # 重启服务
   docker-compose restart [service-name]
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready
   
   # 连接数据库
   docker-compose exec postgres psql -U rag_user -d rag_system
   ```

3. **API 调用失败**
   ```bash
   # 检查 API 网关状态
   curl http://localhost:3000/health
   
   # 查看网关日志
   docker-compose logs api-gateway
   ```

### 性能监控

- **应用监控**: Prometheus + Grafana
- **日志分析**: ELK Stack
- **错误追踪**: Sentry

### 调试技巧

1. **使用 VS Code 调试**
   - 配置 `.vscode/launch.json`
   - 设置断点进行调试

2. **Docker 容器调试**
   ```bash
   # 进入容器
   docker-compose exec [service-name] /bin/bash
   
   # 查看容器资源使用
   docker stats
   ```

3. **网络问题调试**
   ```bash
   # 测试服务间连通性
   docker-compose exec [service-a] ping [service-b]
   
   # 查看网络配置
   docker network ls
   docker network inspect rag-network
   ```

## 📚 相关文档

- [API 文档](./API文档.md)
- [数据库设计](./数据库设计.md)
- [系统架构](./系统架构.md)
- [部署指南](./deployment-guide.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request
5. 代码审查
6. 合并代码

## 📞 联系方式

如有问题，请联系开发团队或创建 Issue。
