{"name": "ai-rag-system", "version": "1.0.0", "description": "一个完整的RAG（检索增强生成）服务系统，集成大语言模型接口", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:frontend", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:frontend": "cd frontend && npm run test:coverage", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "format": "npm run format:backend && npm run format:frontend", "format:backend": "cd backend && npm run format", "format:frontend": "cd frontend && npm run format", "format:check": "npm run format:check:backend && npm run format:check:frontend", "format:check:backend": "cd backend && npm run format:check", "format:check:frontend": "cd frontend && npm run format:check", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:ps": "docker-compose ps", "docker:restart": "docker-compose restart", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "npm run db:drop && npm run db:create && npm run db:migrate && npm run db:seed", "db:drop": "cd backend && npm run db:drop", "db:create": "cd backend && npm run db:create", "init": "./scripts/init-project.sh", "dev-start": "./scripts/dev-start.sh", "setup": "npm run init", "clean": "npm run docker:clean && rm -rf node_modules */node_modules", "health-check": "curl -f http://localhost:3000/health || exit 1", "logs:api": "docker-compose logs -f api-gateway", "logs:user": "docker-compose logs -f user-service", "logs:document": "docker-compose logs -f document-service", "logs:embedding": "docker-compose logs -f embedding-service", "logs:retrieval": "docker-compose logs -f retrieval-service", "logs:generation": "docker-compose logs -f generation-service", "logs:conversation": "docker-compose logs -f conversation-service", "logs:frontend": "docker-compose logs -f frontend"}, "keywords": ["RAG", "AI", "LLM", "Vector Database", "Semantic Search", "Document Processing", "Question Answering"], "author": "AI RAG Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "workspaces": ["backend", "frontend", "shared"]}