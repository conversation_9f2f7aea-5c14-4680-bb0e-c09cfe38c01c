# RAG系统文档服务Docker镜像

# 使用官方Node.js 18 Alpine镜像作为基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    giflib-dev \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S docservice -u 1001

# 复制package文件
COPY package*.json ./

# 安装依赖阶段
FROM base AS deps
RUN npm ci --only=production && npm cache clean --force

# 开发依赖阶段
FROM base AS dev-deps
RUN npm ci

# 构建阶段
FROM dev-deps AS build
COPY . .
RUN npm run build

# 生产阶段
FROM base AS production

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3002

# 复制生产依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制构建产物
COPY --from=build /app/dist ./dist

# 复制必要文件
COPY package*.json ./
COPY migrations ./migrations

# 创建必要目录
RUN mkdir -p logs temp/uploads && \
    chown -R docservice:nodejs logs temp

# 切换到非root用户
USER docservice

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/api/v1/health || exit 1

# 暴露端口
EXPOSE $PORT

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]
