/**
 * 文档验证器
 * 使用Joi验证请求参数
 */

import <PERSON><PERSON> from 'joi';

/**
 * 文档上传验证
 */
export const validateDocumentUpload = (data: any) => {
  const schema = Joi.object({
    tags: Joi.array()
      .items(Joi.string().trim().min(1).max(50))
      .max(10)
      .optional()
      .messages({
        'array.max': '标签数量不能超过10个',
        'string.min': '标签不能为空',
        'string.max': '标签长度不能超过50个字符'
      }),
    
    description: Joi.string()
      .trim()
      .max(500)
      .optional()
      .allow('')
      .messages({
        'string.max': '描述长度不能超过500个字符'
      }),
    
    chunkSize: Joi.number()
      .integer()
      .min(100)
      .max(5000)
      .optional()
      .default(1000)
      .messages({
        'number.min': '分块大小不能小于100',
        'number.max': '分块大小不能大于5000'
      }),
    
    chunkOverlap: Joi.number()
      .integer()
      .min(0)
      .max(1000)
      .optional()
      .default(200)
      .messages({
        'number.min': '分块重叠不能小于0',
        'number.max': '分块重叠不能大于1000'
      }),
    
    enableMetadataExtraction: Joi.boolean()
      .optional()
      .default(true),
    
    enableStructureAnalysis: Joi.boolean()
      .optional()
      .default(true)
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * 文档查询验证
 */
export const validateDocumentQuery = (data: any) => {
  const schema = Joi.object({
    status: Joi.string()
      .valid('uploading', 'processing', 'completed', 'failed', 'deleted')
      .optional()
      .messages({
        'any.only': '状态值无效'
      }),
    
    mimeType: Joi.string()
      .trim()
      .optional()
      .messages({
        'string.base': 'MIME类型必须是字符串'
      }),
    
    tags: Joi.alternatives()
      .try(
        Joi.string().trim(),
        Joi.array().items(Joi.string().trim())
      )
      .optional()
      .messages({
        'alternatives.types': '标签必须是字符串或字符串数组'
      }),
    
    search: Joi.string()
      .trim()
      .min(1)
      .max(100)
      .optional()
      .messages({
        'string.min': '搜索关键词不能为空',
        'string.max': '搜索关键词长度不能超过100个字符'
      }),
    
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(20)
      .messages({
        'number.min': '每页数量不能小于1',
        'number.max': '每页数量不能大于100'
      }),
    
    offset: Joi.number()
      .integer()
      .min(0)
      .optional()
      .default(0)
      .messages({
        'number.min': '偏移量不能小于0'
      }),
    
    sortBy: Joi.string()
      .valid('uploadedAt', 'processedAt', 'filename', 'size')
      .optional()
      .default('uploadedAt')
      .messages({
        'any.only': '排序字段无效'
      }),
    
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .messages({
        'any.only': '排序方向无效'
      })
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * 文档ID验证
 */
export const validateDocumentId = (id: string) => {
  const schema = Joi.string()
    .uuid({ version: 'uuidv4' })
    .required()
    .messages({
      'string.guid': '文档ID格式无效',
      'any.required': '文档ID是必需的'
    });

  return schema.validate(id);
};

/**
 * 文档更新验证
 */
export const validateDocumentUpdate = (data: any) => {
  const schema = Joi.object({
    tags: Joi.array()
      .items(Joi.string().trim().min(1).max(50))
      .max(10)
      .optional()
      .messages({
        'array.max': '标签数量不能超过10个',
        'string.min': '标签不能为空',
        'string.max': '标签长度不能超过50个字符'
      }),
    
    description: Joi.string()
      .trim()
      .max(500)
      .optional()
      .allow('')
      .messages({
        'string.max': '描述长度不能超过500个字符'
      })
  }).min(1).messages({
    'object.min': '至少需要提供一个更新字段'
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * 文档重新处理验证
 */
export const validateDocumentReprocess = (data: any) => {
  const schema = Joi.object({
    chunkSize: Joi.number()
      .integer()
      .min(100)
      .max(5000)
      .optional()
      .default(1000)
      .messages({
        'number.min': '分块大小不能小于100',
        'number.max': '分块大小不能大于5000'
      }),
    
    chunkOverlap: Joi.number()
      .integer()
      .min(0)
      .max(1000)
      .optional()
      .default(200)
      .messages({
        'number.min': '分块重叠不能小于0',
        'number.max': '分块重叠不能大于1000'
      }),
    
    enableMetadataExtraction: Joi.boolean()
      .optional()
      .default(true),
    
    enableStructureAnalysis: Joi.boolean()
      .optional()
      .default(true),
    
    forceReprocess: Joi.boolean()
      .optional()
      .default(false)
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * 批量操作验证
 */
export const validateBatchOperation = (data: any) => {
  const schema = Joi.object({
    documentIds: Joi.array()
      .items(Joi.string().uuid({ version: 'uuidv4' }))
      .min(1)
      .max(50)
      .required()
      .messages({
        'array.min': '至少需要选择一个文档',
        'array.max': '一次最多只能操作50个文档',
        'string.guid': '文档ID格式无效',
        'any.required': '文档ID列表是必需的'
      }),
    
    operation: Joi.string()
      .valid('delete', 'reprocess', 'updateTags')
      .required()
      .messages({
        'any.only': '操作类型无效',
        'any.required': '操作类型是必需的'
      }),
    
    // 用于updateTags操作
    tags: Joi.when('operation', {
      is: 'updateTags',
      then: Joi.array()
        .items(Joi.string().trim().min(1).max(50))
        .max(10)
        .required()
        .messages({
          'array.max': '标签数量不能超过10个',
          'string.min': '标签不能为空',
          'string.max': '标签长度不能超过50个字符',
          'any.required': '更新标签操作需要提供标签列表'
        }),
      otherwise: Joi.forbidden()
    })
  });

  return schema.validate(data, { abortEarly: false });
};

/**
 * 文件类型验证
 */
export const validateFileType = (mimeType: string) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/markdown',
    'text/html',
    'text/csv',
    'image/jpeg',
    'image/png'
  ];

  return allowedTypes.includes(mimeType);
};

/**
 * 文件大小验证
 */
export const validateFileSize = (size: number, mimeType: string) => {
  const limits = {
    'application/pdf': 100 * 1024 * 1024,  // 100MB
    'application/msword': 50 * 1024 * 1024, // 50MB
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 50 * 1024 * 1024,
    'text/plain': 5 * 1024 * 1024,  // 5MB
    'image/jpeg': 10 * 1024 * 1024, // 10MB
    'image/png': 10 * 1024 * 1024
  };

  const limit = limits[mimeType] || 10 * 1024 * 1024; // 默认10MB
  return size <= limit;
};
