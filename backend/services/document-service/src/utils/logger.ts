/**
 * 日志工具
 * 基于Winston的结构化日志记录
 */

import winston from 'winston';
import path from 'path';

// 日志级别
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3
};

// 日志颜色
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue'
};

// 添加颜色配置
winston.addColors(logColors);

/**
 * 创建日志格式
 */
const createLogFormat = (colorize: boolean = false) => {
  const formats = [
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ];

  if (colorize) {
    formats.unshift(winston.format.colorize({ all: true }));
  }

  return winston.format.combine(...formats);
};

/**
 * 控制台格式
 */
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level}]: ${message}`;
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

/**
 * 创建传输器
 */
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [];

  // 控制台输出
  if (process.env.NODE_ENV !== 'production') {
    transports.push(
      new winston.transports.Console({
        level: process.env.LOG_LEVEL || 'debug',
        format: consoleFormat,
        handleExceptions: true,
        handleRejections: true
      })
    );
  }

  // 文件输出
  if (process.env.NODE_ENV === 'production') {
    // 错误日志文件
    transports.push(
      new winston.transports.File({
        filename: path.join(process.cwd(), 'logs', 'error.log'),
        level: 'error',
        format: createLogFormat(),
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        handleExceptions: true,
        handleRejections: true
      })
    );

    // 组合日志文件
    transports.push(
      new winston.transports.File({
        filename: path.join(process.cwd(), 'logs', 'combined.log'),
        level: process.env.LOG_LEVEL || 'info',
        format: createLogFormat(),
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 10
      })
    );
  }

  return transports;
};

/**
 * 创建Logger实例
 */
export const logger = winston.createLogger({
  levels: logLevels,
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  format: createLogFormat(),
  defaultMeta: {
    service: 'document-service',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    hostname: require('os').hostname(),
    pid: process.pid
  },
  transports: createTransports(),
  exitOnError: false
});

/**
 * 请求日志记录器
 */
export function logRequest(req: any, res: any, responseTime: number): void {
  const logData = {
    type: 'request',
    requestId: req.headers['x-request-id'],
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    contentLength: res.get('Content-Length') || '0',
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP请求错误', logData);
  } else {
    logger.info('HTTP请求', logData);
  }
}

/**
 * 错误日志记录器
 */
export function logError(error: Error, req?: any, additionalInfo?: any): void {
  const logData = {
    type: 'error',
    message: error.message,
    stack: error.stack,
    name: error.name,
    requestId: req?.headers?.['x-request-id'],
    method: req?.method,
    url: req?.originalUrl,
    ip: req?.ip,
    userAgent: req?.get?.('User-Agent'),
    userId: req?.user?.id,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };

  logger.error('应用错误', logData);
}

/**
 * 文档处理日志记录器
 */
export function logDocumentProcessing(
  action: string,
  documentId: string,
  details: any,
  userId?: string
): void {
  const logData = {
    type: 'document_processing',
    action,
    documentId,
    details,
    userId,
    timestamp: new Date().toISOString()
  };

  logger.info('文档处理', logData);
}

/**
 * 文件操作日志记录器
 */
export function logFileOperation(
  operation: string,
  fileName: string,
  fileSize?: number,
  details?: any
): void {
  const logData = {
    type: 'file_operation',
    operation,
    fileName,
    fileSize,
    details,
    timestamp: new Date().toISOString()
  };

  logger.info('文件操作', logData);
}

/**
 * 性能日志记录器
 */
export function logPerformance(operation: string, duration: number, additionalInfo?: any): void {
  const logData = {
    type: 'performance',
    operation,
    duration: `${duration}ms`,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };

  if (duration > 1000) {
    logger.warn('性能警告', logData);
  } else {
    logger.debug('性能指标', logData);
  }
}

/**
 * 安全日志记录器
 */
export function logSecurity(event: string, details: any, req?: any): void {
  const logData = {
    type: 'security',
    event,
    details,
    requestId: req?.headers?.['x-request-id'],
    ip: req?.ip,
    userAgent: req?.get?.('User-Agent'),
    userId: req?.user?.id,
    timestamp: new Date().toISOString()
  };

  logger.warn('安全事件', logData);
}

/**
 * 创建子Logger
 */
export function createChildLogger(defaultMeta: any): winston.Logger {
  return logger.child(defaultMeta);
}

/**
 * 日志流（用于Morgan等中间件）
 */
export const logStream = {
  write: (message: string) => {
    logger.info(message.trim());
  }
};

// 确保日志目录存在
if (process.env.NODE_ENV === 'production') {
  const fs = require('fs');
  const logDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

// 导出默认logger
export default logger;
