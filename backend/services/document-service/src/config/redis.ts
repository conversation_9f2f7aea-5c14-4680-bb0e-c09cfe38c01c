/**
 * Redis连接配置
 * 用于缓存和队列管理
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';

let redisClient: RedisClientType;

/**
 * Redis连接配置
 */
const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 5000,
    lazyConnect: true,
    reconnectStrategy: (retries: number) => {
      if (retries > 10) {
        logger.error('Redis重连次数超过限制，停止重连');
        return false;
      }
      const delay = Math.min(retries * 100, 3000);
      logger.warn(`Redis连接失败，${delay}ms后重试 (第${retries}次)`);
      return delay;
    }
  }
};

/**
 * 连接Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    redisClient = createClient(redisConfig);

    // 错误处理
    redisClient.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });

    redisClient.on('connect', () => {
      logger.info('Redis连接建立');
    });

    redisClient.on('ready', () => {
      logger.info('Redis连接就绪');
    });

    redisClient.on('end', () => {
      logger.warn('Redis连接关闭');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis重新连接中...');
    });

    // 连接到Redis
    await redisClient.connect();
    
    // 测试连接
    await redisClient.ping();
    logger.info('Redis连接测试成功');

  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 获取Redis客户端
 */
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis未初始化，请先调用connectRedis()');
  }
  return redisClient;
}

/**
 * 设置缓存
 */
export async function setCache(
  key: string, 
  value: any, 
  ttl: number = 3600
): Promise<void> {
  try {
    const client = getRedisClient();
    const serializedValue = JSON.stringify(value);
    await client.setEx(key, ttl, serializedValue);
    
    logger.debug('缓存设置成功', { key, ttl });
  } catch (error) {
    logger.error('设置缓存失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 获取缓存
 */
export async function getCache(key: string): Promise<any> {
  try {
    const client = getRedisClient();
    const value = await client.get(key);
    
    if (value === null) {
      logger.debug('缓存未命中', { key });
      return null;
    }
    
    const parsedValue = JSON.parse(value);
    logger.debug('缓存命中', { key });
    return parsedValue;
  } catch (error) {
    logger.error('获取缓存失败:', { key, error: error.message });
    return null;
  }
}

/**
 * 删除缓存
 */
export async function deleteCache(key: string): Promise<void> {
  try {
    const client = getRedisClient();
    await client.del(key);
    logger.debug('缓存删除成功', { key });
  } catch (error) {
    logger.error('删除缓存失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 批量删除缓存
 */
export async function deleteCachePattern(pattern: string): Promise<void> {
  try {
    const client = getRedisClient();
    const keys = await client.keys(pattern);
    
    if (keys.length > 0) {
      await client.del(keys);
      logger.debug('批量删除缓存成功', { pattern, count: keys.length });
    }
  } catch (error) {
    logger.error('批量删除缓存失败:', { pattern, error: error.message });
    throw error;
  }
}

/**
 * 检查缓存是否存在
 */
export async function existsCache(key: string): Promise<boolean> {
  try {
    const client = getRedisClient();
    const exists = await client.exists(key);
    return exists === 1;
  } catch (error) {
    logger.error('检查缓存存在性失败:', { key, error: error.message });
    return false;
  }
}

/**
 * 关闭Redis连接
 */
export async function closeRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    logger.info('Redis连接已关闭');
  }
}
