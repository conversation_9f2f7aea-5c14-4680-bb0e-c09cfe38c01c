/**
 * 数据库连接配置
 * 使用PostgreSQL存储文档元数据
 */

import { Pool, PoolClient } from 'pg';
import { logger } from '../utils/logger';

let pool: Pool;

/**
 * 数据库连接配置
 */
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: parseInt(process.env.DB_POOL_MAX || '20'),
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000'),
};

/**
 * 连接数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    pool = new Pool(dbConfig);

    // 测试连接
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();

    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 获取数据库连接池
 */
export function getPool(): Pool {
  if (!pool) {
    throw new Error('数据库未初始化，请先调用connectDatabase()');
  }
  return pool;
}

/**
 * 执行查询
 */
export async function query(text: string, params?: any[]): Promise<any> {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    logger.error('数据库查询失败:', { query: text, params, error: error.message });
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 开始事务
 */
export async function beginTransaction(): Promise<PoolClient> {
  const client = await pool.connect();
  await client.query('BEGIN');
  return client;
}

/**
 * 提交事务
 */
export async function commitTransaction(client: PoolClient): Promise<void> {
  try {
    await client.query('COMMIT');
  } finally {
    client.release();
  }
}

/**
 * 回滚事务
 */
export async function rollbackTransaction(client: PoolClient): Promise<void> {
  try {
    await client.query('ROLLBACK');
  } finally {
    client.release();
  }
}

/**
 * 关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    logger.info('数据库连接已关闭');
  }
}
