/**
 * 文档处理服务
 * 负责文档的预处理、分块和元数据提取
 */

import { v4 as uuidv4 } from 'uuid';
import { DocumentParser, ParseResult } from './documentParser';
import { TextChunker } from './textChunker';
import { MetadataExtractor } from './metadataExtractor';
import { logger, logDocumentProcessing, logPerformance } from '../utils/logger';

/**
 * 文档处理配置
 */
export interface ProcessingConfig {
  chunkSize: number;
  chunkOverlap: number;
  enableMetadataExtraction: boolean;
  enableStructureAnalysis: boolean;
  language?: string;
}

/**
 * 文档块接口
 */
export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  metadata: {
    chunkIndex: number;
    startPosition: number;
    endPosition: number;
    wordCount: number;
    characterCount: number;
    heading?: string;
    section?: string;
    pageNumber?: number;
  };
  embedding?: number[];
}

/**
 * 处理结果接口
 */
export interface ProcessingResult {
  documentId: string;
  originalText: string;
  chunks: DocumentChunk[];
  metadata: any;
  structure?: any;
  processingTime: number;
  statistics: {
    totalChunks: number;
    totalWords: number;
    totalCharacters: number;
    averageChunkSize: number;
  };
}

/**
 * 文档处理器类
 */
export class DocumentProcessor {
  private parser: DocumentParser;
  private chunker: TextChunker;
  private metadataExtractor: MetadataExtractor;

  constructor() {
    this.parser = new DocumentParser();
    this.chunker = new TextChunker();
    this.metadataExtractor = new MetadataExtractor();
  }

  /**
   * 处理文档
   */
  async processDocument(
    filePath: string,
    mimeType: string,
    documentId: string,
    config: ProcessingConfig
  ): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      logger.info('开始处理文档', {
        documentId,
        filePath,
        mimeType,
        config
      });

      // 1. 解析文档
      const parseResult = await this.parser.parseDocument(filePath, mimeType);
      logDocumentProcessing('parse_complete', documentId, {
        textLength: parseResult.text.length,
        pages: parseResult.metadata.pages
      });

      // 2. 提取增强元数据
      let enhancedMetadata = parseResult.metadata;
      if (config.enableMetadataExtraction) {
        const extractedMetadata = await this.metadataExtractor.extractMetadata(
          parseResult.text,
          parseResult.structure
        );
        enhancedMetadata = { ...parseResult.metadata, ...extractedMetadata };
      }

      // 3. 文本分块
      const chunks = await this.chunker.chunkText(
        parseResult.text,
        {
          chunkSize: config.chunkSize,
          chunkOverlap: config.chunkOverlap,
          preserveStructure: config.enableStructureAnalysis
        },
        parseResult.structure
      );

      // 4. 创建文档块对象
      const documentChunks = this.createDocumentChunks(
        documentId,
        chunks,
        parseResult.structure
      );

      // 5. 计算统计信息
      const statistics = this.calculateStatistics(documentChunks);

      const processingTime = Date.now() - startTime;

      const result: ProcessingResult = {
        documentId,
        originalText: parseResult.text,
        chunks: documentChunks,
        metadata: enhancedMetadata,
        structure: parseResult.structure,
        processingTime,
        statistics
      };

      logDocumentProcessing('process_complete', documentId, {
        chunksCount: documentChunks.length,
        processingTime: `${processingTime}ms`,
        statistics
      });

      logPerformance('document_processing', processingTime, {
        documentId,
        textLength: parseResult.text.length,
        chunksCount: documentChunks.length
      });

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('文档处理失败', {
        documentId,
        filePath,
        processingTime: `${processingTime}ms`,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 创建文档块对象
   */
  private createDocumentChunks(
    documentId: string,
    chunks: Array<{
      text: string;
      startPosition: number;
      endPosition: number;
      metadata?: any;
    }>,
    structure?: any
  ): DocumentChunk[] {
    return chunks.map((chunk, index) => {
      const chunkId = uuidv4();
      
      // 查找当前块所属的标题/章节
      const heading = this.findRelevantHeading(
        chunk.startPosition,
        structure?.headings || []
      );

      return {
        id: chunkId,
        documentId,
        content: chunk.text,
        metadata: {
          chunkIndex: index,
          startPosition: chunk.startPosition,
          endPosition: chunk.endPosition,
          wordCount: this.countWords(chunk.text),
          characterCount: chunk.text.length,
          heading: heading?.text,
          section: heading?.text,
          pageNumber: chunk.metadata?.pageNumber,
          ...chunk.metadata
        }
      };
    });
  }

  /**
   * 查找相关标题
   */
  private findRelevantHeading(
    position: number,
    headings: Array<{ text: string; position: number; level: number }>
  ): { text: string; level: number } | null {
    // 找到位置之前最近的标题
    let relevantHeading = null;
    
    for (const heading of headings) {
      if (heading.position <= position) {
        if (!relevantHeading || heading.position > relevantHeading.position) {
          relevantHeading = heading;
        }
      }
    }

    return relevantHeading;
  }

  /**
   * 计算统计信息
   */
  private calculateStatistics(chunks: DocumentChunk[]): any {
    const totalChunks = chunks.length;
    const totalWords = chunks.reduce((sum, chunk) => sum + chunk.metadata.wordCount, 0);
    const totalCharacters = chunks.reduce((sum, chunk) => sum + chunk.metadata.characterCount, 0);
    const averageChunkSize = totalChunks > 0 ? Math.round(totalCharacters / totalChunks) : 0;

    return {
      totalChunks,
      totalWords,
      totalCharacters,
      averageChunkSize
    };
  }

  /**
   * 统计单词数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 重新处理文档块
   */
  async reprocessChunks(
    originalText: string,
    documentId: string,
    config: ProcessingConfig,
    structure?: any
  ): Promise<DocumentChunk[]> {
    try {
      logger.info('重新处理文档块', { documentId, config });

      // 重新分块
      const chunks = await this.chunker.chunkText(
        originalText,
        {
          chunkSize: config.chunkSize,
          chunkOverlap: config.chunkOverlap,
          preserveStructure: config.enableStructureAnalysis
        },
        structure
      );

      // 创建新的文档块
      const documentChunks = this.createDocumentChunks(documentId, chunks, structure);

      logDocumentProcessing('reprocess_complete', documentId, {
        chunksCount: documentChunks.length
      });

      return documentChunks;
    } catch (error) {
      logger.error('重新处理文档块失败', {
        documentId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 验证处理结果
   */
  validateProcessingResult(result: ProcessingResult): boolean {
    try {
      // 检查基本字段
      if (!result.documentId || !result.originalText || !result.chunks) {
        return false;
      }

      // 检查块的完整性
      for (const chunk of result.chunks) {
        if (!chunk.id || !chunk.content || !chunk.metadata) {
          return false;
        }
      }

      // 检查统计信息
      if (!result.statistics || result.statistics.totalChunks !== result.chunks.length) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('验证处理结果失败', { error: error.message });
      return false;
    }
  }

  /**
   * 获取处理摘要
   */
  getProcessingSummary(result: ProcessingResult): any {
    return {
      documentId: result.documentId,
      processingTime: result.processingTime,
      statistics: result.statistics,
      metadata: {
        title: result.metadata.title,
        author: result.metadata.author,
        format: result.metadata.format,
        size: result.metadata.size,
        pages: result.metadata.pages
      },
      structure: {
        headingsCount: result.structure?.headings?.length || 0,
        tablesCount: result.structure?.tables?.length || 0,
        imagesCount: result.structure?.images?.length || 0
      }
    };
  }
}
