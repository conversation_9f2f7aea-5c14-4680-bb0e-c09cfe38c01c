/**
 * 文本分块服务
 * 智能分割文本为适合向量化的块
 */

import { logger } from '../utils/logger';

/**
 * 分块配置接口
 */
export interface ChunkingConfig {
  chunkSize: number;
  chunkOverlap: number;
  preserveStructure: boolean;
  separators?: string[];
  minChunkSize?: number;
  maxChunkSize?: number;
}

/**
 * 文本块接口
 */
export interface TextChunk {
  text: string;
  startPosition: number;
  endPosition: number;
  metadata?: {
    heading?: string;
    section?: string;
    pageNumber?: number;
    chunkType?: 'paragraph' | 'heading' | 'list' | 'table' | 'code';
  };
}

/**
 * 文本分块器类
 */
export class TextChunker {
  private defaultSeparators = [
    '\n\n\n',  // 多个换行符
    '\n\n',    // 段落分隔
    '\n',      // 单行分隔
    '. ',      // 句子分隔
    '! ',      // 感叹句分隔
    '? ',      // 疑问句分隔
    '; ',      // 分号分隔
    ', ',      // 逗号分隔
    ' ',       // 空格分隔
    ''         // 字符分隔
  ];

  /**
   * 分割文本
   */
  async chunkText(
    text: string,
    config: ChunkingConfig,
    structure?: any
  ): Promise<TextChunk[]> {
    try {
      logger.info('开始文本分块', {
        textLength: text.length,
        config
      });

      // 预处理文本
      const cleanedText = this.preprocessText(text);

      let chunks: TextChunk[];

      if (config.preserveStructure && structure) {
        // 基于结构的智能分块
        chunks = await this.structureBasedChunking(cleanedText, config, structure);
      } else {
        // 基于大小的递归分块
        chunks = await this.recursiveChunking(cleanedText, config);
      }

      // 后处理：添加重叠和验证
      chunks = this.addOverlapAndValidate(chunks, config);

      logger.info('文本分块完成', {
        originalLength: text.length,
        chunksCount: chunks.length,
        averageChunkSize: chunks.reduce((sum, chunk) => sum + chunk.text.length, 0) / chunks.length
      });

      return chunks;
    } catch (error) {
      logger.error('文本分块失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 预处理文本
   */
  private preprocessText(text: string): string {
    return text
      .replace(/\r\n/g, '\n')           // 统一换行符
      .replace(/\r/g, '\n')             // 统一换行符
      .replace(/\t/g, '    ')           // 制表符转空格
      .replace(/\u00A0/g, ' ')          // 非断行空格转普通空格
      .replace(/\s{3,}/g, '  ')         // 多个空格压缩为两个
      .replace(/\n{4,}/g, '\n\n\n')     // 多个换行符压缩
      .trim();
  }

  /**
   * 基于结构的智能分块
   */
  private async structureBasedChunking(
    text: string,
    config: ChunkingConfig,
    structure: any
  ): Promise<TextChunk[]> {
    const chunks: TextChunk[] = [];
    const headings = structure.headings || [];
    const tables = structure.tables || [];

    // 按标题分割文本
    if (headings.length > 0) {
      let currentPosition = 0;

      for (let i = 0; i < headings.length; i++) {
        const heading = headings[i];
        const nextHeading = headings[i + 1];
        
        const sectionStart = heading.position;
        const sectionEnd = nextHeading ? nextHeading.position : text.length;
        
        // 处理标题前的内容
        if (sectionStart > currentPosition) {
          const beforeSection = text.substring(currentPosition, sectionStart);
          if (beforeSection.trim().length > 0) {
            const beforeChunks = await this.recursiveChunking(beforeSection, config);
            chunks.push(...beforeChunks.map(chunk => ({
              ...chunk,
              startPosition: chunk.startPosition + currentPosition,
              endPosition: chunk.endPosition + currentPosition
            })));
          }
        }

        // 处理当前章节
        const sectionText = text.substring(sectionStart, sectionEnd);
        const sectionChunks = await this.chunkSection(
          sectionText,
          config,
          heading,
          sectionStart
        );
        chunks.push(...sectionChunks);

        currentPosition = sectionEnd;
      }

      // 处理最后剩余的内容
      if (currentPosition < text.length) {
        const remainingText = text.substring(currentPosition);
        if (remainingText.trim().length > 0) {
          const remainingChunks = await this.recursiveChunking(remainingText, config);
          chunks.push(...remainingChunks.map(chunk => ({
            ...chunk,
            startPosition: chunk.startPosition + currentPosition,
            endPosition: chunk.endPosition + currentPosition
          })));
        }
      }
    } else {
      // 没有标题结构，使用递归分块
      return await this.recursiveChunking(text, config);
    }

    return chunks;
  }

  /**
   * 分块章节
   */
  private async chunkSection(
    sectionText: string,
    config: ChunkingConfig,
    heading: any,
    basePosition: number
  ): Promise<TextChunk[]> {
    const chunks: TextChunk[] = [];

    if (sectionText.length <= config.chunkSize) {
      // 章节足够小，作为单个块
      chunks.push({
        text: sectionText,
        startPosition: basePosition,
        endPosition: basePosition + sectionText.length,
        metadata: {
          heading: heading.text,
          section: heading.text,
          chunkType: 'paragraph'
        }
      });
    } else {
      // 章节太大，需要进一步分块
      const subChunks = await this.recursiveChunking(sectionText, config);
      chunks.push(...subChunks.map(chunk => ({
        ...chunk,
        startPosition: chunk.startPosition + basePosition,
        endPosition: chunk.endPosition + basePosition,
        metadata: {
          ...chunk.metadata,
          heading: heading.text,
          section: heading.text
        }
      })));
    }

    return chunks;
  }

  /**
   * 递归分块
   */
  private async recursiveChunking(
    text: string,
    config: ChunkingConfig
  ): Promise<TextChunk[]> {
    const separators = config.separators || this.defaultSeparators;
    return this.splitTextRecursively(text, separators, config, 0);
  }

  /**
   * 递归分割文本
   */
  private splitTextRecursively(
    text: string,
    separators: string[],
    config: ChunkingConfig,
    basePosition: number
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    
    if (text.length <= config.chunkSize) {
      // 文本足够小，直接返回
      if (text.trim().length > 0) {
        chunks.push({
          text: text.trim(),
          startPosition: basePosition,
          endPosition: basePosition + text.length,
          metadata: { chunkType: 'paragraph' }
        });
      }
      return chunks;
    }

    // 尝试用当前分隔符分割
    const separator = separators[0];
    const splits = text.split(separator);

    if (splits.length === 1) {
      // 当前分隔符无法分割，尝试下一个
      if (separators.length > 1) {
        return this.splitTextRecursively(text, separators.slice(1), config, basePosition);
      } else {
        // 所有分隔符都无法分割，强制按大小分割
        return this.forceSplit(text, config, basePosition);
      }
    }

    // 合并分割后的片段
    let currentChunk = '';
    let currentPosition = basePosition;

    for (let i = 0; i < splits.length; i++) {
      const split = splits[i];
      const splitWithSeparator = i < splits.length - 1 ? split + separator : split;

      if (currentChunk.length + splitWithSeparator.length <= config.chunkSize) {
        // 可以添加到当前块
        currentChunk += splitWithSeparator;
      } else {
        // 当前块已满，保存并开始新块
        if (currentChunk.trim().length > 0) {
          chunks.push({
            text: currentChunk.trim(),
            startPosition: currentPosition,
            endPosition: currentPosition + currentChunk.length,
            metadata: { chunkType: 'paragraph' }
          });
        }

        currentPosition += currentChunk.length;
        currentChunk = splitWithSeparator;

        // 如果单个分割片段仍然太大，递归处理
        if (splitWithSeparator.length > config.chunkSize) {
          const subChunks = this.splitTextRecursively(
            splitWithSeparator,
            separators.slice(1),
            config,
            currentPosition
          );
          chunks.push(...subChunks);
          currentPosition += splitWithSeparator.length;
          currentChunk = '';
        }
      }
    }

    // 处理最后的块
    if (currentChunk.trim().length > 0) {
      chunks.push({
        text: currentChunk.trim(),
        startPosition: currentPosition,
        endPosition: currentPosition + currentChunk.length,
        metadata: { chunkType: 'paragraph' }
      });
    }

    return chunks;
  }

  /**
   * 强制分割（当所有分隔符都无法使用时）
   */
  private forceSplit(text: string, config: ChunkingConfig, basePosition: number): TextChunk[] {
    const chunks: TextChunk[] = [];
    let position = 0;

    while (position < text.length) {
      const chunkEnd = Math.min(position + config.chunkSize, text.length);
      const chunkText = text.substring(position, chunkEnd);

      chunks.push({
        text: chunkText,
        startPosition: basePosition + position,
        endPosition: basePosition + chunkEnd,
        metadata: { chunkType: 'paragraph' }
      });

      position = chunkEnd;
    }

    return chunks;
  }

  /**
   * 添加重叠并验证
   */
  private addOverlapAndValidate(chunks: TextChunk[], config: ChunkingConfig): TextChunk[] {
    if (config.chunkOverlap === 0 || chunks.length <= 1) {
      return chunks;
    }

    const overlappedChunks: TextChunk[] = [];

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      let chunkText = chunk.text;

      // 添加前向重叠
      if (i > 0 && config.chunkOverlap > 0) {
        const prevChunk = chunks[i - 1];
        const overlapText = this.getOverlapText(prevChunk.text, config.chunkOverlap, 'end');
        if (overlapText) {
          chunkText = overlapText + ' ' + chunkText;
        }
      }

      // 添加后向重叠
      if (i < chunks.length - 1 && config.chunkOverlap > 0) {
        const nextChunk = chunks[i + 1];
        const overlapText = this.getOverlapText(nextChunk.text, config.chunkOverlap, 'start');
        if (overlapText) {
          chunkText = chunkText + ' ' + overlapText;
        }
      }

      overlappedChunks.push({
        ...chunk,
        text: chunkText
      });
    }

    return overlappedChunks;
  }

  /**
   * 获取重叠文本
   */
  private getOverlapText(text: string, overlapSize: number, position: 'start' | 'end'): string {
    if (position === 'start') {
      return text.substring(0, Math.min(overlapSize, text.length));
    } else {
      return text.substring(Math.max(0, text.length - overlapSize));
    }
  }

  /**
   * 验证分块结果
   */
  validateChunks(chunks: TextChunk[], config: ChunkingConfig): boolean {
    for (const chunk of chunks) {
      // 检查块大小
      if (chunk.text.length > config.maxChunkSize || 
          chunk.text.length < (config.minChunkSize || 10)) {
        return false;
      }

      // 检查位置信息
      if (chunk.startPosition >= chunk.endPosition) {
        return false;
      }
    }

    return true;
  }
}
