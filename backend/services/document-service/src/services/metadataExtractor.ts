/**
 * 元数据提取服务
 * 从文档内容中提取语义和结构化元数据
 */

import { logger } from '../utils/logger';

/**
 * 提取的元数据接口
 */
export interface ExtractedMetadata {
  // 内容分析
  topics?: string[];
  keywords?: string[];
  entities?: Array<{
    text: string;
    type: 'person' | 'organization' | 'location' | 'date' | 'number' | 'other';
    confidence: number;
  }>;
  
  // 语言和风格
  language?: string;
  readabilityScore?: number;
  sentiment?: 'positive' | 'negative' | 'neutral';
  
  // 结构信息
  documentType?: 'article' | 'report' | 'manual' | 'presentation' | 'other';
  sections?: Array<{
    title: string;
    level: number;
    wordCount: number;
  }>;
  
  // 统计信息
  averageSentenceLength?: number;
  vocabularyComplexity?: number;
  
  // 质量指标
  completeness?: number;
  coherence?: number;
}

/**
 * 元数据提取器类
 */
export class MetadataExtractor {
  
  /**
   * 提取元数据
   */
  async extractMetadata(text: string, structure?: any): Promise<ExtractedMetadata> {
    try {
      logger.info('开始提取元数据', { textLength: text.length });

      const metadata: ExtractedMetadata = {};

      // 并行执行各种提取任务
      const [
        language,
        keywords,
        entities,
        readabilityScore,
        sentiment,
        documentType,
        sections,
        statistics
      ] = await Promise.all([
        this.detectLanguage(text),
        this.extractKeywords(text),
        this.extractEntities(text),
        this.calculateReadabilityScore(text),
        this.analyzeSentiment(text),
        this.classifyDocumentType(text, structure),
        this.extractSections(structure),
        this.calculateStatistics(text)
      ]);

      // 组装元数据
      metadata.language = language;
      metadata.keywords = keywords;
      metadata.entities = entities;
      metadata.readabilityScore = readabilityScore;
      metadata.sentiment = sentiment;
      metadata.documentType = documentType;
      metadata.sections = sections;
      metadata.averageSentenceLength = statistics.averageSentenceLength;
      metadata.vocabularyComplexity = statistics.vocabularyComplexity;
      metadata.completeness = this.assessCompleteness(text, structure);
      metadata.coherence = this.assessCoherence(text);

      logger.info('元数据提取完成', {
        keywordsCount: keywords?.length || 0,
        entitiesCount: entities?.length || 0,
        language,
        documentType
      });

      return metadata;
    } catch (error) {
      logger.error('元数据提取失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检测语言
   */
  private async detectLanguage(text: string): Promise<string> {
    try {
      // 简单的语言检测逻辑
      const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
      const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
      const totalChars = text.length;

      if (chineseChars / totalChars > 0.3) {
        return 'zh';
      } else if (englishChars / totalChars > 0.5) {
        return 'en';
      } else {
        return 'unknown';
      }
    } catch (error) {
      logger.warn('语言检测失败', { error: error.message });
      return 'unknown';
    }
  }

  /**
   * 提取关键词
   */
  private async extractKeywords(text: string): Promise<string[]> {
    try {
      // 简单的关键词提取
      const words = text.toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2);

      // 计算词频
      const wordFreq: Record<string, number> = {};
      words.forEach(word => {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      });

      // 过滤停用词并排序
      const stopWords = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
      ]);

      const keywords = Object.entries(wordFreq)
        .filter(([word, freq]) => !stopWords.has(word) && freq > 1)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 20)
        .map(([word]) => word);

      return keywords;
    } catch (error) {
      logger.warn('关键词提取失败', { error: error.message });
      return [];
    }
  }

  /**
   * 提取实体
   */
  private async extractEntities(text: string): Promise<Array<{
    text: string;
    type: 'person' | 'organization' | 'location' | 'date' | 'number' | 'other';
    confidence: number;
  }>> {
    try {
      const entities: Array<{
        text: string;
        type: 'person' | 'organization' | 'location' | 'date' | 'number' | 'other';
        confidence: number;
      }> = [];

      // 简单的实体识别
      
      // 日期识别
      const dateRegex = /\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?|\d{1,2}\/\d{1,2}\/\d{4}/g;
      let match;
      while ((match = dateRegex.exec(text)) !== null) {
        entities.push({
          text: match[0],
          type: 'date',
          confidence: 0.9
        });
      }

      // 数字识别
      const numberRegex = /\d+(?:\.\d+)?%?/g;
      while ((match = numberRegex.exec(text)) !== null) {
        if (match[0].length > 1) {
          entities.push({
            text: match[0],
            type: 'number',
            confidence: 0.8
          });
        }
      }

      // 中文人名识别（简单模式）
      const chineseNameRegex = /[王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程苏魏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤][一-龯]{1,3}/g;
      while ((match = chineseNameRegex.exec(text)) !== null) {
        entities.push({
          text: match[0],
          type: 'person',
          confidence: 0.6
        });
      }

      // 去重并限制数量
      const uniqueEntities = entities
        .filter((entity, index, self) => 
          index === self.findIndex(e => e.text === entity.text && e.type === entity.type)
        )
        .slice(0, 50);

      return uniqueEntities;
    } catch (error) {
      logger.warn('实体提取失败', { error: error.message });
      return [];
    }
  }

  /**
   * 计算可读性分数
   */
  private async calculateReadabilityScore(text: string): Promise<number> {
    try {
      const sentences = text.split(/[.!?。！？]/).filter(s => s.trim().length > 0);
      const words = text.split(/\s+/).filter(w => w.length > 0);
      const syllables = this.countSyllables(text);

      if (sentences.length === 0 || words.length === 0) {
        return 0;
      }

      // 简化的Flesch Reading Ease公式
      const avgSentenceLength = words.length / sentences.length;
      const avgSyllablesPerWord = syllables / words.length;
      
      const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
      
      // 标准化到0-100
      return Math.max(0, Math.min(100, score));
    } catch (error) {
      logger.warn('可读性分数计算失败', { error: error.message });
      return 50; // 默认中等可读性
    }
  }

  /**
   * 计算音节数（简化版）
   */
  private countSyllables(text: string): number {
    // 对于中文，每个汉字算一个音节
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    
    // 对于英文，简单估算
    const englishWords = text.match(/[a-zA-Z]+/g) || [];
    const englishSyllables = englishWords.reduce((count, word) => {
      return count + Math.max(1, word.length / 3);
    }, 0);

    return chineseChars + englishSyllables;
  }

  /**
   * 分析情感
   */
  private async analyzeSentiment(text: string): Promise<'positive' | 'negative' | 'neutral'> {
    try {
      // 简单的情感词典方法
      const positiveWords = ['好', '棒', '优秀', '成功', '满意', '高兴', '喜欢', '赞', 'excellent', 'good', 'great', 'amazing', 'wonderful', 'fantastic', 'positive', 'success'];
      const negativeWords = ['坏', '差', '失败', '问题', '错误', '困难', '不好', '讨厌', 'bad', 'terrible', 'awful', 'horrible', 'negative', 'problem', 'error', 'fail'];

      const words = text.toLowerCase().split(/\s+/);
      let positiveCount = 0;
      let negativeCount = 0;

      words.forEach(word => {
        if (positiveWords.some(pw => word.includes(pw))) {
          positiveCount++;
        }
        if (negativeWords.some(nw => word.includes(nw))) {
          negativeCount++;
        }
      });

      if (positiveCount > negativeCount) {
        return 'positive';
      } else if (negativeCount > positiveCount) {
        return 'negative';
      } else {
        return 'neutral';
      }
    } catch (error) {
      logger.warn('情感分析失败', { error: error.message });
      return 'neutral';
    }
  }

  /**
   * 分类文档类型
   */
  private async classifyDocumentType(text: string, structure?: any): Promise<'article' | 'report' | 'manual' | 'presentation' | 'other'> {
    try {
      const lowerText = text.toLowerCase();
      
      // 基于关键词和结构特征分类
      if (lowerText.includes('摘要') || lowerText.includes('abstract') || 
          lowerText.includes('结论') || lowerText.includes('conclusion')) {
        return 'report';
      }
      
      if (lowerText.includes('步骤') || lowerText.includes('操作') || 
          lowerText.includes('manual') || lowerText.includes('guide')) {
        return 'manual';
      }
      
      if (structure?.headings && structure.headings.length > 5) {
        return 'report';
      }
      
      if (structure?.tables && structure.tables.length > 2) {
        return 'report';
      }
      
      return 'article';
    } catch (error) {
      logger.warn('文档类型分类失败', { error: error.message });
      return 'other';
    }
  }

  /**
   * 提取章节信息
   */
  private async extractSections(structure?: any): Promise<Array<{
    title: string;
    level: number;
    wordCount: number;
  }>> {
    try {
      if (!structure?.headings) {
        return [];
      }

      return structure.headings.map((heading: any) => ({
        title: heading.text,
        level: heading.level,
        wordCount: heading.text.split(/\s+/).length
      }));
    } catch (error) {
      logger.warn('章节信息提取失败', { error: error.message });
      return [];
    }
  }

  /**
   * 计算统计信息
   */
  private async calculateStatistics(text: string): Promise<{
    averageSentenceLength: number;
    vocabularyComplexity: number;
  }> {
    try {
      const sentences = text.split(/[.!?。！？]/).filter(s => s.trim().length > 0);
      const words = text.split(/\s+/).filter(w => w.length > 0);
      
      const averageSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
      
      // 词汇复杂度：唯一词汇数 / 总词汇数
      const uniqueWords = new Set(words.map(w => w.toLowerCase()));
      const vocabularyComplexity = words.length > 0 ? uniqueWords.size / words.length : 0;

      return {
        averageSentenceLength,
        vocabularyComplexity
      };
    } catch (error) {
      logger.warn('统计信息计算失败', { error: error.message });
      return {
        averageSentenceLength: 0,
        vocabularyComplexity: 0
      };
    }
  }

  /**
   * 评估完整性
   */
  private assessCompleteness(text: string, structure?: any): number {
    try {
      let score = 0.5; // 基础分数

      // 基于文本长度
      if (text.length > 1000) score += 0.2;
      if (text.length > 5000) score += 0.1;

      // 基于结构
      if (structure?.headings && structure.headings.length > 0) score += 0.1;
      if (structure?.tables && structure.tables.length > 0) score += 0.05;
      if (structure?.images && structure.images.length > 0) score += 0.05;

      return Math.min(1.0, score);
    } catch (error) {
      return 0.5;
    }
  }

  /**
   * 评估连贯性
   */
  private assessCoherence(text: string): number {
    try {
      // 简单的连贯性评估
      const sentences = text.split(/[.!?。！？]/).filter(s => s.trim().length > 0);
      
      if (sentences.length < 2) return 0.5;

      // 检查连接词的使用
      const connectors = ['因此', '所以', '但是', '然而', '而且', '另外', 'therefore', 'however', 'moreover', 'furthermore'];
      const connectorCount = connectors.reduce((count, connector) => {
        return count + (text.toLowerCase().split(connector).length - 1);
      }, 0);

      const connectorRatio = connectorCount / sentences.length;
      
      // 基于连接词密度评估连贯性
      return Math.min(1.0, 0.3 + connectorRatio * 2);
    } catch (error) {
      return 0.5;
    }
  }
}
