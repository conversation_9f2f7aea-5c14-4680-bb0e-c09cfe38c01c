/**
 * 文档管理服务
 * 负责文档的CRUD操作和状态管理
 */

import { v4 as uuidv4 } from 'uuid';
import { query, beginTransaction, commitTransaction, rollbackTransaction } from '../config/database';
import { uploadStream, deleteFile, generatePresignedUrl, BUCKETS } from '../config/minio';
import { setCache, getCache, deleteCache } from '../config/redis';
import { logger, logDocumentProcessing } from '../utils/logger';

/**
 * 文档状态枚举
 */
export enum DocumentStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DELETED = 'deleted'
}

/**
 * 文档接口
 */
export interface Document {
  id: string;
  userId: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  status: DocumentStatus;
  uploadedAt: Date;
  processedAt?: Date;
  metadata?: any;
  structure?: any;
  chunksCount?: number;
  storageKey: string;
  thumbnailKey?: string;
  downloadUrl?: string;
  tags?: string[];
  description?: string;
}

/**
 * 文档查询选项
 */
export interface DocumentQueryOptions {
  userId?: string;
  status?: DocumentStatus;
  mimeType?: string;
  tags?: string[];
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'uploadedAt' | 'processedAt' | 'filename' | 'size';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 文档管理器类
 */
export class DocumentManager {
  
  /**
   * 创建文档记录
   */
  async createDocument(
    userId: string,
    filename: string,
    originalName: string,
    mimeType: string,
    size: number,
    tags?: string[],
    description?: string
  ): Promise<Document> {
    const client = await beginTransaction();
    
    try {
      const documentId = uuidv4();
      const storageKey = `${userId}/${documentId}/${filename}`;
      
      const insertQuery = `
        INSERT INTO documents (
          id, user_id, filename, original_name, mime_type, size, 
          status, uploaded_at, storage_key, tags, description
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `;
      
      const values = [
        documentId,
        userId,
        filename,
        originalName,
        mimeType,
        size,
        DocumentStatus.UPLOADING,
        new Date(),
        storageKey,
        tags ? JSON.stringify(tags) : null,
        description
      ];
      
      const result = await client.query(insertQuery, values);
      await commitTransaction(client);
      
      const document = this.mapRowToDocument(result.rows[0]);
      
      logDocumentProcessing('create', documentId, {
        userId,
        filename: originalName,
        size
      }, userId);
      
      // 缓存文档信息
      await this.cacheDocument(document);
      
      return document;
    } catch (error) {
      await rollbackTransaction(client);
      logger.error('创建文档记录失败', { userId, filename, error: error.message });
      throw error;
    }
  }

  /**
   * 获取文档
   */
  async getDocument(documentId: string, userId?: string): Promise<Document | null> {
    try {
      // 先从缓存获取
      const cached = await this.getCachedDocument(documentId);
      if (cached && (!userId || cached.userId === userId)) {
        return cached;
      }

      let selectQuery = 'SELECT * FROM documents WHERE id = $1';
      const values = [documentId];
      
      if (userId) {
        selectQuery += ' AND user_id = $2';
        values.push(userId);
      }
      
      const result = await query(selectQuery, values);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      const document = this.mapRowToDocument(result.rows[0]);
      
      // 缓存文档信息
      await this.cacheDocument(document);
      
      return document;
    } catch (error) {
      logger.error('获取文档失败', { documentId, userId, error: error.message });
      throw error;
    }
  }

  /**
   * 更新文档状态
   */
  async updateDocumentStatus(
    documentId: string,
    status: DocumentStatus,
    metadata?: any,
    structure?: any,
    chunksCount?: number
  ): Promise<void> {
    const client = await beginTransaction();
    
    try {
      const updateQuery = `
        UPDATE documents 
        SET status = $1, processed_at = $2, metadata = $3, structure = $4, chunks_count = $5
        WHERE id = $6
      `;
      
      const values = [
        status,
        status === DocumentStatus.COMPLETED ? new Date() : null,
        metadata ? JSON.stringify(metadata) : null,
        structure ? JSON.stringify(structure) : null,
        chunksCount,
        documentId
      ];
      
      await client.query(updateQuery, values);
      await commitTransaction(client);
      
      logDocumentProcessing('status_update', documentId, {
        status,
        chunksCount
      });
      
      // 清除缓存
      await this.clearDocumentCache(documentId);
      
    } catch (error) {
      await rollbackTransaction(client);
      logger.error('更新文档状态失败', { documentId, status, error: error.message });
      throw error;
    }
  }

  /**
   * 查询文档列表
   */
  async queryDocuments(options: DocumentQueryOptions): Promise<{
    documents: Document[];
    total: number;
  }> {
    try {
      let whereClause = 'WHERE 1=1';
      const values: any[] = [];
      let paramIndex = 1;

      // 构建查询条件
      if (options.userId) {
        whereClause += ` AND user_id = $${paramIndex}`;
        values.push(options.userId);
        paramIndex++;
      }

      if (options.status) {
        whereClause += ` AND status = $${paramIndex}`;
        values.push(options.status);
        paramIndex++;
      }

      if (options.mimeType) {
        whereClause += ` AND mime_type = $${paramIndex}`;
        values.push(options.mimeType);
        paramIndex++;
      }

      if (options.tags && options.tags.length > 0) {
        whereClause += ` AND tags::jsonb ?| $${paramIndex}`;
        values.push(options.tags);
        paramIndex++;
      }

      if (options.search) {
        whereClause += ` AND (original_name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        values.push(`%${options.search}%`);
        paramIndex++;
      }

      // 排序
      const sortBy = options.sortBy || 'uploadedAt';
      const sortOrder = options.sortOrder || 'desc';
      const orderClause = `ORDER BY ${sortBy} ${sortOrder}`;

      // 分页
      const limit = options.limit || 20;
      const offset = options.offset || 0;
      const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      values.push(limit, offset);

      // 查询总数
      const countQuery = `SELECT COUNT(*) FROM documents ${whereClause}`;
      const countResult = await query(countQuery, values.slice(0, -2));
      const total = parseInt(countResult.rows[0].count);

      // 查询文档列表
      const selectQuery = `
        SELECT * FROM documents 
        ${whereClause} 
        ${orderClause} 
        ${limitClause}
      `;
      
      const result = await query(selectQuery, values);
      const documents = result.rows.map(row => this.mapRowToDocument(row));

      return { documents, total };
    } catch (error) {
      logger.error('查询文档列表失败', { options, error: error.message });
      throw error;
    }
  }

  /**
   * 删除文档
   */
  async deleteDocument(documentId: string, userId?: string): Promise<void> {
    const client = await beginTransaction();
    
    try {
      // 获取文档信息
      const document = await this.getDocument(documentId, userId);
      if (!document) {
        throw new Error('文档不存在');
      }

      // 删除数据库记录
      let deleteQuery = 'DELETE FROM documents WHERE id = $1';
      const values = [documentId];
      
      if (userId) {
        deleteQuery += ' AND user_id = $2';
        values.push(userId);
      }
      
      await client.query(deleteQuery, values);

      // 删除存储文件
      try {
        await deleteFile(BUCKETS.DOCUMENTS, document.storageKey);
        if (document.thumbnailKey) {
          await deleteFile(BUCKETS.THUMBNAILS, document.thumbnailKey);
        }
      } catch (storageError) {
        logger.warn('删除存储文件失败', { 
          documentId, 
          storageKey: document.storageKey,
          error: storageError.message 
        });
      }

      await commitTransaction(client);
      
      logDocumentProcessing('delete', documentId, {
        userId: document.userId,
        filename: document.originalName
      }, userId);
      
      // 清除缓存
      await this.clearDocumentCache(documentId);
      
    } catch (error) {
      await rollbackTransaction(client);
      logger.error('删除文档失败', { documentId, userId, error: error.message });
      throw error;
    }
  }

  /**
   * 生成下载URL
   */
  async generateDownloadUrl(documentId: string, userId?: string): Promise<string> {
    try {
      const document = await this.getDocument(documentId, userId);
      if (!document) {
        throw new Error('文档不存在');
      }

      const url = await generatePresignedUrl(
        BUCKETS.DOCUMENTS,
        document.storageKey,
        3600 // 1小时有效期
      );

      return url;
    } catch (error) {
      logger.error('生成下载URL失败', { documentId, userId, error: error.message });
      throw error;
    }
  }

  /**
   * 获取用户文档统计
   */
  async getUserDocumentStats(userId: string): Promise<{
    totalDocuments: number;
    totalSize: number;
    statusCounts: Record<DocumentStatus, number>;
    mimeTypeCounts: Record<string, number>;
  }> {
    try {
      // 总数和总大小
      const totalQuery = `
        SELECT COUNT(*) as total_documents, COALESCE(SUM(size), 0) as total_size
        FROM documents 
        WHERE user_id = $1 AND status != $2
      `;
      const totalResult = await query(totalQuery, [userId, DocumentStatus.DELETED]);
      
      // 状态统计
      const statusQuery = `
        SELECT status, COUNT(*) as count
        FROM documents 
        WHERE user_id = $1 AND status != $2
        GROUP BY status
      `;
      const statusResult = await query(statusQuery, [userId, DocumentStatus.DELETED]);
      
      // MIME类型统计
      const mimeQuery = `
        SELECT mime_type, COUNT(*) as count
        FROM documents 
        WHERE user_id = $1 AND status != $2
        GROUP BY mime_type
        ORDER BY count DESC
        LIMIT 10
      `;
      const mimeResult = await query(mimeQuery, [userId, DocumentStatus.DELETED]);

      const statusCounts: Record<DocumentStatus, number> = {
        [DocumentStatus.UPLOADING]: 0,
        [DocumentStatus.PROCESSING]: 0,
        [DocumentStatus.COMPLETED]: 0,
        [DocumentStatus.FAILED]: 0,
        [DocumentStatus.DELETED]: 0
      };

      statusResult.rows.forEach(row => {
        statusCounts[row.status as DocumentStatus] = parseInt(row.count);
      });

      const mimeTypeCounts: Record<string, number> = {};
      mimeResult.rows.forEach(row => {
        mimeTypeCounts[row.mime_type] = parseInt(row.count);
      });

      return {
        totalDocuments: parseInt(totalResult.rows[0].total_documents),
        totalSize: parseInt(totalResult.rows[0].total_size),
        statusCounts,
        mimeTypeCounts
      };
    } catch (error) {
      logger.error('获取用户文档统计失败', { userId, error: error.message });
      throw error;
    }
  }

  /**
   * 映射数据库行到文档对象
   */
  private mapRowToDocument(row: any): Document {
    return {
      id: row.id,
      userId: row.user_id,
      filename: row.filename,
      originalName: row.original_name,
      mimeType: row.mime_type,
      size: row.size,
      status: row.status,
      uploadedAt: row.uploaded_at,
      processedAt: row.processed_at,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
      structure: row.structure ? JSON.parse(row.structure) : undefined,
      chunksCount: row.chunks_count,
      storageKey: row.storage_key,
      thumbnailKey: row.thumbnail_key,
      tags: row.tags ? JSON.parse(row.tags) : undefined,
      description: row.description
    };
  }

  /**
   * 缓存文档信息
   */
  private async cacheDocument(document: Document): Promise<void> {
    try {
      const cacheKey = `document:${document.id}`;
      await setCache(cacheKey, document, 3600); // 1小时缓存
    } catch (error) {
      logger.warn('缓存文档信息失败', { documentId: document.id, error: error.message });
    }
  }

  /**
   * 获取缓存的文档信息
   */
  private async getCachedDocument(documentId: string): Promise<Document | null> {
    try {
      const cacheKey = `document:${documentId}`;
      return await getCache(cacheKey);
    } catch (error) {
      logger.warn('获取缓存文档信息失败', { documentId, error: error.message });
      return null;
    }
  }

  /**
   * 清除文档缓存
   */
  private async clearDocumentCache(documentId: string): Promise<void> {
    try {
      const cacheKey = `document:${documentId}`;
      await deleteCache(cacheKey);
    } catch (error) {
      logger.warn('清除文档缓存失败', { documentId, error: error.message });
    }
  }
}
