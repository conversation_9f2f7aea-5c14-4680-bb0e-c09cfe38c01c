/**
 * 多粒度文本分块器
 * 实现句子级、语义级、滑动窗口等多种分块策略
 */

import { logger } from '../utils/logger';
import { TextChunk, ChunkingConfig } from './textChunker';

/**
 * 分块组接口
 */
export interface ChunkGroup {
  type: 'sentence' | 'semantic' | 'sliding' | 'structure' | 'paragraph';
  chunks: TextChunk[];
  weight: number; // 权重，用于检索时的重要性评分
  metadata?: {
    totalChunks: number;
    avgChunkSize: number;
    coverage: number; // 文档覆盖率
  };
}

/**
 * 语义分块配置
 */
export interface SemanticChunkingConfig extends ChunkingConfig {
  similarityThreshold: number; // 语义相似度阈值
  minSentencesPerChunk: number; // 每个块最少句子数
  maxSentencesPerChunk: number; // 每个块最多句子数
}

/**
 * 滑动窗口配置
 */
export interface SlidingWindowConfig extends ChunkingConfig {
  windowSize: number; // 窗口大小
  stepSize: number; // 步长
  overlapRatio: number; // 重叠比例
}

/**
 * 多粒度文本分块器
 */
export class MultiGranularityChunker {
  private sentencePattern = /[.!?。！？]+\s*/g;
  private paragraphPattern = /\n\s*\n/g;

  /**
   * 多粒度分块主方法
   */
  async chunkDocument(
    text: string,
    config: ChunkingConfig,
    structure?: any
  ): Promise<ChunkGroup[]> {
    try {
      logger.info('开始多粒度文本分块', {
        textLength: text.length,
        enabledTypes: this.getEnabledChunkTypes(config)
      });

      const chunkGroups: ChunkGroup[] = [];
      const cleanedText = this.preprocessText(text);

      // 1. 句子级分块 - 保持语义完整性
      const sentenceChunks = await this.sentenceLevelChunking(cleanedText, config);
      if (sentenceChunks.length > 0) {
        chunkGroups.push({
          type: 'sentence',
          chunks: sentenceChunks,
          weight: 1.0,
          metadata: this.calculateChunkMetadata(sentenceChunks)
        });
      }

      // 2. 语义级分块 - 基于语义相似度
      const semanticChunks = await this.semanticLevelChunking(cleanedText, config);
      if (semanticChunks.length > 0) {
        chunkGroups.push({
          type: 'semantic',
          chunks: semanticChunks,
          weight: 1.1,
          metadata: this.calculateChunkMetadata(semanticChunks)
        });
      }

      // 3. 滑动窗口分块 - 增加覆盖度
      const slidingChunks = await this.slidingWindowChunking(cleanedText, config);
      if (slidingChunks.length > 0) {
        chunkGroups.push({
          type: 'sliding',
          chunks: slidingChunks,
          weight: 0.9,
          metadata: this.calculateChunkMetadata(slidingChunks)
        });
      }

      // 4. 结构化分块 - 基于文档结构
      if (structure && structure.headings) {
        const structureChunks = await this.structureBasedChunking(cleanedText, config, structure);
        if (structureChunks.length > 0) {
          chunkGroups.push({
            type: 'structure',
            chunks: structureChunks,
            weight: 1.2,
            metadata: this.calculateChunkMetadata(structureChunks)
          });
        }
      }

      // 5. 段落级分块 - 保持逻辑结构
      const paragraphChunks = await this.paragraphLevelChunking(cleanedText, config);
      if (paragraphChunks.length > 0) {
        chunkGroups.push({
          type: 'paragraph',
          chunks: paragraphChunks,
          weight: 1.05,
          metadata: this.calculateChunkMetadata(paragraphChunks)
        });
      }

      logger.info('多粒度分块完成', {
        totalGroups: chunkGroups.length,
        totalChunks: chunkGroups.reduce((sum, group) => sum + group.chunks.length, 0)
      });

      return chunkGroups;
    } catch (error) {
      logger.error('多粒度分块失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 句子级分块
   */
  private async sentenceLevelChunking(text: string, config: ChunkingConfig): Promise<TextChunk[]> {
    try {
      const sentences = this.splitIntoSentences(text);
      const chunks: TextChunk[] = [];
      
      let currentChunk = '';
      let currentStart = 0;
      let sentenceStart = 0;

      for (const sentence of sentences) {
        const trimmedSentence = sentence.trim();
        if (!trimmedSentence) continue;

        // 检查是否可以添加到当前块
        if (currentChunk.length + trimmedSentence.length <= config.chunkSize) {
          if (currentChunk) {
            currentChunk += ' ' + trimmedSentence;
          } else {
            currentChunk = trimmedSentence;
            currentStart = sentenceStart;
          }
        } else {
          // 保存当前块
          if (currentChunk) {
            chunks.push({
              text: currentChunk,
              startPosition: currentStart,
              endPosition: currentStart + currentChunk.length,
              metadata: {
                chunkType: 'sentence',
                sentenceCount: this.countSentences(currentChunk)
              }
            });
          }

          // 开始新块
          currentChunk = trimmedSentence;
          currentStart = sentenceStart;
        }

        sentenceStart += sentence.length;
      }

      // 处理最后一个块
      if (currentChunk) {
        chunks.push({
          text: currentChunk,
          startPosition: currentStart,
          endPosition: currentStart + currentChunk.length,
          metadata: {
            chunkType: 'sentence',
            sentenceCount: this.countSentences(currentChunk)
          }
        });
      }

      return chunks;
    } catch (error) {
      logger.error('句子级分块失败', { error: error.message });
      return [];
    }
  }

  /**
   * 语义级分块（简化实现）
   */
  private async semanticLevelChunking(text: string, config: ChunkingConfig): Promise<TextChunk[]> {
    try {
      // 这里是简化实现，实际应该使用语义相似度计算
      const sentences = this.splitIntoSentences(text);
      const chunks: TextChunk[] = [];
      
      let currentGroup: string[] = [];
      let groupStart = 0;
      let sentenceStart = 0;

      for (let i = 0; i < sentences.length; i++) {
        const sentence = sentences[i].trim();
        if (!sentence) continue;

        currentGroup.push(sentence);
        
        // 简化的语义分组逻辑：基于关键词重叠
        const shouldSplit = this.shouldSplitSemanticGroup(currentGroup, config);
        
        if (shouldSplit || i === sentences.length - 1) {
          if (currentGroup.length > 0) {
            const chunkText = currentGroup.join(' ');
            chunks.push({
              text: chunkText,
              startPosition: groupStart,
              endPosition: groupStart + chunkText.length,
              metadata: {
                chunkType: 'semantic',
                sentenceCount: currentGroup.length,
                semanticCoherence: this.calculateSemanticCoherence(currentGroup)
              }
            });
          }

          // 重置分组
          currentGroup = [];
          groupStart = sentenceStart + sentence.length;
        }

        sentenceStart += sentences[i].length;
      }

      return chunks;
    } catch (error) {
      logger.error('语义级分块失败', { error: error.message });
      return [];
    }
  }

  /**
   * 滑动窗口分块
   */
  private async slidingWindowChunking(text: string, config: ChunkingConfig): Promise<TextChunk[]> {
    try {
      const windowSize = config.chunkSize;
      const stepSize = Math.floor(windowSize * 0.5); // 50% 重叠
      const chunks: TextChunk[] = [];

      for (let i = 0; i < text.length; i += stepSize) {
        const end = Math.min(i + windowSize, text.length);
        const chunkText = text.substring(i, end);

        // 确保不在单词中间截断
        const adjustedChunk = this.adjustChunkBoundary(chunkText, i, text);
        
        if (adjustedChunk.text.trim().length > config.minChunkSize || 50) {
          chunks.push({
            text: adjustedChunk.text.trim(),
            startPosition: adjustedChunk.start,
            endPosition: adjustedChunk.end,
            metadata: {
              chunkType: 'sliding',
              windowIndex: Math.floor(i / stepSize),
              overlapRatio: stepSize / windowSize
            }
          });
        }

        // 如果已经到达文本末尾，停止
        if (end >= text.length) break;
      }

      return chunks;
    } catch (error) {
      logger.error('滑动窗口分块失败', { error: error.message });
      return [];
    }
  }

  /**
   * 段落级分块
   */
  private async paragraphLevelChunking(text: string, config: ChunkingConfig): Promise<TextChunk[]> {
    try {
      const paragraphs = text.split(this.paragraphPattern);
      const chunks: TextChunk[] = [];
      
      let currentChunk = '';
      let currentStart = 0;
      let paragraphStart = 0;

      for (const paragraph of paragraphs) {
        const trimmedParagraph = paragraph.trim();
        if (!trimmedParagraph) continue;

        if (currentChunk.length + trimmedParagraph.length <= config.chunkSize) {
          if (currentChunk) {
            currentChunk += '\n\n' + trimmedParagraph;
          } else {
            currentChunk = trimmedParagraph;
            currentStart = paragraphStart;
          }
        } else {
          // 保存当前块
          if (currentChunk) {
            chunks.push({
              text: currentChunk,
              startPosition: currentStart,
              endPosition: currentStart + currentChunk.length,
              metadata: {
                chunkType: 'paragraph',
                paragraphCount: this.countParagraphs(currentChunk)
              }
            });
          }

          // 开始新块
          currentChunk = trimmedParagraph;
          currentStart = paragraphStart;
        }

        paragraphStart += paragraph.length + 2; // +2 for paragraph separator
      }

      // 处理最后一个块
      if (currentChunk) {
        chunks.push({
          text: currentChunk,
          startPosition: currentStart,
          endPosition: currentStart + currentChunk.length,
          metadata: {
            chunkType: 'paragraph',
            paragraphCount: this.countParagraphs(currentChunk)
          }
        });
      }

      return chunks;
    } catch (error) {
      logger.error('段落级分块失败', { error: error.message });
      return [];
    }
  }

  /**
   * 结构化分块（基于文档结构）
   */
  private async structureBasedChunking(
    text: string,
    config: ChunkingConfig,
    structure: any
  ): Promise<TextChunk[]> {
    try {
      const chunks: TextChunk[] = [];
      const headings = structure.headings || [];

      for (let i = 0; i < headings.length; i++) {
        const heading = headings[i];
        const nextHeading = headings[i + 1];
        
        const sectionStart = heading.position;
        const sectionEnd = nextHeading ? nextHeading.position : text.length;
        const sectionText = text.substring(sectionStart, sectionEnd);

        if (sectionText.length <= config.chunkSize) {
          // 章节足够小，作为单个块
          chunks.push({
            text: sectionText.trim(),
            startPosition: sectionStart,
            endPosition: sectionEnd,
            metadata: {
              chunkType: 'structure',
              heading: heading.text,
              headingLevel: heading.level,
              section: heading.text
            }
          });
        } else {
          // 章节太大，需要进一步分块
          const subChunks = await this.sentenceLevelChunking(sectionText, config);
          for (const subChunk of subChunks) {
            chunks.push({
              ...subChunk,
              startPosition: subChunk.startPosition + sectionStart,
              endPosition: subChunk.endPosition + sectionStart,
              metadata: {
                ...subChunk.metadata,
                chunkType: 'structure',
                heading: heading.text,
                headingLevel: heading.level,
                section: heading.text
              }
            });
          }
        }
      }

      return chunks;
    } catch (error) {
      logger.error('结构化分块失败', { error: error.message });
      return [];
    }
  }

  // 辅助方法

  private preprocessText(text: string): string {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\t/g, '    ')
      .replace(/\u00A0/g, ' ')
      .replace(/\s{3,}/g, '  ')
      .replace(/\n{4,}/g, '\n\n\n')
      .trim();
  }

  private splitIntoSentences(text: string): string[] {
    return text.split(this.sentencePattern).filter(s => s.trim().length > 0);
  }

  private countSentences(text: string): number {
    return this.splitIntoSentences(text).length;
  }

  private countParagraphs(text: string): number {
    return text.split(this.paragraphPattern).filter(p => p.trim().length > 0).length;
  }

  private shouldSplitSemanticGroup(sentences: string[], config: ChunkingConfig): boolean {
    // 简化的语义分组判断逻辑
    const totalLength = sentences.join(' ').length;
    if (totalLength > config.chunkSize) return true;
    if (sentences.length > 10) return true; // 最多10个句子一组
    
    // 基于关键词重叠的简单语义一致性检查
    if (sentences.length >= 3) {
      const coherence = this.calculateSemanticCoherence(sentences);
      return coherence < 0.3; // 语义一致性阈值
    }
    
    return false;
  }

  private calculateSemanticCoherence(sentences: string[]): number {
    // 简化的语义一致性计算
    if (sentences.length < 2) return 1.0;
    
    const allWords = sentences.join(' ').toLowerCase().split(/\s+/);
    const uniqueWords = new Set(allWords);
    const wordFreq = new Map<string, number>();
    
    for (const word of allWords) {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    }
    
    // 计算重复词汇比例作为语义一致性指标
    const repeatedWords = Array.from(wordFreq.values()).filter(freq => freq > 1).length;
    return repeatedWords / uniqueWords.size;
  }

  private adjustChunkBoundary(chunkText: string, start: number, fullText: string): {
    text: string;
    start: number;
    end: number;
  } {
    // 确保不在单词中间截断
    let adjustedText = chunkText;
    let adjustedStart = start;
    let adjustedEnd = start + chunkText.length;

    // 向前调整开始位置（避免在单词中间开始）
    if (adjustedStart > 0 && /\w/.test(fullText[adjustedStart - 1]) && /\w/.test(adjustedText[0])) {
      const spaceIndex = adjustedText.indexOf(' ');
      if (spaceIndex > 0) {
        adjustedText = adjustedText.substring(spaceIndex + 1);
        adjustedStart += spaceIndex + 1;
      }
    }

    // 向后调整结束位置（避免在单词中间结束）
    if (adjustedEnd < fullText.length && /\w/.test(adjustedText[adjustedText.length - 1]) && /\w/.test(fullText[adjustedEnd])) {
      const lastSpaceIndex = adjustedText.lastIndexOf(' ');
      if (lastSpaceIndex > 0) {
        adjustedText = adjustedText.substring(0, lastSpaceIndex);
        adjustedEnd = adjustedStart + adjustedText.length;
      }
    }

    return {
      text: adjustedText,
      start: adjustedStart,
      end: adjustedEnd
    };
  }

  private calculateChunkMetadata(chunks: TextChunk[]) {
    const totalChunks = chunks.length;
    const avgChunkSize = chunks.reduce((sum, chunk) => sum + chunk.text.length, 0) / totalChunks;
    const coverage = chunks.reduce((sum, chunk) => sum + (chunk.endPosition - chunk.startPosition), 0);

    return {
      totalChunks,
      avgChunkSize: Math.round(avgChunkSize),
      coverage
    };
  }

  private getEnabledChunkTypes(config: ChunkingConfig): string[] {
    // 根据配置确定启用的分块类型
    const types = ['sentence', 'paragraph'];
    
    if (config.preserveStructure) {
      types.push('structure');
    }
    
    // 可以根据配置添加更多类型
    types.push('semantic', 'sliding');
    
    return types;
  }
}
