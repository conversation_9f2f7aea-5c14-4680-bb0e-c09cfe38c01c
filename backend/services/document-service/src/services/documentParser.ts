/**
 * 文档解析服务
 * 支持多种文档格式的文本提取
 */

import fs from 'fs';
import path from 'path';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
// import * as XLSX from 'xlsx';
// import { marked } from 'marked';
import * as cheerio from 'cheerio';
import { logger, logDocumentProcessing, logPerformance } from '../utils/logger';

/**
 * 支持的文档类型
 */
export enum DocumentType {
  PDF = 'pdf',
  DOCX = 'docx',
  DOC = 'doc',
  TXT = 'txt',
  MD = 'md',
  HTML = 'html',
  XLSX = 'xlsx',
  XLS = 'xls',
  CSV = 'csv',
  RTF = 'rtf',
  ODT = 'odt'
}

/**
 * 解析结果接口
 */
export interface ParseResult {
  text: string;
  metadata: {
    title?: string;
    author?: string;
    subject?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modificationDate?: Date;
    pages?: number;
    words?: number;
    characters?: number;
    language?: string;
    format: string;
    size: number;
  };
  structure?: {
    headings?: Array<{
      level: number;
      text: string;
      position: number;
    }>;
    tables?: Array<{
      headers: string[];
      rows: string[][];
      position: number;
    }>;
    images?: Array<{
      alt?: string;
      position: number;
    }>;
  };
}

/**
 * 文档解析器类
 */
export class DocumentParser {
  /**
   * 根据文件类型解析文档
   */
  async parseDocument(filePath: string, mimeType: string): Promise<ParseResult> {
    const startTime = Date.now();
    
    try {
      const fileStats = fs.statSync(filePath);
      const documentType = this.getDocumentType(mimeType, path.extname(filePath));
      
      logger.info('开始解析文档', {
        filePath,
        mimeType,
        documentType,
        size: fileStats.size
      });

      let result: ParseResult;

      switch (documentType) {
        case DocumentType.PDF:
          result = await this.parsePDF(filePath, fileStats.size);
          break;
        case DocumentType.DOCX:
        case DocumentType.DOC:
          result = await this.parseWord(filePath, fileStats.size);
          break;
        case DocumentType.TXT:
          result = await this.parseText(filePath, fileStats.size);
          break;
        case DocumentType.MD:
          result = await this.parseMarkdown(filePath, fileStats.size);
          break;
        case DocumentType.HTML:
          result = await this.parseHTML(filePath, fileStats.size);
          break;
        case DocumentType.XLSX:
        case DocumentType.XLS:
        case DocumentType.CSV:
          result = await this.parseSpreadsheet(filePath, fileStats.size);
          break;
        default:
          throw new Error(`不支持的文档类型: ${documentType}`);
      }

      const duration = Date.now() - startTime;
      logPerformance('document_parsing', duration, {
        documentType,
        textLength: result.text.length,
        fileSize: fileStats.size
      });

      logDocumentProcessing('parse_complete', path.basename(filePath), {
        documentType,
        textLength: result.text.length,
        duration: `${duration}ms`
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('文档解析失败', {
        filePath,
        mimeType,
        duration: `${duration}ms`,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取文档类型
   */
  private getDocumentType(mimeType: string, extension: string): DocumentType {
    // 根据MIME类型判断
    const mimeTypeMap: Record<string, DocumentType> = {
      'application/pdf': DocumentType.PDF,
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocumentType.DOCX,
      'application/msword': DocumentType.DOC,
      'text/plain': DocumentType.TXT,
      'text/markdown': DocumentType.MD,
      'text/html': DocumentType.HTML,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': DocumentType.XLSX,
      'application/vnd.ms-excel': DocumentType.XLS,
      'text/csv': DocumentType.CSV,
      'application/rtf': DocumentType.RTF,
      'application/vnd.oasis.opendocument.text': DocumentType.ODT
    };

    if (mimeTypeMap[mimeType]) {
      return mimeTypeMap[mimeType];
    }

    // 根据文件扩展名判断
    const extensionMap: Record<string, DocumentType> = {
      '.pdf': DocumentType.PDF,
      '.docx': DocumentType.DOCX,
      '.doc': DocumentType.DOC,
      '.txt': DocumentType.TXT,
      '.md': DocumentType.MD,
      '.markdown': DocumentType.MD,
      '.html': DocumentType.HTML,
      '.htm': DocumentType.HTML,
      '.xlsx': DocumentType.XLSX,
      '.xls': DocumentType.XLS,
      '.csv': DocumentType.CSV,
      '.rtf': DocumentType.RTF,
      '.odt': DocumentType.ODT
    };

    const ext = extension.toLowerCase();
    if (extensionMap[ext]) {
      return extensionMap[ext];
    }

    throw new Error(`无法识别的文档类型: ${mimeType}, ${extension}`);
  }

  /**
   * 解析PDF文档
   */
  private async parsePDF(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      const dataBuffer = fs.readFileSync(filePath);
      const pdfData = await pdfParse(dataBuffer);

      return {
        text: pdfData.text,
        metadata: {
          title: pdfData.info?.Title,
          author: pdfData.info?.Author,
          subject: pdfData.info?.Subject,
          creator: pdfData.info?.Creator,
          producer: pdfData.info?.Producer,
          creationDate: pdfData.info?.CreationDate ? new Date(pdfData.info.CreationDate) : undefined,
          modificationDate: pdfData.info?.ModDate ? new Date(pdfData.info.ModDate) : undefined,
          pages: pdfData.numpages,
          words: this.countWords(pdfData.text),
          characters: pdfData.text.length,
          format: 'PDF',
          size: fileSize
        },
        structure: this.extractPDFStructure(pdfData.text)
      };
    } catch (error) {
      logger.error('PDF解析失败', { filePath, error: error.message });
      throw new Error(`PDF解析失败: ${error.message}`);
    }
  }

  /**
   * 解析Word文档
   */
  private async parseWord(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      const result = await mammoth.extractRawText({ path: filePath });
      const text = result.value;

      return {
        text,
        metadata: {
          words: this.countWords(text),
          characters: text.length,
          format: 'DOCX',
          size: fileSize
        },
        structure: this.extractTextStructure(text)
      };
    } catch (error) {
      logger.error('Word文档解析失败', { filePath, error: error.message });
      throw new Error(`Word文档解析失败: ${error.message}`);
    }
  }

  /**
   * 解析纯文本文档
   */
  private async parseText(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      const text = fs.readFileSync(filePath, 'utf-8');

      return {
        text,
        metadata: {
          words: this.countWords(text),
          characters: text.length,
          format: 'TXT',
          size: fileSize
        },
        structure: this.extractTextStructure(text)
      };
    } catch (error) {
      logger.error('文本文档解析失败', { filePath, error: error.message });
      throw new Error(`文本文档解析失败: ${error.message}`);
    }
  }

  /**
   * 解析Markdown文档
   */
  private async parseMarkdown(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      const markdownText = fs.readFileSync(filePath, 'utf-8');
      // 简单的Markdown转文本处理
      const plainText = markdownText.replace(/[#*`_\[\]()]/g, '').replace(/\n+/g, '\n');

      return {
        text: plainText,
        metadata: {
          words: this.countWords(plainText),
          characters: plainText.length,
          format: 'Markdown',
          size: fileSize
        },
        structure: this.extractMarkdownStructure(markdownText)
      };
    } catch (error) {
      logger.error('Markdown文档解析失败', { filePath, error: error.message });
      throw new Error(`Markdown文档解析失败: ${error.message}`);
    }
  }

  /**
   * 解析HTML文档
   */
  private async parseHTML(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      const htmlContent = fs.readFileSync(filePath, 'utf-8');
      const $ = cheerio.load(htmlContent);
      
      // 移除脚本和样式标签
      $('script, style').remove();
      
      const text = $.text();
      const title = $('title').text() || $('h1').first().text();

      return {
        text,
        metadata: {
          title,
          words: this.countWords(text),
          characters: text.length,
          format: 'HTML',
          size: fileSize
        },
        structure: this.extractHTMLStructure($)
      };
    } catch (error) {
      logger.error('HTML文档解析失败', { filePath, error: error.message });
      throw new Error(`HTML文档解析失败: ${error.message}`);
    }
  }

  /**
   * 解析电子表格文档
   */
  private async parseSpreadsheet(filePath: string, fileSize: number): Promise<ParseResult> {
    try {
      // 简化的电子表格处理，实际项目中需要安装xlsx库
      const text = '电子表格文档内容解析功能需要安装xlsx库';
      const tables: any[] = [];

      return {
        text,
        metadata: {
          words: this.countWords(text),
          characters: text.length,
          format: 'Spreadsheet',
          size: fileSize
        },
        structure: {
          tables
        }
      };
    } catch (error) {
      logger.error('电子表格解析失败', { filePath, error: error.message });
      throw new Error(`电子表格解析失败: ${error.message}`);
    }
  }

  /**
   * 提取PDF结构
   */
  private extractPDFStructure(text: string): any {
    return this.extractTextStructure(text);
  }

  /**
   * 提取文本结构
   */
  private extractTextStructure(text: string): any {
    const headings: any[] = [];
    const lines = text.split('\n');
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (trimmedLine.length > 0) {
        // 简单的标题检测（全大写或以数字开头）
        if (trimmedLine === trimmedLine.toUpperCase() && trimmedLine.length < 100) {
          headings.push({
            level: 1,
            text: trimmedLine,
            position: text.indexOf(line)
          });
        } else if (/^\d+\./.test(trimmedLine)) {
          headings.push({
            level: 2,
            text: trimmedLine,
            position: text.indexOf(line)
          });
        }
      }
    });

    return { headings };
  }

  /**
   * 提取Markdown结构
   */
  private extractMarkdownStructure(markdownText: string): any {
    const headings: any[] = [];
    const lines = markdownText.split('\n');
    
    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        headings.push({
          level: match[1].length,
          text: match[2],
          position: markdownText.indexOf(line)
        });
      }
    });

    return { headings };
  }

  /**
   * 提取HTML结构
   */
  private extractHTMLStructure($: cheerio.CheerioAPI): any {
    const headings: any[] = [];
    const tables: any[] = [];
    const images: any[] = [];

    // 提取标题
    $('h1, h2, h3, h4, h5, h6').each((index, element) => {
      const $el = $(element);
      const level = parseInt($el.prop('tagName').substring(1));
      headings.push({
        level,
        text: $el.text().trim(),
        position: index
      });
    });

    // 提取表格
    $('table').each((index, element) => {
      const $table = $(element);
      const headers: string[] = [];
      const rows: string[][] = [];

      $table.find('thead tr th, tr:first-child td').each((i, th) => {
        headers.push($(th).text().trim());
      });

      $table.find('tbody tr, tr:not(:first-child)').each((i, tr) => {
        const row: string[] = [];
        $(tr).find('td').each((j, td) => {
          row.push($(td).text().trim());
        });
        if (row.length > 0) {
          rows.push(row);
        }
      });

      if (headers.length > 0 || rows.length > 0) {
        tables.push({
          headers,
          rows,
          position: index
        });
      }
    });

    // 提取图片
    $('img').each((index, element) => {
      const $img = $(element);
      images.push({
        alt: $img.attr('alt'),
        position: index
      });
    });

    return { headings, tables, images };
  }

  /**
   * HTML转纯文本
   */
  private htmlToText(html: string): string {
    const $ = cheerio.load(html);
    $('script, style').remove();
    return $.text().replace(/\s+/g, ' ').trim();
  }

  /**
   * 统计单词数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }
}
