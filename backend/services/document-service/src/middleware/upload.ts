/**
 * 文件上传中间件
 * 使用Multer处理文件上传
 */

import multer from 'multer';
import path from 'path';
import { Request } from 'express';
import { logger } from '../utils/logger';
import { UnsupportedFileTypeError, FileSizeExceededError } from './errorHandler';

/**
 * 支持的文件类型
 */
const SUPPORTED_MIME_TYPES = [
  // 文档类型
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/rtf',
  'application/vnd.oasis.opendocument.text',
  'application/vnd.oasis.opendocument.spreadsheet',
  'application/vnd.oasis.opendocument.presentation',
  
  // 文本类型
  'text/plain',
  'text/markdown',
  'text/html',
  'text/csv',
  'text/xml',
  'application/xml',
  'application/json',
  
  // 图片类型（用于OCR）
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/tiff',
  'image/webp'
];

/**
 * 文件大小限制（字节）
 */
const FILE_SIZE_LIMITS = {
  document: 100 * 1024 * 1024,  // 100MB
  image: 10 * 1024 * 1024,      // 10MB
  text: 5 * 1024 * 1024         // 5MB
};

/**
 * 获取文件大小限制
 */
function getFileSizeLimit(mimeType: string): number {
  if (mimeType.startsWith('image/')) {
    return FILE_SIZE_LIMITS.image;
  } else if (mimeType.startsWith('text/')) {
    return FILE_SIZE_LIMITS.text;
  } else {
    return FILE_SIZE_LIMITS.document;
  }
}

/**
 * 文件过滤器
 */
function fileFilter(req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) {
  try {
    logger.debug('文件上传检查', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // 检查文件类型
    if (!SUPPORTED_MIME_TYPES.includes(file.mimetype)) {
      logger.warn('不支持的文件类型', {
        filename: file.originalname,
        mimetype: file.mimetype
      });
      return cb(new UnsupportedFileTypeError(file.mimetype));
    }

    // 检查文件扩展名
    const ext = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.md', '.html', '.htm', '.csv', '.xml', '.json',
      '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'
    ];

    if (!allowedExtensions.includes(ext)) {
      logger.warn('不支持的文件扩展名', {
        filename: file.originalname,
        extension: ext
      });
      return cb(new UnsupportedFileTypeError(file.mimetype));
    }

    // 检查文件名长度
    if (file.originalname.length > 255) {
      logger.warn('文件名过长', {
        filename: file.originalname,
        length: file.originalname.length
      });
      return cb(new Error('文件名过长，最多255个字符'));
    }

    // 检查文件名字符
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(file.originalname)) {
      logger.warn('文件名包含非法字符', {
        filename: file.originalname
      });
      return cb(new Error('文件名包含非法字符'));
    }

    cb(null, true);
  } catch (error) {
    logger.error('文件过滤器错误', { error: error.message });
    cb(error);
  }
}

/**
 * 内存存储配置
 */
const memoryStorage = multer.memoryStorage();

/**
 * 磁盘存储配置
 */
const diskStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = process.env.UPLOAD_TEMP_DIR || './temp/uploads';
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

/**
 * 单文件上传中间件（内存存储）
 */
export const uploadSingle = multer({
  storage: memoryStorage,
  fileFilter,
  limits: {
    fileSize: Math.max(...Object.values(FILE_SIZE_LIMITS)),
    files: 1,
    fields: 10,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB
  }
}).single('file');

/**
 * 多文件上传中间件（内存存储）
 */
export const uploadMultiple = multer({
  storage: memoryStorage,
  fileFilter,
  limits: {
    fileSize: Math.max(...Object.values(FILE_SIZE_LIMITS)),
    files: 10,
    fields: 20,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB
  }
}).array('files', 10);

/**
 * 单文件上传中间件（磁盘存储）
 */
export const uploadSingleToDisk = multer({
  storage: diskStorage,
  fileFilter,
  limits: {
    fileSize: Math.max(...Object.values(FILE_SIZE_LIMITS)),
    files: 1,
    fields: 10,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB
  }
}).single('file');

/**
 * 多文件上传中间件（磁盘存储）
 */
export const uploadMultipleToDisk = multer({
  storage: diskStorage,
  fileFilter,
  limits: {
    fileSize: Math.max(...Object.values(FILE_SIZE_LIMITS)),
    files: 10,
    fields: 20,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB
  }
}).array('files', 10);

/**
 * 验证上传的文件
 */
export function validateUploadedFile(file: Express.Multer.File): void {
  if (!file) {
    throw new Error('没有上传文件');
  }

  // 再次检查文件大小
  const sizeLimit = getFileSizeLimit(file.mimetype);
  if (file.size > sizeLimit) {
    throw new FileSizeExceededError(file.size, sizeLimit);
  }

  // 检查文件内容（简单的魔数检查）
  if (file.buffer) {
    validateFileContent(file);
  }
}

/**
 * 验证文件内容
 */
function validateFileContent(file: Express.Multer.File): void {
  if (!file.buffer || file.buffer.length === 0) {
    throw new Error('文件内容为空');
  }

  const buffer = file.buffer;
  const header = buffer.slice(0, 16);

  // PDF文件检查
  if (file.mimetype === 'application/pdf') {
    if (!buffer.slice(0, 4).equals(Buffer.from('%PDF'))) {
      throw new Error('PDF文件格式无效');
    }
  }

  // ZIP格式文件检查（DOCX, XLSX, PPTX等）
  if (file.mimetype.includes('openxmlformats')) {
    if (!(header[0] === 0x50 && header[1] === 0x4B)) {
      throw new Error('Office文档格式无效');
    }
  }

  // 图片文件检查
  if (file.mimetype.startsWith('image/')) {
    const isValidImage = 
      // JPEG
      (header[0] === 0xFF && header[1] === 0xD8) ||
      // PNG
      (header[0] === 0x89 && header[1] === 0x50 && header[2] === 0x4E && header[3] === 0x47) ||
      // GIF
      (header[0] === 0x47 && header[1] === 0x49 && header[2] === 0x46) ||
      // BMP
      (header[0] === 0x42 && header[1] === 0x4D) ||
      // WEBP
      (header[8] === 0x57 && header[9] === 0x45 && header[10] === 0x42 && header[11] === 0x50);

    if (!isValidImage) {
      throw new Error('图片文件格式无效');
    }
  }
}

/**
 * 清理临时文件
 */
export function cleanupTempFile(filePath: string): void {
  try {
    const fs = require('fs');
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.debug('临时文件已清理', { filePath });
    }
  } catch (error) {
    logger.warn('清理临时文件失败', { filePath, error: error.message });
  }
}

/**
 * 获取文件信息
 */
export function getFileInfo(file: Express.Multer.File): {
  originalName: string;
  mimeType: string;
  size: number;
  extension: string;
  sizeLimit: number;
} {
  return {
    originalName: file.originalname,
    mimeType: file.mimetype,
    size: file.size,
    extension: path.extname(file.originalname).toLowerCase(),
    sizeLimit: getFileSizeLimit(file.mimetype)
  };
}
