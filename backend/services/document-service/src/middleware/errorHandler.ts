/**
 * 错误处理中间件
 * 统一处理文档服务的错误响应
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 应用错误类
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 错误处理中间件
 */
export function errorHandler(
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 默认错误信息
  let statusCode = 500;
  let errorCode = 'INTERNAL_ERROR';
  let message = '服务器内部错误';
  let details: any = undefined;

  // 处理不同类型的错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
    errorCode = 'VALIDATION_ERROR';
    message = '请求参数验证失败';
    details = extractValidationErrors(error);
  } else if (error.name === 'MulterError') {
    statusCode = 400;
    errorCode = 'FILE_UPLOAD_ERROR';
    message = getMulterErrorMessage(error);
  } else if (error.message.includes('ENOENT')) {
    statusCode = 404;
    errorCode = 'FILE_NOT_FOUND';
    message = '文件不存在';
  } else if (error.message.includes('EACCES')) {
    statusCode = 403;
    errorCode = 'FILE_ACCESS_DENIED';
    message = '文件访问被拒绝';
  } else if (error.message.includes('EMFILE') || error.message.includes('ENFILE')) {
    statusCode = 503;
    errorCode = 'TOO_MANY_FILES';
    message = '系统文件句柄不足';
  } else if (error.message.includes('ENOSPC')) {
    statusCode = 507;
    errorCode = 'INSUFFICIENT_STORAGE';
    message = '存储空间不足';
  }

  // 记录错误日志
  const errorInfo = {
    requestId: req.headers['x-request-id'],
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    statusCode,
    errorCode,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };

  if (statusCode >= 500) {
    logger.error('服务器错误', errorInfo);
  } else {
    logger.warn('客户端错误', errorInfo);
  }

  // 构建错误响应
  const errorResponse: any = {
    success: false,
    error: {
      code: errorCode,
      message
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown'
  };

  // 在开发环境下添加详细信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = details;
    errorResponse.error.stack = error.stack;
  } else if (details) {
    errorResponse.error.details = details;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

/**
 * 异步错误包装器
 */
export function catchAsync(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 提取验证错误详情
 */
function extractValidationErrors(error: any): any {
  if (error.details && Array.isArray(error.details)) {
    // Joi验证错误
    return error.details.map((detail: any) => ({
      field: detail.path?.join('.') || 'unknown',
      message: detail.message,
      value: detail.context?.value
    }));
  }

  if (error.errors) {
    // Mongoose验证错误
    const errors = [];
    for (const field in error.errors) {
      errors.push({
        field,
        message: error.errors[field].message,
        value: error.errors[field].value
      });
    }
    return errors;
  }

  return undefined;
}

/**
 * 获取Multer错误消息
 */
function getMulterErrorMessage(error: any): string {
  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      return '文件大小超过限制';
    case 'LIMIT_FILE_COUNT':
      return '文件数量超过限制';
    case 'LIMIT_FIELD_KEY':
      return '字段名过长';
    case 'LIMIT_FIELD_VALUE':
      return '字段值过长';
    case 'LIMIT_FIELD_COUNT':
      return '字段数量过多';
    case 'LIMIT_UNEXPECTED_FILE':
      return '意外的文件字段';
    case 'MISSING_FIELD_NAME':
      return '缺少字段名';
    default:
      return '文件上传错误';
  }
}

/**
 * 文档相关错误类
 */
export class DocumentNotFoundError extends AppError {
  constructor(documentId: string) {
    super(`文档不存在: ${documentId}`, 404, 'DOCUMENT_NOT_FOUND');
  }
}

export class DocumentProcessingError extends AppError {
  constructor(message: string) {
    super(`文档处理失败: ${message}`, 422, 'DOCUMENT_PROCESSING_ERROR');
  }
}

export class UnsupportedFileTypeError extends AppError {
  constructor(mimeType: string) {
    super(`不支持的文件类型: ${mimeType}`, 415, 'UNSUPPORTED_FILE_TYPE');
  }
}

export class FileSizeExceededError extends AppError {
  constructor(size: number, limit: number) {
    super(`文件大小超过限制: ${size} > ${limit}`, 413, 'FILE_SIZE_EXCEEDED');
  }
}

export class StorageError extends AppError {
  constructor(message: string) {
    super(`存储错误: ${message}`, 500, 'STORAGE_ERROR');
  }
}

export class ProcessingTimeoutError extends AppError {
  constructor() {
    super('文档处理超时', 408, 'PROCESSING_TIMEOUT');
  }
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  code: string,
  message: string,
  statusCode: number = 500,
  details?: any
): any {
  const response: any = {
    success: false,
    error: {
      code,
      message
    },
    timestamp: new Date().toISOString()
  };

  if (details) {
    response.error.details = details;
  }

  return response;
}
