/**
 * 文档管理路由
 * 处理文档的上传、查询、下载和删除
 */

import { Router, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { uploadSingle, validateUploadedFile, getFileInfo } from '../middleware/upload';
import { catchAsync } from '../middleware/errorHandler';
import { DocumentManager, DocumentStatus } from '../services/documentManager';
import { DocumentProcessor } from '../services/documentProcessor';
import { uploadStream, BUCKETS } from '../config/minio';
import { logger, logDocumentProcessing } from '../utils/logger';
import { validateDocumentQuery, validateDocumentUpload } from '../validators/documentValidator';

const router = Router();
const documentManager = new DocumentManager();
const documentProcessor = new DocumentProcessor();

/**
 * 上传文档
 * POST /api/v1/documents/upload
 */
router.post('/upload', uploadSingle, catchAsync(async (req: Request, res: Response) => {
  try {
    // 验证请求
    const { error, value } = validateDocumentUpload(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: error.details
        }
      });
    }

    // 验证文件
    validateUploadedFile(req.file!);
    const fileInfo = getFileInfo(req.file!);

    // 获取用户信息
    const userId = req.headers['x-user-id'] as string;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要用户认证'
        }
      });
    }

    // 生成文件名
    const fileExtension = fileInfo.extension;
    const filename = `${uuidv4()}${fileExtension}`;

    // 创建文档记录
    const document = await documentManager.createDocument(
      userId,
      filename,
      fileInfo.originalName,
      fileInfo.mimeType,
      fileInfo.size,
      value.tags,
      value.description
    );

    // 上传文件到MinIO
    const uploadResult = await uploadStream(
      BUCKETS.DOCUMENTS,
      document.storageKey,
      req.file!.buffer,
      req.file!.size,
      {
        'Content-Type': fileInfo.mimeType,
        'Original-Name': fileInfo.originalName,
        'User-Id': userId
      }
    );

    logDocumentProcessing('upload_complete', document.id, {
      filename: fileInfo.originalName,
      size: fileInfo.size,
      etag: uploadResult
    }, userId);

    // 异步处理文档
    processDocumentAsync(document.id, req.file!.buffer, fileInfo.mimeType);

    res.status(201).json({
      success: true,
      data: {
        document: {
          id: document.id,
          filename: document.originalName,
          mimeType: document.mimeType,
          size: document.size,
          status: document.status,
          uploadedAt: document.uploadedAt,
          tags: document.tags,
          description: document.description
        }
      },
      message: '文档上传成功，正在处理中'
    });

  } catch (error) {
    logger.error('文档上传失败', {
      error: error.message,
      userId: req.headers['x-user-id'],
      filename: req.file?.originalname
    });
    throw error;
  }
}));

/**
 * 获取文档列表
 * GET /api/v1/documents
 */
router.get('/', catchAsync(async (req: Request, res: Response) => {
  const userId = req.headers['x-user-id'] as string;
  
  // 验证查询参数
  const { error, value } = validateDocumentQuery(req.query);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: '查询参数验证失败',
        details: error.details
      }
    });
  }

  const queryOptions = {
    userId,
    ...value
  };

  const result = await documentManager.queryDocuments(queryOptions);

  res.json({
    success: true,
    data: {
      documents: result.documents,
      pagination: {
        total: result.total,
        limit: value.limit || 20,
        offset: value.offset || 0,
        hasMore: (value.offset || 0) + (value.limit || 20) < result.total
      }
    }
  });
}));

/**
 * 获取单个文档
 * GET /api/v1/documents/:id
 */
router.get('/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.headers['x-user-id'] as string;

  const document = await documentManager.getDocument(id, userId);
  
  if (!document) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'DOCUMENT_NOT_FOUND',
        message: '文档不存在'
      }
    });
  }

  res.json({
    success: true,
    data: { document }
  });
}));

/**
 * 下载文档
 * GET /api/v1/documents/:id/download
 */
router.get('/:id/download', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.headers['x-user-id'] as string;

  const document = await documentManager.getDocument(id, userId);
  
  if (!document) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'DOCUMENT_NOT_FOUND',
        message: '文档不存在'
      }
    });
  }

  const downloadUrl = await documentManager.generateDownloadUrl(id, userId);

  res.json({
    success: true,
    data: {
      downloadUrl,
      filename: document.originalName,
      mimeType: document.mimeType,
      size: document.size,
      expiresIn: 3600 // 1小时
    }
  });
}));

/**
 * 删除文档
 * DELETE /api/v1/documents/:id
 */
router.delete('/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.headers['x-user-id'] as string;

  await documentManager.deleteDocument(id, userId);

  res.json({
    success: true,
    message: '文档删除成功'
  });
}));

/**
 * 获取文档统计
 * GET /api/v1/documents/stats
 */
router.get('/stats', catchAsync(async (req: Request, res: Response) => {
  const userId = req.headers['x-user-id'] as string;

  const stats = await documentManager.getUserDocumentStats(userId);

  res.json({
    success: true,
    data: { stats }
  });
}));

/**
 * 重新处理文档
 * POST /api/v1/documents/:id/reprocess
 */
router.post('/:id/reprocess', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = req.headers['x-user-id'] as string;

  const document = await documentManager.getDocument(id, userId);
  
  if (!document) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'DOCUMENT_NOT_FOUND',
        message: '文档不存在'
      }
    });
  }

  if (document.status !== DocumentStatus.COMPLETED && document.status !== DocumentStatus.FAILED) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_DOCUMENT_STATUS',
        message: '只能重新处理已完成或失败的文档'
      }
    });
  }

  // 更新状态为处理中
  await documentManager.updateDocumentStatus(id, DocumentStatus.PROCESSING);

  // 异步重新处理
  reprocessDocumentAsync(id);

  res.json({
    success: true,
    message: '文档重新处理已开始'
  });
}));

/**
 * 异步处理文档
 */
async function processDocumentAsync(
  documentId: string,
  fileBuffer: Buffer,
  mimeType: string
): Promise<void> {
  try {
    // 更新状态为处理中
    await documentManager.updateDocumentStatus(documentId, DocumentStatus.PROCESSING);

    // 创建临时文件
    const fs = require('fs');
    const path = require('path');
    const tempDir = process.env.TEMP_DIR || './temp';
    const tempFilePath = path.join(tempDir, `${documentId}.tmp`);
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    fs.writeFileSync(tempFilePath, fileBuffer);

    // 处理文档
    const processingConfig = {
      chunkSize: parseInt(process.env.CHUNK_SIZE || '1000'),
      chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || '200'),
      enableMetadataExtraction: process.env.ENABLE_METADATA_EXTRACTION !== 'false',
      enableStructureAnalysis: process.env.ENABLE_STRUCTURE_ANALYSIS !== 'false'
    };

    const result = await documentProcessor.processDocument(
      tempFilePath,
      mimeType,
      documentId,
      processingConfig
    );

    // 更新文档状态
    await documentManager.updateDocumentStatus(
      documentId,
      DocumentStatus.COMPLETED,
      result.metadata,
      result.structure,
      result.chunks.length
    );

    // 清理临时文件
    fs.unlinkSync(tempFilePath);

    logDocumentProcessing('process_success', documentId, {
      chunksCount: result.chunks.length,
      processingTime: result.processingTime
    });

  } catch (error) {
    logger.error('文档处理失败', {
      documentId,
      error: error.message
    });

    // 更新状态为失败
    await documentManager.updateDocumentStatus(documentId, DocumentStatus.FAILED);

    logDocumentProcessing('process_failed', documentId, {
      error: error.message
    });
  }
}

/**
 * 异步重新处理文档
 */
async function reprocessDocumentAsync(documentId: string): Promise<void> {
  // 实现重新处理逻辑
  // 这里可以从MinIO重新下载文件并处理
  logger.info('开始重新处理文档', { documentId });
  // TODO: 实现重新处理逻辑
}

export default router;
