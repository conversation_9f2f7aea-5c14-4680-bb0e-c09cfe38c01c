# 文档上传服务存储配置指南

## 📋 概述

文档上传服务支持多种存储后端，包括本地文件系统存储和S3兼容的对象存储。本文档详细说明了如何配置和使用这些存储选项。

## 🗂️ 支持的存储类型

### 1. 本地文件系统存储
- **适用场景**: 开发环境、小规模部署、对数据本地化有要求的场景
- **优势**: 简单易用、无外部依赖、访问速度快
- **劣势**: 扩展性有限、无法跨服务器共享、备份复杂

### 2. S3兼容对象存储
- **适用场景**: 生产环境、大规模部署、需要高可用性的场景
- **支持的服务**: AWS S3、MinIO、阿里云OSS、腾讯云COS等
- **优势**: 高可用性、无限扩展、自动备份、跨区域复制
- **劣势**: 网络延迟、成本较高、依赖外部服务

## ⚙️ 配置说明

### 环境变量配置

```env
# 存储类型配置 (local | s3)
STORAGE_TYPE=s3

# 本地存储配置
LOCAL_STORAGE_PATH=/app/uploads
LOCAL_STORAGE_MAX_SIZE=10GB
LOCAL_STORAGE_PERMISSIONS=0755

# S3存储配置
S3_ENDPOINT=localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin123
S3_BUCKET_NAME=rag-documents
S3_REGION=us-east-1
S3_USE_SSL=false
S3_PATH_STYLE=true

# 通用配置
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,html
UPLOAD_TIMEOUT=300
ENABLE_VIRUS_SCAN=true
ENABLE_DUPLICATE_CHECK=true
```

### 本地存储配置

#### 基础配置
```typescript
// config/storage.ts
export const localStorageConfig = {
  // 存储根目录
  basePath: process.env.LOCAL_STORAGE_PATH || '/app/uploads',
  
  // 目录结构配置
  structure: {
    // 按年月日组织文件
    useTimeBasedPath: true,
    // 路径格式: /uploads/2025/08/27/
    pathFormat: 'YYYY/MM/DD',
    
    // 按用户组织文件
    useUserBasedPath: true,
    // 路径格式: /uploads/users/{userId}/
    userPathFormat: 'users/{userId}'
  },
  
  // 文件权限配置
  permissions: {
    directories: 0o755,
    files: 0o644
  },
  
  // 存储限制
  limits: {
    maxFileSize: '100MB',
    maxTotalSize: '10GB',
    maxFilesPerUser: 1000
  },
  
  // 清理策略
  cleanup: {
    enableAutoCleanup: true,
    retentionDays: 365,
    cleanupInterval: '0 2 * * *' // 每天凌晨2点清理
  }
};
```

#### 目录结构示例
```
/app/uploads/
├── 2025/
│   └── 08/
│       └── 27/
│           ├── user-123/
│           │   ├── document-1.pdf
│           │   └── document-2.docx
│           └── user-456/
│               └── report.txt
├── temp/                    # 临时文件目录
├── quarantine/             # 病毒扫描隔离目录
└── trash/                  # 软删除文件目录
```

### S3兼容存储配置

#### MinIO配置示例
```typescript
// config/minio.ts
export const minioConfig = {
  // 连接配置
  endPoint: process.env.S3_ENDPOINT?.split(':')[0] || 'localhost',
  port: parseInt(process.env.S3_ENDPOINT?.split(':')[1] || '9000'),
  useSSL: process.env.S3_USE_SSL === 'true',
  accessKey: process.env.S3_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.S3_SECRET_KEY || 'minioadmin123',
  
  // 存储桶配置
  buckets: {
    documents: process.env.S3_BUCKET_NAME || 'rag-documents',
    temp: 'rag-temp-files',
    backup: 'rag-backup'
  },
  
  // 对象命名策略
  objectNaming: {
    // 使用UUID作为对象名
    useUUID: true,
    // 保留原始文件名作为元数据
    preserveOriginalName: true,
    // 添加时间戳前缀
    addTimestamp: true,
    // 格式: 2025/08/27/uuid-original-name.ext
    pathFormat: 'YYYY/MM/DD/{uuid}-{originalName}'
  },
  
  // 元数据配置
  metadata: {
    // 自动添加的元数据
    autoMetadata: [
      'Content-Type',
      'Content-Length',
      'Upload-Time',
      'User-Id',
      'Original-Name',
      'File-Hash'
    ],
    
    // 自定义元数据前缀
    customPrefix: 'X-RAG-'
  }
};
```

#### AWS S3配置示例
```typescript
// config/aws-s3.ts
export const awsS3Config = {
  region: process.env.AWS_REGION || 'us-east-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  
  bucket: process.env.S3_BUCKET_NAME || 'rag-documents',
  
  // S3特定配置
  s3Options: {
    signatureVersion: 'v4',
    s3ForcePathStyle: false,
    
    // 服务器端加密
    serverSideEncryption: 'AES256',
    
    // 存储类别
    storageClass: 'STANDARD',
    
    // 生命周期管理
    lifecycleRules: [
      {
        id: 'DeleteOldFiles',
        status: 'Enabled',
        expiration: { days: 365 }
      },
      {
        id: 'TransitionToIA',
        status: 'Enabled',
        transition: {
          days: 30,
          storageClass: 'STANDARD_IA'
        }
      }
    ]
  }
};
```

## 🔧 实现示例

### 存储抽象层
```typescript
// services/storage/StorageInterface.ts
export interface StorageInterface {
  // 上传文件
  upload(file: Buffer, key: string, metadata?: Record<string, string>): Promise<UploadResult>;
  
  // 下载文件
  download(key: string): Promise<Buffer>;
  
  // 删除文件
  delete(key: string): Promise<void>;
  
  // 检查文件是否存在
  exists(key: string): Promise<boolean>;
  
  // 获取文件信息
  getInfo(key: string): Promise<FileInfo>;
  
  // 生成预签名URL
  generatePresignedUrl(key: string, operation: 'get' | 'put', expiresIn?: number): Promise<string>;
}
```

### 本地存储实现
```typescript
// services/storage/LocalStorage.ts
export class LocalStorage implements StorageInterface {
  private basePath: string;
  
  constructor(config: LocalStorageConfig) {
    this.basePath = config.basePath;
    this.ensureDirectoryExists(this.basePath);
  }
  
  async upload(file: Buffer, key: string, metadata?: Record<string, string>): Promise<UploadResult> {
    const filePath = path.join(this.basePath, key);
    const directory = path.dirname(filePath);
    
    // 确保目录存在
    await this.ensureDirectoryExists(directory);
    
    // 写入文件
    await fs.writeFile(filePath, file);
    
    // 设置文件权限
    await fs.chmod(filePath, 0o644);
    
    // 保存元数据
    if (metadata) {
      await this.saveMetadata(key, metadata);
    }
    
    return {
      key,
      size: file.length,
      etag: await this.calculateETag(file),
      location: filePath
    };
  }
  
  async download(key: string): Promise<Buffer> {
    const filePath = path.join(this.basePath, key);
    
    if (!await this.exists(key)) {
      throw new Error(`文件不存在: ${key}`);
    }
    
    return await fs.readFile(filePath);
  }
  
  // ... 其他方法实现
}
```

### S3存储实现
```typescript
// services/storage/S3Storage.ts
export class S3Storage implements StorageInterface {
  private client: S3Client;
  private bucketName: string;
  
  constructor(config: S3Config) {
    this.client = new S3Client(config);
    this.bucketName = config.bucketName;
  }
  
  async upload(file: Buffer, key: string, metadata?: Record<string, string>): Promise<UploadResult> {
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ContentType: this.getContentType(key),
      Metadata: metadata,
      ServerSideEncryption: 'AES256'
    });
    
    const result = await this.client.send(command);
    
    return {
      key,
      size: file.length,
      etag: result.ETag,
      location: `s3://${this.bucketName}/${key}`
    };
  }
  
  async download(key: string): Promise<Buffer> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key
    });
    
    const result = await this.client.send(command);
    
    if (!result.Body) {
      throw new Error(`文件不存在: ${key}`);
    }
    
    return Buffer.from(await result.Body.transformToByteArray());
  }
  
  // ... 其他方法实现
}
```

### 存储工厂
```typescript
// services/storage/StorageFactory.ts
export class StorageFactory {
  static create(type: 'local' | 's3', config: any): StorageInterface {
    switch (type) {
      case 'local':
        return new LocalStorage(config);
      case 's3':
        return new S3Storage(config);
      default:
        throw new Error(`不支持的存储类型: ${type}`);
    }
  }
}
```

## 🚀 使用示例

### 文件上传
```typescript
// routes/upload.ts
import { StorageFactory } from '../services/storage/StorageFactory';

const storage = StorageFactory.create(
  process.env.STORAGE_TYPE as 'local' | 's3',
  storageConfig
);

router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    const file = req.file;
    const userId = req.headers['x-user-id'];
    
    // 生成存储键
    const key = generateStorageKey(userId, file.originalname);
    
    // 准备元数据
    const metadata = {
      'original-name': file.originalname,
      'user-id': userId,
      'upload-time': new Date().toISOString(),
      'content-type': file.mimetype
    };
    
    // 上传文件
    const result = await storage.upload(file.buffer, key, metadata);
    
    res.json({
      success: true,
      data: {
        fileId: key,
        originalName: file.originalname,
        size: result.size,
        uploadedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
```

## 📊 监控和维护

### 存储监控指标
```typescript
// 存储空间使用情况
const storageMetrics = {
  totalSize: '总存储空间使用量',
  fileCount: '文件总数',
  avgFileSize: '平均文件大小',
  uploadRate: '上传速率',
  downloadRate: '下载速率',
  errorRate: '错误率'
};

// 本地存储特定指标
const localMetrics = {
  diskUsage: '磁盘使用率',
  inodeUsage: 'inode使用率',
  ioWait: 'IO等待时间'
};

// S3存储特定指标
const s3Metrics = {
  requestCount: 'API请求次数',
  dataTransfer: '数据传输量',
  cost: '存储成本',
  latency: '网络延迟'
};
```

### 维护任务
```bash
# 清理过期文件
npm run storage:cleanup

# 检查存储完整性
npm run storage:verify

# 迁移存储后端
npm run storage:migrate --from=local --to=s3

# 备份存储数据
npm run storage:backup

# 恢复存储数据
npm run storage:restore --backup-id=20250827
```

## 🔒 安全考虑

### 访问控制
- 文件访问权限验证
- 用户隔离和数据保护
- 预签名URL的安全使用

### 数据保护
- 文件内容病毒扫描
- 敏感信息检测和过滤
- 数据加密存储

### 审计日志
- 文件操作日志记录
- 访问行为审计
- 异常操作告警

## 🚨 故障排查

### 常见问题
1. **文件上传失败**: 检查存储配置和权限
2. **文件下载缓慢**: 优化网络配置和缓存策略
3. **存储空间不足**: 实施清理策略和容量监控
4. **S3连接超时**: 检查网络连接和认证配置

### 日志分析
```bash
# 查看上传日志
grep "upload" /var/log/document-service.log

# 查看错误日志
grep "ERROR" /var/log/document-service.log | grep "storage"

# 监控存储性能
tail -f /var/log/document-service.log | grep "storage_metrics"
```
