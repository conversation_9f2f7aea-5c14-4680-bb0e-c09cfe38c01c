# 文档服务 (Document Service)

RAG系统的文档处理服务，负责文档上传、解析、预处理和管理。

## 📋 功能特性

### 📄 文档处理
- **多格式支持**: PDF、Word、Excel、PowerPoint、文本、Markdown、HTML等
- **智能解析**: 提取文本内容、元数据和文档结构
- **文本分块**: 智能分割文档为适合向量化的文本块
- **元数据提取**: 自动提取标题、作者、关键词、实体等信息

### 📁 文件管理
- **安全上传**: 文件类型验证、大小限制、病毒扫描
- **对象存储**: 使用MinIO进行分布式文件存储
- **缩略图生成**: 自动生成文档预览图
- **版本控制**: 支持文档版本管理

### 🔍 内容分析
- **结构识别**: 识别标题、段落、表格、图片等结构
- **语言检测**: 自动检测文档语言
- **质量评估**: 评估文档完整性和可读性
- **关键词提取**: 自动提取文档关键词和主题

### 📊 数据管理
- **PostgreSQL存储**: 文档元数据和块信息存储
- **Redis缓存**: 热点数据缓存加速
- **全文搜索**: 支持文档内容全文检索
- **统计分析**: 用户文档使用统计

## 🏗️ 技术架构

### 技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js + TypeScript
- **数据库**: PostgreSQL 14+
- **缓存**: Redis 6+
- **存储**: MinIO (S3兼容)
- **文档解析**: pdf-parse, mammoth, sharp等

### 核心组件
```
Document Service
├── 文档解析器        # 多格式文档内容提取
├── 文本分块器        # 智能文本分割
├── 元数据提取器      # 文档信息分析
├── 文档管理器        # CRUD操作和状态管理
├── 文件上传处理      # 安全文件上传
├── 存储管理器        # MinIO对象存储
└── 缓存管理器        # Redis缓存优化
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 14.0
- Redis >= 6.0
- MinIO或S3兼容存储

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
cp .env.example .env
```

主要配置项：
```env
# 服务端口
PORT=3002

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/document_service

# Redis配置
REDIS_URL=redis://localhost:6379

# MinIO配置
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false

# 存储桶配置
MINIO_DOCUMENTS_BUCKET=documents
MINIO_THUMBNAILS_BUCKET=thumbnails
MINIO_TEMP_BUCKET=temp

# 文档处理配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
ENABLE_METADATA_EXTRACTION=true
ENABLE_STRUCTURE_ANALYSIS=true

# 文件上传限制
MAX_FILE_SIZE=100MB
UPLOAD_TEMP_DIR=./temp/uploads
```

### 数据库初始化
```bash
# 运行数据库迁移
psql -d document_service -f migrations/001_create_documents_table.sql
```

### 运行服务

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm run build
npm start
```

#### 使用Docker
```bash
docker build -t document-service .
docker run -p 3002:3002 document-service
```

## 📡 API 接口

### 文档上传
```http
POST /api/v1/documents/upload
Content-Type: multipart/form-data

{
  "file": <文件>,
  "tags": ["标签1", "标签2"],
  "description": "文档描述"
}
```

### 获取文档列表
```http
GET /api/v1/documents?limit=20&offset=0&status=completed&search=关键词
```

### 获取单个文档
```http
GET /api/v1/documents/:id
```

### 下载文档
```http
GET /api/v1/documents/:id/download
```

### 删除文档
```http
DELETE /api/v1/documents/:id
```

### 重新处理文档
```http
POST /api/v1/documents/:id/reprocess
```

### 获取文档统计
```http
GET /api/v1/documents/stats
```

### 健康检查
```http
GET /api/v1/health
```

## 📄 支持的文档格式

### 文档类型
- **PDF**: application/pdf
- **Word**: application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document
- **Excel**: application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
- **PowerPoint**: application/vnd.ms-powerpoint, application/vnd.openxmlformats-officedocument.presentationml.presentation
- **RTF**: application/rtf
- **OpenDocument**: application/vnd.oasis.opendocument.*

### 文本类型
- **纯文本**: text/plain
- **Markdown**: text/markdown
- **HTML**: text/html
- **CSV**: text/csv
- **XML**: text/xml, application/xml
- **JSON**: application/json

### 图片类型（OCR支持）
- **JPEG**: image/jpeg
- **PNG**: image/png
- **GIF**: image/gif
- **BMP**: image/bmp
- **TIFF**: image/tiff
- **WebP**: image/webp

## 🔧 配置说明

### 文档处理配置
```typescript
interface ProcessingConfig {
  chunkSize: number;          // 文本块大小（默认1000字符）
  chunkOverlap: number;       // 块重叠大小（默认200字符）
  enableMetadataExtraction: boolean;  // 启用元数据提取
  enableStructureAnalysis: boolean;   // 启用结构分析
  language?: string;          // 指定文档语言
}
```

### 文件上传限制
```typescript
const FILE_SIZE_LIMITS = {
  document: 100 * 1024 * 1024,  // 100MB
  image: 10 * 1024 * 1024,      // 10MB
  text: 5 * 1024 * 1024         // 5MB
};
```

### 支持的MIME类型
```typescript
const SUPPORTED_MIME_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/markdown',
  'text/html',
  'image/jpeg',
  'image/png'
  // ... 更多类型
];
```

## 📊 数据模型

### 文档表 (documents)
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL,
    status document_status NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    structure JSONB,
    chunks_count INTEGER,
    storage_key VARCHAR(500) NOT NULL,
    tags JSONB DEFAULT '[]',
    description TEXT
);
```

### 文档块表 (document_chunks)
```sql
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    start_position INTEGER NOT NULL,
    end_position INTEGER NOT NULL,
    word_count INTEGER,
    character_count INTEGER,
    heading TEXT,
    section TEXT,
    metadata JSONB,
    embedding VECTOR(1536)
);
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# 覆盖率测试
npm run test:coverage
```

### 测试文档上传
```bash
curl -X POST http://localhost:3002/api/v1/documents/upload \
  -H "x-user-id: test-user-id" \
  -F "file=@test.pdf" \
  -F "description=测试文档"
```

## 📈 性能优化

### 缓存策略
1. **文档元数据缓存**: Redis缓存热点文档信息
2. **解析结果缓存**: 缓存文档解析结果
3. **用户统计缓存**: 缓存用户文档统计信息

### 存储优化
1. **分布式存储**: 使用MinIO集群
2. **CDN加速**: 静态资源CDN分发
3. **压缩存储**: 文档内容压缩存储

### 处理优化
1. **异步处理**: 文档解析异步执行
2. **批量操作**: 支持批量文档处理
3. **流式处理**: 大文件流式上传和处理

## 🚨 故障排查

### 常见问题

1. **文档上传失败**
   ```bash
   # 检查文件大小限制
   curl -I http://localhost:3002/api/v1/health
   
   # 检查MinIO连接
   docker logs minio
   ```

2. **文档解析失败**
   ```bash
   # 查看处理日志
   tail -f logs/combined.log
   
   # 检查临时目录权限
   ls -la temp/
   ```

3. **数据库连接问题**
   ```bash
   # 测试数据库连接
   psql -d document_service -c "SELECT 1"
   
   # 检查连接池状态
   curl http://localhost:3002/api/v1/health
   ```

### 日志分析
```bash
# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log

# 实时监控
docker logs -f document-service
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
