-- 创建文档表
-- 存储文档的基本信息和元数据

-- 创建文档状态枚举类型
CREATE TYPE document_status AS ENUM (
    'uploading',
    'processing', 
    'completed',
    'failed',
    'deleted'
);

-- 创建文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL CHECK (size > 0),
    status document_status NOT NULL DEFAULT 'uploading',
    uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    structure JSONB,
    chunks_count INTEGER DEFAULT 0,
    storage_key VARCHAR(500) NOT NULL,
    thumbnail_key VARCHAR(500),
    tags JSONB DEFAULT '[]'::jsonb,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- 创建文档块表
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    start_position INTEGER NOT NULL,
    end_position INTEGER NOT NULL,
    word_count INTEGER NOT NULL DEFAULT 0,
    character_count INTEGER NOT NULL DEFAULT 0,
    heading TEXT,
    section TEXT,
    page_number INTEGER,
    metadata JSONB DEFAULT '{}'::jsonb,
    embedding VECTOR(1536), -- 假设使用1536维向量
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    CONSTRAINT unique_document_chunk_index UNIQUE (document_id, chunk_index),
    CONSTRAINT valid_position CHECK (start_position <= end_position),
    CONSTRAINT valid_counts CHECK (word_count >= 0 AND character_count >= 0)
);

-- 创建索引
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_mime_type ON documents(mime_type);
CREATE INDEX idx_documents_uploaded_at ON documents(uploaded_at);
CREATE INDEX idx_documents_processed_at ON documents(processed_at);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);
CREATE INDEX idx_documents_metadata ON documents USING GIN(metadata);
CREATE INDEX idx_documents_user_status ON documents(user_id, status);
CREATE INDEX idx_documents_user_uploaded ON documents(user_id, uploaded_at DESC);

CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_document_chunks_chunk_index ON document_chunks(document_id, chunk_index);
CREATE INDEX idx_document_chunks_heading ON document_chunks(heading);
CREATE INDEX idx_document_chunks_section ON document_chunks(section);
CREATE INDEX idx_document_chunks_metadata ON document_chunks USING GIN(metadata);

-- 如果使用向量搜索，创建向量索引
-- CREATE INDEX idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);

-- 创建全文搜索索引
CREATE INDEX idx_documents_search ON documents USING GIN(
    to_tsvector('simple', coalesce(original_name, '') || ' ' || coalesce(description, ''))
);

CREATE INDEX idx_document_chunks_search ON document_chunks USING GIN(
    to_tsvector('simple', content)
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建更新时间触发器
CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建文档统计视图
CREATE VIEW document_stats AS
SELECT 
    user_id,
    COUNT(*) as total_documents,
    SUM(size) as total_size,
    COUNT(CASE WHEN status = 'uploading' THEN 1 END) as uploading_count,
    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_count,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
    AVG(size) as average_size,
    MAX(uploaded_at) as last_upload,
    SUM(chunks_count) as total_chunks
FROM documents 
WHERE status != 'deleted'
GROUP BY user_id;

-- 创建文档类型统计视图
CREATE VIEW document_type_stats AS
SELECT 
    user_id,
    mime_type,
    COUNT(*) as count,
    SUM(size) as total_size,
    AVG(size) as average_size,
    SUM(chunks_count) as total_chunks
FROM documents 
WHERE status != 'deleted'
GROUP BY user_id, mime_type;

-- 添加注释
COMMENT ON TABLE documents IS '文档基本信息表';
COMMENT ON COLUMN documents.id IS '文档唯一标识';
COMMENT ON COLUMN documents.user_id IS '用户ID';
COMMENT ON COLUMN documents.filename IS '存储文件名';
COMMENT ON COLUMN documents.original_name IS '原始文件名';
COMMENT ON COLUMN documents.mime_type IS 'MIME类型';
COMMENT ON COLUMN documents.size IS '文件大小（字节）';
COMMENT ON COLUMN documents.status IS '文档状态';
COMMENT ON COLUMN documents.uploaded_at IS '上传时间';
COMMENT ON COLUMN documents.processed_at IS '处理完成时间';
COMMENT ON COLUMN documents.metadata IS '文档元数据（JSON）';
COMMENT ON COLUMN documents.structure IS '文档结构信息（JSON）';
COMMENT ON COLUMN documents.chunks_count IS '文档块数量';
COMMENT ON COLUMN documents.storage_key IS '存储键值';
COMMENT ON COLUMN documents.thumbnail_key IS '缩略图键值';
COMMENT ON COLUMN documents.tags IS '标签列表（JSON数组）';
COMMENT ON COLUMN documents.description IS '文档描述';

COMMENT ON TABLE document_chunks IS '文档块表';
COMMENT ON COLUMN document_chunks.id IS '块唯一标识';
COMMENT ON COLUMN document_chunks.document_id IS '所属文档ID';
COMMENT ON COLUMN document_chunks.chunk_index IS '块索引';
COMMENT ON COLUMN document_chunks.content IS '块内容';
COMMENT ON COLUMN document_chunks.start_position IS '在原文档中的起始位置';
COMMENT ON COLUMN document_chunks.end_position IS '在原文档中的结束位置';
COMMENT ON COLUMN document_chunks.word_count IS '单词数量';
COMMENT ON COLUMN document_chunks.character_count IS '字符数量';
COMMENT ON COLUMN document_chunks.heading IS '所属标题';
COMMENT ON COLUMN document_chunks.section IS '所属章节';
COMMENT ON COLUMN document_chunks.page_number IS '页码';
COMMENT ON COLUMN document_chunks.metadata IS '块元数据（JSON）';
COMMENT ON COLUMN document_chunks.embedding IS '向量嵌入';

-- 创建示例数据（可选，用于测试）
-- INSERT INTO documents (
--     user_id, filename, original_name, mime_type, size, status, storage_key, description
-- ) VALUES (
--     'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
--     'sample.pdf',
--     'Sample Document.pdf',
--     'application/pdf',
--     1024000,
--     'completed',
--     'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee/sample.pdf',
--     'This is a sample document for testing'
-- );
