/**
 * Jest 测试配置
 * 用于用户服务的单元测试和集成测试
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 根目录
  rootDir: '.',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.test.ts',
    '<rootDir>/src/**/*.test.ts'
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],

  // TypeScript 转换
  preset: 'ts-jest',

  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },

  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],

  // 清除模拟
  clearMocks: true,

  // 详细输出
  verbose: true,

  // 测试超时时间
  testTimeout: 10000,

  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json'
    }
  },

  // 转换忽略模式
  transformIgnorePatterns: [
    '/node_modules/(?!(module-that-needs-to-be-transformed)/)'
  ]
};
