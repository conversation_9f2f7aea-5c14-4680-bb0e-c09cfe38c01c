# 用户服务 (User Service)

RAG系统的用户认证、授权和权限管理微服务。

## 📋 功能特性

### 🔐 认证功能
- **用户注册**: 支持邮箱注册，密码强度验证
- **用户登录**: JWT令牌认证，支持记住我功能
- **令牌刷新**: 自动刷新访问令牌，保持登录状态
- **用户登出**: 安全登出，撤销刷新令牌
- **密码管理**: 忘记密码、重置密码、修改密码

### 📧 邮箱功能
- **邮箱验证**: 注册后邮箱验证，支持重新发送
- **密码重置**: 通过邮箱重置密码
- **欢迎邮件**: 新用户注册欢迎邮件

### 👥 用户管理
- **用户信息**: 查看和更新用户资料
- **角色权限**: 基于角色的访问控制(RBAC)
- **账户状态**: 用户状态管理(激活/禁用)

### 🔒 安全特性
- **密码加密**: 使用bcrypt加密存储密码
- **JWT令牌**: 安全的访问令牌和刷新令牌机制
- **会话管理**: 设备信息记录，支持多设备登录
- **审计日志**: 详细的认证操作日志记录
- **限流保护**: API请求频率限制

## 🏗️ 技术架构

### 技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js
- **语言**: TypeScript
- **数据库**: PostgreSQL
- **缓存**: Redis
- **认证**: JWT (jsonwebtoken)
- **密码加密**: bcryptjs
- **邮件服务**: Nodemailer
- **日志**: Winston
- **测试**: Jest + Supertest

### 项目结构
```
src/
├── config/           # 配置文件
│   ├── database.ts   # 数据库配置
│   └── redis.ts      # Redis配置
├── controllers/      # 控制器层
│   └── authController.ts
├── middleware/       # 中间件
│   ├── authMiddleware.ts
│   ├── errorHandler.ts
│   └── validateRequest.ts
├── routes/          # 路由定义
│   ├── auth.ts
│   ├── user.ts
│   └── health.ts
├── services/        # 业务逻辑层
│   ├── userService.ts
│   ├── tokenService.ts
│   └── emailService.ts
├── utils/           # 工具函数
│   ├── logger.ts
│   ├── appError.ts
│   └── catchAsync.ts
├── __tests__/       # 测试文件
└── index.ts         # 应用入口
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 13
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境变量文件并配置：
```bash
cp .env.example .env
```

主要配置项：
```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/rag_system

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 运行服务

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm run build
npm start
```

#### 使用Docker
```bash
docker build -t user-service .
docker run -p 3001:3001 user-service
```

## 📡 API 接口

### 认证接口

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "confirmPassword": "Password123",
  "name": "用户姓名"
}
```

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123",
  "rememberMe": false
}
```

#### 刷新令牌
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh-token-here"
}
```

#### 用户登出
```http
POST /api/v1/auth/logout
Content-Type: application/json

{
  "refreshToken": "refresh-token-here"
}
```

### 用户管理接口

#### 获取当前用户信息
```http
GET /api/v1/auth/me
Authorization: Bearer <access-token>
```

#### 修改密码
```http
POST /api/v1/auth/change-password
Authorization: Bearer <access-token>
Content-Type: application/json

{
  "currentPassword": "OldPassword123",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

### 健康检查
```http
GET /api/v1/health
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 测试覆盖率
目标覆盖率：
- 分支覆盖率: >= 80%
- 函数覆盖率: >= 80%
- 行覆盖率: >= 80%
- 语句覆盖率: >= 80%

## 📊 监控和日志

### 日志级别
- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息

### 日志输出
- 控制台输出（开发环境）
- 文件输出（生产环境）
- 结构化JSON格式

### 监控指标
- 请求响应时间
- 错误率
- 认证成功/失败率
- 活跃用户数

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `3001` |
| `DATABASE_URL` | 数据库连接字符串 | - |
| `REDIS_URL` | Redis连接字符串 | - |
| `JWT_SECRET` | JWT密钥 | - |
| `JWT_EXPIRES_IN` | 访问令牌过期时间 | `15m` |
| `JWT_REFRESH_EXPIRES_IN` | 刷新令牌过期时间 | `7d` |
| `SMTP_HOST` | SMTP服务器地址 | `smtp.gmail.com` |
| `SMTP_PORT` | SMTP端口 | `587` |
| `SMTP_USER` | SMTP用户名 | - |
| `SMTP_PASS` | SMTP密码 | - |
| `LOG_LEVEL` | 日志级别 | `info` |

## 🚨 错误处理

### 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| `USER_ALREADY_EXISTS` | 400 | 用户已存在 |
| `INVALID_CREDENTIALS` | 401 | 无效的凭据 |
| `ACCOUNT_DISABLED` | 401 | 账户已禁用 |
| `TOKEN_EXPIRED` | 401 | 令牌已过期 |
| `INVALID_TOKEN` | 401 | 无效的令牌 |
| `EMAIL_NOT_VERIFIED` | 403 | 邮箱未验证 |
| `INSUFFICIENT_PERMISSIONS` | 403 | 权限不足 |
| `USER_NOT_FOUND` | 404 | 用户不存在 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |

## 🔐 安全最佳实践

1. **密码安全**
   - 最小长度8位
   - 必须包含大小写字母和数字
   - 使用bcrypt加密存储

2. **令牌安全**
   - 访问令牌短期有效(15分钟)
   - 刷新令牌长期有效(7天)
   - 支持令牌撤销

3. **会话管理**
   - 记录设备信息
   - 支持多设备登录
   - 异常登录检测

4. **API安全**
   - 请求频率限制
   - CORS配置
   - 安全头设置

## 📈 性能优化

1. **缓存策略**
   - 用户会话缓存到Redis
   - 频繁查询数据缓存

2. **数据库优化**
   - 合理的索引设计
   - 连接池管理
   - 查询优化

3. **响应优化**
   - 请求压缩
   - 响应缓存
   - 异步处理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
