/**
 * 数据库连接配置
 * 使用PostgreSQL作为主数据库
 */

import { Pool } from 'pg';
import { logger } from '../utils/logger';

let pool: Pool;

/**
 * 数据库连接配置
 */
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20, // 最大连接数
  idleTimeoutMillis: 30000, // 空闲超时时间
  connectionTimeoutMillis: 2000, // 连接超时时间
};

/**
 * 连接数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    pool = new Pool(dbConfig);
    
    // 测试连接
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    
    logger.info('PostgreSQL数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 获取数据库连接池
 */
export function getPool(): Pool {
  if (!pool) {
    throw new Error('数据库未初始化，请先调用connectDatabase()');
  }
  return pool;
}

/**
 * 执行SQL查询
 */
export async function query(text: string, params?: any[]): Promise<any> {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('SQL查询执行', {
      query: text,
      duration: `${duration}ms`,
      rows: result.rowCount
    });
    
    return result;
  } catch (error) {
    logger.error('SQL查询失败:', {
      query: text,
      params,
      error: error.message
    });
    throw error;
  }
}

/**
 * 开始事务
 */
export async function beginTransaction() {
  const client = await pool.connect();
  await client.query('BEGIN');
  return client;
}

/**
 * 提交事务
 */
export async function commitTransaction(client: any) {
  try {
    await client.query('COMMIT');
  } finally {
    client.release();
  }
}

/**
 * 回滚事务
 */
export async function rollbackTransaction(client: any) {
  try {
    await client.query('ROLLBACK');
  } finally {
    client.release();
  }
}

/**
 * 关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    logger.info('数据库连接已关闭');
  }
}
