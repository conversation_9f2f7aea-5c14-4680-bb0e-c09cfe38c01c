/**
 * Redis连接配置
 * 用于缓存和会话管理
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';

let redisClient: RedisClientType;

/**
 * Redis连接配置
 */
const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 5000,
    lazyConnect: true,
  },
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
};

/**
 * 连接Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    redisClient = createClient(redisConfig);
    
    // 错误处理
    redisClient.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });
    
    redisClient.on('connect', () => {
      logger.info('Redis连接建立');
    });
    
    redisClient.on('ready', () => {
      logger.info('Redis连接就绪');
    });
    
    redisClient.on('end', () => {
      logger.info('Redis连接关闭');
    });
    
    // 连接Redis
    await redisClient.connect();
    
    // 测试连接
    await redisClient.ping();
    
    logger.info('Redis连接成功');
  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 获取Redis客户端
 */
export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis未初始化，请先调用connectRedis()');
  }
  return redisClient;
}

/**
 * 设置缓存
 */
export async function setCache(
  key: string, 
  value: string, 
  ttlSeconds?: number
): Promise<void> {
  try {
    if (ttlSeconds) {
      await redisClient.setEx(key, ttlSeconds, value);
    } else {
      await redisClient.set(key, value);
    }
    
    logger.debug('缓存设置成功', { key, ttl: ttlSeconds });
  } catch (error) {
    logger.error('缓存设置失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 获取缓存
 */
export async function getCache(key: string): Promise<string | null> {
  try {
    const value = await redisClient.get(key);
    logger.debug('缓存获取', { key, found: !!value });
    return value;
  } catch (error) {
    logger.error('缓存获取失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 删除缓存
 */
export async function deleteCache(key: string): Promise<void> {
  try {
    await redisClient.del(key);
    logger.debug('缓存删除成功', { key });
  } catch (error) {
    logger.error('缓存删除失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 检查缓存是否存在
 */
export async function existsCache(key: string): Promise<boolean> {
  try {
    const exists = await redisClient.exists(key);
    return exists === 1;
  } catch (error) {
    logger.error('缓存检查失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 设置哈希缓存
 */
export async function setHashCache(
  key: string, 
  field: string, 
  value: string
): Promise<void> {
  try {
    await redisClient.hSet(key, field, value);
    logger.debug('哈希缓存设置成功', { key, field });
  } catch (error) {
    logger.error('哈希缓存设置失败:', { key, field, error: error.message });
    throw error;
  }
}

/**
 * 获取哈希缓存
 */
export async function getHashCache(
  key: string, 
  field: string
): Promise<string | undefined> {
  try {
    const value = await redisClient.hGet(key, field);
    logger.debug('哈希缓存获取', { key, field, found: !!value });
    return value;
  } catch (error) {
    logger.error('哈希缓存获取失败:', { key, field, error: error.message });
    throw error;
  }
}

/**
 * 获取所有哈希缓存
 */
export async function getAllHashCache(key: string): Promise<Record<string, string>> {
  try {
    const value = await redisClient.hGetAll(key);
    logger.debug('哈希缓存全部获取', { key, count: Object.keys(value).length });
    return value;
  } catch (error) {
    logger.error('哈希缓存全部获取失败:', { key, error: error.message });
    throw error;
  }
}

/**
 * 设置过期时间
 */
export async function expireCache(key: string, ttlSeconds: number): Promise<void> {
  try {
    await redisClient.expire(key, ttlSeconds);
    logger.debug('缓存过期时间设置成功', { key, ttl: ttlSeconds });
  } catch (error) {
    logger.error('缓存过期时间设置失败:', { key, ttl: ttlSeconds, error: error.message });
    throw error;
  }
}

/**
 * 关闭Redis连接
 */
export async function closeRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    logger.info('Redis连接已关闭');
  }
}
