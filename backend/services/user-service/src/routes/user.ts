/**
 * 用户路由
 * 处理用户管理相关操作
 */

import { Router } from 'express';
import { body, query } from 'express-validator';
import { UserController } from '../controllers/userController';
import { validateRequest } from '../middleware/validateRequest';
import { authMiddleware, requireAdmin, requireOwnerOrAdmin } from '../middleware/authMiddleware';

const router = Router();
const userController = new UserController();

/**
 * 获取用户列表 (管理员)
 * GET /api/v1/users
 */
router.get('/', authMiddleware, requireAdmin, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页大小必须在1-100之间'),
  query('role')
    .optional()
    .isIn(['admin', 'user', 'viewer'])
    .withMessage('角色必须是admin、user或viewer'),
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('状态必须是active、inactive或suspended'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('搜索关键词长度不能超过100个字符')
], validateRequest, userController.getUsers);

/**
 * 获取用户统计 (管理员)
 * GET /api/v1/users/stats
 */
router.get('/stats', authMiddleware, requireAdmin, userController.getUserStats);

/**
 * 获取用户详情
 * GET /api/v1/users/:userId
 */
router.get('/:userId', authMiddleware, requireOwnerOrAdmin(), userController.getUserById);

/**
 * 更新用户信息
 * PUT /api/v1/users/:userId
 */
router.put('/:userId', authMiddleware, requireOwnerOrAdmin(), [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('姓名长度必须在2-50个字符之间'),
  body('avatarUrl')
    .optional()
    .isURL()
    .withMessage('头像URL格式不正确'),
  body('role')
    .optional()
    .isIn(['admin', 'user', 'viewer'])
    .withMessage('角色必须是admin、user或viewer'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('状态必须是active、inactive或suspended')
], validateRequest, userController.updateUser);

/**
 * 删除用户 (管理员)
 * DELETE /api/v1/users/:userId
 */
router.delete('/:userId', authMiddleware, requireAdmin, userController.deleteUser);

/**
 * 获取用户偏好设置
 * GET /api/v1/users/:userId/preferences
 */
router.get('/:userId/preferences', authMiddleware, requireOwnerOrAdmin(), userController.getUserPreferences);

/**
 * 更新用户偏好设置
 * PUT /api/v1/users/:userId/preferences
 */
router.put('/:userId/preferences', authMiddleware, requireOwnerOrAdmin(), [
  body('language')
    .optional()
    .isIn(['zh-CN', 'en-US', 'ja-JP'])
    .withMessage('语言必须是zh-CN、en-US或ja-JP'),
  body('timezone')
    .optional()
    .isString()
    .withMessage('时区必须是字符串'),
  body('theme')
    .optional()
    .isIn(['light', 'dark', 'auto'])
    .withMessage('主题必须是light、dark或auto'),
  body('notifications')
    .optional()
    .isObject()
    .withMessage('通知设置必须是对象')
], validateRequest, userController.updateUserPreferences);

/**
 * 获取用户活跃会话
 * GET /api/v1/users/:userId/sessions
 */
router.get('/:userId/sessions', authMiddleware, requireOwnerOrAdmin(), userController.getUserSessions);

/**
 * 撤销用户会话
 * DELETE /api/v1/users/:userId/sessions/:sessionId
 */
router.delete('/:userId/sessions/:sessionId', authMiddleware, requireOwnerOrAdmin(), userController.revokeUserSession);

/**
 * 撤销用户所有会话
 * DELETE /api/v1/users/:userId/sessions
 */
router.delete('/:userId/sessions', authMiddleware, requireOwnerOrAdmin(), userController.revokeAllUserSessions);

export { router as userRoutes };
