/**
 * 健康检查路由
 * 提供服务健康状态检查
 */

import { Router } from 'express';
import { HealthController } from '../controllers/healthController';

const router = Router();
const healthController = new HealthController();

/**
 * 基础健康检查
 * GET /api/v1/health
 */
router.get('/', healthController.basicHealth);

/**
 * 详细健康检查
 * GET /api/v1/health/detailed
 */
router.get('/detailed', healthController.detailedHealth);

/**
 * 就绪检查
 * GET /api/v1/health/ready
 */
router.get('/ready', healthController.readinessCheck);

/**
 * 存活检查
 * GET /api/v1/health/live
 */
router.get('/live', healthController.livenessCheck);

export { router as healthRoutes };
