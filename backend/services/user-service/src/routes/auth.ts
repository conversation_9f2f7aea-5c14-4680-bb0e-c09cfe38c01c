/**
 * 认证路由
 * 处理用户登录、注册、令牌刷新等认证相关操作
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from '../controllers/authController';
import { validateRequest } from '../middleware/validateRequest';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();
const authController = new AuthController();

/**
 * 用户注册
 * POST /api/v1/auth/register
 */
router.post('/register', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('姓名长度必须在2-50个字符之间'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('确认密码与密码不匹配');
      }
      return true;
    })
], validateRequest, authController.register);

/**
 * 用户登录
 * POST /api/v1/auth/login
 */
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空'),
  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('记住我必须是布尔值')
], validateRequest, authController.login);

/**
 * 刷新令牌
 * POST /api/v1/auth/refresh
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
], validateRequest, authController.refreshToken);

/**
 * 用户登出
 * POST /api/v1/auth/logout
 */
router.post('/logout', [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
], validateRequest, authController.logout);

/**
 * 发送密码重置邮件
 * POST /api/v1/auth/forgot-password
 */
router.post('/forgot-password', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址')
], validateRequest, authController.forgotPassword);

/**
 * 重置密码
 * POST /api/v1/auth/reset-password
 */
router.post('/reset-password', [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能为空'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('确认密码与密码不匹配');
      }
      return true;
    })
], validateRequest, authController.resetPassword);

/**
 * 修改密码
 * POST /api/v1/auth/change-password
 */
router.post('/change-password', authMiddleware, [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8位')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含大小写字母和数字'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
], validateRequest, authController.changePassword);

/**
 * 验证邮箱
 * POST /api/v1/auth/verify-email
 */
router.post('/verify-email', [
  body('token')
    .notEmpty()
    .withMessage('验证令牌不能为空')
], validateRequest, authController.verifyEmail);

/**
 * 重新发送验证邮件
 * POST /api/v1/auth/resend-verification
 */
router.post('/resend-verification', authMiddleware, authController.resendVerification);

/**
 * 获取当前用户信息
 * GET /api/v1/auth/me
 */
router.get('/me', authMiddleware, authController.getCurrentUser);

/**
 * 验证令牌
 * POST /api/v1/auth/verify-token
 */
router.post('/verify-token', [
  body('token')
    .notEmpty()
    .withMessage('令牌不能为空')
], validateRequest, authController.verifyToken);

export { router as authRoutes };
