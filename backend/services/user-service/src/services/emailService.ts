/**
 * 邮件服务
 * 处理邮件发送功能，包括验证邮件、密码重置邮件等
 */

import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.initializeTransporter();
  }

  /**
   * 初始化邮件传输器
   */
  private initializeTransporter(): void {
    const config = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    };

    this.transporter = nodemailer.createTransporter(config);

    // 验证连接配置
    this.transporter.verify((error, success) => {
      if (error) {
        logger.error('邮件服务配置错误:', error);
      } else {
        logger.info('邮件服务配置成功');
      }
    });
  }

  /**
   * 发送邮箱验证邮件
   */
  async sendVerificationEmail(
    email: string,
    name: string,
    verificationToken: string
  ): Promise<void> {
    try {
      const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3100'}/verify-email?token=${verificationToken}`;

      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: email,
        subject: 'RAG系统 - 邮箱验证',
        html: this.getVerificationEmailTemplate(name, verificationUrl)
      };

      await this.transporter.sendMail(mailOptions);
      logger.info('验证邮件发送成功:', { email });
    } catch (error) {
      logger.error('发送验证邮件失败:', { email, error: error.message });
      throw new AppError('发送验证邮件失败', 500, 'EMAIL_SEND_FAILED');
    }
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(
    email: string,
    name: string,
    resetToken: string
  ): Promise<void> {
    try {
      const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3100'}/reset-password?token=${resetToken}`;

      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: email,
        subject: 'RAG系统 - 密码重置',
        html: this.getPasswordResetEmailTemplate(name, resetUrl)
      };

      await this.transporter.sendMail(mailOptions);
      logger.info('密码重置邮件发送成功:', { email });
    } catch (error) {
      logger.error('发送密码重置邮件失败:', { email, error: error.message });
      throw new AppError('发送密码重置邮件失败', 500, 'EMAIL_SEND_FAILED');
    }
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      const mailOptions = {
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: email,
        subject: 'RAG系统 - 欢迎加入',
        html: this.getWelcomeEmailTemplate(name)
      };

      await this.transporter.sendMail(mailOptions);
      logger.info('欢迎邮件发送成功:', { email });
    } catch (error) {
      logger.error('发送欢迎邮件失败:', { email, error: error.message });
      // 欢迎邮件发送失败不应该阻止用户注册流程
      logger.warn('欢迎邮件发送失败，但不影响用户注册');
    }
  }

  /**
   * 邮箱验证邮件模板
   */
  private getVerificationEmailTemplate(name: string, verificationUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 20px 0;
          }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>RAG系统</h1>
          </div>
          <div class="content">
            <h2>邮箱验证</h2>
            <p>亲爱的 ${name}，</p>
            <p>感谢您注册RAG系统！请点击下面的按钮验证您的邮箱地址：</p>
            <a href="${verificationUrl}" class="button">验证邮箱</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p><a href="${verificationUrl}">${verificationUrl}</a></p>
            <p>此链接将在24小时后过期。</p>
            <p>如果您没有注册RAG系统，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由RAG系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 密码重置邮件模板
   */
  private getPasswordResetEmailTemplate(name: string, resetUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { 
            display: inline-block; 
            padding: 12px 24px; 
            background: #dc3545; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin: 20px 0;
          }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>RAG系统</h1>
          </div>
          <div class="content">
            <h2>密码重置</h2>
            <p>亲爱的 ${name}，</p>
            <p>我们收到了您的密码重置请求。请点击下面的按钮重置您的密码：</p>
            <a href="${resetUrl}" class="button">重置密码</a>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p><a href="${resetUrl}">${resetUrl}</a></p>
            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>此链接将在1小时后过期</li>
                <li>如果您没有请求密码重置，请忽略此邮件</li>
                <li>为了您的账户安全，请不要将此链接分享给他人</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>此邮件由RAG系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 欢迎邮件模板
   */
  private getWelcomeEmailTemplate(name: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎加入RAG系统</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .feature { margin: 15px 0; padding: 10px; background: white; border-radius: 4px; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎加入RAG系统！</h1>
          </div>
          <div class="content">
            <p>亲爱的 ${name}，</p>
            <p>欢迎加入RAG系统！您现在可以享受以下功能：</p>
            
            <div class="feature">
              <h3>🤖 智能问答</h3>
              <p>基于您上传的文档进行智能问答，获得准确的答案。</p>
            </div>
            
            <div class="feature">
              <h3>📚 文档管理</h3>
              <p>上传、管理和组织您的文档，支持多种格式。</p>
            </div>
            
            <div class="feature">
              <h3>💬 对话历史</h3>
              <p>保存和回顾您的对话历史，随时查看之前的问答记录。</p>
            </div>
            
            <div class="feature">
              <h3>🔒 安全可靠</h3>
              <p>您的数据安全是我们的首要任务，采用企业级安全措施。</p>
            </div>
            
            <p>如果您有任何问题或需要帮助，请随时联系我们的支持团队。</p>
            <p>祝您使用愉快！</p>
          </div>
          <div class="footer">
            <p>RAG系统团队</p>
            <p>此邮件由RAG系统自动发送，请勿回复。</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 测试邮件连接
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      logger.error('邮件服务连接测试失败:', error);
      return false;
    }
  }
}
