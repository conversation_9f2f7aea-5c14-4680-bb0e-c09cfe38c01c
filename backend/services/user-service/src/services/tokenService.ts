/**
 * 令牌服务
 * 处理JWT令牌生成、验证和会话管理
 */

import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { query } from '../config/database';
import { setCache, getCache, deleteCache } from '../config/redis';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';

export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

export interface SessionData {
  id: string;
  userId: string;
  refreshTokenHash: string;
  deviceInfo?: any;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
  lastUsedAt: Date;
}

export class TokenService {
  private readonly jwtSecret: string;
  private readonly accessTokenExpiry: string;
  private readonly refreshTokenExpiry: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
    this.accessTokenExpiry = process.env.JWT_EXPIRES_IN || '15m';
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRES_IN || '7d';
  }

  /**
   * 生成访问令牌
   */
  generateAccessToken(user: { id: string; email: string; role: string }): string {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.accessTokenExpiry,
      issuer: 'rag-system',
      audience: 'rag-users'
    });
  }

  /**
   * 验证访问令牌
   */
  verifyAccessToken(token: string): TokenPayload {
    try {
      return jwt.verify(token, this.jwtSecret, {
        issuer: 'rag-system',
        audience: 'rag-users'
      }) as TokenPayload;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AppError('令牌已过期', 401, 'TOKEN_EXPIRED');
      } else if (error.name === 'JsonWebTokenError') {
        throw new AppError('无效的令牌', 401, 'INVALID_TOKEN');
      } else {
        throw new AppError('令牌验证失败', 401, 'TOKEN_VERIFICATION_FAILED');
      }
    }
  }

  /**
   * 生成刷新令牌
   */
  async generateRefreshToken(
    userId: string,
    deviceInfo: {
      ip?: string;
      userAgent?: string;
      rememberMe?: boolean;
    }
  ): Promise<string> {
    try {
      const refreshToken = uuidv4();
      const refreshTokenHash = crypto
        .createHash('sha256')
        .update(refreshToken)
        .digest('hex');

      // 计算过期时间
      const expiresAt = new Date();
      if (deviceInfo.rememberMe) {
        expiresAt.setDate(expiresAt.getDate() + 30); // 30天
      } else {
        expiresAt.setDate(expiresAt.getDate() + 7); // 7天
      }

      // 保存会话到数据库
      await query(
        `INSERT INTO user_sessions (user_id, refresh_token_hash, device_info, ip_address, user_agent, expires_at)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          userId,
          refreshTokenHash,
          JSON.stringify(deviceInfo),
          deviceInfo.ip,
          deviceInfo.userAgent,
          expiresAt
        ]
      );

      logger.info('刷新令牌生成成功:', { userId, expiresAt });
      return refreshToken;
    } catch (error) {
      logger.error('生成刷新令牌失败:', { userId, error: error.message });
      throw new AppError('生成刷新令牌失败', 500, 'TOKEN_GENERATION_FAILED');
    }
  }

  /**
   * 验证刷新令牌
   */
  async validateRefreshToken(refreshToken: string): Promise<SessionData | null> {
    try {
      const refreshTokenHash = crypto
        .createHash('sha256')
        .update(refreshToken)
        .digest('hex');

      const result = await query(
        `SELECT * FROM user_sessions 
         WHERE refresh_token_hash = $1 AND expires_at > CURRENT_TIMESTAMP`,
        [refreshTokenHash]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      logger.error('验证刷新令牌失败:', { error: error.message });
      return null;
    }
  }

  /**
   * 撤销刷新令牌
   */
  async revokeRefreshToken(refreshToken: string): Promise<void> {
    try {
      const refreshTokenHash = crypto
        .createHash('sha256')
        .update(refreshToken)
        .digest('hex');

      await query(
        'DELETE FROM user_sessions WHERE refresh_token_hash = $1',
        [refreshTokenHash]
      );

      logger.info('刷新令牌撤销成功');
    } catch (error) {
      logger.error('撤销刷新令牌失败:', { error: error.message });
      throw new AppError('撤销刷新令牌失败', 500, 'TOKEN_REVOCATION_FAILED');
    }
  }

  /**
   * 撤销用户所有会话
   */
  async revokeAllUserSessions(userId: string): Promise<void> {
    try {
      await query(
        'DELETE FROM user_sessions WHERE user_id = $1',
        [userId]
      );

      logger.info('用户所有会话撤销成功:', { userId });
    } catch (error) {
      logger.error('撤销用户所有会话失败:', { userId, error: error.message });
      throw new AppError('撤销用户所有会话失败', 500, 'SESSION_REVOCATION_FAILED');
    }
  }

  /**
   * 更新会话最后使用时间
   */
  async updateSessionLastUsed(sessionId: string): Promise<void> {
    try {
      await query(
        'UPDATE user_sessions SET last_used_at = CURRENT_TIMESTAMP WHERE id = $1',
        [sessionId]
      );
    } catch (error) {
      logger.error('更新会话最后使用时间失败:', { sessionId, error: error.message });
      // 这个错误不应该阻止令牌刷新流程
    }
  }

  /**
   * 生成邮箱验证令牌
   */
  async generateEmailVerificationToken(userId: string): Promise<string> {
    try {
      const token = uuidv4();
      const key = `email_verification:${token}`;
      const ttl = 24 * 60 * 60; // 24小时

      await setCache(key, userId, ttl);

      logger.info('邮箱验证令牌生成成功:', { userId });
      return token;
    } catch (error) {
      logger.error('生成邮箱验证令牌失败:', { userId, error: error.message });
      throw new AppError('生成邮箱验证令牌失败', 500, 'TOKEN_GENERATION_FAILED');
    }
  }

  /**
   * 验证邮箱验证令牌
   */
  async validateEmailVerificationToken(token: string): Promise<string | null> {
    try {
      const key = `email_verification:${token}`;
      const userId = await getCache(key);

      if (!userId) {
        return null;
      }

      return userId;
    } catch (error) {
      logger.error('验证邮箱验证令牌失败:', { error: error.message });
      return null;
    }
  }

  /**
   * 删除邮箱验证令牌
   */
  async deleteEmailVerificationToken(token: string): Promise<void> {
    try {
      const key = `email_verification:${token}`;
      await deleteCache(key);
    } catch (error) {
      logger.error('删除邮箱验证令牌失败:', { error: error.message });
    }
  }

  /**
   * 生成密码重置令牌
   */
  async generatePasswordResetToken(userId: string): Promise<string> {
    try {
      const token = uuidv4();
      const key = `password_reset:${token}`;
      const ttl = 60 * 60; // 1小时

      await setCache(key, userId, ttl);

      logger.info('密码重置令牌生成成功:', { userId });
      return token;
    } catch (error) {
      logger.error('生成密码重置令牌失败:', { userId, error: error.message });
      throw new AppError('生成密码重置令牌失败', 500, 'TOKEN_GENERATION_FAILED');
    }
  }

  /**
   * 验证密码重置令牌
   */
  async validatePasswordResetToken(token: string): Promise<string | null> {
    try {
      const key = `password_reset:${token}`;
      const userId = await getCache(key);

      if (!userId) {
        return null;
      }

      return userId;
    } catch (error) {
      logger.error('验证密码重置令牌失败:', { error: error.message });
      return null;
    }
  }

  /**
   * 删除密码重置令牌
   */
  async deletePasswordResetToken(token: string): Promise<void> {
    try {
      const key = `password_reset:${token}`;
      await deleteCache(key);
    } catch (error) {
      logger.error('删除密码重置令牌失败:', { error: error.message });
    }
  }

  /**
   * 获取访问令牌过期时间（秒）
   */
  getAccessTokenExpiry(): number {
    // 解析过期时间字符串
    const expiry = this.accessTokenExpiry;
    if (expiry.endsWith('m')) {
      return parseInt(expiry) * 60;
    } else if (expiry.endsWith('h')) {
      return parseInt(expiry) * 60 * 60;
    } else if (expiry.endsWith('d')) {
      return parseInt(expiry) * 24 * 60 * 60;
    } else {
      return parseInt(expiry);
    }
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      const result = await query(
        'DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP'
      );

      logger.info('清理过期会话完成:', { deletedCount: result.rowCount });
    } catch (error) {
      logger.error('清理过期会话失败:', { error: error.message });
    }
  }

  /**
   * 获取用户活跃会话
   */
  async getUserActiveSessions(userId: string): Promise<SessionData[]> {
    try {
      const result = await query(
        `SELECT * FROM user_sessions 
         WHERE user_id = $1 AND expires_at > CURRENT_TIMESTAMP
         ORDER BY last_used_at DESC`,
        [userId]
      );

      return result.rows;
    } catch (error) {
      logger.error('获取用户活跃会话失败:', { userId, error: error.message });
      throw new AppError('获取用户活跃会话失败', 500, 'DATABASE_ERROR');
    }
  }
}
