/**
 * 用户服务
 * 处理用户相关的数据库操作
 */

import { query } from '../config/database';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';

export interface User {
  id: string;
  email: string;
  passwordHash: string;
  name: string;
  avatarUrl?: string;
  role: string;
  status: string;
  emailVerified: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserData {
  email: string;
  passwordHash: string;
  name: string;
  role: string;
  status: string;
  emailVerified: boolean;
  avatarUrl?: string;
}

export interface UpdateUserData {
  name?: string;
  avatarUrl?: string;
  role?: string;
  status?: string;
}

export class UserService {
  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await query(
        'SELECT * FROM users WHERE email = $1',
        [email]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      logger.error('查找用户失败:', { email, error: error.message });
      throw new AppError('查找用户失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: string): Promise<User | null> {
    try {
      const result = await query(
        'SELECT * FROM users WHERE id = $1',
        [id]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      logger.error('查找用户失败:', { id, error: error.message });
      throw new AppError('查找用户失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 创建用户
   */
  async create(userData: CreateUserData): Promise<User> {
    try {
      const result = await query(
        `INSERT INTO users (email, password_hash, name, role, status, email_verified, avatar_url)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING *`,
        [
          userData.email,
          userData.passwordHash,
          userData.name,
          userData.role,
          userData.status,
          userData.emailVerified,
          userData.avatarUrl
        ]
      );

      const user = result.rows[0];
      logger.info('用户创建成功:', { userId: user.id, email: user.email });
      
      return user;
    } catch (error) {
      logger.error('创建用户失败:', { email: userData.email, error: error.message });
      
      if (error.code === '23505') { // 唯一约束违反
        throw new AppError('邮箱已被注册', 400, 'EMAIL_ALREADY_EXISTS');
      }
      
      throw new AppError('创建用户失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 更新用户信息
   */
  async update(id: string, userData: UpdateUserData): Promise<User> {
    try {
      const setParts: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      // 动态构建更新字段
      if (userData.name !== undefined) {
        setParts.push(`name = $${paramIndex++}`);
        values.push(userData.name);
      }
      
      if (userData.avatarUrl !== undefined) {
        setParts.push(`avatar_url = $${paramIndex++}`);
        values.push(userData.avatarUrl);
      }
      
      if (userData.role !== undefined) {
        setParts.push(`role = $${paramIndex++}`);
        values.push(userData.role);
      }
      
      if (userData.status !== undefined) {
        setParts.push(`status = $${paramIndex++}`);
        values.push(userData.status);
      }

      if (setParts.length === 0) {
        throw new AppError('没有提供更新字段', 400, 'NO_UPDATE_FIELDS');
      }

      // 添加更新时间
      setParts.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(id);

      const result = await query(
        `UPDATE users SET ${setParts.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
        values
      );

      if (result.rows.length === 0) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      const user = result.rows[0];
      logger.info('用户更新成功:', { userId: id, fields: Object.keys(userData) });
      
      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('更新用户失败:', { id, error: error.message });
      throw new AppError('更新用户失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 更新最后登录时间
   */
  async updateLastLogin(id: string): Promise<void> {
    try {
      await query(
        'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
        [id]
      );
      
      logger.debug('更新最后登录时间成功:', { userId: id });
    } catch (error) {
      logger.error('更新最后登录时间失败:', { id, error: error.message });
      // 这个错误不应该阻止登录流程
    }
  }

  /**
   * 更新密码
   */
  async updatePassword(id: string, passwordHash: string): Promise<void> {
    try {
      const result = await query(
        'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
        [passwordHash, id]
      );

      if (result.rowCount === 0) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      logger.info('密码更新成功:', { userId: id });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('更新密码失败:', { id, error: error.message });
      throw new AppError('更新密码失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(id: string): Promise<void> {
    try {
      const result = await query(
        'UPDATE users SET email_verified = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      logger.info('邮箱验证成功:', { userId: id });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('邮箱验证失败:', { id, error: error.message });
      throw new AppError('邮箱验证失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 删除用户
   */
  async delete(id: string): Promise<void> {
    try {
      const result = await query(
        'DELETE FROM users WHERE id = $1',
        [id]
      );

      if (result.rowCount === 0) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      logger.info('用户删除成功:', { userId: id });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      
      logger.error('删除用户失败:', { id, error: error.message });
      throw new AppError('删除用户失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 获取用户列表
   */
  async findMany(options: {
    page?: number;
    pageSize?: number;
    role?: string;
    status?: string;
    search?: string;
  } = {}): Promise<{ users: User[]; total: number }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        role,
        status,
        search
      } = options;

      const offset = (page - 1) * pageSize;
      const conditions: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      // 构建查询条件
      if (role) {
        conditions.push(`role = $${paramIndex++}`);
        values.push(role);
      }

      if (status) {
        conditions.push(`status = $${paramIndex++}`);
        values.push(status);
      }

      if (search) {
        conditions.push(`(name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`);
        values.push(`%${search}%`);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 查询总数
      const countResult = await query(
        `SELECT COUNT(*) FROM users ${whereClause}`,
        values
      );
      const total = parseInt(countResult.rows[0].count);

      // 查询用户列表
      values.push(pageSize, offset);
      const usersResult = await query(
        `SELECT id, email, name, avatar_url, role, status, email_verified, last_login_at, created_at, updated_at
         FROM users ${whereClause}
         ORDER BY created_at DESC
         LIMIT $${paramIndex++} OFFSET $${paramIndex}`,
        values
      );

      return {
        users: usersResult.rows,
        total
      };
    } catch (error) {
      logger.error('查询用户列表失败:', { options, error: error.message });
      throw new AppError('查询用户列表失败', 500, 'DATABASE_ERROR');
    }
  }

  /**
   * 统计用户数量
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    verified: number;
    admins: number;
  }> {
    try {
      const result = await query(`
        SELECT 
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE status = 'active') as active,
          COUNT(*) FILTER (WHERE email_verified = true) as verified,
          COUNT(*) FILTER (WHERE role = 'admin') as admins
        FROM users
      `);

      const stats = result.rows[0];
      return {
        total: parseInt(stats.total),
        active: parseInt(stats.active),
        verified: parseInt(stats.verified),
        admins: parseInt(stats.admins)
      };
    } catch (error) {
      logger.error('获取用户统计失败:', { error: error.message });
      throw new AppError('获取用户统计失败', 500, 'DATABASE_ERROR');
    }
  }
}
