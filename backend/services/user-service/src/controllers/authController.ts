/**
 * 认证控制器
 * 处理用户认证相关的业务逻辑
 */

import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { UserService } from '../services/userService';
import { TokenService } from '../services/tokenService';
import { EmailService } from '../services/emailService';
import { logger, logAuth } from '../utils/logger';
import { AppError } from '../utils/appError';
import { catchAsync } from '../utils/catchAsync';

export class AuthController {
  private userService: UserService;
  private tokenService: TokenService;
  private emailService: EmailService;

  constructor() {
    this.userService = new UserService();
    this.tokenService = new TokenService();
    this.emailService = new EmailService();
  }

  /**
   * 用户注册
   */
  register = catchAsync(async (req: Request, res: Response) => {
    const { email, password, name } = req.body;
    const ip = req.ip;

    // 检查用户是否已存在
    const existingUser = await this.userService.findByEmail(email);
    if (existingUser) {
      logAuth('register_failed', undefined, email, ip, false, new Error('用户已存在'));
      throw new AppError('该邮箱已被注册', 400, 'USER_ALREADY_EXISTS');
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = await this.userService.create({
      email,
      passwordHash,
      name,
      role: 'user',
      status: 'active',
      emailVerified: false
    });

    // 生成邮箱验证令牌
    const verificationToken = await this.tokenService.generateEmailVerificationToken(user.id);

    // 发送验证邮件
    await this.emailService.sendVerificationEmail(email, name, verificationToken);

    // 记录日志
    logAuth('register_success', user.id, email, ip);

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          emailVerified: user.emailVerified
        },
        message: '注册成功，请查看邮箱完成验证'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 用户登录
   */
  login = catchAsync(async (req: Request, res: Response) => {
    const { email, password, rememberMe = false } = req.body;
    const ip = req.ip;
    const userAgent = req.get('User-Agent');

    // 查找用户
    const user = await this.userService.findByEmail(email);
    if (!user) {
      logAuth('login_failed', undefined, email, ip, false, new Error('用户不存在'));
      throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
    }

    // 检查用户状态
    if (user.status !== 'active') {
      logAuth('login_failed', user.id, email, ip, false, new Error('用户已被禁用'));
      throw new AppError('账户已被禁用，请联系管理员', 401, 'ACCOUNT_DISABLED');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      logAuth('login_failed', user.id, email, ip, false, new Error('密码错误'));
      throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
    }

    // 生成令牌
    const accessToken = this.tokenService.generateAccessToken(user);
    const refreshToken = await this.tokenService.generateRefreshToken(user.id, {
      ip,
      userAgent,
      rememberMe
    });

    // 更新最后登录时间
    await this.userService.updateLastLogin(user.id);

    // 记录日志
    logAuth('login_success', user.id, email, ip);

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          emailVerified: user.emailVerified,
          lastLoginAt: new Date().toISOString()
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: this.tokenService.getAccessTokenExpiry()
        }
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 刷新令牌
   */
  refreshToken = catchAsync(async (req: Request, res: Response) => {
    const { refreshToken } = req.body;
    const ip = req.ip;

    // 验证刷新令牌
    const session = await this.tokenService.validateRefreshToken(refreshToken);
    if (!session) {
      throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
    }

    // 获取用户信息
    const user = await this.userService.findById(session.userId);
    if (!user || user.status !== 'active') {
      throw new AppError('用户不存在或已被禁用', 401, 'USER_NOT_FOUND');
    }

    // 生成新的访问令牌
    const newAccessToken = this.tokenService.generateAccessToken(user);

    // 更新会话最后使用时间
    await this.tokenService.updateSessionLastUsed(session.id);

    // 记录日志
    logAuth('token_refresh', user.id, user.email, ip);

    res.json({
      success: true,
      data: {
        accessToken: newAccessToken,
        expiresIn: this.tokenService.getAccessTokenExpiry()
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 用户登出
   */
  logout = catchAsync(async (req: Request, res: Response) => {
    const { refreshToken } = req.body;
    const ip = req.ip;

    // 删除会话
    await this.tokenService.revokeRefreshToken(refreshToken);

    // 记录日志
    logAuth('logout', undefined, undefined, ip);

    res.json({
      success: true,
      data: {
        message: '登出成功'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 忘记密码
   */
  forgotPassword = catchAsync(async (req: Request, res: Response) => {
    const { email } = req.body;
    const ip = req.ip;

    // 查找用户
    const user = await this.userService.findByEmail(email);
    if (!user) {
      // 为了安全，即使用户不存在也返回成功
      res.json({
        success: true,
        data: {
          message: '如果该邮箱已注册，您将收到密码重置邮件'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    // 生成密码重置令牌
    const resetToken = await this.tokenService.generatePasswordResetToken(user.id);

    // 发送重置邮件
    await this.emailService.sendPasswordResetEmail(email, user.name, resetToken);

    // 记录日志
    logAuth('password_reset_request', user.id, email, ip);

    res.json({
      success: true,
      data: {
        message: '如果该邮箱已注册，您将收到密码重置邮件'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 重置密码
   */
  resetPassword = catchAsync(async (req: Request, res: Response) => {
    const { token, password } = req.body;
    const ip = req.ip;

    // 验证重置令牌
    const userId = await this.tokenService.validatePasswordResetToken(token);
    if (!userId) {
      throw new AppError('无效或已过期的重置令牌', 400, 'INVALID_RESET_TOKEN');
    }

    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 更新密码
    await this.userService.updatePassword(userId, passwordHash);

    // 撤销所有会话
    await this.tokenService.revokeAllUserSessions(userId);

    // 删除重置令牌
    await this.tokenService.deletePasswordResetToken(token);

    // 获取用户信息
    const user = await this.userService.findById(userId);

    // 记录日志
    logAuth('password_reset_success', userId, user?.email, ip);

    res.json({
      success: true,
      data: {
        message: '密码重置成功，请重新登录'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 修改密码
   */
  changePassword = catchAsync(async (req: Request, res: Response) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;
    const ip = req.ip;

    // 获取用户信息
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      logAuth('password_change_failed', userId, user.email, ip, false, new Error('当前密码错误'));
      throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
    }

    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await this.userService.updatePassword(userId, passwordHash);

    // 记录日志
    logAuth('password_change_success', userId, user.email, ip);

    res.json({
      success: true,
      data: {
        message: '密码修改成功'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 验证邮箱
   */
  verifyEmail = catchAsync(async (req: Request, res: Response) => {
    const { token } = req.body;
    const ip = req.ip;

    // 验证邮箱验证令牌
    const userId = await this.tokenService.validateEmailVerificationToken(token);
    if (!userId) {
      throw new AppError('无效或已过期的验证令牌', 400, 'INVALID_VERIFICATION_TOKEN');
    }

    // 更新邮箱验证状态
    await this.userService.verifyEmail(userId);

    // 删除验证令牌
    await this.tokenService.deleteEmailVerificationToken(token);

    // 获取用户信息
    const user = await this.userService.findById(userId);

    // 记录日志
    logAuth('email_verification_success', userId, user?.email, ip);

    res.json({
      success: true,
      data: {
        message: '邮箱验证成功'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 重新发送验证邮件
   */
  resendVerification = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user.id;
    const ip = req.ip;

    // 获取用户信息
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 检查是否已验证
    if (user.emailVerified) {
      throw new AppError('邮箱已验证', 400, 'EMAIL_ALREADY_VERIFIED');
    }

    // 生成新的验证令牌
    const verificationToken = await this.tokenService.generateEmailVerificationToken(userId);

    // 发送验证邮件
    await this.emailService.sendVerificationEmail(user.email, user.name, verificationToken);

    // 记录日志
    logAuth('verification_resend', userId, user.email, ip);

    res.json({
      success: true,
      data: {
        message: '验证邮件已重新发送'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 获取当前用户信息
   */
  getCurrentUser = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user.id;

    // 获取用户信息
    const user = await this.userService.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          status: user.status,
          emailVerified: user.emailVerified,
          avatarUrl: user.avatarUrl,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        }
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  });

  /**
   * 验证令牌
   */
  verifyToken = catchAsync(async (req: Request, res: Response) => {
    const { token } = req.body;

    try {
      // 验证JWT令牌
      const decoded = this.tokenService.verifyAccessToken(token);
      
      // 获取用户信息
      const user = await this.userService.findById(decoded.userId);
      if (!user || user.status !== 'active') {
        throw new AppError('用户不存在或已被禁用', 401, 'USER_NOT_FOUND');
      }

      res.json({
        success: true,
        data: {
          valid: true,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          }
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    } catch (error) {
      res.json({
        success: true,
        data: {
          valid: false,
          reason: error.message
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  });
}
