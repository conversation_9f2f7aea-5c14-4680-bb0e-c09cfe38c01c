/**
 * 认证功能测试
 * 测试用户注册、登录、令牌验证等功能
 */

import request from 'supertest';
import express from 'express';
import { AuthController } from '../controllers/authController';
import { UserService } from '../services/userService';
import { TokenService } from '../services/tokenService';
import { EmailService } from '../services/emailService';

// Mock 依赖服务
jest.mock('../services/userService');
jest.mock('../services/tokenService');
jest.mock('../services/emailService');
jest.mock('../config/database');
jest.mock('../config/redis');

const MockedUserService = UserService as jest.MockedClass<typeof UserService>;
const MockedTokenService = TokenService as jest.MockedClass<typeof TokenService>;
const MockedEmailService = EmailService as jest.MockedClass<typeof EmailService>;

describe('认证控制器测试', () => {
  let app: express.Application;
  let authController: AuthController;
  let mockUserService: jest.Mocked<UserService>;
  let mockTokenService: jest.Mocked<TokenService>;
  let mockEmailService: jest.Mocked<EmailService>;

  beforeEach(() => {
    // 创建Express应用
    app = express();
    app.use(express.json());

    // 创建Mock实例
    mockUserService = new MockedUserService() as jest.Mocked<UserService>;
    mockTokenService = new MockedTokenService() as jest.Mocked<TokenService>;
    mockEmailService = new MockedEmailService() as jest.Mocked<EmailService>;

    // 创建控制器实例
    authController = new AuthController();

    // 设置路由
    app.post('/register', authController.register);
    app.post('/login', authController.login);
    app.post('/refresh', authController.refreshToken);
    app.post('/logout', authController.logout);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('用户注册', () => {
    const validRegisterData = {
      email: '<EMAIL>',
      password: 'Password123',
      confirmPassword: 'Password123',
      name: '测试用户'
    };

    it('应该成功注册新用户', async () => {
      // 模拟用户不存在
      mockUserService.findByEmail.mockResolvedValue(null);
      
      // 模拟创建用户成功
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: '测试用户',
        role: 'user',
        emailVerified: false
      };
      mockUserService.create.mockResolvedValue(mockUser as any);

      // 模拟生成验证令牌
      mockTokenService.generateEmailVerificationToken.mockResolvedValue('verification-token');

      // 模拟发送邮件成功
      mockEmailService.sendVerificationEmail.mockResolvedValue();

      const response = await request(app)
        .post('/register')
        .send(validRegisterData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.message).toContain('注册成功');
      
      // 验证服务调用
      expect(mockUserService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUserService.create).toHaveBeenCalled();
      expect(mockTokenService.generateEmailVerificationToken).toHaveBeenCalledWith('user-123');
      expect(mockEmailService.sendVerificationEmail).toHaveBeenCalled();
    });

    it('应该拒绝已存在的邮箱', async () => {
      // 模拟用户已存在
      const existingUser = {
        id: 'existing-user',
        email: '<EMAIL>'
      };
      mockUserService.findByEmail.mockResolvedValue(existingUser as any);

      const response = await request(app)
        .post('/register')
        .send(validRegisterData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('USER_ALREADY_EXISTS');
      expect(response.body.error.message).toContain('该邮箱已被注册');
    });

    it('应该验证密码强度', async () => {
      const weakPasswordData = {
        ...validRegisterData,
        password: '123',
        confirmPassword: '123'
      };

      const response = await request(app)
        .post('/register')
        .send(weakPasswordData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('应该验证邮箱格式', async () => {
      const invalidEmailData = {
        ...validRegisterData,
        email: 'invalid-email'
      };

      const response = await request(app)
        .post('/register')
        .send(invalidEmailData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('用户登录', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'Password123'
    };

    it('应该成功登录有效用户', async () => {
      // 模拟找到用户
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        name: '测试用户',
        role: 'user',
        status: 'active',
        emailVerified: true
      };
      mockUserService.findByEmail.mockResolvedValue(mockUser as any);

      // 模拟密码验证成功（通过bcrypt.compare）
      const bcrypt = require('bcryptjs');
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true);

      // 模拟生成令牌
      mockTokenService.generateAccessToken.mockReturnValue('access-token');
      mockTokenService.generateRefreshToken.mockResolvedValue('refresh-token');
      mockTokenService.getAccessTokenExpiry.mockReturnValue('15m');

      // 模拟更新最后登录时间
      mockUserService.updateLastLogin.mockResolvedValue();

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.tokens.accessToken).toBe('access-token');
      expect(response.body.data.tokens.refreshToken).toBe('refresh-token');
    });

    it('应该拒绝不存在的用户', async () => {
      mockUserService.findByEmail.mockResolvedValue(null);

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });

    it('应该拒绝错误的密码', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        status: 'active'
      };
      mockUserService.findByEmail.mockResolvedValue(mockUser as any);

      // 模拟密码验证失败
      const bcrypt = require('bcryptjs');
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false);

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });

    it('应该拒绝被禁用的用户', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'hashed-password',
        status: 'inactive'
      };
      mockUserService.findByEmail.mockResolvedValue(mockUser as any);

      const response = await request(app)
        .post('/login')
        .send(validLoginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('ACCOUNT_DISABLED');
    });
  });

  describe('令牌刷新', () => {
    it('应该成功刷新有效的令牌', async () => {
      const refreshTokenData = {
        refreshToken: 'valid-refresh-token'
      };

      // 模拟验证刷新令牌成功
      const mockSession = {
        id: 'session-123',
        userId: 'user-123'
      };
      mockTokenService.validateRefreshToken.mockResolvedValue(mockSession as any);

      // 模拟找到用户
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user',
        status: 'active'
      };
      mockUserService.findById.mockResolvedValue(mockUser as any);

      // 模拟生成新的访问令牌
      mockTokenService.generateAccessToken.mockReturnValue('new-access-token');
      mockTokenService.getAccessTokenExpiry.mockReturnValue('15m');
      mockTokenService.updateSessionLastUsed.mockResolvedValue();

      const response = await request(app)
        .post('/refresh')
        .send(refreshTokenData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.accessToken).toBe('new-access-token');
    });

    it('应该拒绝无效的刷新令牌', async () => {
      const refreshTokenData = {
        refreshToken: 'invalid-refresh-token'
      };

      mockTokenService.validateRefreshToken.mockResolvedValue(null);

      const response = await request(app)
        .post('/refresh')
        .send(refreshTokenData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_REFRESH_TOKEN');
    });
  });

  describe('用户登出', () => {
    it('应该成功登出用户', async () => {
      const logoutData = {
        refreshToken: 'valid-refresh-token'
      };

      mockTokenService.revokeRefreshToken.mockResolvedValue();

      const response = await request(app)
        .post('/logout')
        .send(logoutData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toContain('登出成功');
      expect(mockTokenService.revokeRefreshToken).toHaveBeenCalledWith('valid-refresh-token');
    });
  });
});
