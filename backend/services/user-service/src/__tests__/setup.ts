/**
 * Jest 测试设置文件
 * 配置测试环境和全局设置
 */

import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test_user:test_password@localhost:5432/test_db';
process.env.REDIS_URL = 'redis://localhost:6379/1';

// 模拟日志输出（避免测试时输出过多日志）
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  },
  logAuth: jest.fn()
}));

// 模拟数据库连接
jest.mock('../config/database', () => ({
  connectDatabase: jest.fn(),
  query: jest.fn(),
  getPool: jest.fn(),
  beginTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn(),
  closeDatabase: jest.fn()
}));

// 模拟Redis连接
jest.mock('../config/redis', () => ({
  connectRedis: jest.fn(),
  getCache: jest.fn(),
  setCache: jest.fn(),
  deleteCache: jest.fn(),
  closeRedis: jest.fn()
}));

// 全局测试超时设置
jest.setTimeout(10000);

// 测试前的全局设置
beforeAll(async () => {
  // 这里可以添加全局的测试前设置
});

// 测试后的全局清理
afterAll(async () => {
  // 这里可以添加全局的测试后清理
});

// 每个测试前的设置
beforeEach(() => {
  // 清除所有模拟调用
  jest.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 这里可以添加每个测试后的清理逻辑
});
