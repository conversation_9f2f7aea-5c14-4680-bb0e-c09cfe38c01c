/**
 * 全局错误处理中间件
 * 统一处理应用中的所有错误
 */

import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/appError';
import { logger } from '../utils/logger';

/**
 * 全局错误处理中间件
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，则交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 默认错误信息
  let statusCode = 500;
  let code = 'INTERNAL_SERVER_ERROR';
  let message = '服务器内部错误';

  // 处理应用错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    code = error.code;
    message = error.message;
  }
  // 处理数据库错误
  else if (error.name === 'SequelizeValidationError') {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = '数据验证失败';
  }
  // 处理JWT错误
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = 'INVALID_TOKEN';
    message = '无效的令牌';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    code = 'TOKEN_EXPIRED';
    message = '令牌已过期';
  }
  // 处理语法错误
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = 400;
    code = 'INVALID_JSON';
    message = '无效的JSON格式';
  }

  // 记录错误日志
  if (statusCode >= 500) {
    logger.error('服务器错误:', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    });
  } else {
    logger.warn('客户端错误:', {
      error: error.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userId: req.user?.id
    });
  }

  // 构建错误响应
  const errorResponse: any = {
    success: false,
    error: {
      code,
      message
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown'
  };

  // 开发环境下包含错误堆栈
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
    errorResponse.error.details = error;
  }

  res.status(statusCode).json(errorResponse);
}

/**
 * 404错误处理
 */
export function notFoundHandler(req: Request, res: Response): void {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在'
    },
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown'
  });
}

/**
 * 未捕获异常处理
 */
export function setupUncaughtExceptionHandlers(): void {
  process.on('uncaughtException', (error: Error) => {
    logger.error('未捕获的异常:', {
      error: error.message,
      stack: error.stack
    });
    
    // 优雅关闭
    process.exit(1);
  });

  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('未处理的Promise拒绝:', {
      reason: reason?.message || reason,
      stack: reason?.stack
    });
    
    // 优雅关闭
    process.exit(1);
  });
}
