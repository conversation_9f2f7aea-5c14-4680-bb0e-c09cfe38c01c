/**
 * 认证中间件
 * 验证JWT令牌并提取用户信息
 */

import { Request, Response, NextFunction } from 'express';
import { TokenService } from '../services/tokenService';
import { UserService } from '../services/userService';
import { logger } from '../utils/logger';
import { AppError } from '../utils/appError';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user: {
        id: string;
        email: string;
        role: string;
      };
    }
  }
}

const tokenService = new TokenService();
const userService = new UserService();

/**
 * 认证中间件
 * 验证Authorization头中的Bearer令牌
 */
export async function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // 获取Authorization头
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('缺少认证令牌', 401, 'MISSING_TOKEN');
    }

    // 提取令牌
    const token = authHeader.substring(7);
    if (!token) {
      throw new AppError('无效的令牌格式', 401, 'INVALID_TOKEN_FORMAT');
    }

    // 验证令牌
    const decoded = tokenService.verifyAccessToken(token);

    // 验证用户是否存在且状态正常
    const user = await userService.findById(decoded.userId);
    if (!user) {
      throw new AppError('用户不存在', 401, 'USER_NOT_FOUND');
    }

    if (user.status !== 'active') {
      throw new AppError('账户已被禁用', 401, 'ACCOUNT_DISABLED');
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role
    };

    logger.debug('用户认证成功:', {
      userId: user.id,
      email: user.email,
      role: user.role,
      path: req.path
    });

    next();
  } catch (error) {
    logger.warn('认证失败:', {
      error: error.message,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        error: {
          code: error.code,
          message: error.message
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    } else {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: '认证失败'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
    }
  }
}

/**
 * 可选认证中间件
 * 如果提供了令牌则验证，否则继续执行
 */
export async function optionalAuthMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有提供令牌，继续执行
      next();
      return;
    }

    const token = authHeader.substring(7);
    if (!token) {
      next();
      return;
    }

    // 验证令牌
    const decoded = tokenService.verifyAccessToken(token);
    const user = await userService.findById(decoded.userId);

    if (user && user.status === 'active') {
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role
      };
    }

    next();
  } catch (error) {
    // 可选认证失败时不阻止请求
    logger.debug('可选认证失败:', { error: error.message });
    next();
  }
}

/**
 * 角色权限中间件
 * 检查用户是否具有指定角色
 */
export function requireRole(...roles: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要认证'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    if (!roles.includes(req.user.role)) {
      logger.warn('权限不足:', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        path: req.path
      });

      res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    next();
  };
}

/**
 * 管理员权限中间件
 */
export const requireAdmin = requireRole('admin');

/**
 * 用户或管理员权限中间件
 */
export const requireUserOrAdmin = requireRole('user', 'admin');

/**
 * 邮箱验证中间件
 * 检查用户邮箱是否已验证
 */
export async function requireEmailVerified(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要认证'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    const user = await userService.findById(req.user.id);
    if (!user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: '用户不存在'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    if (!user.emailVerified) {
      res.status(403).json({
        success: false,
        error: {
          code: 'EMAIL_NOT_VERIFIED',
          message: '邮箱未验证，请先验证邮箱'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('邮箱验证检查失败:', { error: error.message });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: '服务器内部错误'
      },
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id']
    });
  }
}

/**
 * 资源所有者权限中间件
 * 检查用户是否为资源所有者或管理员
 */
export function requireOwnerOrAdmin(userIdParam: string = 'userId') {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要认证'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    const resourceUserId = req.params[userIdParam];
    
    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      next();
      return;
    }

    // 用户只能访问自己的资源
    if (req.user.id !== resourceUserId) {
      logger.warn('资源访问被拒绝:', {
        userId: req.user.id,
        resourceUserId,
        path: req.path
      });

      res.status(403).json({
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '访问被拒绝'
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id']
      });
      return;
    }

    next();
  };
}
