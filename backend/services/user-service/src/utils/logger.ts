/**
 * 日志工具配置
 * 使用Winston进行结构化日志记录
 */

import winston from 'winston';

// 日志级别配置
const logLevel = process.env.LOG_LEVEL || 'info';

// 日志格式配置
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service = 'user-service', ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      service,
      message,
      ...meta
    };
    return JSON.stringify(logEntry);
  })
);

// 控制台格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, service = 'user-service', ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
  })
);

// 创建logger实例
export const logger = winston.createLogger({
  level: logLevel,
  format: logFormat,
  defaultMeta: { service: 'user-service' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// 生产环境可以添加其他传输方式
if (process.env.NODE_ENV === 'production') {
  // 可以添加如ELK、Datadog等日志服务
  // logger.add(new winston.transports.Http({
  //   host: 'localhost',
  //   port: 3000,
  //   path: '/logs'
  // }));
}

/**
 * 创建子logger
 */
export function createChildLogger(module: string) {
  return logger.child({ module });
}

/**
 * 记录HTTP请求日志
 */
export function logRequest(req: any, res: any, responseTime: number) {
  logger.info('HTTP请求', {
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id
  });
}

/**
 * 记录数据库操作日志
 */
export function logDatabase(operation: string, table: string, duration: number, error?: any) {
  if (error) {
    logger.error('数据库操作失败', {
      operation,
      table,
      duration: `${duration}ms`,
      error: error.message
    });
  } else {
    logger.debug('数据库操作成功', {
      operation,
      table,
      duration: `${duration}ms`
    });
  }
}

/**
 * 记录缓存操作日志
 */
export function logCache(operation: string, key: string, hit?: boolean, error?: any) {
  if (error) {
    logger.error('缓存操作失败', {
      operation,
      key,
      error: error.message
    });
  } else {
    logger.debug('缓存操作', {
      operation,
      key,
      hit
    });
  }
}

/**
 * 记录认证日志
 */
export function logAuth(action: string, userId?: string, email?: string, ip?: string, success: boolean = true, error?: any) {
  const logData = {
    action,
    userId,
    email,
    ip,
    success
  };

  if (error) {
    logger.warn('认证操作失败', { ...logData, error: error.message });
  } else {
    logger.info('认证操作', logData);
  }
}

/**
 * 记录安全事件日志
 */
export function logSecurity(event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium') {
  logger.warn('安全事件', {
    event,
    severity,
    ...details
  });
}

export default logger;
