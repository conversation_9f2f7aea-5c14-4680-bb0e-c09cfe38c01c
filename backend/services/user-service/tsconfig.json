{
  "compilerOptions": {
    // 目标JavaScript版本
    "target": "ES2020",
    
    // 模块系统
    "module": "commonjs",
    
    // 模块解析策略
    "moduleResolution": "node",
    
    // 库文件
    "lib": ["ES2020"],
    
    // 输出目录
    "outDir": "./dist",
    
    // 根目录
    "rootDir": "./src",
    
    // 允许导入JSON文件
    "resolveJsonModule": true,
    
    // 生成声明文件
    "declaration": true,
    
    // 声明文件输出目录
    "declarationDir": "./dist/types",
    
    // 生成源映射
    "sourceMap": true,
    
    // 严格模式
    "strict": true,
    
    // 严格的null检查
    "strictNullChecks": true,
    
    // 严格的函数类型检查
    "strictFunctionTypes": true,
    
    // 严格的属性初始化检查
    "strictPropertyInitialization": true,
    
    // 隐式any类型检查
    "noImplicitAny": true,
    
    // 隐式返回检查
    "noImplicitReturns": true,
    
    // 未使用的局部变量检查
    "noUnusedLocals": true,
    
    // 未使用的参数检查
    "noUnusedParameters": true,
    
    // 允许合成默认导入
    "allowSyntheticDefaultImports": true,
    
    // ES模块互操作
    "esModuleInterop": true,
    
    // 跳过库检查
    "skipLibCheck": true,
    
    // 强制一致的大小写
    "forceConsistentCasingInFileNames": true,
    
    // 实验性装饰器
    "experimentalDecorators": true,
    
    // 装饰器元数据
    "emitDecoratorMetadata": true,
    
    // 路径映射
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/config/*": ["config/*"],
      "@/controllers/*": ["controllers/*"],
      "@/middleware/*": ["middleware/*"],
      "@/routes/*": ["routes/*"],
      "@/services/*": ["services/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"]
    }
  },
  
  // 包含的文件
  "include": [
    "src/**/*"
  ],
  
  // 排除的文件
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "**/*.test.ts",
    "**/__tests__/**"
  ],
  
  // TypeScript编译选项
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  }
}
