/**
 * 配置中心服务主应用
 * 提供配置管理、配置分发、热更新等功能
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { config } from './config';
import { logger, createRequestLogger } from './utils/logger';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { metricsMiddleware, prometheusMetrics } from './middleware/metrics';
import { ConfigManager } from './lib/ConfigManager';
import { healthRouter } from './routes/health';
import { configRouter } from './routes/config';
import { namespaceRouter } from './routes/namespace';
import { historyRouter } from './routes/history';
import { adminRouter } from './routes/admin';
import { webhookRouter } from './routes/webhook';

const app = express();

// 初始化配置管理器
const configManager = new ConfigManager(config.backend);

// 基础中间件
app.use(helmet());
app.use(cors({
  origin: config.cors.origins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

app.use(compression());

// 请求日志
app.use(createRequestLogger('config-service'));

// 指标收集
app.use(metricsMiddleware);

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// JSON解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 将配置管理器添加到请求对象
app.use((req, res, next) => {
  req.configManager = configManager;
  next();
});

// 健康检查路由（无需认证）
app.use('/health', healthRouter);

// Prometheus指标路由（无需认证）
app.get('/metrics', prometheusMetrics);

// Webhook路由（无需认证，但需要验证签名）
app.use('/webhook', webhookRouter);

// API路由（需要认证）
app.use('/api/v1/config', authMiddleware, configRouter);
app.use('/api/v1/namespace', authMiddleware, namespaceRouter);
app.use('/api/v1/history', authMiddleware, historyRouter);
app.use('/api/v1/admin', authMiddleware, adminRouter);

// 根路径
app.get('/', (req, res) => {
  res.json({
    service: 'RAG Config Service',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    backend: config.backend.type,
    endpoints: {
      health: '/health',
      metrics: '/metrics',
      config: '/api/v1/config',
      namespace: '/api/v1/namespace',
      history: '/api/v1/history',
      admin: '/api/v1/admin',
      webhook: '/webhook',
    }
  });
});

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 启动配置管理器
async function initializeConfigManager() {
  try {
    await configManager.initialize();
    logger.info('配置管理器初始化成功');
    
    // 启动配置监听
    configManager.startWatching();
    logger.info('配置监听已启动');
    
  } catch (error) {
    logger.error('配置管理器初始化失败:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  try {
    await configManager.close();
    logger.info('配置管理器已关闭');
  } catch (error) {
    logger.error('关闭配置管理器时出错:', error);
  }
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  
  try {
    await configManager.close();
    logger.info('配置管理器已关闭');
  } catch (error) {
    logger.error('关闭配置管理器时出错:', error);
  }
  
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', { reason, promise });
  process.exit(1);
});

// 初始化配置管理器
initializeConfigManager();

export default app;
