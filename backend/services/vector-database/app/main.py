"""
向量数据库服务主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import List, Dict, Any
from contextlib import asynccontextmanager

from app.core.vector_db_manager import VectorDatabaseManager
from app.core.performance_monitor import performance_monitor
from app.models.vector_db import SearchRequest, InsertRequest, SearchResponse
from app.utils.logger import get_logger
import time

logger = get_logger(__name__)

# 全局服务实例
vector_db_manager: VectorDatabaseManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global vector_db_manager
    
    logger.info("🚀 启动向量数据库服务...")
    
    try:
        vector_db_manager = VectorDatabaseManager()
        await vector_db_manager.initialize()

        # 启动性能监控
        performance_monitor.start_monitoring()
        logger.info("✅ 向量数据库服务初始化完成")

        yield

    except Exception as e:
        logger.error(f"❌ 向量数据库服务初始化失败: {e}")
        raise
    finally:
        logger.info("🔄 关闭向量数据库服务...")

        # 停止性能监控
        performance_monitor.stop_monitoring()

        if vector_db_manager:
            await vector_db_manager.cleanup()
        logger.info("✅ 向量数据库服务已关闭")


app = FastAPI(
    title="RAG向量数据库服务",
    description="提供高性能的向量存储和检索功能",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    return {"message": "RAG向量数据库服务运行正常", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    try:
        health_status = await vector_db_manager.health_check()
        return {"status": "healthy", "details": health_status}
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


@app.post("/api/v1/collections/{collection_name}/insert")
async def insert_vectors(collection_name: str, request: InsertRequest):
    """插入向量"""
    try:
        result = await vector_db_manager.insert_vectors(
            collection_name=collection_name,
            vectors=request.vectors,
            documents=request.documents,
            metadatas=request.metadatas,
            ids=request.ids
        )
        return result
    except Exception as e:
        logger.error(f"向量插入失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/collections/{collection_name}/search", response_model=SearchResponse)
async def search_vectors(collection_name: str, request: SearchRequest):
    """搜索相似向量"""
    try:
        result = await vector_db_manager.search_similar(
            collection_name=collection_name,
            query_vector=request.query_vector,
            top_k=request.top_k,
            where=request.where
        )
        return result
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/collections/{collection_name}")
async def create_collection(collection_name: str, dimension: int):
    """创建集合"""
    try:
        result = await vector_db_manager.create_collection(collection_name, dimension)
        return result
    except Exception as e:
        logger.error(f"集合创建失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/performance/metrics")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        metrics = performance_monitor.get_current_metrics()
        return {"status": "success", "data": metrics}
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/performance/health")
async def get_health_status():
    """获取系统健康状态"""
    try:
        health = performance_monitor.get_health_status()
        return {"status": "success", "data": health}
    except Exception as e:
        logger.error(f"获取健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/performance/report")
async def get_performance_report():
    """获取性能报告"""
    try:
        report = performance_monitor.get_performance_report()
        return {"status": "success", "data": report}
    except Exception as e:
        logger.error(f"获取性能报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8005, reload=True)
