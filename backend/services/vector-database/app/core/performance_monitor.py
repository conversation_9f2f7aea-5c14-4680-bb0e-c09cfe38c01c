"""
向量数据库性能监控器
提供实时性能监控、指标收集和自动优化建议
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
import json

from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    query_count: int
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    cache_hit_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int
    error_rate: float
    throughput_qps: float


@dataclass
class SystemHealth:
    """系统健康状态"""
    status: str  # healthy, warning, critical
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_connections: int
    error_count: int
    uptime_seconds: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collection_interval: int = 60):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 指标收集间隔（秒）
        """
        self.collection_interval = collection_interval
        self.metrics_history: deque = deque(maxlen=1440)  # 保留24小时数据
        self.response_times: deque = deque(maxlen=10000)  # 保留最近10000次请求
        self.error_count = 0
        self.total_requests = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.start_time = time.time()
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 性能阈值配置
        self.thresholds = {
            'response_time_warning': 1.0,  # 1秒
            'response_time_critical': 3.0,  # 3秒
            'cpu_warning': 70.0,  # 70%
            'cpu_critical': 90.0,  # 90%
            'memory_warning': 80.0,  # 80%
            'memory_critical': 95.0,  # 95%
            'error_rate_warning': 0.05,  # 5%
            'error_rate_critical': 0.10,  # 10%
        }
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.is_monitoring:
            logger.warning("性能监控已在运行中")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("性能监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 检查健康状态
                health = self._assess_health(metrics)
                if health.status != 'healthy':
                    logger.warning(f"系统健康状态: {health.status}")
                
                time.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        now = datetime.now()
        
        # 计算响应时间统计
        response_times_list = list(self.response_times)
        avg_response_time = sum(response_times_list) / len(response_times_list) if response_times_list else 0
        p95_response_time = self._calculate_percentile(response_times_list, 0.95)
        p99_response_time = self._calculate_percentile(response_times_list, 0.99)
        
        # 计算缓存命中率
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / total_cache_requests if total_cache_requests > 0 else 0
        
        # 计算错误率
        error_rate = self.error_count / self.total_requests if self.total_requests > 0 else 0
        
        # 计算QPS
        uptime = time.time() - self.start_time
        throughput_qps = self.total_requests / uptime if uptime > 0 else 0
        
        # 系统资源使用情况
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        return PerformanceMetrics(
            timestamp=now,
            query_count=self.total_requests,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            cache_hit_rate=cache_hit_rate,
            memory_usage_mb=memory_info.used / 1024 / 1024,
            cpu_usage_percent=cpu_percent,
            active_connections=0,  # 需要从连接池获取
            error_rate=error_rate,
            throughput_qps=throughput_qps
        )
    
    def _calculate_percentile(self, values: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile)
        if index >= len(sorted_values):
            index = len(sorted_values) - 1
        return sorted_values[index]
    
    def _assess_health(self, metrics: PerformanceMetrics) -> SystemHealth:
        """评估系统健康状态"""
        status = 'healthy'
        
        # 检查各项指标
        if (metrics.avg_response_time > self.thresholds['response_time_critical'] or
            metrics.cpu_usage_percent > self.thresholds['cpu_critical'] or
            metrics.memory_usage_mb / 1024 > self.thresholds['memory_critical'] or
            metrics.error_rate > self.thresholds['error_rate_critical']):
            status = 'critical'
        elif (metrics.avg_response_time > self.thresholds['response_time_warning'] or
              metrics.cpu_usage_percent > self.thresholds['cpu_warning'] or
              metrics.memory_usage_mb / 1024 > self.thresholds['memory_warning'] or
              metrics.error_rate > self.thresholds['error_rate_warning']):
            status = 'warning'
        
        # 获取系统信息
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        net_io = psutil.net_io_counters()
        
        return SystemHealth(
            status=status,
            cpu_usage=metrics.cpu_usage_percent,
            memory_usage=memory_info.percent,
            disk_usage=disk_info.percent,
            network_io={
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv
            },
            active_connections=metrics.active_connections,
            error_count=self.error_count,
            uptime_seconds=time.time() - self.start_time
        )
    
    def record_query(self, response_time: float, cache_hit: bool = False, error: bool = False):
        """记录查询性能"""
        self.response_times.append(response_time)
        self.total_requests += 1
        
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
        
        if error:
            self.error_count += 1
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if not self.metrics_history:
            return self._collect_metrics()
        return self.metrics_history[-1]
    
    def get_metrics_history(self, hours: int = 24) -> List[PerformanceMetrics]:
        """获取历史性能指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_health_status(self) -> SystemHealth:
        """获取系统健康状态"""
        current_metrics = self.get_current_metrics()
        return self._assess_health(current_metrics)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        current_metrics = self.get_current_metrics()
        health = self.get_health_status()
        
        # 计算趋势
        recent_metrics = self.get_metrics_history(hours=1)
        if len(recent_metrics) >= 2:
            response_time_trend = recent_metrics[-1].avg_response_time - recent_metrics[0].avg_response_time
            throughput_trend = recent_metrics[-1].throughput_qps - recent_metrics[0].throughput_qps
        else:
            response_time_trend = 0
            throughput_trend = 0
        
        return {
            'current_metrics': asdict(current_metrics) if current_metrics else {},
            'health_status': asdict(health),
            'trends': {
                'response_time_trend': response_time_trend,
                'throughput_trend': throughput_trend
            },
            'recommendations': self._generate_recommendations(current_metrics, health)
        }
    
    def _generate_recommendations(self, metrics: PerformanceMetrics, health: SystemHealth) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if metrics.avg_response_time > self.thresholds['response_time_warning']:
            recommendations.append("响应时间较高，建议优化查询或增加缓存")
        
        if metrics.cache_hit_rate < 0.7:
            recommendations.append("缓存命中率较低，建议调整缓存策略或增加缓存容量")
        
        if health.cpu_usage > self.thresholds['cpu_warning']:
            recommendations.append("CPU使用率较高，建议优化算法或增加计算资源")
        
        if health.memory_usage > self.thresholds['memory_warning']:
            recommendations.append("内存使用率较高，建议优化内存使用或增加内存容量")
        
        if metrics.error_rate > self.thresholds['error_rate_warning']:
            recommendations.append("错误率较高，建议检查系统日志和错误处理")
        
        if metrics.throughput_qps < 10:
            recommendations.append("吞吐量较低，建议检查系统瓶颈和优化配置")
        
        return recommendations
    
    def export_metrics(self, format: str = 'json') -> str:
        """导出性能指标"""
        metrics_data = [asdict(m) for m in self.metrics_history]
        
        if format == 'json':
            return json.dumps(metrics_data, default=str, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()
