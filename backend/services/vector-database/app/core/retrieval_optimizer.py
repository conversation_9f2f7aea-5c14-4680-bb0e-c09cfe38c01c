"""
向量数据库高性能检索优化器
"""

import asyncio
import time
import numpy as np
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass
import heapq
import json

from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class QueryProfile:
    """查询性能档案"""
    query_hash: str
    collection_name: str
    vector_dimension: int
    top_k: int
    avg_response_time: float
    query_count: int
    last_used: datetime
    cache_hit_rate: float


@dataclass
class IndexStats:
    """索引统计信息"""
    collection_name: str
    total_vectors: int
    index_size_mb: float
    avg_query_time: float
    queries_per_second: float
    cache_hit_rate: float
    last_optimized: datetime


class QueryCache:
    """智能查询缓存"""
    
    def __init__(self, max_size: int = 10000, ttl_seconds: int = 3600):
        self.cache: Dict[str, Tuple[Any, datetime]] = {}
        self.access_times: Dict[str, datetime] = {}
        self.max_size = max_size
        self.ttl = timedelta(seconds=ttl_seconds)
        self.hit_count = 0
        self.miss_count = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        if key in self.cache:
            value, timestamp = self.cache[key]
            if datetime.utcnow() - timestamp < self.ttl:
                self.access_times[key] = datetime.utcnow()
                self.hit_count += 1
                return value
            else:
                # 缓存过期
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
        
        self.miss_count += 1
        return None
    
    def put(self, key: str, value: Any):
        """存储缓存项"""
        # 检查缓存大小限制
        if len(self.cache) >= self.max_size:
            self._evict_lru()
        
        self.cache[key] = (value, datetime.utcnow())
        self.access_times[key] = datetime.utcnow()
    
    def _evict_lru(self):
        """驱逐最近最少使用的项"""
        if not self.access_times:
            return
        
        # 找到最旧的访问时间
        oldest_key = min(self.access_times.keys(), 
                        key=lambda k: self.access_times[k])
        
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
    
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.hit_count + self.miss_count
        return self.hit_count / total if total > 0 else 0.0
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
        self.hit_count = 0
        self.miss_count = 0


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 1000):
        self.query_times = deque(maxlen=window_size)
        self.query_profiles: Dict[str, QueryProfile] = {}
        self.collection_stats: Dict[str, IndexStats] = {}
        self.slow_queries = deque(maxlen=100)  # 慢查询记录
        self.slow_query_threshold = 1.0  # 1秒
    
    def record_query(self, collection_name: str, query_vector: List[float], 
                    top_k: int, response_time: float, cache_hit: bool):
        """记录查询性能"""
        self.query_times.append(response_time)
        
        # 生成查询哈希
        query_hash = self._hash_query(query_vector, top_k)
        
        # 更新查询档案
        if query_hash in self.query_profiles:
            profile = self.query_profiles[query_hash]
            profile.avg_response_time = (profile.avg_response_time * profile.query_count + response_time) / (profile.query_count + 1)
            profile.query_count += 1
            profile.last_used = datetime.utcnow()
            profile.cache_hit_rate = (profile.cache_hit_rate * (profile.query_count - 1) + (1 if cache_hit else 0)) / profile.query_count
        else:
            self.query_profiles[query_hash] = QueryProfile(
                query_hash=query_hash,
                collection_name=collection_name,
                vector_dimension=len(query_vector),
                top_k=top_k,
                avg_response_time=response_time,
                query_count=1,
                last_used=datetime.utcnow(),
                cache_hit_rate=1.0 if cache_hit else 0.0
            )
        
        # 记录慢查询
        if response_time > self.slow_query_threshold:
            self.slow_queries.append({
                "collection": collection_name,
                "response_time": response_time,
                "top_k": top_k,
                "timestamp": datetime.utcnow(),
                "query_hash": query_hash
            })
    
    def _hash_query(self, query_vector: List[float], top_k: int) -> str:
        """生成查询哈希"""
        # 使用向量的前几个维度和top_k生成哈希
        vector_sample = query_vector[:min(10, len(query_vector))]
        query_str = f"{vector_sample}_{top_k}"
        return str(hash(query_str))
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.query_times:
            return {"error": "No query data available"}
        
        query_times_list = list(self.query_times)
        
        return {
            "avg_response_time": sum(query_times_list) / len(query_times_list),
            "min_response_time": min(query_times_list),
            "max_response_time": max(query_times_list),
            "p95_response_time": np.percentile(query_times_list, 95),
            "p99_response_time": np.percentile(query_times_list, 99),
            "total_queries": len(query_times_list),
            "slow_queries_count": len(self.slow_queries),
            "query_profiles_count": len(self.query_profiles)
        }
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取慢查询列表"""
        return list(self.slow_queries)[-limit:]
    
    def get_top_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热门查询"""
        sorted_profiles = sorted(
            self.query_profiles.values(),
            key=lambda p: p.query_count,
            reverse=True
        )
        
        return [
            {
                "query_hash": p.query_hash,
                "collection": p.collection_name,
                "query_count": p.query_count,
                "avg_response_time": p.avg_response_time,
                "cache_hit_rate": p.cache_hit_rate
            }
            for p in sorted_profiles[:limit]
        ]


class RetrievalOptimizer:
    """检索优化器"""
    
    def __init__(self, cache_size: int = 10000, cache_ttl: int = 3600):
        self.query_cache = QueryCache(cache_size, cache_ttl)
        self.performance_monitor = PerformanceMonitor()
        self.optimization_rules = []
        self.auto_optimization_enabled = True
        self.optimization_interval = 300  # 5分钟
        self.last_optimization = datetime.utcnow()
    
    async def optimize_query(self, collection_name: str, query_vector: List[float],
                           top_k: int, where: Optional[Dict] = None) -> Tuple[List[float], int, Optional[Dict]]:
        """优化查询参数"""
        try:
            # 查询预处理
            optimized_vector = self._optimize_query_vector(query_vector)
            optimized_top_k = self._optimize_top_k(top_k, collection_name)
            optimized_where = self._optimize_where_clause(where)
            
            return optimized_vector, optimized_top_k, optimized_where
            
        except Exception as e:
            logger.error(f"查询优化失败: {e}")
            return query_vector, top_k, where
    
    def _optimize_query_vector(self, query_vector: List[float]) -> List[float]:
        """优化查询向量"""
        # 向量归一化
        vector_array = np.array(query_vector)
        norm = np.linalg.norm(vector_array)
        if norm > 0:
            normalized_vector = vector_array / norm
            return normalized_vector.tolist()
        return query_vector
    
    def _optimize_top_k(self, top_k: int, collection_name: str) -> int:
        """优化top_k参数"""
        # 基于历史查询模式优化top_k
        if top_k > 100:
            logger.warning(f"top_k值过大: {top_k}, 建议使用较小值以提高性能")
            return min(top_k, 100)
        return top_k
    
    def _optimize_where_clause(self, where: Optional[Dict]) -> Optional[Dict]:
        """优化过滤条件"""
        if not where:
            return where
        
        # 优化过滤条件的顺序，将选择性高的条件放在前面
        # 这里是简化实现
        return where
    
    async def get_cached_result(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        return self.query_cache.get(cache_key)
    
    async def cache_result(self, cache_key: str, result: Any):
        """缓存查询结果"""
        self.query_cache.put(cache_key, result)
    
    def record_query_performance(self, collection_name: str, query_vector: List[float],
                               top_k: int, response_time: float, cache_hit: bool):
        """记录查询性能"""
        self.performance_monitor.record_query(
            collection_name, query_vector, top_k, response_time, cache_hit
        )
    
    async def auto_optimize(self):
        """自动优化"""
        if not self.auto_optimization_enabled:
            return
        
        now = datetime.utcnow()
        if (now - self.last_optimization).total_seconds() < self.optimization_interval:
            return
        
        try:
            logger.info("开始自动优化...")
            
            # 分析慢查询
            slow_queries = self.performance_monitor.get_slow_queries()
            if slow_queries:
                await self._optimize_slow_queries(slow_queries)
            
            # 优化缓存策略
            await self._optimize_cache_strategy()
            
            # 清理过期的查询档案
            await self._cleanup_query_profiles()
            
            self.last_optimization = now
            logger.info("自动优化完成")
            
        except Exception as e:
            logger.error(f"自动优化失败: {e}")
    
    async def _optimize_slow_queries(self, slow_queries: List[Dict[str, Any]]):
        """优化慢查询"""
        for query in slow_queries:
            if query["response_time"] > 2.0:  # 超过2秒的查询
                logger.warning(f"检测到慢查询: 集合={query['collection']}, "
                             f"响应时间={query['response_time']:.2f}s")
                # 这里可以添加具体的优化策略
    
    async def _optimize_cache_strategy(self):
        """优化缓存策略"""
        hit_rate = self.query_cache.get_hit_rate()
        if hit_rate < 0.5:  # 命中率低于50%
            logger.info(f"缓存命中率较低: {hit_rate:.2%}, 考虑调整缓存策略")
            # 可以动态调整缓存大小或TTL
    
    async def _cleanup_query_profiles(self):
        """清理查询档案"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        expired_profiles = [
            hash_key for hash_key, profile in self.performance_monitor.query_profiles.items()
            if profile.last_used < cutoff_time
        ]
        
        for hash_key in expired_profiles:
            del self.performance_monitor.query_profiles[hash_key]
        
        if expired_profiles:
            logger.info(f"清理了{len(expired_profiles)}个过期查询档案")
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计"""
        return {
            "cache_stats": {
                "hit_rate": self.query_cache.get_hit_rate(),
                "cache_size": len(self.query_cache.cache),
                "max_size": self.query_cache.max_size
            },
            "performance_stats": self.performance_monitor.get_performance_stats(),
            "top_queries": self.performance_monitor.get_top_queries(),
            "slow_queries": self.performance_monitor.get_slow_queries(),
            "optimization_info": {
                "auto_optimization_enabled": self.auto_optimization_enabled,
                "last_optimization": self.last_optimization.isoformat(),
                "optimization_interval": self.optimization_interval
            }
        }
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理检索优化器资源...")
        self.query_cache.clear()
        self.performance_monitor.query_profiles.clear()
        self.performance_monitor.collection_stats.clear()
        logger.info("检索优化器资源清理完成")
