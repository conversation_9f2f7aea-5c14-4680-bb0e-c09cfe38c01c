"""
向量数据库管理器
高性能向量检索和存储优化
"""

import chromadb
import numpy as np
import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import json
from dataclasses import dataclass

from app.utils.logger import get_logger
from app.core.retrieval_optimizer import RetrievalOptimizer

logger = get_logger(__name__)


@dataclass
class SearchResult:
    """搜索结果"""
    id: str
    document: str
    metadata: Dict[str, Any]
    distance: float
    score: float


@dataclass
class CollectionStats:
    """集合统计信息"""
    name: str
    count: int
    dimension: int
    created_at: datetime
    last_updated: datetime
    index_type: str
    memory_usage: int


class VectorDatabaseManager:
    """向量数据库管理器"""

    def __init__(self, max_workers: int = 4):
        self.chroma_client = None
        self.collections = {}
        self.collection_stats = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.cache = {}  # 简单的查询缓存
        self.cache_ttl = 300  # 缓存5分钟

        # 检索优化器
        self.retrieval_optimizer = RetrievalOptimizer()

        # 性能统计
        self.performance_stats = {
            "total_searches": 0,
            "total_inserts": 0,
            "average_search_time": 0.0,
            "average_insert_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0
        }

    async def initialize(self):
        """初始化向量数据库"""
        try:
            logger.info("初始化ChromaDB客户端...")

            # 尝试连接ChromaDB
            try:
                self.chroma_client = chromadb.HttpClient(host="localhost", port=8000)
                # 测试连接
                self.chroma_client.heartbeat()
                logger.info("ChromaDB HTTP客户端连接成功")
            except Exception as e:
                logger.warning(f"ChromaDB HTTP连接失败: {e}, 尝试使用内存客户端")
                self.chroma_client = chromadb.Client()
                logger.info("使用ChromaDB内存客户端")

            # 加载现有集合
            await self._load_existing_collections()

            logger.info("ChromaDB客户端初始化完成")

        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise

    async def _load_existing_collections(self):
        """加载现有集合"""
        try:
            loop = asyncio.get_event_loop()
            collections = await loop.run_in_executor(
                self.executor,
                self.chroma_client.list_collections
            )

            for collection in collections:
                self.collections[collection.name] = collection

                # 获取集合统计信息
                count = await loop.run_in_executor(
                    self.executor,
                    collection.count
                )

                self.collection_stats[collection.name] = CollectionStats(
                    name=collection.name,
                    count=count,
                    dimension=collection.metadata.get("dimension", 0),
                    created_at=datetime.utcnow(),
                    last_updated=datetime.utcnow(),
                    index_type="hnsw",
                    memory_usage=0
                )

            logger.info(f"加载了{len(collections)}个现有集合")

        except Exception as e:
            logger.error(f"加载现有集合失败: {e}")
            # 不抛出异常，允许继续初始化
    
    async def create_collection(self, name: str, dimension: int,
                              distance_function: str = "cosine") -> Dict[str, Any]:
        """
        创建集合

        Args:
            name: 集合名称
            dimension: 向量维度
            distance_function: 距离函数 (cosine, l2, ip)

        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            loop = asyncio.get_event_loop()

            # 检查集合是否已存在
            if name in self.collections:
                return {"status": "exists", "collection_name": name}

            # 创建集合
            collection = await loop.run_in_executor(
                self.executor,
                lambda: self.chroma_client.create_collection(
                    name=name,
                    metadata={
                        "dimension": dimension,
                        "distance_function": distance_function,
                        "created_at": datetime.utcnow().isoformat()
                    }
                )
            )

            self.collections[name] = collection

            # 创建统计信息
            self.collection_stats[name] = CollectionStats(
                name=name,
                count=0,
                dimension=dimension,
                created_at=datetime.utcnow(),
                last_updated=datetime.utcnow(),
                index_type="hnsw",
                memory_usage=0
            )

            logger.info(f"集合创建成功: {name}, 维度: {dimension}")
            return {
                "status": "success",
                "collection_name": name,
                "dimension": dimension,
                "distance_function": distance_function
            }

        except Exception as e:
            logger.error(f"集合创建失败: {name}, 错误: {e}")
            raise
    
    async def insert_vectors(self, collection_name: str, vectors: List[List[float]],
                           documents: List[str], metadatas: Optional[List[Dict]] = None,
                           ids: Optional[List[str]] = None, batch_size: int = 1000) -> Dict[str, Any]:
        """
        批量插入向量

        Args:
            collection_name: 集合名称
            vectors: 向量列表
            documents: 文档列表
            metadatas: 元数据列表
            ids: ID列表
            batch_size: 批处理大小

        Returns:
            Dict[str, Any]: 插入结果
        """
        start_time = time.time()

        try:
            collection = self.collections.get(collection_name)
            if not collection:
                collection = await self._get_or_create_collection(collection_name)

            if not ids:
                ids = [f"doc_{int(time.time())}_{i}" for i in range(len(vectors))]

            if metadatas is None:
                metadatas = [{"inserted_at": datetime.utcnow().isoformat()} for _ in range(len(vectors))]

            # 验证数据一致性
            if not (len(vectors) == len(documents) == len(ids) == len(metadatas)):
                raise ValueError("向量、文档、ID和元数据的数量必须一致")

            # 批量插入
            total_inserted = 0
            loop = asyncio.get_event_loop()

            for i in range(0, len(vectors), batch_size):
                batch_vectors = vectors[i:i + batch_size]
                batch_documents = documents[i:i + batch_size]
                batch_metadatas = metadatas[i:i + batch_size]
                batch_ids = ids[i:i + batch_size]

                await loop.run_in_executor(
                    self.executor,
                    lambda: collection.add(
                        embeddings=batch_vectors,
                        documents=batch_documents,
                        metadatas=batch_metadatas,
                        ids=batch_ids
                    )
                )

                total_inserted += len(batch_vectors)
                logger.info(f"批次插入完成: {collection_name}, 当前批次: {len(batch_vectors)}, 总计: {total_inserted}")

            # 更新统计信息
            if collection_name in self.collection_stats:
                self.collection_stats[collection_name].count += total_inserted
                self.collection_stats[collection_name].last_updated = datetime.utcnow()

            # 更新性能统计
            insert_time = time.time() - start_time
            self.performance_stats["total_inserts"] += 1
            self._update_average_insert_time(insert_time)

            logger.info(f"向量插入成功: {collection_name}, 数量: {total_inserted}, 耗时: {insert_time:.2f}秒")

            return {
                "status": "success",
                "inserted_count": total_inserted,
                "collection_name": collection_name,
                "insert_time": insert_time,
                "throughput": total_inserted / insert_time if insert_time > 0 else 0
            }

        except Exception as e:
            logger.error(f"向量插入失败: {collection_name}, 错误: {e}")
            raise

    async def _get_or_create_collection(self, collection_name: str):
        """获取或创建集合"""
        try:
            loop = asyncio.get_event_loop()
            collection = await loop.run_in_executor(
                self.executor,
                lambda: self.chroma_client.get_collection(collection_name)
            )
            self.collections[collection_name] = collection
            return collection
        except Exception:
            # 集合不存在，创建默认集合
            logger.info(f"集合不存在，创建默认集合: {collection_name}")
            await self.create_collection(collection_name, dimension=384)  # 默认维度
            return self.collections[collection_name]
    
    async def search_similar(self, collection_name: str, query_vector: List[float],
                           top_k: int = 10, where: Optional[Dict] = None,
                           include_distances: bool = True, use_cache: bool = True) -> Dict[str, Any]:
        """
        搜索相似向量

        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            top_k: 返回结果数量
            where: 过滤条件
            include_distances: 是否包含距离
            use_cache: 是否使用缓存

        Returns:
            Dict[str, Any]: 搜索结果
        """
        start_time = time.time()

        try:
            # 查询优化
            optimized_vector, optimized_top_k, optimized_where = await self.retrieval_optimizer.optimize_query(
                collection_name, query_vector, top_k, where
            )

            # 生成缓存键
            cache_key = None
            cache_hit = False
            if use_cache:
                cache_key = self._generate_cache_key(collection_name, optimized_vector, optimized_top_k, optimized_where)
                cached_result = await self.retrieval_optimizer.get_cached_result(cache_key)
                if cached_result:
                    self.performance_stats["cache_hits"] += 1
                    cache_hit = True
                    logger.debug(f"缓存命中: {collection_name}")

                    # 记录性能（缓存命中）
                    self.retrieval_optimizer.record_query_performance(
                        collection_name, query_vector, top_k, 0.001, True
                    )

                    cached_result["cache_used"] = True
                    return cached_result
                else:
                    self.performance_stats["cache_misses"] += 1

            collection = self.collections.get(collection_name)
            if not collection:
                collection = await self._get_or_create_collection(collection_name)

            # 执行搜索
            loop = asyncio.get_event_loop()
            include_fields = ["documents", "metadatas"]
            if include_distances:
                include_fields.append("distances")

            raw_results = await loop.run_in_executor(
                self.executor,
                lambda: collection.query(
                    query_embeddings=[optimized_vector],
                    n_results=optimized_top_k,
                    where=optimized_where,
                    include=include_fields
                )
            )

            # 处理搜索结果
            processed_results = self._process_search_results(raw_results, include_distances)

            search_time = time.time() - start_time

            # 更新性能统计
            self.performance_stats["total_searches"] += 1
            self._update_average_search_time(search_time)

            result = {
                "status": "success",
                "collection_name": collection_name,
                "query_time": search_time,
                "total_results": len(processed_results),
                "results": processed_results,
                "cache_used": False
            }

            # 记录性能
            self.retrieval_optimizer.record_query_performance(
                collection_name, query_vector, top_k, search_time, cache_hit
            )

            # 缓存结果
            if use_cache and cache_key:
                await self.retrieval_optimizer.cache_result(cache_key, result)

            # 自动优化
            await self.retrieval_optimizer.auto_optimize()

            logger.info(f"向量搜索完成: {collection_name}, 结果数: {len(processed_results)}, 耗时: {search_time:.3f}秒")
            return result

        except Exception as e:
            logger.error(f"向量搜索失败: {collection_name}, 错误: {e}")
            raise

    def _process_search_results(self, raw_results: Dict, include_distances: bool) -> List[SearchResult]:
        """处理搜索结果"""
        try:
            results = []

            if not raw_results.get("ids") or not raw_results["ids"][0]:
                return results

            ids = raw_results["ids"][0]
            documents = raw_results.get("documents", [[]])[0]
            metadatas = raw_results.get("metadatas", [[]])[0]
            distances = raw_results.get("distances", [[]])[0] if include_distances else [0.0] * len(ids)

            for i, doc_id in enumerate(ids):
                # 计算相似度分数 (1 - distance)
                distance = distances[i] if i < len(distances) else 1.0
                score = max(0.0, 1.0 - distance)

                result = SearchResult(
                    id=doc_id,
                    document=documents[i] if i < len(documents) else "",
                    metadata=metadatas[i] if i < len(metadatas) else {},
                    distance=distance,
                    score=score
                )
                results.append(result)

            # 按分数排序
            results.sort(key=lambda x: x.score, reverse=True)

            return [
                {
                    "id": r.id,
                    "document": r.document,
                    "metadata": r.metadata,
                    "distance": r.distance,
                    "score": r.score
                }
                for r in results
            ]

        except Exception as e:
            logger.error(f"处理搜索结果失败: {e}")
            return []

    def _generate_cache_key(self, collection_name: str, query_vector: List[float],
                          top_k: int, where: Optional[Dict]) -> str:
        """生成缓存键"""
        try:
            # 对查询向量进行哈希
            vector_hash = hash(tuple(query_vector[:10]))  # 只使用前10维计算哈希
            where_str = json.dumps(where, sort_keys=True) if where else ""
            return f"{collection_name}:{vector_hash}:{top_k}:{hash(where_str)}"
        except Exception:
            return f"{collection_name}:{time.time()}"

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        try:
            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    cached_data["cache_used"] = True
                    return cached_data
                else:
                    # 缓存过期，删除
                    del self.cache[cache_key]
            return None
        except Exception:
            return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存结果"""
        try:
            # 简单的LRU缓存，限制缓存大小
            if len(self.cache) > 1000:
                # 删除最旧的缓存项
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
                del self.cache[oldest_key]

            self.cache[cache_key] = (result.copy(), time.time())
        except Exception as e:
            logger.error(f"缓存结果失败: {e}")

    def _update_average_search_time(self, search_time: float):
        """更新平均搜索时间"""
        total_searches = self.performance_stats["total_searches"]
        if total_searches == 1:
            self.performance_stats["average_search_time"] = search_time
        else:
            current_avg = self.performance_stats["average_search_time"]
            self.performance_stats["average_search_time"] = (current_avg * (total_searches - 1) + search_time) / total_searches

    def _update_average_insert_time(self, insert_time: float):
        """更新平均插入时间"""
        total_inserts = self.performance_stats["total_inserts"]
        if total_inserts == 1:
            self.performance_stats["average_insert_time"] = insert_time
        else:
            current_avg = self.performance_stats["average_insert_time"]
            self.performance_stats["average_insert_time"] = (current_avg * (total_inserts - 1) + insert_time) / total_inserts
    
    async def delete_collection(self, collection_name: str) -> Dict[str, Any]:
        """删除集合"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                lambda: self.chroma_client.delete_collection(collection_name)
            )

            # 清理本地缓存
            if collection_name in self.collections:
                del self.collections[collection_name]
            if collection_name in self.collection_stats:
                del self.collection_stats[collection_name]

            # 清理相关缓存
            keys_to_remove = [key for key in self.cache.keys() if key.startswith(f"{collection_name}:")]
            for key in keys_to_remove:
                del self.cache[key]

            logger.info(f"集合删除成功: {collection_name}")
            return {"status": "success", "collection_name": collection_name}

        except Exception as e:
            logger.error(f"集合删除失败: {collection_name}, 错误: {e}")
            raise

    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            collection = self.collections.get(collection_name)
            if not collection:
                collection = await self._get_or_create_collection(collection_name)

            loop = asyncio.get_event_loop()
            count = await loop.run_in_executor(self.executor, collection.count)

            stats = self.collection_stats.get(collection_name)

            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata,
                "stats": stats.__dict__ if stats else None
            }

        except Exception as e:
            logger.error(f"获取集合信息失败: {collection_name}, 错误: {e}")
            raise

    async def list_collections(self) -> List[Dict[str, Any]]:
        """列出所有集合"""
        try:
            loop = asyncio.get_event_loop()
            collections = await loop.run_in_executor(
                self.executor,
                self.chroma_client.list_collections
            )

            result = []
            for collection in collections:
                count = await loop.run_in_executor(self.executor, collection.count)
                result.append({
                    "name": collection.name,
                    "count": count,
                    "metadata": collection.metadata
                })

            return result

        except Exception as e:
            logger.error(f"列出集合失败: {e}")
            raise

    async def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        try:
            cache_hit_rate = 0.0
            total_cache_requests = self.performance_stats["cache_hits"] + self.performance_stats["cache_misses"]
            if total_cache_requests > 0:
                cache_hit_rate = self.performance_stats["cache_hits"] / total_cache_requests

            optimization_stats = self.retrieval_optimizer.get_optimization_stats()

            return {
                "performance": self.performance_stats.copy(),
                "cache": {
                    "hit_rate": cache_hit_rate,
                    "cache_size": len(self.cache),
                    "cache_ttl": self.cache_ttl
                },
                "collections": {
                    "total_collections": len(self.collections),
                    "collection_stats": {name: stats.__dict__ for name, stats in self.collection_stats.items()}
                },
                "optimization": optimization_stats
            }

        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {"error": str(e)}

    async def optimize_collection(self, collection_name: str) -> Dict[str, Any]:
        """优化集合性能"""
        try:
            # 清理该集合的缓存
            keys_to_remove = [key for key in self.cache.keys() if key.startswith(f"{collection_name}:")]
            removed_count = len(keys_to_remove)
            for key in keys_to_remove:
                del self.cache[key]

            # 更新统计信息
            collection = self.collections.get(collection_name)
            if collection:
                loop = asyncio.get_event_loop()
                count = await loop.run_in_executor(self.executor, collection.count)

                if collection_name in self.collection_stats:
                    self.collection_stats[collection_name].count = count
                    self.collection_stats[collection_name].last_updated = datetime.utcnow()

            logger.info(f"集合优化完成: {collection_name}, 清理缓存: {removed_count}项")

            return {
                "status": "success",
                "collection_name": collection_name,
                "cache_cleared": removed_count,
                "optimized_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"集合优化失败: {collection_name}, 错误: {e}")
            raise

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 测试ChromaDB连接
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, self.chroma_client.heartbeat)

            # 获取系统状态
            status = {
                "chromadb": "healthy",
                "collections_count": len(self.collections),
                "cache_size": len(self.cache),
                "performance": {
                    "total_searches": self.performance_stats["total_searches"],
                    "total_inserts": self.performance_stats["total_inserts"],
                    "average_search_time": f"{self.performance_stats['average_search_time']:.3f}s",
                    "average_insert_time": f"{self.performance_stats['average_insert_time']:.3f}s"
                }
            }

            return status

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {"chromadb": "unhealthy", "error": str(e)}

    async def cleanup(self):
        """清理资源"""
        logger.info("清理向量数据库资源...")

        # 清理缓存
        self.cache.clear()

        # 清理集合引用
        self.collections.clear()
        self.collection_stats.clear()

        # 清理检索优化器
        if self.retrieval_optimizer:
            await self.retrieval_optimizer.cleanup()

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)

        logger.info("向量数据库资源清理完成")
