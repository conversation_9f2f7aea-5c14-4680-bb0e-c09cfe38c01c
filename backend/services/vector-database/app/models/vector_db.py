"""
向量数据库相关数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class InsertRequest(BaseModel):
    """插入请求"""
    vectors: List[List[float]] = Field(..., description="向量列表")
    documents: List[str] = Field(..., description="文档列表")
    metadatas: Optional[List[Dict[str, Any]]] = Field(None, description="元数据列表")
    ids: Optional[List[str]] = Field(None, description="ID列表")


class SearchRequest(BaseModel):
    """搜索请求"""
    query_vector: List[float] = Field(..., description="查询向量")
    top_k: int = Field(10, description="返回结果数量")
    where: Optional[Dict[str, Any]] = Field(None, description="过滤条件")


class SearchResponse(BaseModel):
    """搜索响应"""
    status: str = Field(..., description="状态")
    results: Dict[str, Any] = Field(..., description="搜索结果")
    total_results: int = Field(..., description="结果总数")
    created_at: datetime = Field(default_factory=datetime.utcnow)
