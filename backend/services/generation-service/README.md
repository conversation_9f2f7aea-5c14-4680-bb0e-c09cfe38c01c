# 生成服务 (Generation Service)

RAG系统的文本生成服务，基于大语言模型提供智能文本生成、问答、对话和摘要功能。

## 📋 功能特性

### 🤖 多模型支持
- **OpenAI**: GPT-3.5/4系列模型
- **Anthropic**: Claude系列模型
- **模型切换**: 支持动态模型选择
- **故障转移**: 自动故障转移机制

### 🎯 RAG增强生成
- **智能问答**: 基于检索文档的问答生成
- **多轮对话**: 支持上下文感知的对话
- **文档摘要**: 智能文档摘要生成
- **内容分析**: 深度文档分析和洞察

### 📝 提示词管理
- **模板系统**: 丰富的提示词模板库
- **动态渲染**: 支持变量替换和条件逻辑
- **长度优化**: 智能提示词长度优化
- **缓存机制**: 提示词渲染结果缓存

### ⚡ 高性能特性
- **流式输出**: 支持实时流式文本生成
- **并发处理**: 高并发请求处理
- **智能缓存**: Redis缓存优化
- **负载均衡**: 多模型负载均衡

## 🏗️ 技术架构

### 技术栈
- **框架**: Express.js + TypeScript
- **运行时**: Node.js 18+
- **缓存**: Redis
- **LLM**: OpenAI API, Anthropic API
- **模板**: Handlebars

### 核心组件
```
Generation Service
├── LLM管理器        # 多模型统一管理
├── 提示词管理器      # 模板管理和渲染
├── 缓存管理器       # Redis缓存优化
├── 错误处理器       # 统一错误处理
├── 日志系统        # 结构化日志
└── API路由        # RESTful接口
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0
- Redis >= 6.0
- OpenAI API Key 或 Anthropic API Key

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
cp .env.example .env
```

主要配置项：
```env
# 服务配置
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3100

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7

# Anthropic配置
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=2048
ANTHROPIC_TEMPERATURE=0.7

# LLM配置
LLM_DEFAULT_PROVIDER=openai
LLM_ENABLE_FALLBACK=true
LLM_MAX_RETRIES=3

# 提示词配置
SYSTEM_PROMPT=你是一个有用的AI助手
MAX_CONTEXT_LENGTH=8000
ENABLE_PROMPT_OPTIMIZATION=true

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
```

### 运行服务

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm run build
npm start
```

#### 使用Docker
```bash
docker build -t generation-service .
docker run -p 3000:3000 generation-service
```

## 📡 API 接口

### 基础文本生成
```http
POST /api/generation/generate
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "你好，请介绍一下人工智能"}
  ],
  "model": "gpt-3.5-turbo",
  "maxTokens": 1000,
  "temperature": 0.7,
  "provider": "openai"
}
```

### 流式文本生成
```http
POST /api/generation/generate/stream
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "请写一篇关于机器学习的文章"}
  ],
  "model": "gpt-3.5-turbo",
  "maxTokens": 2000,
  "temperature": 0.7
}
```

### RAG问答生成
```http
POST /api/generation/rag/qa
Content-Type: application/json

{
  "query": "什么是深度学习？",
  "documents": [
    {
      "content": "深度学习是机器学习的一个分支...",
      "metadata": {"source": "AI教程", "page": 1},
      "score": 0.95
    }
  ],
  "conversationHistory": [
    {"role": "user", "content": "请介绍机器学习"},
    {"role": "assistant", "content": "机器学习是..."}
  ],
  "templateId": "rag-qa",
  "model": "gpt-3.5-turbo"
}
```

### RAG对话生成
```http
POST /api/generation/rag/chat
Content-Type: application/json

{
  "query": "能详细解释一下神经网络吗？",
  "documents": [
    {
      "content": "神经网络是一种模拟人脑神经元的计算模型...",
      "metadata": {"title": "神经网络基础"}
    }
  ],
  "conversationHistory": [
    {"role": "user", "content": "什么是深度学习？"},
    {"role": "assistant", "content": "深度学习是..."}
  ],
  "conversationId": "conv-123",
  "templateId": "rag-chat"
}
```

### RAG摘要生成
```http
POST /api/generation/rag/summary
Content-Type: application/json

{
  "documents": [
    {
      "content": "人工智能技术在近年来取得了显著进展..."
    },
    {
      "content": "机器学习算法的应用越来越广泛..."
    }
  ],
  "maxLength": 300,
  "model": "gpt-3.5-turbo"
}
```

### 获取可用提供商
```http
GET /api/generation/providers
```

### 获取可用模型
```http
GET /api/generation/models?provider=openai
```

### 获取提示词模板
```http
GET /api/generation/templates?category=rag
```

### 获取模板详情
```http
GET /api/generation/templates/rag-qa
```

### 健康检查
```http
GET /api/generation/health
```

## 🔧 配置说明

### LLM提供商配置
```typescript
// OpenAI配置
{
  apiKey: "your-api-key",
  baseURL: "https://api.openai.com/v1",
  defaultModel: "gpt-3.5-turbo",
  maxTokens: 2048,
  temperature: 0.7,
  timeout: 30000
}

// Anthropic配置
{
  apiKey: "your-api-key",
  baseURL: "https://api.anthropic.com",
  defaultModel: "claude-3-sonnet-20240229",
  maxTokens: 2048,
  temperature: 0.7,
  timeout: 30000
}
```

### 提示词模板配置
```typescript
{
  id: "rag-qa",
  name: "RAG问答模板",
  template: `基于以下文档回答问题：
{{#each documents}}
文档{{@index}}: {{content}}
{{/each}}

问题：{{query}}
回答：`,
  variables: ["query", "documents"],
  category: "rag"
}
```

### 缓存配置
```typescript
{
  enabled: true,
  ttl: 3600,        // 缓存过期时间（秒）
  maxSize: 1000,    // 最大缓存条目数
  enableCompression: true
}
```

## 📊 监控和日志

### 日志级别
- **ERROR**: 错误信息
- **WARN**: 警告信息
- **INFO**: 一般信息
- **DEBUG**: 调试信息

### 性能监控
```typescript
// 请求日志
{
  "requestId": "req-123",
  "method": "POST",
  "url": "/api/generation/rag/qa",
  "duration": 1500,
  "statusCode": 200,
  "model": "gpt-3.5-turbo",
  "tokens": 1024
}

// 错误日志
{
  "level": "error",
  "message": "LLM生成失败",
  "error": {
    "name": "LLMError",
    "provider": "openai",
    "code": "rate_limit_exceeded"
  },
  "requestId": "req-123"
}
```

### 健康检查指标
- LLM提供商可用性
- Redis连接状态
- 内存使用情况
- 响应时间统计

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# 覆盖率测试
npm run test:coverage
```

### 测试用例
```bash
# 测试基础生成
curl -X POST http://localhost:3000/api/generation/generate \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "gpt-3.5-turbo"
  }'

# 测试RAG问答
curl -X POST http://localhost:3000/api/generation/rag/qa \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是AI？",
    "documents": [{"content": "AI是人工智能的缩写"}]
  }'
```

## 📈 性能优化

### 缓存策略
1. **提示词缓存**: 缓存渲染后的提示词
2. **结果缓存**: 缓存生成结果（可选）
3. **模板缓存**: 缓存编译后的模板
4. **连接池**: LLM API连接池

### 并发优化
1. **请求队列**: 控制并发请求数量
2. **负载均衡**: 多模型负载均衡
3. **故障转移**: 自动故障转移
4. **重试机制**: 智能重试策略

### 内存优化
1. **流式处理**: 大文本流式处理
2. **垃圾回收**: 及时释放内存
3. **对象池**: 重用对象减少GC
4. **内存监控**: 实时内存监控

## 🚨 故障排查

### 常见问题

1. **LLM API调用失败**
   ```bash
   # 检查API密钥
   echo $OPENAI_API_KEY
   
   # 测试API连接
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   redis-cli ping
   
   # 检查连接配置
   redis-cli -h localhost -p 6379 info
   ```

3. **内存泄漏**
   ```bash
   # 监控内存使用
   node --inspect app.js
   
   # 生成内存快照
   kill -USR2 <pid>
   ```

### 错误代码
- `LLM_ERROR`: LLM服务错误
- `TEMPLATE_NOT_FOUND`: 模板不存在
- `VALIDATION_ERROR`: 参数验证失败
- `RATE_LIMIT_ERROR`: 请求频率限制
- `TIMEOUT_ERROR`: 请求超时

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
