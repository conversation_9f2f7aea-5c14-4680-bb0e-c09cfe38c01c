{"name": "generation-service", "version": "1.0.0", "description": "RAG系统生成服务 - 负责LLM调用、提示词工程和内容生成", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["generation-service", "llm", "openai", "anthropic", "content-generation"], "author": "RAG Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "redis": "^4.6.10", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1", "openai": "^4.20.1", "anthropic": "^0.7.8", "axios": "^1.6.2", "ioredis": "^5.3.2", "bull": "^4.12.2", "tiktoken": "^1.0.10", "handlebars": "^4.7.8", "marked": "^11.1.1", "dompurify": "^3.0.7", "jsdom": "^23.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/handlebars": "^4.1.0", "@types/marked": "^6.0.0", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.6", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}