# RAG生成服务 - 中文说明文档

## 📋 概述

RAG生成服务是一个高性能的文本生成服务，专为RAG（检索增强生成）系统设计。该服务集成多个LLM提供商，提供统一的文本生成API，支持流式响应、提示词工程和内容安全检查等功能。

## 🚀 主要功能

### 🧠 多LLM提供商集成
- **OpenAI集成**: 支持GPT-3.5、GPT-4等模型
- **Anthropic集成**: 支持Claude系列模型
- **统一接口**: 提供统一的API接口，屏蔽不同提供商的差异
- **智能回退**: 当主要提供商不可用时自动切换到备用提供商
- **负载均衡**: 支持多提供商间的负载分配

### ⚡ RAG生成管道
- **上下文融合**: 智能融合检索到的文档片段
- **提示词工程**: 优化的提示词模板和动态生成
- **多轮对话**: 支持上下文相关的多轮对话生成
- **内容过滤**: 集成内容安全检查和敏感信息过滤
- **质量控制**: 生成内容的质量评估和优化

### 🌊 流式响应处理
- **实时流式**: 支持Server-Sent Events (SSE)流式响应
- **WebSocket支持**: 双向实时通信
- **断点续传**: 支持流式传输的断点续传
- **缓冲优化**: 智能缓冲策略提升用户体验
- **错误恢复**: 流式传输中的错误处理和恢复

### 🛡️ 内容安全和过滤
- **敏感词过滤**: 基于词典和AI的敏感内容检测
- **内容分类**: 自动识别和分类生成内容
- **合规检查**: 符合相关法规的内容审核
- **用户安全**: 防止恶意输入和注入攻击
- **审计日志**: 完整的内容生成和审核日志

### 📊 性能监控和优化
- **响应时间监控**: 实时监控生成响应时间
- **Token使用统计**: 详细的Token消耗分析
- **错误率追踪**: 生成失败率和错误类型统计
- **成本控制**: API调用成本监控和预算控制
- **性能优化**: 基于监控数据的自动优化建议

## 🏗️ 技术架构

### 核心组件架构
```
生成服务架构
├── API层 (Express.js)
│   ├── 文本生成接口
│   ├── 流式生成接口
│   ├── 模型管理接口
│   └── 监控统计接口
├── 业务逻辑层
│   ├── LLMManager (LLM管理器)
│   ├── PromptManager (提示词管理器)
│   ├── ContentFilter (内容过滤器)
│   ├── RAGPipeline (RAG管道)
│   └── QualityAssessor (质量评估器)
├── LLM集成层
│   ├── OpenAI客户端
│   ├── Anthropic客户端
│   ├── 本地模型客户端
│   └── 自定义提供商适配器
├── 缓存和队列层
│   ├── Redis缓存
│   ├── 任务队列
│   ├── 结果缓存
│   └── 会话管理
└── 监控层
    ├── Prometheus指标
    ├── 性能日志
    ├── 错误追踪
    └── 成本统计
```

### 技术栈
- **Web框架**: Express.js + TypeScript
- **LLM SDK**: OpenAI SDK、Anthropic SDK
- **流式处理**: Server-Sent Events、WebSocket
- **缓存**: Redis + 内存缓存
- **队列**: Bull Queue (Redis)
- **监控**: Prometheus + Winston日志
- **安全**: Helmet、Rate Limiting

## 📦 安装和部署

### 环境要求
- Node.js 18+
- Redis 6+
- 8GB+ RAM (推荐16GB+)
- 有效的LLM API密钥

### 快速启动
```bash
# 1. 进入服务目录
cd backend/services/generation-service

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置API密钥

# 4. 启动服务
npm run dev
```

### 生产部署
```bash
# 构建项目
npm run build

# 启动生产服务
npm start
```

### Docker部署
```bash
# 构建镜像
docker build -t rag-generation-service .

# 运行容器
docker run -d \
  --name rag-generation-service \
  -p 3000:3000 \
  -e OPENAI_API_KEY=your_key \
  -e ANTHROPIC_API_KEY=your_key \
  -e REDIS_URL=redis://redis:6379 \
  rag-generation-service
```

## 🔧 配置说明

### 主要配置项
```typescript
// 基础配置
export const config = {
  port: 3000,
  nodeEnv: 'development',
  
  // LLM配置
  llm: {
    defaultProvider: 'openai',
    enableFallback: true,
    
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: 'https://api.openai.com/v1',
      defaultModel: 'gpt-3.5-turbo',
      maxTokens: 2048,
      temperature: 0.7,
      timeout: 30000
    },
    
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
      defaultModel: 'claude-3-sonnet-20240229',
      maxTokens: 2048,
      temperature: 0.7,
      timeout: 30000
    }
  },
  
  // 缓存配置
  cache: {
    ttl: 3600, // 1小时
    maxSize: 1000
  },
  
  // 安全配置
  security: {
    enableContentFilter: true,
    maxRequestSize: '10mb',
    rateLimit: {
      windowMs: 900000, // 15分钟
      maxRequests: 100
    }
  }
};
```

### 提示词模板配置
```typescript
export const promptTemplates = {
  // RAG问答模板
  ragQA: {
    system: `你是一个专业的AI助手，基于提供的文档内容回答用户问题。
请遵循以下原则：
1. 仅基于提供的文档内容回答
2. 如果文档中没有相关信息，请明确说明
3. 保持回答准确、简洁、有用
4. 使用中文回答`,
    
    user: `文档内容：
{context}

用户问题：{question}

请基于上述文档内容回答用户问题。`
  },
  
  // 文档摘要模板
  summarization: {
    system: `你是一个专业的文档摘要助手，请为用户提供准确、简洁的文档摘要。`,
    user: `请为以下文档生成摘要：

{document}

摘要要求：
- 长度控制在200字以内
- 突出关键信息和要点
- 使用中文`
  }
};
```

## 📚 API接口文档

### 文本生成
```http
POST /api/v1/generation/generate
Content-Type: application/json

{
  "messages": [
    {"role": "system", "content": "你是一个有用的AI助手"},
    {"role": "user", "content": "请介绍一下人工智能"}
  ],
  "model": "gpt-3.5-turbo",
  "maxTokens": 1000,
  "temperature": 0.7,
  "provider": "openai"
}

# 响应
{
  "success": true,
  "data": {
    "content": "人工智能（AI）是计算机科学的一个分支...",
    "model": "gpt-3.5-turbo",
    "provider": "openai",
    "usage": {
      "promptTokens": 50,
      "completionTokens": 200,
      "totalTokens": 250
    },
    "finishReason": "stop",
    "responseTime": 1500
  }
}
```

### 流式生成
```http
POST /api/v1/generation/stream
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "写一首关于春天的诗"}
  ],
  "stream": true
}

# 响应 (Server-Sent Events)
data: {"content": "春", "delta": "春", "finished": false}
data: {"content": "春风", "delta": "风", "finished": false}
data: {"content": "春风拂面", "delta": "拂面", "finished": false}
...
data: {"content": "完整诗歌内容", "delta": "", "finished": true}
```

### RAG生成
```http
POST /api/v1/generation/rag
Content-Type: application/json

{
  "question": "什么是机器学习？",
  "context": [
    {
      "content": "机器学习是人工智能的一个子领域...",
      "source": "AI教程.pdf",
      "score": 0.95
    }
  ],
  "template": "ragQA",
  "maxTokens": 500
}

# 响应
{
  "success": true,
  "data": {
    "answer": "基于提供的文档，机器学习是...",
    "sources": ["AI教程.pdf"],
    "confidence": 0.92,
    "model": "gpt-3.5-turbo",
    "usage": {...}
  }
}
```

### 模型管理
```http
# 获取可用模型
GET /api/v1/generation/models

# 响应
{
  "success": true,
  "data": {
    "openai": [
      "gpt-3.5-turbo",
      "gpt-4",
      "gpt-4-turbo"
    ],
    "anthropic": [
      "claude-3-opus-20240229",
      "claude-3-sonnet-20240229",
      "claude-3-haiku-20240307"
    ]
  }
}

# 健康检查
GET /api/v1/generation/health

# 响应
{
  "success": true,
  "data": {
    "status": "healthy",
    "providers": {
      "openai": true,
      "anthropic": true
    },
    "uptime": 3600,
    "version": "1.0.0"
  }
}
```

### 性能统计
```http
GET /api/v1/generation/stats

# 响应
{
  "success": true,
  "data": {
    "requests": {
      "total": 1000,
      "successful": 980,
      "failed": 20,
      "successRate": 0.98
    },
    "performance": {
      "averageResponseTime": 1200,
      "p95ResponseTime": 2500,
      "p99ResponseTime": 4000
    },
    "usage": {
      "totalTokens": 500000,
      "promptTokens": 200000,
      "completionTokens": 300000,
      "estimatedCost": 10.50
    },
    "providers": {
      "openai": {
        "requests": 800,
        "successRate": 0.99
      },
      "anthropic": {
        "requests": 200,
        "successRate": 0.95
      }
    }
  }
}
```

## 🧪 测试和验证

### 运行测试
```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# 性能测试
npm run test:performance

# 生成覆盖率报告
npm run test:coverage
```

### 功能验证
```bash
# 健康检查
curl http://localhost:3000/api/v1/generation/health

# 简单生成测试
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}' \
  http://localhost:3000/api/v1/generation/generate

# 流式生成测试
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "写一首诗"}], "stream": true}' \
  http://localhost:3000/api/v1/generation/stream
```

## 📊 监控和运维

### 监控指标
- **请求指标**: QPS、响应时间、成功率、错误率
- **业务指标**: Token使用量、生成质量分数、用户满意度
- **成本指标**: API调用成本、Token成本、提供商费用分布
- **系统指标**: CPU、内存、网络使用率

### 性能优化建议
1. **缓存策略**: 缓存常见问题的回答，减少API调用
2. **批量处理**: 合并多个请求，提高处理效率
3. **模型选择**: 根据任务复杂度选择合适的模型
4. **提示词优化**: 优化提示词减少Token消耗
5. **并发控制**: 合理控制并发请求数量

### 故障排除
- **API密钥错误**: 检查环境变量配置
- **请求超时**: 调整超时设置或检查网络连接
- **内容被过滤**: 检查内容安全策略设置
- **成本超限**: 监控API使用量和成本控制

## 🔮 未来规划

### 短期计划 (1-2个月)
- [ ] 支持更多LLM提供商
- [ ] 增强内容安全检查
- [ ] 优化提示词模板
- [ ] 添加A/B测试功能

### 中期计划 (3-6个月)
- [ ] 支持多模态生成
- [ ] 实现模型微调
- [ ] 添加生成质量评估
- [ ] 支持自定义插件

### 长期计划 (6-12个月)
- [ ] AI驱动的提示词优化
- [ ] 智能成本控制
- [ ] 边缘计算支持
- [ ] 联邦学习集成

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: RAG开发团队
- **技术支持**: 通过GitHub Issues提交问题
- **文档更新**: 2025-08-28

---

*该文档将随着项目发展持续更新，确保信息的准确性和完整性。*
