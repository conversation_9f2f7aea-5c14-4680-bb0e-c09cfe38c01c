/**
 * 错误处理中间件
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 业务错误类
 */
export class BusinessError extends AppError {
  constructor(message: string, code: string = 'BUSINESS_ERROR', details?: any) {
    super(message, 400, code, true, details);
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', true, details);
  }
}

/**
 * 认证错误类
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR', true);
  }
}

/**
 * 授权错误类
 */
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR', true);
  }
}

/**
 * 资源未找到错误类
 */
export class NotFoundError extends AppError {
  constructor(message: string = '资源不存在') {
    super(message, 404, 'NOT_FOUND', true);
  }
}

/**
 * 冲突错误类
 */
export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT_ERROR', true, details);
  }
}

/**
 * 限流错误类
 */
export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_ERROR', true);
  }
}

/**
 * 外部服务错误类
 */
export class ExternalServiceError extends AppError {
  constructor(message: string, service: string, details?: any) {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR', true, { service, ...details });
  }
}

/**
 * LLM服务错误类
 */
export class LLMError extends AppError {
  constructor(message: string, provider: string, details?: any) {
    super(message, 502, 'LLM_ERROR', true, { provider, ...details });
  }
}

/**
 * 超时错误类
 */
export class TimeoutError extends AppError {
  constructor(message: string = '请求超时', operation?: string) {
    super(message, 408, 'TIMEOUT_ERROR', true, { operation });
  }
}

/**
 * 错误响应格式
 */
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  requestId?: string;
  path: string;
}

/**
 * 格式化错误响应
 */
function formatErrorResponse(
  error: AppError | Error,
  req: Request,
  includeStack: boolean = false
): ErrorResponse {
  const isAppError = error instanceof AppError;
  
  const response: ErrorResponse = {
    success: false,
    error: {
      code: isAppError ? error.code : 'INTERNAL_ERROR',
      message: error.message || '内部服务器错误',
      details: isAppError ? error.details : undefined
    },
    timestamp: new Date().toISOString(),
    requestId: (req as any).requestId,
    path: req.path
  };

  // 开发环境包含堆栈信息
  if (includeStack && error.stack) {
    response.error.stack = error.stack;
  }

  return response;
}

/**
 * 错误处理中间件
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  const isAppError = error instanceof AppError;
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // 确定状态码
  let statusCode = 500;
  if (isAppError) {
    statusCode = error.statusCode;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
  } else if (error.name === 'CastError') {
    statusCode = 400;
  }

  // 记录错误日志
  const logContext = {
    requestId: (req as any).requestId,
    method: req.method,
    url: req.url,
    statusCode,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  };

  if (statusCode >= 500) {
    // 服务器错误
    logger.error('服务器错误', error, logContext);
  } else if (statusCode >= 400) {
    // 客户端错误
    logger.warn('客户端错误', logContext);
  }

  // 格式化错误响应
  const errorResponse = formatErrorResponse(error, req, isDevelopment);

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

/**
 * 404错误处理中间件
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new NotFoundError(`路由 ${req.method} ${req.path} 不存在`);
  next(error);
}

/**
 * 异步路由错误捕获装饰器
 */
export function catchAsync(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 验证错误处理
 */
export function handleValidationError(errors: any[]): ValidationError {
  const details = errors.map(error => ({
    field: error.param || error.path,
    message: error.msg || error.message,
    value: error.value
  }));

  return new ValidationError('请求参数验证失败', details);
}

/**
 * LLM错误处理
 */
export function handleLLMError(error: any, provider: string): LLMError {
  let message = '语言模型服务错误';
  let details: any = {};

  // OpenAI错误处理
  if (provider === 'openai') {
    if (error.response?.data?.error) {
      const apiError = error.response.data.error;
      message = apiError.message || message;
      details = {
        type: apiError.type,
        code: apiError.code,
        param: apiError.param
      };
    } else if (error.code === 'ECONNREFUSED') {
      message = 'OpenAI服务连接失败';
    } else if (error.code === 'ETIMEDOUT') {
      message = 'OpenAI服务请求超时';
    }
  }

  // Anthropic错误处理
  if (provider === 'anthropic') {
    if (error.response?.data?.error) {
      const apiError = error.response.data.error;
      message = apiError.message || message;
      details = {
        type: apiError.type,
        code: apiError.code
      };
    } else if (error.code === 'ECONNREFUSED') {
      message = 'Anthropic服务连接失败';
    } else if (error.code === 'ETIMEDOUT') {
      message = 'Anthropic服务请求超时';
    }
  }

  return new LLMError(message, provider, details);
}

/**
 * Redis错误处理
 */
export function handleRedisError(error: any): ExternalServiceError {
  let message = 'Redis服务错误';

  if (error.code === 'ECONNREFUSED') {
    message = 'Redis服务连接失败';
  } else if (error.code === 'ETIMEDOUT') {
    message = 'Redis服务请求超时';
  } else if (error.message) {
    message = error.message;
  }

  return new ExternalServiceError(message, 'redis', {
    code: error.code,
    errno: error.errno
  });
}

/**
 * 数据库错误处理
 */
export function handleDatabaseError(error: any): AppError {
  let message = '数据库操作错误';
  let statusCode = 500;

  // 重复键错误
  if (error.code === 11000 || error.code === 'ER_DUP_ENTRY') {
    message = '数据已存在';
    statusCode = 409;
    return new ConflictError(message, { duplicateKey: error.keyValue });
  }

  // 验证错误
  if (error.name === 'ValidationError') {
    const details = Object.values(error.errors).map((err: any) => ({
      field: err.path,
      message: err.message,
      value: err.value
    }));
    return new ValidationError('数据验证失败', details);
  }

  // 转换错误
  if (error.name === 'CastError') {
    message = `无效的${error.path}: ${error.value}`;
    return new ValidationError(message);
  }

  return new AppError(message, statusCode, 'DATABASE_ERROR', true, {
    code: error.code,
    name: error.name
  });
}

/**
 * 全局未捕获异常处理
 */
export function setupGlobalErrorHandlers(): void {
  // 未捕获的异常
  process.on('uncaughtException', (error: Error) => {
    logger.error('未捕获的异常', error);
    
    // 优雅关闭
    process.exit(1);
  });

  // 未处理的Promise拒绝
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('未处理的Promise拒绝', new Error(reason), {
      promise: promise.toString()
    });
    
    // 优雅关闭
    process.exit(1);
  });

  // 警告处理
  process.on('warning', (warning: any) => {
    logger.warn('进程警告', {
      name: warning.name,
      message: warning.message,
      stack: warning.stack
    });
  });
}
