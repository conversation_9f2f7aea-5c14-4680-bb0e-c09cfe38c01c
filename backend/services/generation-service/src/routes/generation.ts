/**
 * 生成服务API路由
 */

import { Router, Request, Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { LLMManager, GenerationRequest, Message } from '../services/llmManager';
import { PromptManager, RAGContext } from '../services/promptManager';
import { RAGPipeline, RAGRequest } from '../services/ragPipeline';
import { catchAsync, handleValidationError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = Router();
const llmManager = LLMManager.getInstance();
const promptManager = PromptManager.getInstance();
const ragPipeline = new RAGPipeline();

/**
 * 验证中间件
 */
const validateGeneration = [
  body('messages')
    .isArray({ min: 1 })
    .withMessage('messages必须是非空数组'),
  body('messages.*.role')
    .isIn(['system', 'user', 'assistant'])
    .withMessage('消息角色必须是system、user或assistant'),
  body('messages.*.content')
    .isString()
    .isLength({ min: 1, max: 10000 })
    .withMessage('消息内容必须是1-10000字符的字符串'),
  body('model')
    .optional()
    .isString()
    .withMessage('模型名称必须是字符串'),
  body('maxTokens')
    .optional()
    .isInt({ min: 1, max: 4096 })
    .withMessage('maxTokens必须是1-4096的整数'),
  body('temperature')
    .optional()
    .isFloat({ min: 0, max: 2 })
    .withMessage('temperature必须是0-2的浮点数'),
  body('provider')
    .optional()
    .isIn(['openai', 'anthropic'])
    .withMessage('provider必须是openai或anthropic'),
];

const validateRAGGeneration = [
  body('query')
    .isString()
    .isLength({ min: 1, max: 1000 })
    .withMessage('查询必须是1-1000字符的字符串'),
  body('documents')
    .isArray({ min: 1 })
    .withMessage('documents必须是非空数组'),
  body('documents.*.content')
    .isString()
    .isLength({ min: 1 })
    .withMessage('文档内容不能为空'),
  body('templateId')
    .optional()
    .isString()
    .withMessage('模板ID必须是字符串'),
  body('conversationHistory')
    .optional()
    .isArray()
    .withMessage('对话历史必须是数组'),
];

/**
 * 基础文本生成
 */
router.post('/generate', validateGeneration, catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const request: GenerationRequest = {
    messages: req.body.messages,
    model: req.body.model,
    maxTokens: req.body.maxTokens,
    temperature: req.body.temperature,
    topP: req.body.topP,
    frequencyPenalty: req.body.frequencyPenalty,
    presencePenalty: req.body.presencePenalty,
    stop: req.body.stop,
    provider: req.body.provider,
  };

  const result = await llmManager.generate(request);

  res.json({
    success: true,
    data: result,
    message: '生成成功',
  });
}));

/**
 * 流式文本生成
 */
router.post('/generate/stream', validateGeneration, catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const request: GenerationRequest = {
    messages: req.body.messages,
    model: req.body.model,
    maxTokens: req.body.maxTokens,
    temperature: req.body.temperature,
    topP: req.body.topP,
    frequencyPenalty: req.body.frequencyPenalty,
    presencePenalty: req.body.presencePenalty,
    stop: req.body.stop,
    provider: req.body.provider,
    stream: true,
  };

  // 设置SSE响应头
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Access-Control-Allow-Origin', '*');

  try {
    for await (const chunk of llmManager.generateStream(request)) {
      const data = JSON.stringify(chunk);
      res.write(`data: ${data}\n\n`);
      
      if (chunk.finished) {
        break;
      }
    }
    
    res.write('data: [DONE]\n\n');
    res.end();
  } catch (error) {
    const errorData = JSON.stringify({
      error: {
        message: (error as Error).message,
        type: 'stream_error',
      },
    });
    res.write(`data: ${errorData}\n\n`);
    res.end();
  }
}));

/**
 * RAG问答生成
 */
router.post('/rag/qa', validateRAGGeneration, catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const context: RAGContext = {
    query: req.body.query,
    documents: req.body.documents,
    conversationHistory: req.body.conversationHistory,
    userProfile: req.body.userProfile,
    systemInstructions: req.body.systemInstructions,
  };

  const templateId = req.body.templateId || 'rag-qa';
  const options = {
    maxLength: req.body.maxLength,
    customVariables: req.body.customVariables,
  };

  // 渲染提示词
  const renderedPrompt = await promptManager.renderRAGPrompt(templateId, context, options);
  
  // 优化提示词长度
  const optimizedPrompt = await promptManager.optimizePromptLength(
    templateId,
    renderedPrompt.variables
  );

  // 构建消息
  const messages: Message[] = [
    { role: 'user', content: optimizedPrompt.content },
  ];

  // 生成回答
  const generationRequest: GenerationRequest = {
    messages,
    model: req.body.model,
    maxTokens: req.body.maxTokens,
    temperature: req.body.temperature,
    provider: req.body.provider,
  };

  const result = await llmManager.generate(generationRequest);

  res.json({
    success: true,
    data: {
      answer: result.content,
      context: {
        documentsUsed: context.documents.length,
        templateUsed: templateId,
        promptLength: optimizedPrompt.content.length,
      },
      generation: {
        model: result.model,
        provider: result.provider,
        usage: result.usage,
        responseTime: result.responseTime,
      },
    },
    message: 'RAG问答生成成功',
  });
}));

/**
 * RAG对话生成
 */
router.post('/rag/chat', validateRAGGeneration, catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const context: RAGContext = {
    query: req.body.query,
    documents: req.body.documents,
    conversationHistory: req.body.conversationHistory || [],
    userProfile: req.body.userProfile,
    systemInstructions: req.body.systemInstructions,
  };

  const templateId = req.body.templateId || 'rag-chat';
  
  // 渲染提示词
  const renderedPrompt = await promptManager.renderRAGPrompt(templateId, context);
  
  // 优化提示词长度
  const optimizedPrompt = await promptManager.optimizePromptLength(
    templateId,
    renderedPrompt.variables
  );

  // 构建消息
  const messages: Message[] = [
    { role: 'user', content: optimizedPrompt.content },
  ];

  // 生成回答
  const generationRequest: GenerationRequest = {
    messages,
    model: req.body.model,
    maxTokens: req.body.maxTokens,
    temperature: req.body.temperature || 0.7,
    provider: req.body.provider,
  };

  const result = await llmManager.generate(generationRequest);

  res.json({
    success: true,
    data: {
      response: result.content,
      conversationId: req.body.conversationId,
      context: {
        documentsUsed: context.documents.length,
        historyLength: context.conversationHistory?.length || 0,
        templateUsed: templateId,
      },
      generation: {
        model: result.model,
        provider: result.provider,
        usage: result.usage,
        responseTime: result.responseTime,
      },
    },
    message: 'RAG对话生成成功',
  });
}));

/**
 * RAG摘要生成
 */
router.post('/rag/summary', [
  body('documents').isArray({ min: 1 }).withMessage('documents必须是非空数组'),
  body('maxLength').optional().isInt({ min: 50, max: 2000 }).withMessage('摘要长度必须在50-2000字符之间'),
], catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const context: RAGContext = {
    query: '', // 摘要不需要查询
    documents: req.body.documents,
  };

  const options = {
    maxLength: req.body.maxLength || 500,
    customVariables: req.body.customVariables,
  };

  // 渲染提示词
  const renderedPrompt = await promptManager.renderRAGPrompt('rag-summary', context, options);

  // 构建消息
  const messages: Message[] = [
    { role: 'user', content: renderedPrompt.content },
  ];

  // 生成摘要
  const generationRequest: GenerationRequest = {
    messages,
    model: req.body.model,
    maxTokens: req.body.maxTokens || 1000,
    temperature: req.body.temperature || 0.3,
    provider: req.body.provider,
  };

  const result = await llmManager.generate(generationRequest);

  res.json({
    success: true,
    data: {
      summary: result.content,
      context: {
        documentsCount: context.documents.length,
        targetLength: options.maxLength,
      },
      generation: {
        model: result.model,
        provider: result.provider,
        usage: result.usage,
        responseTime: result.responseTime,
      },
    },
    message: 'RAG摘要生成成功',
  });
}));

/**
 * 获取可用的LLM提供商
 */
router.get('/providers', catchAsync(async (req: Request, res: Response) => {
  const providers = llmManager.getAvailableProviders();
  const healthStatus = await llmManager.healthCheck();

  res.json({
    success: true,
    data: {
      providers: providers.map(provider => ({
        name: provider,
        available: healthStatus[provider],
      })),
    },
    message: '获取提供商列表成功',
  });
}));

/**
 * 获取可用的模型
 */
router.get('/models', [
  query('provider').optional().isIn(['openai', 'anthropic']).withMessage('provider必须是openai或anthropic'),
], catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const provider = req.query.provider as 'openai' | 'anthropic' | undefined;
  const models = await llmManager.getModels(provider);

  res.json({
    success: true,
    data: { models },
    message: '获取模型列表成功',
  });
}));

/**
 * 获取提示词模板
 */
router.get('/templates', [
  query('category').optional().isString().withMessage('category必须是字符串'),
], catchAsync(async (req: Request, res: Response) => {
  // 验证请求参数
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors.array());
  }

  const category = req.query.category as string;
  
  let templates;
  if (category) {
    templates = promptManager.getTemplatesByCategory(category);
  } else {
    templates = promptManager.getAllTemplates();
  }

  res.json({
    success: true,
    data: {
      templates: templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        variables: template.variables,
        version: template.version,
      })),
    },
    message: '获取模板列表成功',
  });
}));

/**
 * 获取特定模板详情
 */
router.get('/templates/:id', catchAsync(async (req: Request, res: Response) => {
  const template = promptManager.getTemplate(req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      error: {
        code: 'TEMPLATE_NOT_FOUND',
        message: '模板不存在',
      },
    });
  }

  res.json({
    success: true,
    data: { template },
    message: '获取模板详情成功',
  });
}));

/**
 * 健康检查
 */
router.get('/health', catchAsync(async (req: Request, res: Response) => {
  const healthStatus = await llmManager.healthCheck();
  const allHealthy = Object.values(healthStatus).every(status => status);

  res.json({
    success: true,
    data: {
      status: allHealthy ? 'healthy' : 'degraded',
      providers: healthStatus,
      timestamp: new Date().toISOString(),
    },
    message: '健康检查完成',
  });
}));

/**
 * RAG问答接口
 */
router.post('/rag/question', [
  body('question')
    .isString()
    .isLength({ min: 1, max: 1000 })
    .withMessage('问题必须是1-1000字符的字符串'),
  body('retrievalTopK')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('retrievalTopK必须是1-20的整数'),
  body('maxContextLength')
    .optional()
    .isInt({ min: 100, max: 8000 })
    .withMessage('maxContextLength必须是100-8000的整数'),
  body('documentIds')
    .optional()
    .isArray()
    .withMessage('documentIds必须是数组'),
  handleValidationError,
], catchAsync(async (req: Request, res: Response) => {
  const { question, retrievalTopK, maxContextLength, documentIds, model, temperature, stream } = req.body;

  const ragRequest: RAGRequest = {
    question,
    retrievalTopK,
    maxContextLength,
    documentIds,
    model,
    temperature,
    stream: stream || false
  };

  if (stream) {
    // 流式响应
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    try {
      for await (const chunk of ragPipeline.generateStream(ragRequest)) {
        res.write(`data: ${JSON.stringify({ content: chunk, finished: false })}\n\n`);
      }
      res.write(`data: ${JSON.stringify({ content: '', finished: true })}\n\n`);
      res.end();
    } catch (error) {
      res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
      res.end();
    }
  } else {
    // 普通响应
    const result = await ragPipeline.generate(ragRequest);

    res.json({
      success: true,
      data: result,
      message: 'RAG问答完成'
    });
  }
}));

/**
 * 文档摘要接口
 */
router.post('/rag/summarize', [
  body('document')
    .isString()
    .isLength({ min: 10, max: 50000 })
    .withMessage('文档内容必须是10-50000字符的字符串'),
  body('maxLength')
    .optional()
    .isInt({ min: 50, max: 1000 })
    .withMessage('maxLength必须是50-1000的整数'),
  handleValidationError,
], catchAsync(async (req: Request, res: Response) => {
  const { document, maxLength } = req.body;

  const summary = await ragPipeline.summarizeDocument(document, maxLength);

  res.json({
    success: true,
    data: {
      summary,
      originalLength: document.length,
      summaryLength: summary.length
    },
    message: '文档摘要生成完成'
  });
}));

/**
 * 多轮对话RAG接口
 */
router.post('/rag/conversation', [
  body('question')
    .isString()
    .isLength({ min: 1, max: 1000 })
    .withMessage('问题必须是1-1000字符的字符串'),
  body('history')
    .isArray()
    .withMessage('对话历史必须是数组'),
  body('history.*.role')
    .isIn(['user', 'assistant'])
    .withMessage('历史消息角色必须是user或assistant'),
  body('history.*.content')
    .isString()
    .withMessage('历史消息内容必须是字符串'),
  handleValidationError,
], catchAsync(async (req: Request, res: Response) => {
  const { question, history, context } = req.body;

  const result = await ragPipeline.generateWithHistory(question, history, context);

  res.json({
    success: true,
    data: result,
    message: '多轮对话RAG完成'
  });
}));

export default router;
