/**
 * 提示词管理器
 * 管理和优化提示词模板
 */

import { config } from '../config';
import { logger, createPerformanceLogger } from '../utils/logger';
import { createCache } from '../config/redis';
import { ValidationError } from '../middleware/errorHandler';

/**
 * 提示词模板接口
 */
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: string[];
  category: string;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

/**
 * 提示词变量
 */
export interface PromptVariable {
  name: string;
  value: string;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  description?: string;
}

/**
 * 提示词渲染结果
 */
export interface RenderedPrompt {
  content: string;
  variables: Record<string, any>;
  template: PromptTemplate;
  renderTime: number;
}

/**
 * RAG提示词上下文
 */
export interface RAGContext {
  query: string;
  documents: Array<{
    content: string;
    metadata?: Record<string, any>;
    score?: number;
  }>;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp?: Date;
  }>;
  userProfile?: Record<string, any>;
  systemInstructions?: string;
}

/**
 * 内置提示词模板
 */
const BUILTIN_TEMPLATES: Record<string, PromptTemplate> = {
  'rag-qa': {
    id: 'rag-qa',
    name: 'RAG问答模板',
    description: '基于检索文档回答用户问题的标准模板',
    template: `你是一个有用的AI助手。请基于以下提供的上下文信息来回答用户的问题。

## 上下文信息
{{#each documents}}
### 文档 {{@index}}
{{content}}
{{#if metadata.source}}
来源: {{metadata.source}}
{{/if}}
{{#if score}}
相关度: {{score}}
{{/if}}

{{/each}}

## 对话历史
{{#each conversationHistory}}
{{#if (eq role "user")}}
用户: {{content}}
{{else}}
助手: {{content}}
{{/if}}
{{/each}}

## 回答要求
1. 请仅基于上述上下文信息回答问题
2. 如果上下文信息不足以回答问题，请明确说明
3. 回答要准确、简洁、有条理
4. 如果可能，请引用相关的文档来源

## 用户问题
{{query}}

请回答:`,
    variables: ['query', 'documents', 'conversationHistory'],
    category: 'rag',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  'rag-summary': {
    id: 'rag-summary',
    name: 'RAG摘要模板',
    description: '基于检索文档生成摘要的模板',
    template: `请基于以下文档内容，生成一个简洁准确的摘要。

## 文档内容
{{#each documents}}
### 文档 {{@index}}
{{content}}

{{/each}}

## 摘要要求
1. 提取关键信息和要点
2. 保持逻辑清晰，结构合理
3. 长度控制在{{maxLength}}字以内
4. 使用客观、准确的语言

请生成摘要:`,
    variables: ['documents', 'maxLength'],
    category: 'rag',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  'rag-chat': {
    id: 'rag-chat',
    name: 'RAG对话模板',
    description: '支持多轮对话的RAG模板',
    template: `你是一个智能助手，能够基于提供的知识库信息与用户进行自然对话。

## 知识库信息
{{#each documents}}
### 相关文档 {{@index}}
{{content}}
{{#if metadata.title}}
标题: {{metadata.title}}
{{/if}}
{{#if metadata.source}}
来源: {{metadata.source}}
{{/if}}

{{/each}}

## 对话历史
{{#each conversationHistory}}
{{#if (eq role "user")}}
用户: {{content}}
{{else}}
助手: {{content}}
{{/if}}
{{/each}}

## 对话指导原则
1. 基于知识库信息回答问题，确保准确性
2. 保持对话的连贯性和上下文理解
3. 如果知识库中没有相关信息，诚实说明
4. 用友好、专业的语调与用户交流
5. 必要时可以要求用户提供更多信息

## 当前用户输入
用户: {{query}}

助手:`,
    variables: ['query', 'documents', 'conversationHistory'],
    category: 'rag',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  'rag-analysis': {
    id: 'rag-analysis',
    name: 'RAG分析模板',
    description: '对检索到的文档进行深度分析的模板',
    template: `请对以下文档进行深度分析，并回答用户的问题。

## 分析文档
{{#each documents}}
### 文档 {{@index}}
{{content}}
{{#if metadata}}
元数据: {{json metadata}}
{{/if}}

{{/each}}

## 分析要求
1. 仔细阅读和理解文档内容
2. 识别关键信息、观点和数据
3. 分析文档之间的关联性
4. 基于分析结果回答用户问题
5. 提供有依据的结论和建议

## 用户问题
{{query}}

## 分析类型
{{analysisType}}

请进行分析:`,
    variables: ['query', 'documents', 'analysisType'],
    category: 'rag',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

/**
 * 提示词管理器
 */
export class PromptManager {
  private static instance: PromptManager;
  private templates: Map<string, PromptTemplate> = new Map();
  private cache = createCache();

  private constructor() {
    // 加载内置模板
    for (const template of Object.values(BUILTIN_TEMPLATES)) {
      this.templates.set(template.id, template);
    }
  }

  static getInstance(): PromptManager {
    if (!PromptManager.instance) {
      PromptManager.instance = new PromptManager();
    }
    return PromptManager.instance;
  }

  /**
   * 注册提示词模板
   */
  registerTemplate(template: PromptTemplate): void {
    this.validateTemplate(template);
    this.templates.set(template.id, template);
    logger.info(`注册提示词模板: ${template.id}`);
  }

  /**
   * 获取提示词模板
   */
  getTemplate(id: string): PromptTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * 按类别获取模板
   */
  getTemplatesByCategory(category: string): PromptTemplate[] {
    return Array.from(this.templates.values()).filter(
      template => template.category === category
    );
  }

  /**
   * 渲染提示词
   */
  async renderPrompt(
    templateId: string,
    variables: Record<string, any>
  ): Promise<RenderedPrompt> {
    const perfLogger = createPerformanceLogger('render_prompt');
    
    try {
      const template = this.getTemplate(templateId);
      if (!template) {
        throw new ValidationError(`提示词模板不存在: ${templateId}`);
      }

      // 检查缓存
      const cacheKey = this.generateCacheKey(templateId, variables);
      if (config.cache.enabled) {
        const cached = await this.cache.get<RenderedPrompt>(cacheKey);
        if (cached) {
          perfLogger.finish({ cached: true });
          return cached;
        }
      }

      // 验证变量
      this.validateVariables(template, variables);

      // 渲染模板
      const startTime = Date.now();
      const content = await this.renderTemplate(template.template, variables);
      const renderTime = Date.now() - startTime;

      const result: RenderedPrompt = {
        content,
        variables,
        template,
        renderTime,
      };

      // 缓存结果
      if (config.cache.enabled) {
        await this.cache.set(cacheKey, result, config.cache.ttl);
      }

      perfLogger.finish({ renderTime, templateId });
      return result;
    } catch (error) {
      perfLogger.fail(error as Error);
      throw error;
    }
  }

  /**
   * 渲染RAG提示词
   */
  async renderRAGPrompt(
    templateId: string,
    context: RAGContext,
    options?: {
      maxLength?: number;
      analysisType?: string;
      customVariables?: Record<string, any>;
    }
  ): Promise<RenderedPrompt> {
    const variables: Record<string, any> = {
      query: context.query,
      documents: context.documents,
      conversationHistory: context.conversationHistory || [],
      userProfile: context.userProfile || {},
      systemInstructions: context.systemInstructions || config.prompts.systemPrompt,
      ...options?.customVariables,
    };

    // 添加选项变量
    if (options?.maxLength) {
      variables.maxLength = options.maxLength;
    }
    if (options?.analysisType) {
      variables.analysisType = options.analysisType;
    }

    return this.renderPrompt(templateId, variables);
  }

  /**
   * 优化提示词长度
   */
  async optimizePromptLength(
    templateId: string,
    variables: Record<string, any>,
    maxLength: number = config.prompts.maxContextLength
  ): Promise<RenderedPrompt> {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new ValidationError(`提示词模板不存在: ${templateId}`);
    }

    // 首先尝试正常渲染
    let result = await this.renderPrompt(templateId, variables);
    
    if (result.content.length <= maxLength) {
      return result;
    }

    logger.info(`提示词长度超限，开始优化: ${result.content.length} > ${maxLength}`);

    // 优化策略1: 截断文档内容
    if (variables.documents && Array.isArray(variables.documents)) {
      const optimizedVariables = { ...variables };
      const documents = [...variables.documents];
      
      // 按相关度排序（如果有分数）
      documents.sort((a, b) => (b.score || 0) - (a.score || 0));
      
      // 逐步减少文档内容
      while (result.content.length > maxLength && documents.length > 0) {
        // 移除最后一个文档或截断最长的文档
        if (documents.length > 1) {
          documents.pop();
        } else {
          // 截断最后一个文档
          const lastDoc = documents[0];
          const maxDocLength = Math.floor(lastDoc.content.length * 0.8);
          lastDoc.content = lastDoc.content.substring(0, maxDocLength) + '...';
        }
        
        optimizedVariables.documents = documents;
        result = await this.renderPrompt(templateId, optimizedVariables);
      }
    }

    // 优化策略2: 截断对话历史
    if (result.content.length > maxLength && variables.conversationHistory) {
      const optimizedVariables = { ...variables };
      const history = [...variables.conversationHistory];
      
      // 保留最近的对话
      while (result.content.length > maxLength && history.length > 2) {
        history.shift(); // 移除最早的对话
        optimizedVariables.conversationHistory = history;
        result = await this.renderPrompt(templateId, optimizedVariables);
      }
    }

    if (result.content.length > maxLength) {
      logger.warn(`提示词优化后仍超长: ${result.content.length}`);
    }

    return result;
  }

  /**
   * 验证模板
   */
  private validateTemplate(template: PromptTemplate): void {
    if (!template.id || !template.name || !template.template) {
      throw new ValidationError('提示词模板缺少必需字段');
    }

    if (template.id.length > 100) {
      throw new ValidationError('模板ID过长');
    }

    if (template.template.length > 50000) {
      throw new ValidationError('模板内容过长');
    }
  }

  /**
   * 验证变量
   */
  private validateVariables(template: PromptTemplate, variables: Record<string, any>): void {
    const missingVariables = template.variables.filter(
      variable => !(variable in variables)
    );

    if (missingVariables.length > 0) {
      throw new ValidationError(
        `缺少必需的模板变量: ${missingVariables.join(', ')}`
      );
    }
  }

  /**
   * 渲染模板
   */
  private async renderTemplate(template: string, variables: Record<string, any>): Promise<string> {
    // 使用Handlebars进行模板渲染
    const Handlebars = require('handlebars');
    
    // 注册辅助函数
    Handlebars.registerHelper('eq', (a: any, b: any) => a === b);
    Handlebars.registerHelper('json', (obj: any) => JSON.stringify(obj, null, 2));
    
    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate(variables);
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(templateId: string, variables: Record<string, any>): string {
    const variablesHash = require('crypto')
      .createHash('md5')
      .update(JSON.stringify(variables))
      .digest('hex');
    
    return `prompt:${templateId}:${variablesHash}`;
  }

  /**
   * 清除缓存
   */
  async clearCache(templateId?: string): Promise<void> {
    if (templateId) {
      await this.cache.delPattern(`prompt:${templateId}:*`);
    } else {
      await this.cache.delPattern('prompt:*');
    }
  }

  /**
   * 获取缓存统计
   */
  async getCacheStats(): Promise<{ hits: number; misses: number; size: number }> {
    // 这里需要实现缓存统计逻辑
    return { hits: 0, misses: 0, size: 0 };
  }
}
