/**
 * RAG生成管道
 * 整合检索和生成功能，提供端到端的RAG服务
 */

import axios from 'axios';
import { LLMManager, GenerationRequest, GenerationResponse, Message } from './llmManager';
import { logger } from '../utils/logger';
import { config } from '../config';

/**
 * 检索结果接口
 */
export interface RetrievalResult {
  id: string;
  content: string;
  score: number;
  metadata?: Record<string, any>;
  source?: string;
}

/**
 * RAG请求参数
 */
export interface RAGRequest {
  question: string;
  context?: RetrievalResult[];
  maxContextLength?: number;
  retrievalTopK?: number;
  model?: string;
  temperature?: number;
  stream?: boolean;
  includeSourceCitations?: boolean;
  documentIds?: string[];
}

/**
 * RAG响应
 */
export interface RAGResponse {
  answer: string;
  sources: string[];
  confidence: number;
  retrievalResults: RetrievalResult[];
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  responseTime: number;
}

/**
 * 提示词模板
 */
export class PromptTemplate {
  static readonly RAG_QA = `你是一个专业的AI助手，请基于提供的文档内容回答用户问题。

请遵循以下原则：
1. 仅基于提供的文档内容回答，不要添加文档中没有的信息
2. 如果文档中没有相关信息，请明确说明"根据提供的文档，我无法找到相关信息"
3. 保持回答准确、简洁、有用
4. 如果需要，可以引用具体的文档片段
5. 使用中文回答

文档内容：
{context}

用户问题：{question}

请基于上述文档内容回答用户问题：`;

  static readonly SUMMARIZATION = `请为以下文档生成简洁的摘要：

文档内容：
{document}

摘要要求：
- 长度控制在200字以内
- 突出关键信息和要点
- 使用中文
- 保持客观准确

摘要：`;

  static readonly CONTEXT_FUSION = `以下是多个相关文档片段，请将它们融合成一个连贯的上下文：

文档片段：
{fragments}

请将这些片段整合成一个连贯的上下文，去除重复信息，保持逻辑清晰：`;
}

/**
 * RAG生成管道
 */
export class RAGPipeline {
  private llmManager: LLMManager;
  private retrievalServiceUrl: string;
  private documentServiceUrl: string;

  constructor() {
    this.llmManager = new LLMManager();
    this.retrievalServiceUrl = config.services.retrieval || 'http://localhost:8003';
    this.documentServiceUrl = config.services.document || 'http://localhost:8001';
  }

  /**
   * 执行RAG生成
   */
  async generate(request: RAGRequest): Promise<RAGResponse> {
    const startTime = Date.now();
    
    try {
      logger.info('开始RAG生成', { question: request.question });

      // 1. 检索相关文档（如果没有提供上下文）
      let retrievalResults = request.context || [];
      if (retrievalResults.length === 0) {
        retrievalResults = await this.retrieveDocuments(
          request.question,
          request.retrievalTopK || 5,
          request.documentIds
        );
      }

      // 2. 构建上下文
      const context = await this.buildContext(
        retrievalResults,
        request.maxContextLength || 4000
      );

      // 3. 生成回答
      const generationRequest: GenerationRequest = {
        messages: [
          {
            role: 'user',
            content: PromptTemplate.RAG_QA
              .replace('{context}', context)
              .replace('{question}', request.question)
          }
        ],
        model: request.model,
        temperature: request.temperature || 0.7,
        stream: request.stream || false
      };

      const generationResponse = await this.llmManager.generate(generationRequest);

      // 4. 计算置信度
      const confidence = this.calculateConfidence(
        retrievalResults,
        generationResponse.content
      );

      // 5. 提取来源
      const sources = this.extractSources(retrievalResults);

      const responseTime = Date.now() - startTime;

      const response: RAGResponse = {
        answer: generationResponse.content,
        sources,
        confidence,
        retrievalResults,
        model: generationResponse.model,
        usage: generationResponse.usage,
        responseTime
      };

      logger.info('RAG生成完成', {
        responseTime,
        confidence,
        sourcesCount: sources.length
      });

      return response;

    } catch (error) {
      logger.error('RAG生成失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 流式RAG生成
   */
  async *generateStream(request: RAGRequest): AsyncGenerator<string, void, unknown> {
    try {
      // 1. 检索文档和构建上下文（同步部分）
      let retrievalResults = request.context || [];
      if (retrievalResults.length === 0) {
        retrievalResults = await this.retrieveDocuments(
          request.question,
          request.retrievalTopK || 5,
          request.documentIds
        );
      }

      const context = await this.buildContext(
        retrievalResults,
        request.maxContextLength || 4000
      );

      // 2. 流式生成
      const generationRequest: GenerationRequest = {
        messages: [
          {
            role: 'user',
            content: PromptTemplate.RAG_QA
              .replace('{context}', context)
              .replace('{question}', request.question)
          }
        ],
        model: request.model,
        temperature: request.temperature || 0.7,
        stream: true
      };

      // 3. 流式输出
      for await (const chunk of this.llmManager.generateStream(generationRequest)) {
        yield chunk;
      }

    } catch (error) {
      logger.error('流式RAG生成失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检索相关文档
   */
  private async retrieveDocuments(
    query: string,
    topK: number = 5,
    documentIds?: string[]
  ): Promise<RetrievalResult[]> {
    try {
      const searchParams = {
        query,
        top_k: topK,
        search_type: 'hybrid',
        document_ids: documentIds
      };

      const response = await axios.get(`${this.retrievalServiceUrl}/api/v1/search`, {
        params: searchParams,
        timeout: 10000
      });

      if (response.data.success) {
        return response.data.data.results.map((result: any) => ({
          id: result.id,
          content: result.text || result.content,
          score: result.score,
          metadata: result.metadata,
          source: result.source || result.document_name
        }));
      } else {
        throw new Error(`检索失败: ${response.data.message}`);
      }

    } catch (error) {
      logger.error('文档检索失败', { error: error.message, query });
      // 如果检索失败，返回空结果而不是抛出错误
      return [];
    }
  }

  /**
   * 构建上下文
   */
  private async buildContext(
    retrievalResults: RetrievalResult[],
    maxLength: number
  ): Promise<string> {
    if (retrievalResults.length === 0) {
      return '没有找到相关文档内容。';
    }

    // 按相关性分数排序
    const sortedResults = retrievalResults.sort((a, b) => b.score - a.score);

    let context = '';
    let currentLength = 0;

    for (const result of sortedResults) {
      const fragment = `[文档片段 ${result.id}]${result.source ? ` (来源: ${result.source})` : ''}\n${result.content}\n\n`;
      
      if (currentLength + fragment.length > maxLength) {
        break;
      }

      context += fragment;
      currentLength += fragment.length;
    }

    return context.trim();
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(
    retrievalResults: RetrievalResult[],
    generatedAnswer: string
  ): number {
    if (retrievalResults.length === 0) {
      return 0.1; // 没有检索结果，置信度很低
    }

    // 基于检索结果的平均分数
    const avgScore = retrievalResults.reduce((sum, result) => sum + result.score, 0) / retrievalResults.length;
    
    // 基于回答长度（合理长度的回答通常更可信）
    const answerLengthFactor = Math.min(generatedAnswer.length / 200, 1);
    
    // 基于检索结果数量
    const resultCountFactor = Math.min(retrievalResults.length / 3, 1);

    // 综合计算置信度
    const confidence = (avgScore * 0.6 + answerLengthFactor * 0.2 + resultCountFactor * 0.2);
    
    return Math.max(0.1, Math.min(0.95, confidence)); // 限制在0.1-0.95之间
  }

  /**
   * 提取来源信息
   */
  private extractSources(retrievalResults: RetrievalResult[]): string[] {
    const sources = new Set<string>();
    
    for (const result of retrievalResults) {
      if (result.source) {
        sources.add(result.source);
      } else if (result.metadata?.source) {
        sources.add(result.metadata.source);
      } else if (result.metadata?.document_name) {
        sources.add(result.metadata.document_name);
      }
    }

    return Array.from(sources);
  }

  /**
   * 文档摘要生成
   */
  async summarizeDocument(document: string, maxLength: number = 200): Promise<string> {
    try {
      const generationRequest: GenerationRequest = {
        messages: [
          {
            role: 'user',
            content: PromptTemplate.SUMMARIZATION.replace('{document}', document)
          }
        ],
        temperature: 0.3,
        maxTokens: Math.floor(maxLength * 1.5) // 估算token数量
      };

      const response = await this.llmManager.generate(generationRequest);
      return response.content;

    } catch (error) {
      logger.error('文档摘要生成失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 多轮对话RAG
   */
  async generateWithHistory(
    question: string,
    conversationHistory: Message[],
    context?: RetrievalResult[]
  ): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      // 1. 检索相关文档
      let retrievalResults = context || [];
      if (retrievalResults.length === 0) {
        retrievalResults = await this.retrieveDocuments(question, 5);
      }

      // 2. 构建上下文
      const documentContext = await this.buildContext(retrievalResults, 3000);

      // 3. 构建包含历史对话的消息
      const messages: Message[] = [
        {
          role: 'system',
          content: `你是一个专业的AI助手，请基于提供的文档内容和对话历史回答用户问题。

文档内容：
${documentContext}

请基于上述文档内容和对话历史回答用户的最新问题。`
        },
        ...conversationHistory,
        {
          role: 'user',
          content: question
        }
      ];

      // 4. 生成回答
      const generationRequest: GenerationRequest = {
        messages,
        temperature: 0.7
      };

      const generationResponse = await this.llmManager.generate(generationRequest);

      // 5. 构建响应
      const confidence = this.calculateConfidence(retrievalResults, generationResponse.content);
      const sources = this.extractSources(retrievalResults);
      const responseTime = Date.now() - startTime;

      return {
        answer: generationResponse.content,
        sources,
        confidence,
        retrievalResults,
        model: generationResponse.model,
        usage: generationResponse.usage,
        responseTime
      };

    } catch (error) {
      logger.error('多轮对话RAG生成失败', { error: error.message });
      throw error;
    }
  }
}
