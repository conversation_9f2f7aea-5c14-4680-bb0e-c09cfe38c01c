/**
 * LLM管理器
 * 统一管理多个LLM提供商
 */

import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { config, getLLMConfig } from '../config';
import { logger, createPerformanceLogger } from '../utils/logger';
import { LLMError, TimeoutError } from '../middleware/errorHandler';
import { createCache } from '../config/redis';

/**
 * LLM提供商类型
 */
export type LLMProvider = 'openai' | 'anthropic';

/**
 * 消息角色
 */
export type MessageRole = 'system' | 'user' | 'assistant';

/**
 * 消息接口
 */
export interface Message {
  role: MessageRole;
  content: string;
}

/**
 * 生成请求参数
 */
export interface GenerationRequest {
  messages: Message[];
  model?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
  provider?: LLMProvider;
}

/**
 * 生成响应
 */
export interface GenerationResponse {
  content: string;
  model: string;
  provider: LLMProvider;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason: string;
  responseTime: number;
}

/**
 * 流式响应块
 */
export interface StreamChunk {
  content: string;
  delta: string;
  finished: boolean;
  model: string;
  provider: LLMProvider;
}

/**
 * LLM客户端抽象类
 */
abstract class LLMClient {
  protected config: any;
  protected cache = createCache();

  constructor(config: any) {
    this.config = config;
  }

  abstract generate(request: GenerationRequest): Promise<GenerationResponse>;
  abstract generateStream(request: GenerationRequest): AsyncGenerator<StreamChunk>;
  abstract isAvailable(): Promise<boolean>;
  abstract getModels(): Promise<string[]>;
}

/**
 * OpenAI客户端
 */
class OpenAIClient extends LLMClient {
  private client: OpenAI;

  constructor(config: any) {
    super(config);
    
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      organization: config.organization,
      timeout: config.timeout,
      maxRetries: 3,
    });
  }

  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    const perfLogger = createPerformanceLogger('openai_generate');
    
    try {
      const startTime = Date.now();
      
      const response = await this.client.chat.completions.create({
        model: request.model || this.config.defaultModel,
        messages: request.messages as OpenAI.Chat.Completions.ChatCompletionMessageParam[],
        max_tokens: request.maxTokens || this.config.maxTokens,
        temperature: request.temperature ?? this.config.temperature,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: false,
      });

      const responseTime = Date.now() - startTime;
      const choice = response.choices[0];
      
      if (!choice?.message?.content) {
        throw new LLMError('OpenAI返回空内容', 'openai');
      }

      const result: GenerationResponse = {
        content: choice.message.content,
        model: response.model,
        provider: 'openai',
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0,
        },
        finishReason: choice.finish_reason || 'unknown',
        responseTime,
      };

      perfLogger.finish({ 
        model: result.model, 
        tokens: result.usage.totalTokens,
        responseTime 
      });

      return result;
    } catch (error: any) {
      perfLogger.fail(error);
      
      if (error.code === 'ETIMEDOUT') {
        throw new TimeoutError('OpenAI请求超时', 'openai_generate');
      }
      
      throw new LLMError(
        error.message || 'OpenAI生成失败',
        'openai',
        { 
          code: error.code,
          type: error.type,
          status: error.status 
        }
      );
    }
  }

  async *generateStream(request: GenerationRequest): AsyncGenerator<StreamChunk> {
    const perfLogger = createPerformanceLogger('openai_generate_stream');
    
    try {
      const stream = await this.client.chat.completions.create({
        model: request.model || this.config.defaultModel,
        messages: request.messages as OpenAI.Chat.Completions.ChatCompletionMessageParam[],
        max_tokens: request.maxTokens || this.config.maxTokens,
        temperature: request.temperature ?? this.config.temperature,
        top_p: request.topP,
        frequency_penalty: request.frequencyPenalty,
        presence_penalty: request.presencePenalty,
        stop: request.stop,
        stream: true,
      });

      let content = '';
      
      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        const delta = choice?.delta?.content || '';
        
        if (delta) {
          content += delta;
          
          yield {
            content,
            delta,
            finished: false,
            model: chunk.model,
            provider: 'openai',
          };
        }
        
        if (choice?.finish_reason) {
          yield {
            content,
            delta: '',
            finished: true,
            model: chunk.model,
            provider: 'openai',
          };
          break;
        }
      }

      perfLogger.finish({ model: request.model });
    } catch (error: any) {
      perfLogger.fail(error);
      throw new LLMError(
        error.message || 'OpenAI流式生成失败',
        'openai',
        { code: error.code, type: error.type }
      );
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      logger.warn('OpenAI服务不可用', { error });
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      logger.error('获取OpenAI模型列表失败', { error });
      return [];
    }
  }
}

/**
 * Anthropic客户端
 */
class AnthropicClient extends LLMClient {
  private client: Anthropic;

  constructor(config: any) {
    super(config);
    
    this.client = new Anthropic({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      timeout: config.timeout,
      maxRetries: 3,
    });
  }

  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    const perfLogger = createPerformanceLogger('anthropic_generate');
    
    try {
      const startTime = Date.now();
      
      // 转换消息格式
      const systemMessage = request.messages.find(m => m.role === 'system');
      const messages = request.messages
        .filter(m => m.role !== 'system')
        .map(m => ({ role: m.role as 'user' | 'assistant', content: m.content }));

      const response = await this.client.messages.create({
        model: request.model || this.config.defaultModel,
        max_tokens: request.maxTokens || this.config.maxTokens,
        temperature: request.temperature ?? this.config.temperature,
        top_p: request.topP,
        system: systemMessage?.content,
        messages,
        stop_sequences: request.stop,
        stream: false,
      });

      const responseTime = Date.now() - startTime;
      
      if (!response.content[0] || response.content[0].type !== 'text') {
        throw new LLMError('Anthropic返回空内容', 'anthropic');
      }

      const result: GenerationResponse = {
        content: response.content[0].text,
        model: response.model,
        provider: 'anthropic',
        usage: {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens,
        },
        finishReason: response.stop_reason || 'unknown',
        responseTime,
      };

      perfLogger.finish({ 
        model: result.model, 
        tokens: result.usage.totalTokens,
        responseTime 
      });

      return result;
    } catch (error: any) {
      perfLogger.fail(error);
      
      if (error.code === 'ETIMEDOUT') {
        throw new TimeoutError('Anthropic请求超时', 'anthropic_generate');
      }
      
      throw new LLMError(
        error.message || 'Anthropic生成失败',
        'anthropic',
        { 
          code: error.code,
          type: error.type,
          status: error.status 
        }
      );
    }
  }

  async *generateStream(request: GenerationRequest): AsyncGenerator<StreamChunk> {
    const perfLogger = createPerformanceLogger('anthropic_generate_stream');
    
    try {
      const systemMessage = request.messages.find(m => m.role === 'system');
      const messages = request.messages
        .filter(m => m.role !== 'system')
        .map(m => ({ role: m.role as 'user' | 'assistant', content: m.content }));

      const stream = await this.client.messages.create({
        model: request.model || this.config.defaultModel,
        max_tokens: request.maxTokens || this.config.maxTokens,
        temperature: request.temperature ?? this.config.temperature,
        top_p: request.topP,
        system: systemMessage?.content,
        messages,
        stop_sequences: request.stop,
        stream: true,
      });

      let content = '';
      
      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          const delta = chunk.delta.text;
          content += delta;
          
          yield {
            content,
            delta,
            finished: false,
            model: request.model || this.config.defaultModel,
            provider: 'anthropic',
          };
        }
        
        if (chunk.type === 'message_stop') {
          yield {
            content,
            delta: '',
            finished: true,
            model: request.model || this.config.defaultModel,
            provider: 'anthropic',
          };
          break;
        }
      }

      perfLogger.finish({ model: request.model });
    } catch (error: any) {
      perfLogger.fail(error);
      throw new LLMError(
        error.message || 'Anthropic流式生成失败',
        'anthropic',
        { code: error.code, type: error.type }
      );
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Anthropic没有直接的健康检查接口，尝试一个简单的请求
      await this.client.messages.create({
        model: this.config.defaultModel,
        max_tokens: 1,
        messages: [{ role: 'user', content: 'test' }],
      });
      return true;
    } catch (error) {
      logger.warn('Anthropic服务不可用', { error });
      return false;
    }
  }

  async getModels(): Promise<string[]> {
    // Anthropic没有公开的模型列表API，返回已知模型
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-2.1',
      'claude-2.0',
      'claude-instant-1.2',
    ];
  }
}

/**
 * LLM管理器
 */
export class LLMManager {
  private static instance: LLMManager;
  private clients: Map<LLMProvider, LLMClient> = new Map();
  private cache = createCache();

  private constructor() {}

  static getInstance(): LLMManager {
    if (!LLMManager.instance) {
      LLMManager.instance = new LLMManager();
    }
    return LLMManager.instance;
  }

  /**
   * 初始化LLM客户端
   */
  async initialize(): Promise<void> {
    // 初始化OpenAI客户端
    if (config.llm.openai.apiKey) {
      const openaiClient = new OpenAIClient(config.llm.openai);
      this.clients.set('openai', openaiClient);
      logger.info('OpenAI客户端初始化成功');
    }

    // 初始化Anthropic客户端
    if (config.llm.anthropic.apiKey) {
      const anthropicClient = new AnthropicClient(config.llm.anthropic);
      this.clients.set('anthropic', anthropicClient);
      logger.info('Anthropic客户端初始化成功');
    }

    if (this.clients.size === 0) {
      throw new Error('没有可用的LLM客户端，请检查API密钥配置');
    }

    // 检查客户端可用性
    await this.checkAvailability();
  }

  /**
   * 检查客户端可用性
   */
  private async checkAvailability(): Promise<void> {
    const availabilityChecks = Array.from(this.clients.entries()).map(
      async ([provider, client]) => {
        try {
          const isAvailable = await client.isAvailable();
          logger.info(`${provider} 客户端可用性: ${isAvailable}`);
          return { provider, isAvailable };
        } catch (error) {
          logger.error(`检查 ${provider} 客户端可用性失败`, { error });
          return { provider, isAvailable: false };
        }
      }
    );

    const results = await Promise.all(availabilityChecks);
    const availableClients = results.filter(r => r.isAvailable);

    if (availableClients.length === 0) {
      logger.warn('没有可用的LLM客户端');
    }
  }

  /**
   * 生成文本
   */
  async generate(request: GenerationRequest): Promise<GenerationResponse> {
    const provider = request.provider || config.llm.defaultProvider;
    const client = this.clients.get(provider);

    if (!client) {
      throw new LLMError(`LLM提供商 ${provider} 不可用`, provider);
    }

    try {
      return await client.generate(request);
    } catch (error) {
      // 如果启用了回退机制，尝试其他提供商
      if (config.llm.enableFallback && this.clients.size > 1) {
        logger.warn(`${provider} 生成失败，尝试回退`, { error });
        
        for (const [fallbackProvider, fallbackClient] of this.clients) {
          if (fallbackProvider !== provider) {
            try {
              logger.info(`使用回退提供商: ${fallbackProvider}`);
              const fallbackRequest = { ...request, provider: fallbackProvider };
              return await fallbackClient.generate(fallbackRequest);
            } catch (fallbackError) {
              logger.warn(`回退提供商 ${fallbackProvider} 也失败`, { fallbackError });
            }
          }
        }
      }
      
      throw error;
    }
  }

  /**
   * 流式生成文本
   */
  async *generateStream(request: GenerationRequest): AsyncGenerator<StreamChunk> {
    const provider = request.provider || config.llm.defaultProvider;
    const client = this.clients.get(provider);

    if (!client) {
      throw new LLMError(`LLM提供商 ${provider} 不可用`, provider);
    }

    yield* client.generateStream(request);
  }

  /**
   * 获取可用的提供商
   */
  getAvailableProviders(): LLMProvider[] {
    return Array.from(this.clients.keys());
  }

  /**
   * 获取提供商的模型列表
   */
  async getModels(provider?: LLMProvider): Promise<Record<LLMProvider, string[]>> {
    const result: Record<LLMProvider, string[]> = {} as any;

    if (provider) {
      const client = this.clients.get(provider);
      if (client) {
        result[provider] = await client.getModels();
      }
    } else {
      for (const [providerName, client] of this.clients) {
        result[providerName] = await client.getModels();
      }
    }

    return result;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<Record<LLMProvider, boolean>> {
    const result: Record<LLMProvider, boolean> = {} as any;

    for (const [provider, client] of this.clients) {
      result[provider] = await client.isAvailable();
    }

    return result;
  }
}
