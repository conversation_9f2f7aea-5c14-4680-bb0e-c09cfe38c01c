/**
 * Redis连接配置
 */

import Redis from 'ioredis';
import { config } from './index';
import { logger } from '../utils/logger';

let redisClient: Redis | null = null;

/**
 * 创建Redis客户端
 */
export function createRedisClient(): Redis {
  const client = new Redis({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    db: config.redis.db,
    keyPrefix: config.redis.keyPrefix,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
  });

  // 连接事件监听
  client.on('connect', () => {
    logger.info('Redis连接建立');
  });

  client.on('ready', () => {
    logger.info('Redis连接就绪');
  });

  client.on('error', (error) => {
    logger.error('Redis连接错误:', error);
  });

  client.on('close', () => {
    logger.warn('Redis连接关闭');
  });

  client.on('reconnecting', () => {
    logger.info('Redis重新连接中...');
  });

  return client;
}

/**
 * 连接Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    redisClient = createRedisClient();
    await redisClient.connect();
    
    // 测试连接
    await redisClient.ping();
    logger.info('Redis连接测试成功');
  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 获取Redis客户端
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    throw new Error('Redis客户端未初始化');
  }
  return redisClient;
}

/**
 * 关闭Redis连接
 */
export async function closeRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Redis连接已关闭');
  }
}

/**
 * Redis缓存操作类
 */
export class RedisCache {
  private client: Redis;

  constructor(client: Redis) {
    this.client = client;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    
    if (ttl) {
      await this.client.setex(key, ttl, serializedValue);
    } else {
      await this.client.set(key, serializedValue);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    const value = await this.client.get(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('缓存值解析失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    await this.client.expire(key, ttl);
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    return await this.client.ttl(key);
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const values = await this.client.mget(...keys);
    
    return values.map(value => {
      if (!value) {
        return null;
      }

      try {
        return JSON.parse(value) as T;
      } catch (error) {
        logger.error('批量缓存值解析失败:', error);
        return null;
      }
    });
  }

  /**
   * 批量设置
   */
  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<void> {
    const pipeline = this.client.pipeline();
    
    for (const [key, value] of Object.entries(keyValuePairs)) {
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        pipeline.setex(key, ttl, serializedValue);
      } else {
        pipeline.set(key, serializedValue);
      }
    }
    
    await pipeline.exec();
  }

  /**
   * 模糊匹配删除
   */
  async delPattern(pattern: string): Promise<number> {
    const keys = await this.client.keys(pattern);
    
    if (keys.length === 0) {
      return 0;
    }
    
    return await this.client.del(...keys);
  }

  /**
   * 增加计数器
   */
  async incr(key: string, increment: number = 1): Promise<number> {
    if (increment === 1) {
      return await this.client.incr(key);
    } else {
      return await this.client.incrby(key, increment);
    }
  }

  /**
   * 减少计数器
   */
  async decr(key: string, decrement: number = 1): Promise<number> {
    if (decrement === 1) {
      return await this.client.decr(key);
    } else {
      return await this.client.decrby(key, decrement);
    }
  }

  /**
   * 列表操作 - 左推入
   */
  async lpush(key: string, ...values: any[]): Promise<number> {
    const serializedValues = values.map(v => JSON.stringify(v));
    return await this.client.lpush(key, ...serializedValues);
  }

  /**
   * 列表操作 - 右推入
   */
  async rpush(key: string, ...values: any[]): Promise<number> {
    const serializedValues = values.map(v => JSON.stringify(v));
    return await this.client.rpush(key, ...serializedValues);
  }

  /**
   * 列表操作 - 左弹出
   */
  async lpop<T>(key: string): Promise<T | null> {
    const value = await this.client.lpop(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('列表值解析失败:', error);
      return null;
    }
  }

  /**
   * 列表操作 - 右弹出
   */
  async rpop<T>(key: string): Promise<T | null> {
    const value = await this.client.rpop(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('列表值解析失败:', error);
      return null;
    }
  }

  /**
   * 列表操作 - 获取范围
   */
  async lrange<T>(key: string, start: number, stop: number): Promise<T[]> {
    const values = await this.client.lrange(key, start, stop);
    
    return values.map(value => {
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        logger.error('列表值解析失败:', error);
        return null;
      }
    }).filter(v => v !== null) as T[];
  }

  /**
   * 集合操作 - 添加成员
   */
  async sadd(key: string, ...members: any[]): Promise<number> {
    const serializedMembers = members.map(m => JSON.stringify(m));
    return await this.client.sadd(key, ...serializedMembers);
  }

  /**
   * 集合操作 - 获取所有成员
   */
  async smembers<T>(key: string): Promise<T[]> {
    const members = await this.client.smembers(key);
    
    return members.map(member => {
      try {
        return JSON.parse(member) as T;
      } catch (error) {
        logger.error('集合成员解析失败:', error);
        return null;
      }
    }).filter(m => m !== null) as T[];
  }

  /**
   * 有序集合操作 - 添加成员
   */
  async zadd(key: string, score: number, member: any): Promise<number> {
    const serializedMember = JSON.stringify(member);
    return await this.client.zadd(key, score, serializedMember);
  }

  /**
   * 有序集合操作 - 获取范围
   */
  async zrange<T>(key: string, start: number, stop: number, withScores: boolean = false): Promise<T[] | Array<{member: T, score: number}>> {
    let result;
    
    if (withScores) {
      result = await this.client.zrange(key, start, stop, 'WITHSCORES');
      const pairs: Array<{member: T, score: number}> = [];
      
      for (let i = 0; i < result.length; i += 2) {
        try {
          const member = JSON.parse(result[i]) as T;
          const score = parseFloat(result[i + 1]);
          pairs.push({ member, score });
        } catch (error) {
          logger.error('有序集合成员解析失败:', error);
        }
      }
      
      return pairs;
    } else {
      result = await this.client.zrange(key, start, stop);
      
      return result.map(member => {
        try {
          return JSON.parse(member) as T;
        } catch (error) {
          logger.error('有序集合成员解析失败:', error);
          return null;
        }
      }).filter(m => m !== null) as T[];
    }
  }
}

/**
 * 创建缓存实例
 */
export function createCache(): RedisCache {
  return new RedisCache(getRedisClient());
}
