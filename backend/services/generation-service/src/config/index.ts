/**
 * 生成服务配置模块
 */

import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

export interface Config {
  // 服务配置
  port: number;
  nodeEnv: string;
  corsOrigin: string;
  
  // Redis配置
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
  };
  
  // LLM配置
  llm: {
    // OpenAI配置
    openai: {
      apiKey: string;
      baseURL: string;
      organization?: string;
      defaultModel: string;
      maxTokens: number;
      temperature: number;
      timeout: number;
    };
    
    // Anthropic配置
    anthropic: {
      apiKey: string;
      baseURL: string;
      defaultModel: string;
      maxTokens: number;
      temperature: number;
      timeout: number;
    };
    
    // 通用配置
    defaultProvider: 'openai' | 'anthropic';
    maxRetries: number;
    retryDelay: number;
    enableFallback: boolean;
  };
  
  // 提示词配置
  prompts: {
    systemPrompt: string;
    maxContextLength: number;
    enableOptimization: boolean;
    templateCacheSize: number;
  };
  
  // 缓存配置
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
    enableCompression: boolean;
  };
  
  // 队列配置
  queue: {
    concurrency: number;
    maxRetries: number;
    retryDelay: number;
    removeOnComplete: number;
    removeOnFail: number;
  };
  
  // 限流配置
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  
  // 监控配置
  monitoring: {
    enableMetrics: boolean;
    metricsPort: number;
    enableTracing: boolean;
  };
  
  // 安全配置
  security: {
    enableAuth: boolean;
    jwtSecret: string;
    allowedOrigins: string[];
    maxRequestSize: string;
  };
}

/**
 * 获取配置
 */
export const config: Config = {
  // 服务配置
  port: parseInt(process.env.PORT || '3000'),
  nodeEnv: process.env.NODE_ENV || 'development',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3100',
  
  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'generation:',
  },
  
  // LLM配置
  llm: {
    // OpenAI配置
    openai: {
      apiKey: process.env.OPENAI_API_KEY || '',
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      organization: process.env.OPENAI_ORGANIZATION,
      defaultModel: process.env.OPENAI_DEFAULT_MODEL || 'gpt-3.5-turbo',
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2048'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
      timeout: parseInt(process.env.OPENAI_TIMEOUT || '30000'),
    },
    
    // Anthropic配置
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY || '',
      baseURL: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
      defaultModel: process.env.ANTHROPIC_DEFAULT_MODEL || 'claude-3-sonnet-20240229',
      maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '2048'),
      temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7'),
      timeout: parseInt(process.env.ANTHROPIC_TIMEOUT || '30000'),
    },
    
    // 通用配置
    defaultProvider: (process.env.LLM_DEFAULT_PROVIDER as 'openai' | 'anthropic') || 'openai',
    maxRetries: parseInt(process.env.LLM_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.LLM_RETRY_DELAY || '1000'),
    enableFallback: process.env.LLM_ENABLE_FALLBACK === 'true',
  },
  
  // 提示词配置
  prompts: {
    systemPrompt: process.env.SYSTEM_PROMPT || '你是一个有用的AI助手，基于提供的上下文信息回答用户问题。',
    maxContextLength: parseInt(process.env.MAX_CONTEXT_LENGTH || '8000'),
    enableOptimization: process.env.ENABLE_PROMPT_OPTIMIZATION === 'true',
    templateCacheSize: parseInt(process.env.TEMPLATE_CACHE_SIZE || '100'),
  },
  
  // 缓存配置
  cache: {
    enabled: process.env.CACHE_ENABLED !== 'false',
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
    enableCompression: process.env.CACHE_ENABLE_COMPRESSION === 'true',
  },
  
  // 队列配置
  queue: {
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '5'),
    maxRetries: parseInt(process.env.QUEUE_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.QUEUE_RETRY_DELAY || '5000'),
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE || '100'),
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL || '50'),
  },
  
  // 限流配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
  },
  
  // 监控配置
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090'),
    enableTracing: process.env.ENABLE_TRACING === 'true',
  },
  
  // 安全配置
  security: {
    enableAuth: process.env.ENABLE_AUTH === 'true',
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    allowedOrigins: (process.env.ALLOWED_ORIGINS || 'http://localhost:3100').split(','),
    maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
  },
};

/**
 * 验证配置
 */
export function validateConfig(): void {
  const errors: string[] = [];
  
  // 验证必需的环境变量
  if (!config.llm.openai.apiKey && !config.llm.anthropic.apiKey) {
    errors.push('至少需要配置一个LLM API密钥 (OPENAI_API_KEY 或 ANTHROPIC_API_KEY)');
  }
  
  if (config.llm.defaultProvider === 'openai' && !config.llm.openai.apiKey) {
    errors.push('默认提供商为OpenAI但未配置OPENAI_API_KEY');
  }
  
  if (config.llm.defaultProvider === 'anthropic' && !config.llm.anthropic.apiKey) {
    errors.push('默认提供商为Anthropic但未配置ANTHROPIC_API_KEY');
  }
  
  if (config.security.enableAuth && !config.security.jwtSecret) {
    errors.push('启用认证但未配置JWT_SECRET');
  }
  
  // 验证数值范围
  if (config.port < 1 || config.port > 65535) {
    errors.push('端口号必须在1-65535之间');
  }
  
  if (config.llm.openai.temperature < 0 || config.llm.openai.temperature > 2) {
    errors.push('OpenAI temperature必须在0-2之间');
  }
  
  if (config.llm.anthropic.temperature < 0 || config.llm.anthropic.temperature > 1) {
    errors.push('Anthropic temperature必须在0-1之间');
  }
  
  if (errors.length > 0) {
    throw new Error(`配置验证失败:\n${errors.join('\n')}`);
  }
}

/**
 * 获取LLM提供商配置
 */
export function getLLMConfig(provider: 'openai' | 'anthropic') {
  return config.llm[provider];
}

/**
 * 是否为开发环境
 */
export function isDevelopment(): boolean {
  return config.nodeEnv === 'development';
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return config.nodeEnv === 'production';
}

/**
 * 获取Redis连接URL
 */
export function getRedisUrl(): string {
  const { host, port, password, db } = config.redis;
  const auth = password ? `:${password}@` : '';
  return `redis://${auth}${host}:${port}/${db}`;
}
