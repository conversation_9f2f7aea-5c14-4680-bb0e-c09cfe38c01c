# 文档服务Python版本依赖

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 文档解析
PyMuPDF==1.23.8  # PDF解析
python-docx==1.1.0  # Word文档解析
python-pptx==0.6.23  # PowerPoint解析
openpyxl==3.1.2  # Excel解析
beautifulsoup4==4.12.2  # HTML解析
lxml==4.9.3  # XML解析
markdown==3.5.1  # Markdown解析

# OCR和图像处理
pytesseract==0.3.10  # OCR文字识别
Pillow==10.1.0  # 图像处理
pdf2image==1.16.3  # PDF转图像

# 文本处理和NLP
spacy==3.7.2  # 自然语言处理
jieba==0.42.1  # 中文分词
nltk==3.8.1  # 英文文本处理
sentence-transformers==2.2.2  # 句子嵌入
transformers==4.35.2  # 预训练模型
torch==2.1.1  # PyTorch

# 数据库和缓存
asyncpg==0.29.0  # PostgreSQL异步驱动
redis==5.0.1  # Redis客户端
sqlalchemy==2.0.23  # ORM
alembic==1.12.1  # 数据库迁移

# 搜索引擎
elasticsearch[async]==8.11.0  # Elasticsearch异步客户端

# 对象存储
minio==7.2.0  # MinIO客户端
boto3==1.34.0  # AWS S3客户端

# 数据处理
pandas==2.1.3  # 数据分析
numpy==1.25.2  # 数值计算
scikit-learn==1.3.2  # 机器学习

# 配置和环境
pydantic==2.5.0  # 数据验证
python-dotenv==1.0.0  # 环境变量
pyyaml==6.0.1  # YAML配置

# 日志和监控
loguru==0.7.2  # 日志记录
prometheus-client==0.19.0  # 监控指标

# 异步任务
celery==5.3.4  # 任务队列
flower==2.0.1  # Celery监控

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2  # 异步HTTP客户端

# 开发工具
black==23.11.0  # 代码格式化
isort==5.12.0  # 导入排序
flake8==6.1.0  # 代码检查
mypy==1.7.1  # 类型检查
