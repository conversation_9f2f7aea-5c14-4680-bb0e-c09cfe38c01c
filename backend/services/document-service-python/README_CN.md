# RAG文档服务Python版本 - 中文说明文档

## 📋 概述

RAG文档服务Python版本是一个高性能的文档处理和索引服务，专为RAG（检索增强生成）系统设计。该服务提供完整的文档解析、智能分块、全文索引和向量索引功能。

## 🚀 主要功能

### 📄 文档解析引擎
- **多格式支持**: PDF、Word、PowerPoint、Excel、TXT、Markdown、HTML
- **OCR文字识别**: 支持中英文文字识别，基于Tesseract
- **元数据提取**: 自动提取文档标题、作者、创建时间等信息
- **表格和图像提取**: 智能识别和提取文档中的表格和图像
- **内容清洗**: 自动去除无用字符和格式化文本

### 🧩 智能分块系统
- **句子级分块**: 基于句子边界的精确分块
- **段落级分块**: 保持段落完整性的分块策略
- **语义分块**: 基于BERT语义相似度的智能分块
- **滑动窗口分块**: 重叠窗口确保信息连续性
- **自适应分块**: 根据文档类型自动选择最佳分块策略

### 🔍 索引和检索系统
- **Elasticsearch集成**: 高性能全文检索和混合检索
- **中文分词优化**: 集成IK分词器，支持中文智能分词
- **向量索引**: 支持密集向量检索（待向量数据库模块完成）
- **混合检索**: 结合全文检索和向量检索的最佳效果
- **实时索引**: 文档上传后立即可搜索

### 📊 版本管理系统
- **Git风格版本控制**: 类似Git的文档版本管理
- **增量更新**: 只存储文档变更部分，节省存储空间
- **版本比较**: 支持不同版本间的内容对比
- **回滚功能**: 可以回滚到任意历史版本

## 🏗️ 技术架构

### 核心组件
```
文档服务架构
├── API层 (FastAPI)
│   ├── 文档解析接口
│   ├── 分块处理接口
│   ├── 索引管理接口
│   └── 搜索查询接口
├── 业务逻辑层
│   ├── DocumentService (主服务)
│   ├── DocumentParser (解析器)
│   ├── IntelligentChunker (分块器)
│   ├── DocumentIndexer (索引器)
│   └── VersionManager (版本管理)
├── 数据存储层
│   ├── PostgreSQL (元数据)
│   ├── Elasticsearch (全文索引)
│   ├── MinIO (文件存储)
│   └── Redis (缓存)
└── 监控层
    ├── Prometheus (指标收集)
    ├── 结构化日志
    └── 健康检查
```

### 技术栈
- **Web框架**: FastAPI (高性能异步API)
- **文档解析**: PyMuPDF, python-docx, openpyxl, beautifulsoup4
- **OCR引擎**: Tesseract + pytesseract
- **NLP处理**: transformers, sentence-transformers, jieba
- **搜索引擎**: Elasticsearch + IK分词器
- **数据库**: PostgreSQL + Redis
- **对象存储**: MinIO (S3兼容)
- **监控**: Prometheus + 结构化日志

## 📦 安装和部署

### 环境要求
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- Elasticsearch 7.x/8.x
- MinIO或S3兼容存储
- Tesseract OCR引擎

### 快速启动
```bash
# 1. 克隆项目
cd backend/services/document-service-python

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库和服务连接

# 4. 启动服务
python start.py
```

### Docker部署
```bash
# 构建镜像
docker build -t rag-document-service .

# 运行容器
docker run -d \
  --name rag-document-service \
  -p 8001:8001 \
  -e DATABASE_URL=********************************/db \
  -e ELASTICSEARCH_URL=http://elasticsearch:9200 \
  -e REDIS_URL=redis://redis:6379 \
  rag-document-service
```

## 🔧 配置说明

### 主要配置项
```python
# 基础配置
HOST = "0.0.0.0"
PORT = 8001
DEBUG = False

# 数据库配置
DATABASE_URL = "postgresql://postgres:password@localhost:5432/rag_db"
REDIS_URL = "redis://localhost:6379"
ELASTICSEARCH_URL = "http://localhost:9200"

# 文档处理配置
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
SUPPORTED_FORMATS = ["pdf", "docx", "txt", "md", "html"]
DEFAULT_CHUNK_SIZE = 512
DEFAULT_OVERLAP = 64

# OCR配置
OCR_ENABLED = True
OCR_LANGUAGE = "chi_sim+eng"  # 中英文
OCR_CONFIDENCE_THRESHOLD = 0.8

# 嵌入模型配置
DEFAULT_EMBEDDING_MODEL = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
EMBEDDING_DIMENSION = 384
```

### 分块策略配置
```python
# 句子级分块
SENTENCE_MAX_LENGTH = 512
SENTENCE_OVERLAP = 0

# 段落级分块
PARAGRAPH_MIN_LENGTH = 100
PARAGRAPH_MAX_LENGTH = 1024

# 语义分块
SEMANTIC_SIMILARITY_THRESHOLD = 0.7
SEMANTIC_MAX_LENGTH = 800

# 滑动窗口分块
SLIDING_WINDOW_SIZE = 256
SLIDING_WINDOW_OVERLAP = 64
```

## 📚 API接口文档

### 文档解析接口
```http
POST /api/v1/documents/parse
Content-Type: multipart/form-data

# 参数
file: 上传的文件
extract_metadata: 是否提取元数据 (默认: true)
perform_ocr: 是否执行OCR (默认: false)

# 响应
{
  "document_id": "uuid",
  "filename": "document.pdf",
  "content": "解析后的文本内容",
  "metadata": {...},
  "images": [...],
  "tables": [...],
  "status": "success"
}
```

### 文档分块接口
```http
POST /api/v1/documents/{document_id}/chunk
Content-Type: application/json

{
  "strategy": "semantic",  // sentence, paragraph, semantic, sliding_window
  "chunk_size": 512,
  "overlap": 64
}

# 响应
{
  "document_id": "uuid",
  "strategy": "semantic",
  "chunks": [
    {
      "chunk_id": "uuid",
      "text": "分块内容",
      "metadata": {...}
    }
  ],
  "total_chunks": 10
}
```

### 文档索引接口
```http
POST /api/v1/documents/{document_id}/index
Content-Type: application/json

{
  "index_type": "hybrid"  // fulltext, vector, hybrid
}

# 响应
{
  "status": "success",
  "document_id": "uuid",
  "index_type": "hybrid",
  "details": {
    "fulltext_index": {...},
    "vector_index": {...}
  }
}
```

### 文档搜索接口
```http
GET /api/v1/search?query=搜索关键词&search_type=hybrid&top_k=10

# 响应
{
  "query": "搜索关键词",
  "search_type": "hybrid",
  "total_results": 5,
  "results": [
    {
      "document_id": "uuid",
      "chunk_id": "uuid",
      "text": "匹配的文本内容",
      "score": 0.95,
      "highlights": {...}
    }
  ],
  "search_time": 0.1
}
```

### 文档管理接口
```http
# 获取文档列表
GET /api/v1/documents?page=1&size=20&search=关键词

# 获取文档详情
GET /api/v1/documents/{document_id}

# 删除文档
DELETE /api/v1/documents/{document_id}

# 重新处理文档
POST /api/v1/documents/{document_id}/reprocess
{
  "operations": ["chunk", "index"]
}

# 获取统计信息
GET /api/v1/statistics
```

## 🧪 测试和验证

### 运行测试
```bash
# 单元测试
pytest tests/unit/ -v

# 集成测试
pytest tests/integration/ -v

# 性能测试
pytest tests/performance/ -v

# 生成覆盖率报告
pytest --cov=app tests/ --cov-report=html
```

### 功能验证
```bash
# 健康检查
curl http://localhost:8001/health

# 上传测试文档
curl -X POST \
  -F "file=@test.pdf" \
  -F "extract_metadata=true" \
  -F "perform_ocr=false" \
  http://localhost:8001/api/v1/documents/parse

# 搜索测试
curl "http://localhost:8001/api/v1/search?query=测试&search_type=hybrid&top_k=5"
```

## 📊 监控和运维

### 监控指标
- **请求指标**: QPS、响应时间、错误率
- **业务指标**: 文档处理数量、索引状态、搜索性能
- **系统指标**: CPU、内存、磁盘使用率
- **依赖服务**: 数据库连接、Elasticsearch状态

### 日志管理
- **结构化日志**: JSON格式，便于分析
- **日志级别**: DEBUG、INFO、WARNING、ERROR
- **日志轮转**: 按大小和时间自动轮转
- **错误追踪**: 详细的错误堆栈和上下文

### 性能优化
- **异步处理**: 全异步架构，提高并发性能
- **缓存策略**: Redis缓存热点数据
- **批量处理**: 支持批量文档处理
- **资源池**: 数据库连接池和线程池

## 🔮 未来规划

### 短期计划 (1-2个月)
- [ ] 完善向量索引集成
- [ ] 添加更多文档格式支持
- [ ] 优化OCR识别准确率
- [ ] 增强中文处理能力

### 中期计划 (3-6个月)
- [ ] 支持实时文档协作
- [ ] 添加文档智能分类
- [ ] 实现文档内容摘要
- [ ] 支持多语言文档处理

### 长期计划 (6-12个月)
- [ ] AI驱动的文档理解
- [ ] 智能文档推荐系统
- [ ] 文档质量评估
- [ ] 知识图谱集成

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: RAG开发团队
- **技术支持**: 通过GitHub Issues提交问题
- **文档更新**: 2025-08-28

---

*该文档将随着项目发展持续更新，确保信息的准确性和完整性。*
