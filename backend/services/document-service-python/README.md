# RAG文档服务 (Python版本)

## 📋 概述

RAG文档服务是一个高性能的文档处理微服务，提供文档解析、智能分块、索引构建等核心功能。基于FastAPI构建，支持多种文档格式和智能处理策略。

## 🚀 主要功能

### 文档解析
- **多格式支持**: PDF、Word、PowerPoint、Excel、文本、Markdown、HTML
- **OCR识别**: 支持图像文字识别，中英文混合识别
- **元数据提取**: 自动提取文档标题、作者、创建时间等信息
- **表格提取**: 智能提取文档中的表格数据
- **图像提取**: 从PDF等文档中提取图像内容

### 智能分块
- **多种策略**: 句子级、段落级、语义级、滑动窗口、自适应分块
- **语义分析**: 基于BERT的语义相似度分块
- **中文优化**: 针对中文的分词和语义分析
- **重叠控制**: 可配置的分块重叠率

### 索引构建
- **全文索引**: 基于Elasticsearch的全文检索
- **向量索引**: 集成向量数据库进行语义检索
- **混合索引**: 结合关键词和语义的混合检索
- **增量更新**: 支持文档增量索引更新

### 版本管理
- **版本控制**: Git-like的文档版本管理
- **差异检测**: 文档内容变更检测
- **历史追踪**: 完整的文档修改历史
- **回滚机制**: 支持版本回滚和恢复

## 🛠️ 技术栈

- **Web框架**: FastAPI + Uvicorn
- **文档解析**: PyMuPDF, python-docx, BeautifulSoup
- **OCR**: Tesseract + pytesseract
- **NLP**: spaCy, jieba, sentence-transformers
- **机器学习**: PyTorch, scikit-learn
- **数据库**: PostgreSQL, Redis
- **对象存储**: MinIO
- **监控**: Prometheus + Grafana

## 📦 安装和部署

### 环境要求
- Python 3.9+
- Docker (可选)
- PostgreSQL
- Redis
- MinIO

### 本地开发

1. **克隆代码**
```bash
git clone <repository-url>
cd backend/services/document-service-python
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **安装系统依赖**
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-eng poppler-utils

# macOS
brew install tesseract tesseract-lang poppler
```

4. **配置环境**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等
```

5. **下载模型**
```bash
# spaCy模型
python -m spacy download zh_core_web_sm
python -m spacy download en_core_web_sm

# 嵌入模型
python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')"
```

6. **启动服务**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
```

### Docker部署

1. **构建镜像**
```bash
docker build -t rag-document-service .
```

2. **运行容器**
```bash
docker run -d \
  --name rag-document-service \
  -p 8001:8001 \
  -e DATABASE_URL=postgresql://postgres:<EMAIL>:5432/rag_db \
  -e REDIS_URL=redis://host.docker.internal:6379 \
  rag-document-service
```

### Docker Compose部署

```yaml
version: '3.8'
services:
  document-service:
    build: .
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=********************************************/rag_db
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - postgres
      - redis
      - minio
```

## 📚 API文档

### 核心接口

#### 1. 文档解析
```http
POST /api/v1/documents/parse
Content-Type: multipart/form-data

file: <文件>
extract_metadata: true
perform_ocr: false
```

#### 2. 文档分块
```http
POST /api/v1/documents/chunk
Content-Type: application/json

{
  "document_id": "uuid",
  "strategy": "semantic",
  "max_length": 512,
  "overlap": 64
}
```

#### 3. 索引构建
```http
POST /api/v1/documents/index
Content-Type: application/json

{
  "document_id": "uuid",
  "index_type": "hybrid"
}
```

#### 4. 版本历史
```http
GET /api/v1/documents/{document_id}/versions
```

#### 5. 批量处理
```http
POST /api/v1/documents/batch/process
Content-Type: application/json

{
  "document_ids": ["uuid1", "uuid2"],
  "operations": ["parse", "chunk", "index"]
}
```

### 健康检查
```http
GET /health
```

### 监控指标
```http
GET /metrics
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DEBUG` | 调试模式 | `false` |
| `HOST` | 服务主机 | `0.0.0.0` |
| `PORT` | 服务端口 | `8001` |
| `DATABASE_URL` | 数据库连接 | - |
| `REDIS_URL` | Redis连接 | - |
| `DEFAULT_EMBEDDING_MODEL` | 嵌入模型 | `paraphrase-multilingual-MiniLM-L12-v2` |
| `OCR_ENABLED` | 启用OCR | `true` |
| `MAX_FILE_SIZE` | 最大文件大小 | `100MB` |

### 分块策略配置

- **sentence**: 句子级分块，适合短文档
- **paragraph**: 段落级分块，保持段落完整性
- **semantic**: 语义级分块，基于语义相似度
- **sliding_window**: 滑动窗口分块，保证连续性
- **adaptive**: 自适应分块，根据文档特征选择策略

## 🧪 测试

### 运行测试
```bash
# 安装测试依赖
pip install pytest pytest-asyncio pytest-cov

# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_document_parser.py

# 生成覆盖率报告
pytest --cov=app tests/
```

### 测试覆盖率
目标测试覆盖率: > 90%

## 📊 监控和日志

### 监控指标
- 请求数量和延迟
- 文档处理速度
- 错误率和成功率
- 系统资源使用

### 日志级别
- `DEBUG`: 详细调试信息
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息

### 日志文件
- `logs/document_service.log`: 主日志文件
- `logs/document_service_error.log`: 错误日志文件

## 🔒 安全考虑

- 文件类型验证
- 文件大小限制
- 输入内容过滤
- API访问控制
- 敏感信息脱敏

## 🚀 性能优化

### 处理性能
- 异步处理架构
- 批量处理优化
- 内存使用优化
- GPU加速支持

### 缓存策略
- 文档内容缓存
- 模型结果缓存
- 数据库查询缓存

## 🐛 故障排除

### 常见问题

1. **OCR识别失败**
   - 检查Tesseract安装
   - 验证语言包安装
   - 调整图像质量

2. **模型加载失败**
   - 检查网络连接
   - 验证模型路径
   - 检查磁盘空间

3. **内存不足**
   - 调整批处理大小
   - 启用模型量化
   - 增加系统内存

## 📝 更新日志

### v1.0.0 (2025-08-27)
- 初始版本发布
- 支持多格式文档解析
- 实现智能分块算法
- 集成OCR功能
- 添加版本管理

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 文档地址: [Documentation]
