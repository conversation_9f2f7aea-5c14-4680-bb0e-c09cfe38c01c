"""
监控指标中间件
"""

from fastapi import FastAPI, Request, Response
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
import time
from typing import Callable

from app.utils.logger import get_logger

logger = get_logger(__name__)

# Prometheus指标
REQUEST_COUNT = Counter(
    'document_service_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'document_service_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'document_service_active_requests',
    'Number of active requests'
)

DOCUMENT_PROCESSING_COUNT = Counter(
    'document_processing_total',
    'Total number of documents processed',
    ['operation', 'status']
)

DOCUMENT_PROCESSING_DURATION = Histogram(
    'document_processing_duration_seconds',
    'Document processing duration in seconds',
    ['operation']
)


async def metrics_middleware(request: Request, call_next: Callable) -> Response:
    """监控指标中间件"""
    start_time = time.time()
    
    # 增加活跃请求计数
    ACTIVE_REQUESTS.inc()
    
    try:
        # 处理请求
        response = await call_next(request)
        
        # 记录指标
        duration = time.time() - start_time
        method = request.method
        endpoint = request.url.path
        status_code = response.status_code
        
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        return response
        
    except Exception as e:
        # 记录错误指标
        duration = time.time() - start_time
        method = request.method
        endpoint = request.url.path
        
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=500
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        raise
    
    finally:
        # 减少活跃请求计数
        ACTIVE_REQUESTS.dec()


def add_metrics_middleware(app: FastAPI):
    """添加监控中间件到FastAPI应用"""
    app.middleware("http")(metrics_middleware)


def record_document_processing(operation: str, duration: float, status: str):
    """记录文档处理指标"""
    DOCUMENT_PROCESSING_COUNT.labels(
        operation=operation,
        status=status
    ).inc()
    
    DOCUMENT_PROCESSING_DURATION.labels(
        operation=operation
    ).observe(duration)
