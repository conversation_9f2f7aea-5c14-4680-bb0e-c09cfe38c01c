"""
文档索引器
支持全文索引、向量索引和混合索引
"""

from typing import List, Dict, Any, Optional
import asyncio
from datetime import datetime
import json
import hashlib
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError
import numpy as np

from app.models.document import Chunk, IndexType
from app.config.settings import get_settings, get_indexing_config
from app.utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()
indexing_config = get_indexing_config()


class DocumentIndexer:
    """文档索引器主类"""

    def __init__(self):
        self.elasticsearch_client: Optional[AsyncElasticsearch] = None
        self.vector_db_client = None
        self.initialized = False
        self.index_mappings = self._get_index_mappings()

    async def initialize(self):
        """初始化索引器"""
        try:
            logger.info("初始化文档索引器...")

            # 初始化Elasticsearch客户端
            await self._initialize_elasticsearch()

            # 初始化向量数据库客户端（暂时占位）
            await self._initialize_vector_db()

            self.initialized = True
            logger.info("文档索引器初始化完成")

        except Exception as e:
            logger.error(f"文档索引器初始化失败: {e}")
            raise

    async def _initialize_elasticsearch(self):
        """初始化Elasticsearch连接"""
        try:
            self.elasticsearch_client = AsyncElasticsearch(
                hosts=[settings.ELASTICSEARCH_URL],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )

            # 测试连接
            if await self.elasticsearch_client.ping():
                logger.info(f"Elasticsearch连接成功: {settings.ELASTICSEARCH_URL}")
            else:
                raise ConnectionError("Elasticsearch连接失败")

        except Exception as e:
            logger.error(f"Elasticsearch初始化失败: {e}")
            # 在开发环境中，如果ES不可用，使用模拟模式
            if settings.DEBUG:
                logger.warning("使用Elasticsearch模拟模式")
                self.elasticsearch_client = None
            else:
                raise

    async def _initialize_vector_db(self):
        """初始化向量数据库连接"""
        try:
            # 这里将在向量数据库模块实现时完善
            logger.info("向量数据库连接初始化（占位）")
            self.vector_db_client = None

        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            raise
    
    def _get_index_mappings(self) -> Dict[str, Any]:
        """获取Elasticsearch索引映射"""
        return {
            "mappings": {
                "properties": {
                    "document_id": {"type": "keyword"},
                    "chunk_id": {"type": "keyword"},
                    "chunk_index": {"type": "integer"},
                    "text": {
                        "type": "text",
                        "analyzer": indexing_config.FULLTEXT_ANALYZER,
                        "search_analyzer": indexing_config.FULLTEXT_SEARCH_ANALYZER,
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "english": {
                                "type": "text",
                                "analyzer": "english"
                            }
                        }
                    },
                    "metadata": {"type": "object"},
                    "embedding": {
                        "type": "dense_vector",
                        "dims": settings.EMBEDDING_DIMENSION,
                        "index": True,
                        "similarity": indexing_config.VECTOR_SIMILARITY_METRIC
                    },
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "ik_max_word": {
                            "type": "ik_max_word"
                        },
                        "ik_smart": {
                            "type": "ik_smart"
                        }
                    }
                }
            }
        }

    async def build_index(self, document_id: str, chunks: List[Chunk],
                         index_type: str = "hybrid") -> Dict[str, Any]:
        """
        构建文档索引

        Args:
            document_id: 文档ID
            chunks: 文档分块列表
            index_type: 索引类型

        Returns:
            Dict[str, Any]: 索引构建结果
        """
        try:
            logger.info(f"开始构建索引: {document_id}, 类型: {index_type}, 分块数: {len(chunks)}")

            result = {
                "document_id": document_id,
                "index_type": index_type,
                "chunk_count": len(chunks),
                "created_at": datetime.utcnow(),
                "status": "success",
                "details": {}
            }

            if index_type == "fulltext" or index_type == "hybrid":
                fulltext_result = await self._build_fulltext_index(document_id, chunks)
                result["details"]["fulltext_index"] = fulltext_result

            if index_type == "vector" or index_type == "hybrid":
                vector_result = await self._build_vector_index(document_id, chunks)
                result["details"]["vector_index"] = vector_result

            logger.info(f"索引构建完成: {document_id}")
            return result

        except Exception as e:
            logger.error(f"索引构建失败: {document_id}, 错误: {e}")
            result = {
                "document_id": document_id,
                "index_type": index_type,
                "status": "failed",
                "error": str(e),
                "created_at": datetime.utcnow()
            }
            return result

    async def _build_fulltext_index(self, document_id: str, chunks: List[Chunk]) -> Dict[str, Any]:
        """构建全文索引"""
        try:
            logger.info(f"构建全文索引: {document_id}")

            if not self.elasticsearch_client:
                logger.warning("Elasticsearch客户端未初始化，跳过全文索引")
                return {"status": "skipped", "reason": "elasticsearch_unavailable"}

            # 创建索引名称
            index_name = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_{document_id.replace('-', '_')}"

            # 创建索引
            await self._ensure_index_exists(index_name)

            # 批量插入文档
            actions = []
            for i, chunk in enumerate(chunks):
                doc = {
                    "document_id": document_id,
                    "chunk_id": f"{document_id}_{i}",
                    "chunk_index": i,
                    "text": chunk.text,
                    "metadata": chunk.metadata.dict() if chunk.metadata else {},
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }

                action = {
                    "_index": index_name,
                    "_id": f"{document_id}_{i}",
                    "_source": doc
                }
                actions.append(action)

            # 执行批量插入
            if actions:
                from elasticsearch.helpers import async_bulk
                success_count, failed_items = await async_bulk(
                    self.elasticsearch_client,
                    actions,
                    chunk_size=100,
                    request_timeout=60
                )

                logger.info(f"全文索引创建完成: {document_id}, 成功: {success_count}, 失败: {len(failed_items)}")

                return {
                    "status": "success",
                    "index_name": index_name,
                    "indexed_chunks": success_count,
                    "failed_chunks": len(failed_items)
                }
            else:
                return {"status": "success", "indexed_chunks": 0}

        except Exception as e:
            logger.error(f"全文索引构建失败: {document_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}

    async def _ensure_index_exists(self, index_name: str):
        """确保索引存在"""
        try:
            exists = await self.elasticsearch_client.indices.exists(index=index_name)
            if not exists:
                await self.elasticsearch_client.indices.create(
                    index=index_name,
                    body=self.index_mappings
                )
                logger.info(f"创建Elasticsearch索引: {index_name}")
            else:
                logger.info(f"Elasticsearch索引已存在: {index_name}")

        except Exception as e:
            logger.error(f"创建索引失败: {index_name}, 错误: {e}")
            raise

    async def _build_vector_index(self, document_id: str, chunks: List[Chunk]) -> Dict[str, Any]:
        """构建向量索引"""
        try:
            logger.info(f"构建向量索引: {document_id}")

            # 这里将在向量数据库模块完成后实现
            # 暂时返回占位结果
            return {
                "status": "placeholder",
                "message": "向量索引将在向量数据库模块实现",
                "chunk_count": len(chunks)
            }

        except Exception as e:
            logger.error(f"向量索引构建失败: {document_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}

    async def search_fulltext(self, query: str, document_ids: Optional[List[str]] = None,
                             size: int = 10, from_: int = 0) -> Dict[str, Any]:
        """
        全文搜索

        Args:
            query: 搜索查询
            document_ids: 限制搜索的文档ID列表
            size: 返回结果数量
            from_: 起始位置

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            if not self.elasticsearch_client:
                return {"status": "error", "message": "Elasticsearch不可用"}

            # 构建搜索查询
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["text^2", "text.english"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            }
                        ]
                    }
                },
                "highlight": {
                    "fields": {
                        "text": {
                            "fragment_size": 150,
                            "number_of_fragments": 3
                        }
                    }
                },
                "size": size,
                "from": from_,
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"created_at": {"order": "desc"}}
                ]
            }

            # 如果指定了文档ID，添加过滤条件
            if document_ids:
                search_body["query"]["bool"]["filter"] = [
                    {"terms": {"document_id": document_ids}}
                ]

            # 执行搜索
            index_pattern = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_*"
            response = await self.elasticsearch_client.search(
                index=index_pattern,
                body=search_body
            )

            # 处理搜索结果
            hits = response["hits"]
            results = []

            for hit in hits["hits"]:
                result = {
                    "document_id": hit["_source"]["document_id"],
                    "chunk_id": hit["_source"]["chunk_id"],
                    "chunk_index": hit["_source"]["chunk_index"],
                    "text": hit["_source"]["text"],
                    "score": hit["_score"],
                    "metadata": hit["_source"].get("metadata", {}),
                    "highlights": hit.get("highlight", {})
                }
                results.append(result)

            return {
                "status": "success",
                "total": hits["total"]["value"],
                "results": results,
                "took": response["took"]
            }

        except Exception as e:
            logger.error(f"全文搜索失败: {query}, 错误: {e}")
            return {"status": "error", "message": str(e)}

    async def delete_document_index(self, document_id: str) -> Dict[str, Any]:
        """
        删除文档索引

        Args:
            document_id: 文档ID

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            logger.info(f"删除文档索引: {document_id}")

            result = {"document_id": document_id, "status": "success", "details": {}}

            # 删除Elasticsearch索引
            if self.elasticsearch_client:
                index_name = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_{document_id.replace('-', '_')}"
                try:
                    await self.elasticsearch_client.indices.delete(index=index_name)
                    result["details"]["elasticsearch"] = "deleted"
                    logger.info(f"Elasticsearch索引已删除: {index_name}")
                except NotFoundError:
                    result["details"]["elasticsearch"] = "not_found"
                    logger.warning(f"Elasticsearch索引不存在: {index_name}")

            # 删除向量索引（待实现）
            result["details"]["vector_db"] = "placeholder"

            return result

        except Exception as e:
            logger.error(f"删除文档索引失败: {document_id}, 错误: {e}")
            return {"status": "failed", "error": str(e)}

    async def get_index_stats(self, document_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取索引统计信息

        Args:
            document_id: 可选的文档ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {"elasticsearch": {}, "vector_db": {}}

            if self.elasticsearch_client:
                if document_id:
                    index_name = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_{document_id.replace('-', '_')}"
                    try:
                        es_stats = await self.elasticsearch_client.indices.stats(index=index_name)
                        stats["elasticsearch"] = {
                            "index_name": index_name,
                            "doc_count": es_stats["indices"][index_name]["total"]["docs"]["count"],
                            "store_size": es_stats["indices"][index_name]["total"]["store"]["size_in_bytes"]
                        }
                    except NotFoundError:
                        stats["elasticsearch"] = {"status": "not_found"}
                else:
                    # 获取所有索引统计
                    index_pattern = f"{settings.ELASTICSEARCH_INDEX_PREFIX}_*"
                    es_stats = await self.elasticsearch_client.indices.stats(index=index_pattern)
                    total_docs = sum(
                        idx_stats["total"]["docs"]["count"]
                        for idx_stats in es_stats["indices"].values()
                    )
                    total_size = sum(
                        idx_stats["total"]["store"]["size_in_bytes"]
                        for idx_stats in es_stats["indices"].values()
                    )
                    stats["elasticsearch"] = {
                        "total_indices": len(es_stats["indices"]),
                        "total_documents": total_docs,
                        "total_size_bytes": total_size
                    }

            # 向量数据库统计（待实现）
            stats["vector_db"] = {"status": "placeholder"}

            return {"status": "success", "stats": stats}

        except Exception as e:
            logger.error(f"获取索引统计失败: {e}")
            return {"status": "error", "message": str(e)}

    async def cleanup(self):
        """清理资源"""
        logger.info("清理索引器资源...")

        if self.elasticsearch_client:
            await self.elasticsearch_client.close()
            logger.info("Elasticsearch连接已关闭")

        if self.vector_db_client:
            # 清理向量数据库连接
            pass

        logger.info("索引器资源清理完成")
