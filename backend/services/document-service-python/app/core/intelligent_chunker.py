"""
智能文档分块器
支持多种分块策略：句子级、段落级、语义级、滑动窗口
"""

import re
import spacy
import jieba
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from typing import List, Dict, Optional, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.config.settings import get_chunking_config, get_settings
from app.models.document import Chunk, ChunkMetadata
from app.utils.logger import get_logger

logger = get_logger(__name__)
chunking_config = get_chunking_config()
settings = get_settings()


class IntelligentChunker:
    """智能分块器主类"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.nlp_zh = None  # 中文NLP模型
        self.nlp_en = None  # 英文NLP模型
        self.embedding_model = None  # 嵌入模型
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化NLP模型"""
        try:
            # 初始化中文分词
            jieba.initialize()
            
            # 加载spaCy模型（如果可用）
            try:
                self.nlp_zh = spacy.load("zh_core_web_sm")
            except OSError:
                logger.warning("中文spaCy模型未安装，将使用jieba分词")
            
            try:
                self.nlp_en = spacy.load("en_core_web_sm")
            except OSError:
                logger.warning("英文spaCy模型未安装，将使用基础分句")
            
            # 初始化嵌入模型（延迟加载）
            logger.info("智能分块器初始化完成")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
    
    def _get_embedding_model(self):
        """获取嵌入模型（延迟加载）"""
        if self.embedding_model is None:
            try:
                self.embedding_model = SentenceTransformer(
                    settings.DEFAULT_EMBEDDING_MODEL,
                    device=settings.DEVICE
                )
                logger.info(f"嵌入模型加载完成: {settings.DEFAULT_EMBEDDING_MODEL}")
            except Exception as e:
                logger.error(f"嵌入模型加载失败: {e}")
                raise
        return self.embedding_model
    
    async def chunk_text(self, text: str, strategy: str = "semantic", 
                        max_length: int = None, overlap: int = None) -> List[Chunk]:
        """
        对文本进行智能分块
        
        Args:
            text: 输入文本
            strategy: 分块策略 (sentence, paragraph, semantic, sliding_window, adaptive)
            max_length: 最大分块长度
            overlap: 重叠长度
        
        Returns:
            List[Chunk]: 分块结果列表
        """
        if not text.strip():
            return []
        
        # 设置默认参数
        if max_length is None:
            max_length = chunking_config.SEMANTIC_MAX_LENGTH
        if overlap is None:
            overlap = chunking_config.SLIDING_WINDOW_OVERLAP
        
        try:
            logger.info(f"开始分块: 策略={strategy}, 长度={len(text)}, 最大分块={max_length}")
            
            # 根据策略选择分块方法
            if strategy == "sentence":
                chunks = await self._sentence_chunk(text, max_length)
            elif strategy == "paragraph":
                chunks = await self._paragraph_chunk(text, max_length)
            elif strategy == "semantic":
                chunks = await self._semantic_chunk(text, max_length)
            elif strategy == "sliding_window":
                chunks = await self._sliding_window_chunk(text, max_length, overlap)
            elif strategy == "adaptive":
                chunks = await self._adaptive_chunk(text, max_length)
            else:
                raise ValueError(f"不支持的分块策略: {strategy}")
            
            logger.info(f"分块完成: 生成{len(chunks)}个分块")
            return chunks
            
        except Exception as e:
            logger.error(f"文本分块失败: {e}")
            raise
    
    async def _sentence_chunk(self, text: str, max_length: int) -> List[Chunk]:
        """句子级分块"""
        sentences = self._split_sentences(text)
        chunks = []
        current_chunk = []
        current_length = 0
        
        for i, sentence in enumerate(sentences):
            sentence_length = len(sentence)
            
            if current_length + sentence_length > max_length and current_chunk:
                # 创建分块
                chunk_text = " ".join(current_chunk)
                chunks.append(self._create_chunk(
                    text=chunk_text,
                    chunk_id=len(chunks),
                    start_sentence=i - len(current_chunk),
                    end_sentence=i - 1,
                    strategy="sentence"
                ))
                current_chunk = [sentence]
                current_length = sentence_length
            else:
                current_chunk.append(sentence)
                current_length += sentence_length
        
        # 处理最后一个分块
        if current_chunk:
            chunk_text = " ".join(current_chunk)
            chunks.append(self._create_chunk(
                text=chunk_text,
                chunk_id=len(chunks),
                start_sentence=len(sentences) - len(current_chunk),
                end_sentence=len(sentences) - 1,
                strategy="sentence"
            ))
        
        return chunks
    
    async def _paragraph_chunk(self, text: str, max_length: int) -> List[Chunk]:
        """段落级分块"""
        paragraphs = self._split_paragraphs(text)
        chunks = []
        current_chunk = []
        current_length = 0
        
        for i, paragraph in enumerate(paragraphs):
            paragraph_length = len(paragraph)
            
            # 如果单个段落就超过最大长度，需要进一步分割
            if paragraph_length > max_length:
                # 如果当前有累积的分块，先保存
                if current_chunk:
                    chunk_text = "\n\n".join(current_chunk)
                    chunks.append(self._create_chunk(
                        text=chunk_text,
                        chunk_id=len(chunks),
                        start_paragraph=i - len(current_chunk),
                        end_paragraph=i - 1,
                        strategy="paragraph"
                    ))
                    current_chunk = []
                    current_length = 0
                
                # 对长段落进行句子级分割
                long_para_chunks = await self._sentence_chunk(paragraph, max_length)
                chunks.extend(long_para_chunks)
                
            elif current_length + paragraph_length > max_length and current_chunk:
                # 保存当前分块
                chunk_text = "\n\n".join(current_chunk)
                chunks.append(self._create_chunk(
                    text=chunk_text,
                    chunk_id=len(chunks),
                    start_paragraph=i - len(current_chunk),
                    end_paragraph=i - 1,
                    strategy="paragraph"
                ))
                current_chunk = [paragraph]
                current_length = paragraph_length
            else:
                current_chunk.append(paragraph)
                current_length += paragraph_length
        
        # 处理最后一个分块
        if current_chunk:
            chunk_text = "\n\n".join(current_chunk)
            chunks.append(self._create_chunk(
                text=chunk_text,
                chunk_id=len(chunks),
                start_paragraph=len(paragraphs) - len(current_chunk),
                end_paragraph=len(paragraphs) - 1,
                strategy="paragraph"
            ))
        
        return chunks
    
    async def _semantic_chunk(self, text: str, max_length: int) -> List[Chunk]:
        """语义级分块"""
        sentences = self._split_sentences(text)
        
        if len(sentences) <= 1:
            return [self._create_chunk(text, 0, 0, 0, "semantic")]
        
        # 异步计算句子嵌入
        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            self.executor,
            self._compute_sentence_embeddings,
            sentences
        )
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for i, sentence in enumerate(sentences):
            sentence_length = len(sentence)
            
            if current_length + sentence_length > max_length and current_chunk:
                # 检查语义相似度
                if self._should_merge_semantic(current_chunk, sentence, embeddings, i):
                    current_chunk.append(sentence)
                    current_length += sentence_length
                else:
                    # 创建分块
                    chunk_text = " ".join(current_chunk)
                    chunks.append(self._create_chunk(
                        text=chunk_text,
                        chunk_id=len(chunks),
                        start_sentence=i - len(current_chunk),
                        end_sentence=i - 1,
                        strategy="semantic"
                    ))
                    current_chunk = [sentence]
                    current_length = sentence_length
            else:
                current_chunk.append(sentence)
                current_length += sentence_length
        
        # 处理最后一个分块
        if current_chunk:
            chunk_text = " ".join(current_chunk)
            chunks.append(self._create_chunk(
                text=chunk_text,
                chunk_id=len(chunks),
                start_sentence=len(sentences) - len(current_chunk),
                end_sentence=len(sentences) - 1,
                strategy="semantic"
            ))
        
        return chunks
    
    async def _sliding_window_chunk(self, text: str, window_size: int, overlap: int) -> List[Chunk]:
        """滑动窗口分块"""
        sentences = self._split_sentences(text)
        chunks = []
        
        if not sentences:
            return chunks
        
        stride = window_size - overlap
        current_pos = 0
        
        while current_pos < len(sentences):
            # 确定窗口范围
            end_pos = min(current_pos + window_size, len(sentences))
            window_sentences = sentences[current_pos:end_pos]
            
            chunk_text = " ".join(window_sentences)
            chunks.append(self._create_chunk(
                text=chunk_text,
                chunk_id=len(chunks),
                start_sentence=current_pos,
                end_sentence=end_pos - 1,
                strategy="sliding_window",
                overlap_info={
                    "window_size": window_size,
                    "overlap": overlap,
                    "stride": stride
                }
            ))
            
            # 移动窗口
            current_pos += stride
            
            # 如果剩余句子不足一个完整窗口，直接处理完毕
            if current_pos >= len(sentences):
                break
        
        return chunks
    
    async def _adaptive_chunk(self, text: str, max_length: int) -> List[Chunk]:
        """自适应分块"""
        # 首先分析文本特征
        text_features = self._analyze_text_features(text)
        
        # 根据文本特征选择最佳策略
        if text_features['has_clear_paragraphs']:
            return await self._paragraph_chunk(text, max_length)
        elif text_features['sentence_count'] > 10:
            return await self._semantic_chunk(text, max_length)
        else:
            return await self._sentence_chunk(text, max_length)
    
    def _split_sentences(self, text: str) -> List[str]:
        """分句"""
        # 中英文混合分句
        sentences = []
        
        # 使用正则表达式进行基础分句
        sentence_pattern = r'[.!?。！？；;]\s*'
        parts = re.split(sentence_pattern, text)
        
        for part in parts:
            part = part.strip()
            if part:
                sentences.append(part)
        
        return sentences
    
    def _split_paragraphs(self, text: str) -> List[str]:
        """分段"""
        paragraphs = []
        
        # 按双换行符分段
        parts = text.split('\n\n')
        
        for part in parts:
            part = part.strip()
            if part:
                paragraphs.append(part)
        
        return paragraphs
    
    def _compute_sentence_embeddings(self, sentences: List[str]) -> np.ndarray:
        """计算句子嵌入"""
        try:
            model = self._get_embedding_model()
            embeddings = model.encode(sentences, convert_to_numpy=True)
            return embeddings
        except Exception as e:
            logger.error(f"句子嵌入计算失败: {e}")
            # 返回随机嵌入作为fallback
            return np.random.rand(len(sentences), 384)
    
    def _should_merge_semantic(self, current_chunk: List[str], new_sentence: str, 
                              embeddings: np.ndarray, sentence_index: int) -> bool:
        """判断是否应该基于语义相似度合并"""
        if not current_chunk:
            return True
        
        try:
            # 计算当前分块的平均嵌入
            chunk_indices = list(range(sentence_index - len(current_chunk), sentence_index))
            chunk_embeddings = embeddings[chunk_indices]
            chunk_avg_embedding = np.mean(chunk_embeddings, axis=0)
            
            # 新句子的嵌入
            new_sentence_embedding = embeddings[sentence_index]
            
            # 计算相似度
            similarity = cosine_similarity(
                [chunk_avg_embedding], 
                [new_sentence_embedding]
            )[0][0]
            
            return similarity > chunking_config.SEMANTIC_SIMILARITY_THRESHOLD
            
        except Exception as e:
            logger.error(f"语义相似度计算失败: {e}")
            return False
    
    def _analyze_text_features(self, text: str) -> Dict:
        """分析文本特征"""
        features = {
            'length': len(text),
            'sentence_count': len(self._split_sentences(text)),
            'paragraph_count': len(self._split_paragraphs(text)),
            'has_clear_paragraphs': '\n\n' in text,
            'avg_sentence_length': 0,
            'language': self._detect_language(text)
        }
        
        sentences = self._split_sentences(text)
        if sentences:
            features['avg_sentence_length'] = sum(len(s) for s in sentences) / len(sentences)
        
        return features
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        
        if chinese_chars / total_chars > 0.3:
            return 'zh'
        else:
            return 'en'
    
    def _create_chunk(self, text: str, chunk_id: int, start_sentence: int, 
                     end_sentence: int, strategy: str, **kwargs) -> Chunk:
        """创建分块对象"""
        metadata = ChunkMetadata(
            chunk_id=chunk_id,
            start_sentence=start_sentence,
            end_sentence=end_sentence,
            strategy=strategy,
            length=len(text),
            **kwargs
        )
        
        return Chunk(
            text=text,
            metadata=metadata
        )
    
    async def cleanup(self):
        """清理资源"""
        if self.executor:
            self.executor.shutdown(wait=True)
