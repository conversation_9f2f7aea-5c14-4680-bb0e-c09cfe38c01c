"""
文档版本管理器
Git-like版本控制系统
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import uuid
import hashlib
import difflib
import json
from dataclasses import dataclass, asdict

from app.models.document import DocumentVersion, calculate_content_hash
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class VersionDiff:
    """版本差异"""
    added_lines: List[str]
    removed_lines: List[str]
    modified_lines: List[Tuple[str, str]]  # (old, new)
    similarity_ratio: float


@dataclass
class MergeResult:
    """合并结果"""
    success: bool
    merged_content: Optional[str]
    conflicts: List[Dict[str, Any]]
    message: str


class DocumentVersionManager:
    """文档版本管理器"""

    def __init__(self):
        self.versions_storage = {}  # 简单的内存存储
        self.content_storage = {}   # 内容存储
        self.branch_storage = {}    # 分支存储
    
    async def create_version(self, document_id: str, content: str,
                           created_by: Optional[str] = None,
                           commit_message: Optional[str] = None,
                           branch: str = "main") -> DocumentVersion:
        """
        创建文档版本

        Args:
            document_id: 文档ID
            content: 文档内容
            created_by: 创建者
            commit_message: 提交信息
            branch: 分支名称

        Returns:
            DocumentVersion: 版本信息
        """
        try:
            # 获取当前版本号
            current_versions = self.versions_storage.get(document_id, [])
            version_number = len(current_versions) + 1

            # 计算内容哈希
            content_hash = calculate_content_hash(content)

            # 检查是否有内容变更
            if current_versions:
                latest_version = current_versions[-1]
                if latest_version.content_hash == content_hash:
                    logger.info(f"文档内容未变更: {document_id}")
                    return latest_version

            # 计算与上一版本的差异
            changes = []
            if current_versions:
                previous_content = self.content_storage.get(current_versions[-1].version_id, "")
                diff = self._calculate_diff(previous_content, content)
                changes = self._format_changes(diff)

            # 创建版本记录
            version = DocumentVersion(
                version_id=str(uuid.uuid4()),
                document_id=document_id,
                version_number=version_number,
                content_hash=content_hash,
                created_at=datetime.utcnow(),
                created_by=created_by,
                changes=changes,
                metadata={
                    "commit_message": commit_message or f"Version {version_number}",
                    "branch": branch,
                    "content_length": len(content),
                    "lines_count": len(content.splitlines())
                }
            )

            # 保存版本和内容
            if document_id not in self.versions_storage:
                self.versions_storage[document_id] = []
            self.versions_storage[document_id].append(version)
            self.content_storage[version.version_id] = content

            # 更新分支信息
            self._update_branch(document_id, branch, version.version_id)

            logger.info(f"创建文档版本: {document_id}, 版本号: {version_number}, 分支: {branch}")
            return version

        except Exception as e:
            logger.error(f"创建文档版本失败: {document_id}, 错误: {e}")
            raise

    def _calculate_diff(self, old_content: str, new_content: str) -> VersionDiff:
        """计算版本差异"""
        old_lines = old_content.splitlines()
        new_lines = new_content.splitlines()

        differ = difflib.unified_diff(old_lines, new_lines, lineterm='')
        diff_lines = list(differ)

        added_lines = []
        removed_lines = []
        modified_lines = []

        i = 0
        while i < len(diff_lines):
            line = diff_lines[i]
            if line.startswith('+') and not line.startswith('+++'):
                added_lines.append(line[1:])
            elif line.startswith('-') and not line.startswith('---'):
                removed_lines.append(line[1:])
            i += 1

        # 计算相似度
        similarity_ratio = difflib.SequenceMatcher(None, old_content, new_content).ratio()

        return VersionDiff(
            added_lines=added_lines,
            removed_lines=removed_lines,
            modified_lines=modified_lines,
            similarity_ratio=similarity_ratio
        )

    def _format_changes(self, diff: VersionDiff) -> List[str]:
        """格式化变更信息"""
        changes = []

        if diff.added_lines:
            changes.append(f"新增 {len(diff.added_lines)} 行")
        if diff.removed_lines:
            changes.append(f"删除 {len(diff.removed_lines)} 行")
        if diff.modified_lines:
            changes.append(f"修改 {len(diff.modified_lines)} 行")

        changes.append(f"相似度: {diff.similarity_ratio:.2%}")

        return changes

    def _update_branch(self, document_id: str, branch: str, version_id: str):
        """更新分支信息"""
        if document_id not in self.branch_storage:
            self.branch_storage[document_id] = {}

        self.branch_storage[document_id][branch] = {
            "latest_version_id": version_id,
            "updated_at": datetime.utcnow()
        }
    
    async def get_version_history(self, document_id: str, branch: Optional[str] = None) -> List[DocumentVersion]:
        """
        获取文档版本历史

        Args:
            document_id: 文档ID
            branch: 分支名称（可选）

        Returns:
            List[DocumentVersion]: 版本历史列表
        """
        try:
            versions = self.versions_storage.get(document_id, [])

            # 如果指定了分支，过滤版本
            if branch:
                versions = [
                    v for v in versions
                    if v.metadata and v.metadata.get("branch") == branch
                ]

            return sorted(versions, key=lambda v: v.version_number, reverse=True)

        except Exception as e:
            logger.error(f"获取版本历史失败: {document_id}, 错误: {e}")
            raise

    async def get_version_content(self, version_id: str) -> Optional[str]:
        """
        获取指定版本的内容

        Args:
            version_id: 版本ID

        Returns:
            Optional[str]: 版本内容
        """
        try:
            return self.content_storage.get(version_id)
        except Exception as e:
            logger.error(f"获取版本内容失败: {version_id}, 错误: {e}")
            return None

    async def compare_versions(self, version1_id: str, version2_id: str) -> Dict[str, Any]:
        """
        比较两个版本

        Args:
            version1_id: 版本1 ID
            version2_id: 版本2 ID

        Returns:
            Dict[str, Any]: 比较结果
        """
        try:
            content1 = self.content_storage.get(version1_id, "")
            content2 = self.content_storage.get(version2_id, "")

            if not content1 or not content2:
                return {"error": "版本内容不存在"}

            diff = self._calculate_diff(content1, content2)

            return {
                "version1_id": version1_id,
                "version2_id": version2_id,
                "diff": asdict(diff),
                "summary": {
                    "added_lines": len(diff.added_lines),
                    "removed_lines": len(diff.removed_lines),
                    "modified_lines": len(diff.modified_lines),
                    "similarity_ratio": diff.similarity_ratio
                }
            }

        except Exception as e:
            logger.error(f"版本比较失败: {version1_id} vs {version2_id}, 错误: {e}")
            return {"error": str(e)}

    async def rollback_to_version(self, document_id: str, target_version_id: str,
                                 created_by: Optional[str] = None) -> DocumentVersion:
        """
        回滚到指定版本

        Args:
            document_id: 文档ID
            target_version_id: 目标版本ID
            created_by: 操作者

        Returns:
            DocumentVersion: 新创建的版本
        """
        try:
            # 获取目标版本内容
            target_content = self.content_storage.get(target_version_id)
            if not target_content:
                raise ValueError(f"目标版本不存在: {target_version_id}")

            # 获取目标版本信息
            target_version = None
            for version in self.versions_storage.get(document_id, []):
                if version.version_id == target_version_id:
                    target_version = version
                    break

            if not target_version:
                raise ValueError(f"目标版本信息不存在: {target_version_id}")

            # 创建回滚版本
            rollback_message = f"回滚到版本 {target_version.version_number}"
            new_version = await self.create_version(
                document_id=document_id,
                content=target_content,
                created_by=created_by,
                commit_message=rollback_message
            )

            logger.info(f"版本回滚完成: {document_id} -> {target_version_id}")
            return new_version

        except Exception as e:
            logger.error(f"版本回滚失败: {document_id} -> {target_version_id}, 错误: {e}")
            raise

    async def create_branch(self, document_id: str, branch_name: str,
                           from_version_id: Optional[str] = None) -> Dict[str, Any]:
        """
        创建分支

        Args:
            document_id: 文档ID
            branch_name: 分支名称
            from_version_id: 起始版本ID（可选）

        Returns:
            Dict[str, Any]: 分支创建结果
        """
        try:
            if document_id not in self.branch_storage:
                self.branch_storage[document_id] = {}

            if branch_name in self.branch_storage[document_id]:
                return {"error": f"分支已存在: {branch_name}"}

            # 确定起始版本
            if from_version_id:
                start_version_id = from_version_id
            else:
                # 使用主分支的最新版本
                main_branch = self.branch_storage[document_id].get("main", {})
                start_version_id = main_branch.get("latest_version_id")

            if not start_version_id:
                return {"error": "无法确定起始版本"}

            # 创建分支
            self.branch_storage[document_id][branch_name] = {
                "latest_version_id": start_version_id,
                "created_at": datetime.utcnow(),
                "created_from": from_version_id or "main"
            }

            logger.info(f"分支创建成功: {document_id}/{branch_name}")
            return {
                "status": "success",
                "branch_name": branch_name,
                "start_version_id": start_version_id
            }

        except Exception as e:
            logger.error(f"分支创建失败: {document_id}/{branch_name}, 错误: {e}")
            return {"error": str(e)}

    async def merge_branches(self, document_id: str, source_branch: str,
                           target_branch: str, created_by: Optional[str] = None) -> MergeResult:
        """
        合并分支

        Args:
            document_id: 文档ID
            source_branch: 源分支
            target_branch: 目标分支
            created_by: 操作者

        Returns:
            MergeResult: 合并结果
        """
        try:
            branches = self.branch_storage.get(document_id, {})

            if source_branch not in branches or target_branch not in branches:
                return MergeResult(
                    success=False,
                    merged_content=None,
                    conflicts=[],
                    message="分支不存在"
                )

            # 获取两个分支的最新版本内容
            source_version_id = branches[source_branch]["latest_version_id"]
            target_version_id = branches[target_branch]["latest_version_id"]

            source_content = self.content_storage.get(source_version_id, "")
            target_content = self.content_storage.get(target_version_id, "")

            # 简单合并策略：如果内容相同则无需合并，否则标记为冲突
            if source_content == target_content:
                return MergeResult(
                    success=True,
                    merged_content=target_content,
                    conflicts=[],
                    message="无需合并，内容相同"
                )

            # 检测冲突（简化实现）
            diff = self._calculate_diff(target_content, source_content)

            if diff.similarity_ratio > 0.8:
                # 自动合并
                merged_content = source_content  # 简化：使用源分支内容

                # 创建合并版本
                merge_message = f"合并分支 {source_branch} 到 {target_branch}"
                await self.create_version(
                    document_id=document_id,
                    content=merged_content,
                    created_by=created_by,
                    commit_message=merge_message,
                    branch=target_branch
                )

                return MergeResult(
                    success=True,
                    merged_content=merged_content,
                    conflicts=[],
                    message="自动合并成功"
                )
            else:
                # 存在冲突
                conflicts = [
                    {
                        "type": "content_conflict",
                        "source_branch": source_branch,
                        "target_branch": target_branch,
                        "similarity": diff.similarity_ratio
                    }
                ]

                return MergeResult(
                    success=False,
                    merged_content=None,
                    conflicts=conflicts,
                    message="存在合并冲突，需要手动解决"
                )

        except Exception as e:
            logger.error(f"分支合并失败: {document_id}, 错误: {e}")
            return MergeResult(
                success=False,
                merged_content=None,
                conflicts=[],
                message=f"合并失败: {str(e)}"
            )

    async def get_branches(self, document_id: str) -> Dict[str, Any]:
        """
        获取文档的所有分支

        Args:
            document_id: 文档ID

        Returns:
            Dict[str, Any]: 分支信息
        """
        try:
            branches = self.branch_storage.get(document_id, {})
            return {
                "document_id": document_id,
                "branches": branches,
                "total_branches": len(branches)
            }
        except Exception as e:
            logger.error(f"获取分支信息失败: {document_id}, 错误: {e}")
            return {"error": str(e)}

    async def cleanup(self):
        """清理资源"""
        logger.info("清理版本管理器资源...")
        self.versions_storage.clear()
        self.content_storage.clear()
        self.branch_storage.clear()
        logger.info("版本管理器资源清理完成")
