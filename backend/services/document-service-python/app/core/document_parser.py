"""
文档解析器
支持多种文档格式的解析和内容提取
"""

import fitz  # PyMuPDF
from docx import Document as DocxDocument
from pptx import Presentation
import openpyxl
from bs4 import BeautifulSoup
import markdown
import pytesseract
from PIL import Image
import io
import re
from typing import Dict, List, Optional, Tuple, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor
import mimetypes

from app.config.settings import get_document_parser_config
from app.models.document import DocumentContent, DocumentMetadata
from app.utils.logger import get_logger

logger = get_logger(__name__)
config = get_document_parser_config()


class DocumentParser:
    """文档解析器主类"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.supported_formats = {
            'application/pdf': self._parse_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._parse_docx,
            'application/msword': self._parse_doc,
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': self._parse_pptx,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': self._parse_xlsx,
            'text/plain': self._parse_text,
            'text/markdown': self._parse_markdown,
            'text/html': self._parse_html,
        }
    
    async def parse_document(self, file_content: bytes, filename: str, 
                           extract_metadata: bool = True, 
                           perform_ocr: bool = False) -> DocumentContent:
        """
        解析文档内容
        
        Args:
            file_content: 文件二进制内容
            filename: 文件名
            extract_metadata: 是否提取元数据
            perform_ocr: 是否执行OCR
        
        Returns:
            DocumentContent: 解析后的文档内容
        """
        try:
            # 检测文件类型
            mime_type = self._detect_mime_type(filename, file_content)
            logger.info(f"检测到文件类型: {mime_type}")
            
            if mime_type not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {mime_type}")
            
            # 异步执行解析
            parser_func = self.supported_formats[mime_type]
            loop = asyncio.get_event_loop()
            
            content = await loop.run_in_executor(
                self.executor,
                parser_func,
                file_content,
                filename,
                perform_ocr
            )
            
            # 提取元数据
            metadata = None
            if extract_metadata:
                metadata = await self._extract_metadata(file_content, filename, mime_type)
            
            return DocumentContent(
                text=content['text'],
                images=content.get('images', []),
                tables=content.get('tables', []),
                metadata=metadata,
                format=mime_type,
                filename=filename
            )
            
        except Exception as e:
            logger.error(f"文档解析失败: {filename}, 错误: {e}")
            raise
    
    def _detect_mime_type(self, filename: str, file_content: bytes) -> str:
        """检测文件MIME类型"""
        # 首先根据文件扩展名判断
        mime_type, _ = mimetypes.guess_type(filename)
        
        if mime_type:
            return mime_type
        
        # 根据文件内容特征判断
        if file_content.startswith(b'%PDF'):
            return 'application/pdf'
        elif file_content.startswith(b'PK\x03\x04'):
            # ZIP格式，可能是Office文档
            if filename.endswith('.docx'):
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            elif filename.endswith('.pptx'):
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            elif filename.endswith('.xlsx'):
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        
        # 默认为文本文件
        return 'text/plain'
    
    def _parse_pdf(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析PDF文档"""
        try:
            doc = fitz.open(stream=file_content, filetype="pdf")
            text_content = []
            images = []
            tables = []
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # 提取文本
                page_text = page.get_text()
                
                # 如果文本为空且启用OCR，则进行OCR识别
                if not page_text.strip() and perform_ocr and config.OCR_ENABLED:
                    page_text = self._perform_ocr_on_page(page)
                
                text_content.append(page_text)
                
                # 提取图像
                if config.PDF_EXTRACT_IMAGES:
                    page_images = self._extract_images_from_page(page, page_num)
                    images.extend(page_images)
                
                # 提取表格
                if config.PDF_EXTRACT_TABLES:
                    page_tables = self._extract_tables_from_page(page, page_num)
                    tables.extend(page_tables)
            
            doc.close()
            
            return {
                'text': '\n\n'.join(text_content),
                'images': images,
                'tables': tables
            }
            
        except Exception as e:
            logger.error(f"PDF解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_docx(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析Word文档"""
        try:
            doc = DocxDocument(io.BytesIO(file_content))
            text_content = []
            tables = []
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # 提取表格
            if config.DOCX_EXTRACT_TABLES:
                for table in doc.tables:
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        table_data.append(row_data)
                    if table_data:
                        tables.append(table_data)
            
            return {
                'text': '\n\n'.join(text_content),
                'tables': tables
            }
            
        except Exception as e:
            logger.error(f"DOCX解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_doc(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析旧版Word文档"""
        # 对于.doc文件，可以使用python-docx2txt或其他库
        # 这里简化处理，返回提示信息
        logger.warning(f"暂不支持.doc格式，建议转换为.docx格式: {filename}")
        return {
            'text': f"暂不支持.doc格式文件: {filename}，请转换为.docx格式后重新上传。"
        }
    
    def _parse_pptx(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析PowerPoint文档"""
        try:
            prs = Presentation(io.BytesIO(file_content))
            text_content = []
            
            for slide_num, slide in enumerate(prs.slides):
                slide_text = []
                
                # 提取文本框内容
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text)
                
                if slide_text:
                    text_content.append(f"幻灯片 {slide_num + 1}:\n" + '\n'.join(slide_text))
            
            return {
                'text': '\n\n'.join(text_content)
            }
            
        except Exception as e:
            logger.error(f"PPTX解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_xlsx(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析Excel文档"""
        try:
            workbook = openpyxl.load_workbook(io.BytesIO(file_content), data_only=True)
            text_content = []
            tables = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_data = []
                
                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else '' for cell in row]
                    if any(cell.strip() for cell in row_data):  # 跳过空行
                        sheet_data.append(row_data)
                
                if sheet_data:
                    tables.append({
                        'sheet_name': sheet_name,
                        'data': sheet_data
                    })
                    
                    # 将表格数据转换为文本
                    sheet_text = f"工作表: {sheet_name}\n"
                    for row in sheet_data[:10]:  # 只取前10行作为文本
                        sheet_text += '\t'.join(row) + '\n'
                    text_content.append(sheet_text)
            
            return {
                'text': '\n\n'.join(text_content),
                'tables': tables
            }
            
        except Exception as e:
            logger.error(f"XLSX解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_text(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析纯文本文件"""
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            
            for encoding in encodings:
                try:
                    text = file_content.decode(encoding)
                    return {'text': text}
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，使用错误处理
            text = file_content.decode('utf-8', errors='ignore')
            return {'text': text}
            
        except Exception as e:
            logger.error(f"文本文件解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_markdown(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析Markdown文件"""
        try:
            text = file_content.decode('utf-8')
            # 转换为HTML然后提取纯文本
            html = markdown.markdown(text)
            soup = BeautifulSoup(html, 'html.parser')
            plain_text = soup.get_text()
            
            return {'text': plain_text}
            
        except Exception as e:
            logger.error(f"Markdown文件解析失败: {filename}, 错误: {e}")
            raise
    
    def _parse_html(self, file_content: bytes, filename: str, perform_ocr: bool = False) -> Dict[str, Any]:
        """解析HTML文件"""
        try:
            html_content = file_content.decode('utf-8')
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式
            if config.HTML_REMOVE_SCRIPTS:
                for script in soup(["script", "style"]):
                    script.decompose()
            
            if config.HTML_EXTRACT_TEXT_ONLY:
                text = soup.get_text()
            else:
                text = str(soup)
            
            return {'text': text}
            
        except Exception as e:
            logger.error(f"HTML文件解析失败: {filename}, 错误: {e}")
            raise
    
    def _perform_ocr_on_page(self, page) -> str:
        """对PDF页面执行OCR"""
        try:
            # 将页面转换为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(config.PDF_DPI/72, config.PDF_DPI/72))
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))
            
            # 执行OCR
            text = pytesseract.image_to_string(
                image, 
                lang=config.OCR_LANGUAGE,
                config=f'--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十'
            )
            
            return text
            
        except Exception as e:
            logger.error(f"OCR处理失败: {e}")
            return ""
    
    def _extract_images_from_page(self, page, page_num: int) -> List[Dict]:
        """从PDF页面提取图像"""
        images = []
        try:
            image_list = page.get_images()
            for img_index, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图像
                    img_data = pix.tobytes("png")
                    images.append({
                        'page': page_num + 1,
                        'index': img_index,
                        'data': img_data,
                        'format': 'png'
                    })
                pix = None
                
        except Exception as e:
            logger.error(f"图像提取失败: {e}")
        
        return images
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[Dict]:
        """从PDF页面提取表格"""
        tables = []
        try:
            # 这里可以使用更复杂的表格检测算法
            # 简化实现：查找文本块的规律排列
            blocks = page.get_text("dict")["blocks"]
            
            # 简单的表格检测逻辑
            # 实际应用中可以使用更复杂的算法
            
        except Exception as e:
            logger.error(f"表格提取失败: {e}")
        
        return tables
    
    async def _extract_metadata(self, file_content: bytes, filename: str, mime_type: str) -> DocumentMetadata:
        """提取文档元数据"""
        try:
            metadata = DocumentMetadata(
                filename=filename,
                file_size=len(file_content),
                mime_type=mime_type
            )
            
            # 根据文件类型提取特定元数据
            if mime_type == 'application/pdf':
                metadata.update(self._extract_pdf_metadata(file_content))
            elif 'officedocument' in mime_type:
                metadata.update(self._extract_office_metadata(file_content, mime_type))
            
            return metadata
            
        except Exception as e:
            logger.error(f"元数据提取失败: {filename}, 错误: {e}")
            return DocumentMetadata(filename=filename, file_size=len(file_content), mime_type=mime_type)
    
    def _extract_pdf_metadata(self, file_content: bytes) -> Dict:
        """提取PDF元数据"""
        try:
            doc = fitz.open(stream=file_content, filetype="pdf")
            metadata = doc.metadata
            doc.close()
            
            return {
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'subject': metadata.get('subject', ''),
                'creator': metadata.get('creator', ''),
                'producer': metadata.get('producer', ''),
                'creation_date': metadata.get('creationDate', ''),
                'modification_date': metadata.get('modDate', ''),
                'page_count': len(doc)
            }
            
        except Exception as e:
            logger.error(f"PDF元数据提取失败: {e}")
            return {}
    
    def _extract_office_metadata(self, file_content: bytes, mime_type: str) -> Dict:
        """提取Office文档元数据"""
        try:
            if 'wordprocessingml' in mime_type:
                doc = DocxDocument(io.BytesIO(file_content))
                core_props = doc.core_properties
                
                return {
                    'title': core_props.title or '',
                    'author': core_props.author or '',
                    'subject': core_props.subject or '',
                    'creator': core_props.author or '',
                    'creation_date': core_props.created.isoformat() if core_props.created else '',
                    'modification_date': core_props.modified.isoformat() if core_props.modified else '',
                    'word_count': len(doc.paragraphs)
                }
            
        except Exception as e:
            logger.error(f"Office元数据提取失败: {e}")
            return {}
        
        return {}

    async def cleanup(self):
        """清理资源"""
        if self.executor:
            self.executor.shutdown(wait=True)
