"""
文档服务配置设置
"""

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from pydantic import Field
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "RAG文档服务"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8001, env="PORT")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/rag_db",
        env="DATABASE_URL"
    )
    
    # Redis配置
    REDIS_URL: str = Field(
        default="redis://localhost:6379",
        env="REDIS_URL"
    )
    
    # MinIO/S3配置
    MINIO_ENDPOINT: str = Field(default="localhost:9000", env="MINIO_ENDPOINT")
    MINIO_ACCESS_KEY: str = Field(default="minioadmin", env="MINIO_ACCESS_KEY")
    MINIO_SECRET_KEY: str = Field(default="minioadmin123", env="MINIO_SECRET_KEY")
    MINIO_BUCKET_NAME: str = Field(default="rag-documents", env="MINIO_BUCKET_NAME")
    MINIO_USE_SSL: bool = Field(default=False, env="MINIO_USE_SSL")
    
    # 文档解析配置
    SUPPORTED_FORMATS: List[str] = [
        "pdf", "docx", "doc", "txt", "md", "html", "pptx", "xlsx"
    ]
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    
    # OCR配置
    OCR_ENABLED: bool = Field(default=True, env="OCR_ENABLED")
    OCR_LANGUAGE: str = Field(default="chi_sim+eng", env="OCR_LANGUAGE")
    OCR_CONFIDENCE_THRESHOLD: float = Field(default=0.8, env="OCR_CONFIDENCE_THRESHOLD")
    
    # 分块配置
    DEFAULT_CHUNK_SIZE: int = Field(default=512, env="DEFAULT_CHUNK_SIZE")
    DEFAULT_OVERLAP: int = Field(default=64, env="DEFAULT_OVERLAP")
    MAX_CHUNK_SIZE: int = Field(default=2048, env="MAX_CHUNK_SIZE")
    
    # 嵌入模型配置
    DEFAULT_EMBEDDING_MODEL: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        env="DEFAULT_EMBEDDING_MODEL"
    )
    EMBEDDING_DIMENSION: int = Field(default=384, env="EMBEDDING_DIMENSION")
    DEVICE: str = Field(default="auto", env="DEVICE")  # auto, cpu, cuda
    
    # Elasticsearch配置
    ELASTICSEARCH_URL: str = Field(
        default="http://localhost:9200",
        env="ELASTICSEARCH_URL"
    )
    ELASTICSEARCH_INDEX_PREFIX: str = Field(
        default="rag_documents",
        env="ELASTICSEARCH_INDEX_PREFIX"
    )
    
    # 任务队列配置
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/0",
        env="CELERY_BROKER_URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/0",
        env="CELERY_RESULT_BACKEND"
    )
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=8002, env="METRICS_PORT")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        env="LOG_FORMAT"
    )
    
    # 安全配置
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    
    # 性能配置
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    BATCH_SIZE: int = Field(default=32, env="BATCH_SIZE")
    TIMEOUT: int = Field(default=300, env="TIMEOUT")  # 5分钟
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class DocumentParserConfig(BaseSettings):
    """文档解析器配置"""
    
    # PDF解析配置
    PDF_EXTRACT_IMAGES: bool = Field(default=True, env="PDF_EXTRACT_IMAGES")
    PDF_EXTRACT_TABLES: bool = Field(default=True, env="PDF_EXTRACT_TABLES")
    PDF_DPI: int = Field(default=300, env="PDF_DPI")
    
    # Word文档配置
    DOCX_EXTRACT_TABLES: bool = Field(default=True, env="DOCX_EXTRACT_TABLES")
    DOCX_PRESERVE_FORMATTING: bool = Field(default=True, env="DOCX_PRESERVE_FORMATTING")
    
    # HTML解析配置
    HTML_EXTRACT_TEXT_ONLY: bool = Field(default=False, env="HTML_EXTRACT_TEXT_ONLY")
    HTML_REMOVE_SCRIPTS: bool = Field(default=True, env="HTML_REMOVE_SCRIPTS")
    
    class Config:
        env_file = ".env"


class ChunkingConfig(BaseSettings):
    """分块配置"""
    
    # 句子级分块
    SENTENCE_MAX_LENGTH: int = Field(default=512, env="SENTENCE_MAX_LENGTH")
    SENTENCE_OVERLAP: int = Field(default=0, env="SENTENCE_OVERLAP")
    
    # 段落级分块
    PARAGRAPH_MIN_LENGTH: int = Field(default=100, env="PARAGRAPH_MIN_LENGTH")
    PARAGRAPH_MAX_LENGTH: int = Field(default=1024, env="PARAGRAPH_MAX_LENGTH")
    
    # 语义分块
    SEMANTIC_SIMILARITY_THRESHOLD: float = Field(default=0.7, env="SEMANTIC_SIMILARITY_THRESHOLD")
    SEMANTIC_MAX_LENGTH: int = Field(default=800, env="SEMANTIC_MAX_LENGTH")
    
    # 滑动窗口分块
    SLIDING_WINDOW_SIZE: int = Field(default=256, env="SLIDING_WINDOW_SIZE")
    SLIDING_WINDOW_OVERLAP: int = Field(default=64, env="SLIDING_WINDOW_OVERLAP")
    
    class Config:
        env_file = ".env"


class IndexingConfig(BaseSettings):
    """索引配置"""
    
    # 全文索引配置
    FULLTEXT_ANALYZER: str = Field(default="ik_max_word", env="FULLTEXT_ANALYZER")
    FULLTEXT_SEARCH_ANALYZER: str = Field(default="ik_smart", env="FULLTEXT_SEARCH_ANALYZER")
    
    # 向量索引配置
    VECTOR_INDEX_TYPE: str = Field(default="hnsw", env="VECTOR_INDEX_TYPE")
    VECTOR_SIMILARITY_METRIC: str = Field(default="cosine", env="VECTOR_SIMILARITY_METRIC")
    
    # HNSW参数
    HNSW_M: int = Field(default=16, env="HNSW_M")
    HNSW_EF_CONSTRUCTION: int = Field(default=200, env="HNSW_EF_CONSTRUCTION")
    HNSW_EF_SEARCH: int = Field(default=100, env="HNSW_EF_SEARCH")
    
    class Config:
        env_file = ".env"


# 全局配置实例
_settings: Optional[Settings] = None
_document_parser_config: Optional[DocumentParserConfig] = None
_chunking_config: Optional[ChunkingConfig] = None
_indexing_config: Optional[IndexingConfig] = None


def get_settings() -> Settings:
    """获取应用配置"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def get_document_parser_config() -> DocumentParserConfig:
    """获取文档解析器配置"""
    global _document_parser_config
    if _document_parser_config is None:
        _document_parser_config = DocumentParserConfig()
    return _document_parser_config


def get_chunking_config() -> ChunkingConfig:
    """获取分块配置"""
    global _chunking_config
    if _chunking_config is None:
        _chunking_config = ChunkingConfig()
    return _chunking_config


def get_indexing_config() -> IndexingConfig:
    """获取索引配置"""
    global _indexing_config
    if _indexing_config is None:
        _indexing_config = IndexingConfig()
    return _indexing_config
