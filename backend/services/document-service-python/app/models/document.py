"""
文档相关数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class DocumentFormat(str, Enum):
    """文档格式枚举"""
    PDF = "application/pdf"
    DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    DOC = "application/msword"
    PPTX = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    TXT = "text/plain"
    MD = "text/markdown"
    HTML = "text/html"


class ChunkStrategy(str, Enum):
    """分块策略枚举"""
    SENTENCE = "sentence"
    PARAGRAPH = "paragraph"
    SEMANTIC = "semantic"
    SLIDING_WINDOW = "sliding_window"
    ADAPTIVE = "adaptive"


class IndexType(str, Enum):
    """索引类型枚举"""
    FULLTEXT = "fulltext"
    VECTOR = "vector"
    HYBRID = "hybrid"


class DocumentMetadata(BaseModel):
    """文档元数据"""
    filename: str
    file_size: int
    mime_type: str
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    creator: Optional[str] = None
    producer: Optional[str] = None
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    language: Optional[str] = None
    encoding: Optional[str] = None
    
    class Config:
        extra = "allow"  # 允许额外字段


class DocumentImage(BaseModel):
    """文档图像"""
    page: int
    index: int
    data: bytes
    format: str
    width: Optional[int] = None
    height: Optional[int] = None
    description: Optional[str] = None


class DocumentTable(BaseModel):
    """文档表格"""
    page: Optional[int] = None
    sheet_name: Optional[str] = None
    data: List[List[str]]
    headers: Optional[List[str]] = None
    caption: Optional[str] = None


class DocumentContent(BaseModel):
    """文档内容"""
    text: str
    images: List[DocumentImage] = []
    tables: List[DocumentTable] = []
    metadata: Optional[DocumentMetadata] = None
    format: str
    filename: str
    
    class Config:
        arbitrary_types_allowed = True


class ChunkMetadata(BaseModel):
    """分块元数据"""
    chunk_id: int
    start_sentence: int
    end_sentence: int
    start_paragraph: Optional[int] = None
    end_paragraph: Optional[int] = None
    strategy: str
    length: int
    overlap_info: Optional[Dict[str, Any]] = None
    semantic_score: Optional[float] = None
    language: Optional[str] = None
    
    class Config:
        extra = "allow"


class Chunk(BaseModel):
    """文档分块"""
    text: str
    metadata: ChunkMetadata
    embedding: Optional[List[float]] = None
    
    class Config:
        arbitrary_types_allowed = True


class DocumentVersion(BaseModel):
    """文档版本"""
    version_id: str
    document_id: str
    version_number: int
    content_hash: str
    created_at: datetime
    created_by: Optional[str] = None
    changes: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class IndexInfo(BaseModel):
    """索引信息"""
    index_id: str
    document_id: str
    index_type: IndexType
    created_at: datetime
    updated_at: Optional[datetime] = None
    status: str = "active"
    metadata: Optional[Dict[str, Any]] = None


# API请求模型
class DocumentCreate(BaseModel):
    """创建文档请求"""
    filename: str
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    extract_metadata: bool = True
    perform_ocr: bool = False


class ChunkRequest(BaseModel):
    """分块请求"""
    document_id: str
    strategy: ChunkStrategy = ChunkStrategy.SEMANTIC
    max_length: int = Field(default=512, ge=100, le=2048)
    overlap: int = Field(default=64, ge=0, le=512)
    
    class Config:
        use_enum_values = True


class IndexRequest(BaseModel):
    """索引请求"""
    document_id: str
    index_type: IndexType = IndexType.HYBRID
    force_rebuild: bool = False
    
    class Config:
        use_enum_values = True


class BatchProcessRequest(BaseModel):
    """批处理请求"""
    document_ids: List[str]
    operations: List[str] = ["parse", "chunk", "index"]
    chunk_strategy: ChunkStrategy = ChunkStrategy.SEMANTIC
    index_type: IndexType = IndexType.HYBRID


# API响应模型
class DocumentResponse(BaseModel):
    """文档响应"""
    document_id: str
    filename: str
    content: Optional[str] = None
    metadata: Optional[DocumentMetadata] = None
    images: List[DocumentImage] = []
    tables: List[DocumentTable] = []
    created_at: datetime
    status: str = "processed"
    
    class Config:
        arbitrary_types_allowed = True


class ChunkResponse(BaseModel):
    """分块响应"""
    chunk_id: str
    document_id: str
    text: str
    metadata: ChunkMetadata
    created_at: datetime
    
    class Config:
        arbitrary_types_allowed = True


class IndexResponse(BaseModel):
    """索引响应"""
    index_id: str
    document_id: str
    index_type: IndexType
    status: str
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None


class BatchProcessResponse(BaseModel):
    """批处理响应"""
    task_id: str
    total_documents: int
    processed_documents: int
    success_count: int
    error_count: int
    status: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    errors: List[Dict[str, str]] = []


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime
    version: str
    components: Dict[str, str]
    uptime: float


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str
    detail: Optional[str] = None
    timestamp: datetime
    request_id: Optional[str] = None


# 数据库模型（用于ORM）
class DocumentDB(BaseModel):
    """文档数据库模型"""
    id: str
    filename: str
    original_filename: str
    file_size: int
    mime_type: str
    content_hash: str
    storage_path: str
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    status: str = "active"
    
    class Config:
        from_attributes = True


class ChunkDB(BaseModel):
    """分块数据库模型"""
    id: str
    document_id: str
    chunk_index: int
    text: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class IndexDB(BaseModel):
    """索引数据库模型"""
    id: str
    document_id: str
    index_type: str
    index_name: str
    status: str
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class VersionDB(BaseModel):
    """版本数据库模型"""
    id: str
    document_id: str
    version_number: int
    content_hash: str
    changes: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    created_by: Optional[str] = None
    
    class Config:
        from_attributes = True


# 工具函数
def create_document_id() -> str:
    """生成文档ID"""
    import uuid
    return str(uuid.uuid4())


def create_chunk_id(document_id: str, chunk_index: int) -> str:
    """生成分块ID"""
    return f"{document_id}_{chunk_index}"


def create_index_id(document_id: str, index_type: str) -> str:
    """生成索引ID"""
    return f"{document_id}_{index_type}"


def calculate_content_hash(content: str) -> str:
    """计算内容哈希"""
    import hashlib
    return hashlib.sha256(content.encode('utf-8')).hexdigest()


def validate_file_format(filename: str) -> bool:
    """验证文件格式"""
    import os
    ext = os.path.splitext(filename)[1].lower()
    supported_extensions = ['.pdf', '.docx', '.doc', '.pptx', '.xlsx', '.txt', '.md', '.html']
    return ext in supported_extensions


def estimate_processing_time(file_size: int, file_type: str) -> int:
    """估算处理时间（秒）"""
    # 基于文件大小和类型的简单估算
    base_time = {
        'pdf': 2,
        'docx': 1,
        'txt': 0.5,
        'html': 0.5,
        'md': 0.5
    }
    
    # 每MB额外时间
    size_mb = file_size / (1024 * 1024)
    extra_time = size_mb * base_time.get(file_type, 1)
    
    return int(base_time.get(file_type, 1) + extra_time)
