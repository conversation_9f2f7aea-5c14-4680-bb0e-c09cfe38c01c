"""
文档服务主要业务逻辑
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
import uuid

from app.core.document_parser import DocumentParser
from app.core.intelligent_chunker import IntelligentChunker
from app.core.document_indexer import DocumentIndexer
from app.core.version_manager import DocumentVersionManager
from app.models.document import (
    DocumentContent, DocumentResponse, Chunk, ChunkResponse,
    DocumentDB, ChunkDB, create_document_id, create_chunk_id,
    calculate_content_hash
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


class DocumentService:
    """文档服务主类"""
    
    def __init__(self, parser: DocumentParser, chunker: IntelligentChunker,
                 indexer: DocumentIndexer, version_manager: DocumentVersionManager):
        self.parser = parser
        self.chunker = chunker
        self.indexer = indexer
        self.version_manager = version_manager
        self.documents_cache = {}  # 简单的内存缓存
    
    async def initialize(self):
        """初始化服务"""
        logger.info("初始化文档服务...")
        # 这里可以添加数据库连接、缓存初始化等
        logger.info("文档服务初始化完成")
    
    async def parse_document(self, file_content: bytes, filename: str,
                           extract_metadata: bool = True,
                           perform_ocr: bool = False) -> DocumentResponse:
        """
        解析文档
        
        Args:
            file_content: 文件内容
            filename: 文件名
            extract_metadata: 是否提取元数据
            perform_ocr: 是否执行OCR
        
        Returns:
            DocumentResponse: 解析结果
        """
        try:
            # 生成文档ID
            document_id = create_document_id()
            
            # 解析文档内容
            content = await self.parser.parse_document(
                file_content=file_content,
                filename=filename,
                extract_metadata=extract_metadata,
                perform_ocr=perform_ocr
            )
            
            # 计算内容哈希
            content_hash = calculate_content_hash(content.text)
            
            # 创建文档记录
            document = DocumentDB(
                id=document_id,
                filename=filename,
                original_filename=filename,
                file_size=len(file_content),
                mime_type=content.format,
                content_hash=content_hash,
                storage_path=f"documents/{document_id}",
                metadata=content.metadata.dict() if content.metadata else None,
                created_at=datetime.utcnow(),
                status="processed"
            )
            
            # 缓存文档内容
            self.documents_cache[document_id] = content
            
            # 创建版本记录
            await self.version_manager.create_version(
                document_id=document_id,
                content=content.text,
                created_by="system"
            )
            
            logger.info(f"文档解析完成: {document_id}")
            
            return DocumentResponse(
                document_id=document_id,
                filename=filename,
                content=content.text,
                metadata=content.metadata,
                images=content.images,
                tables=content.tables,
                created_at=document.created_at,
                status=document.status
            )
            
        except Exception as e:
            logger.error(f"文档解析失败: {filename}, 错误: {e}")
            raise
    
    async def chunk_document(self, document_id: str, strategy: str = "semantic",
                           max_length: int = 512, overlap: int = 64) -> List[ChunkResponse]:
        """
        对文档进行分块
        
        Args:
            document_id: 文档ID
            strategy: 分块策略
            max_length: 最大长度
            overlap: 重叠长度
        
        Returns:
            List[ChunkResponse]: 分块结果列表
        """
        try:
            # 获取文档内容
            content = await self._get_document_content(document_id)
            if not content:
                raise ValueError(f"文档不存在: {document_id}")
            
            # 执行分块
            chunks = await self.chunker.chunk_text(
                text=content.text,
                strategy=strategy,
                max_length=max_length,
                overlap=overlap
            )
            
            # 保存分块结果
            chunk_responses = []
            for i, chunk in enumerate(chunks):
                chunk_id = create_chunk_id(document_id, i)
                
                # 创建分块记录
                chunk_db = ChunkDB(
                    id=chunk_id,
                    document_id=document_id,
                    chunk_index=i,
                    text=chunk.text,
                    metadata=chunk.metadata.dict(),
                    created_at=datetime.utcnow()
                )
                
                chunk_response = ChunkResponse(
                    chunk_id=chunk_id,
                    document_id=document_id,
                    text=chunk.text,
                    metadata=chunk.metadata,
                    created_at=chunk_db.created_at
                )
                
                chunk_responses.append(chunk_response)
            
            logger.info(f"文档分块完成: {document_id}, 生成{len(chunks)}个分块")
            return chunk_responses
            
        except Exception as e:
            logger.error(f"文档分块失败: {document_id}, 错误: {e}")
            raise
    
    async def index_document(self, document_id: str, index_type: str = "hybrid") -> Dict[str, Any]:
        """
        为文档构建索引
        
        Args:
            document_id: 文档ID
            index_type: 索引类型
        
        Returns:
            Dict[str, Any]: 索引结果
        """
        try:
            # 获取文档分块
            chunks = await self._get_document_chunks(document_id)
            if not chunks:
                raise ValueError(f"文档分块不存在: {document_id}")
            
            # 构建索引
            result = await self.indexer.build_index(
                document_id=document_id,
                chunks=chunks,
                index_type=index_type
            )
            
            logger.info(f"文档索引构建完成: {document_id}")
            return result
            
        except Exception as e:
            logger.error(f"文档索引构建失败: {document_id}, 错误: {e}")
            raise
    
    async def get_document_versions(self, document_id: str) -> List[Dict[str, Any]]:
        """
        获取文档版本历史
        
        Args:
            document_id: 文档ID
        
        Returns:
            List[Dict[str, Any]]: 版本历史
        """
        try:
            versions = await self.version_manager.get_version_history(document_id)
            return [version.dict() for version in versions]
            
        except Exception as e:
            logger.error(f"获取文档版本失败: {document_id}, 错误: {e}")
            raise
    
    async def batch_process_documents(self, document_ids: List[str],
                                    operations: List[str]) -> Dict[str, Any]:
        """
        批量处理文档
        
        Args:
            document_ids: 文档ID列表
            operations: 操作列表
        
        Returns:
            Dict[str, Any]: 批处理结果
        """
        try:
            task_id = str(uuid.uuid4())
            total_documents = len(document_ids)
            success_count = 0
            error_count = 0
            errors = []
            
            logger.info(f"开始批量处理: {task_id}, {total_documents}个文档")
            
            for document_id in document_ids:
                try:
                    # 执行指定操作
                    for operation in operations:
                        if operation == "chunk":
                            await self.chunk_document(document_id)
                        elif operation == "index":
                            await self.index_document(document_id)
                    
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    errors.append({
                        "document_id": document_id,
                        "error": str(e)
                    })
                    logger.error(f"文档处理失败: {document_id}, 错误: {e}")
            
            result = {
                "task_id": task_id,
                "total_documents": total_documents,
                "processed_documents": success_count + error_count,
                "success_count": success_count,
                "error_count": error_count,
                "status": "completed",
                "errors": errors
            }
            
            logger.info(f"批量处理完成: {task_id}")
            return result
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            raise
    
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            components = {
                "parser": "healthy",
                "chunker": "healthy",
                "indexer": "healthy",
                "version_manager": "healthy"
            }
            
            # 这里可以添加更详细的健康检查逻辑
            
            return components
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def _get_document_content(self, document_id: str) -> Optional[DocumentContent]:
        """获取文档内容"""
        # 首先从缓存获取
        if document_id in self.documents_cache:
            return self.documents_cache[document_id]
        
        # 这里应该从数据库或存储中获取
        # 暂时返回None
        return None
    
    async def _get_document_chunks(self, document_id: str) -> List[Chunk]:
        """获取文档分块"""
        # 这里应该从数据库获取分块数据
        # 暂时返回空列表
        return []
    
    async def list_documents(self, page: int = 1, size: int = 20,
                           search: Optional[str] = None) -> Dict[str, Any]:
        """
        获取文档列表

        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词

        Returns:
            Dict[str, Any]: 文档列表
        """
        try:
            # 这里应该从数据库获取文档列表
            # 暂时使用缓存数据模拟
            all_documents = []
            for doc_id, content in self.documents_cache.items():
                doc_info = {
                    "document_id": doc_id,
                    "filename": content.filename,
                    "format": content.format,
                    "created_at": "2025-08-27T00:00:00Z",
                    "status": "processed"
                }

                # 如果有搜索关键词，进行过滤
                if search:
                    if search.lower() in content.filename.lower() or search.lower() in content.text.lower():
                        all_documents.append(doc_info)
                else:
                    all_documents.append(doc_info)

            # 分页
            start = (page - 1) * size
            end = start + size
            documents = all_documents[start:end]

            return {
                "documents": documents,
                "total": len(all_documents),
                "page": page,
                "size": size,
                "total_pages": (len(all_documents) + size - 1) // size
            }

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            raise

    async def get_document(self, document_id: str) -> Optional[DocumentResponse]:
        """
        获取文档详情

        Args:
            document_id: 文档ID

        Returns:
            Optional[DocumentResponse]: 文档详情
        """
        try:
            content = self.documents_cache.get(document_id)
            if not content:
                return None

            return DocumentResponse(
                document_id=document_id,
                filename=content.filename,
                content=content.text,
                metadata=content.metadata,
                images=content.images,
                tables=content.tables,
                created_at=datetime.utcnow(),
                status="processed"
            )

        except Exception as e:
            logger.error(f"获取文档详情失败: {document_id}, 错误: {e}")
            raise

    async def delete_document(self, document_id: str) -> Dict[str, Any]:
        """
        删除文档

        Args:
            document_id: 文档ID

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 删除缓存中的文档
            if document_id in self.documents_cache:
                del self.documents_cache[document_id]

            # 删除索引
            if self.indexer:
                await self.indexer.delete_document_index(document_id)

            # 删除版本历史
            if self.version_manager:
                # 这里可以添加删除版本历史的逻辑
                pass

            logger.info(f"文档删除完成: {document_id}")

            return {
                "document_id": document_id,
                "status": "deleted",
                "deleted_at": datetime.utcnow()
            }

        except Exception as e:
            logger.error(f"文档删除失败: {document_id}, 错误: {e}")
            raise

    async def reprocess_document(self, document_id: str,
                               operations: List[str]) -> Dict[str, Any]:
        """
        重新处理文档

        Args:
            document_id: 文档ID
            operations: 处理操作列表

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            content = self.documents_cache.get(document_id)
            if not content:
                raise ValueError(f"文档不存在: {document_id}")

            results = {}

            for operation in operations:
                if operation == "chunk":
                    chunks = await self.chunk_document(document_id)
                    results["chunk"] = {"status": "success", "chunk_count": len(chunks)}
                elif operation == "index":
                    index_result = await self.index_document(document_id)
                    results["index"] = index_result
                else:
                    results[operation] = {"status": "skipped", "reason": "unknown_operation"}

            logger.info(f"文档重新处理完成: {document_id}")

            return {
                "document_id": document_id,
                "operations": operations,
                "results": results,
                "processed_at": datetime.utcnow()
            }

        except Exception as e:
            logger.error(f"文档重新处理失败: {document_id}, 错误: {e}")
            raise

    async def search_documents(self, query: str, search_type: str = "hybrid",
                             top_k: int = 10, document_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        搜索文档

        Args:
            query: 搜索查询
            search_type: 搜索类型
            top_k: 返回结果数量
            document_ids: 限制搜索的文档ID列表

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            results = []

            if search_type in ["fulltext", "hybrid"]:
                # 全文搜索
                if self.indexer:
                    fulltext_results = await self.indexer.search_fulltext(
                        query=query,
                        document_ids=document_ids,
                        size=top_k
                    )
                    if fulltext_results.get("status") == "success":
                        results.extend(fulltext_results.get("results", []))

            if search_type in ["vector", "hybrid"]:
                # 向量搜索（待实现）
                logger.info("向量搜索功能待向量化服务完成后实现")

            # 简单的文本匹配搜索（作为fallback）
            if not results:
                for doc_id, content in self.documents_cache.items():
                    if document_ids and doc_id not in document_ids:
                        continue

                    if query.lower() in content.text.lower():
                        results.append({
                            "document_id": doc_id,
                            "filename": content.filename,
                            "score": 0.8,  # 模拟评分
                            "snippet": content.text[:200] + "..."
                        })

                    if len(results) >= top_k:
                        break

            return {
                "query": query,
                "search_type": search_type,
                "total_results": len(results),
                "results": results[:top_k],
                "search_time": 0.1  # 模拟搜索时间
            }

        except Exception as e:
            logger.error(f"文档搜索失败: {query}, 错误: {e}")
            raise

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取系统统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                "documents": {
                    "total_count": len(self.documents_cache),
                    "total_size": sum(len(content.text) for content in self.documents_cache.values()),
                    "formats": {}
                },
                "processing": {
                    "parsed_documents": len(self.documents_cache),
                    "indexed_documents": 0,  # 这里应该从索引器获取
                    "chunked_documents": 0   # 这里应该从数据库获取
                },
                "system": {
                    "uptime": "1h 30m",  # 这里应该计算实际运行时间
                    "memory_usage": "512MB",  # 这里应该获取实际内存使用
                    "cpu_usage": "15%"  # 这里应该获取实际CPU使用
                }
            }

            # 统计文档格式
            for content in self.documents_cache.values():
                format_type = content.format
                stats["documents"]["formats"][format_type] = stats["documents"]["formats"].get(format_type, 0) + 1

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            raise

    async def cleanup(self):
        """清理资源"""
        logger.info("清理文档服务资源...")

        # 清理各个组件
        if self.parser:
            await self.parser.cleanup()
        if self.chunker:
            await self.chunker.cleanup()
        if self.indexer:
            await self.indexer.cleanup()
        if self.version_manager:
            await self.version_manager.cleanup()

        # 清理缓存
        self.documents_cache.clear()

        logger.info("文档服务资源清理完成")
