"""
文档服务主应用
提供文档解析、分块、索引等核心功能
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import List, Optional
import asyncio
from contextlib import asynccontextmanager

from app.config.settings import get_settings
from app.core.document_parser import DocumentParser
from app.core.intelligent_chunker import IntelligentChunker
from app.core.document_indexer import DocumentIndexer
from app.core.version_manager import DocumentVersionManager
from app.models.document import DocumentCreate, DocumentResponse, ChunkResponse
from app.services.document_service import DocumentService
from app.utils.logger import get_logger
from app.middleware.error_handler import add_error_handlers
from app.middleware.metrics import add_metrics_middleware

logger = get_logger(__name__)
settings = get_settings()

# 全局服务实例
document_service: Optional[DocumentService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global document_service
    
    # 启动时初始化
    logger.info("🚀 启动文档服务...")
    
    try:
        # 初始化核心组件
        document_parser = DocumentParser()
        intelligent_chunker = IntelligentChunker()
        document_indexer = DocumentIndexer()
        version_manager = DocumentVersionManager()
        
        # 初始化文档服务
        document_service = DocumentService(
            parser=document_parser,
            chunker=intelligent_chunker,
            indexer=document_indexer,
            version_manager=version_manager
        )
        
        await document_service.initialize()
        logger.info("✅ 文档服务初始化完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 文档服务初始化失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("🔄 关闭文档服务...")
        if document_service:
            await document_service.cleanup()
        logger.info("✅ 文档服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="RAG文档服务",
    description="提供文档解析、智能分块、索引构建等功能",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加错误处理和监控中间件
add_error_handlers(app)
add_metrics_middleware(app)


def get_document_service() -> DocumentService:
    """获取文档服务实例"""
    if document_service is None:
        raise HTTPException(status_code=500, detail="文档服务未初始化")
    return document_service


@app.get("/")
async def root():
    """根路径健康检查"""
    return {"message": "RAG文档服务运行正常", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        service = get_document_service()
        health_status = await service.health_check()
        return {"status": "healthy", "details": health_status}
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


@app.post("/api/v1/documents/parse", response_model=DocumentResponse)
async def parse_document(
    file: UploadFile = File(...),
    extract_metadata: bool = True,
    perform_ocr: bool = False,
    service: DocumentService = Depends(get_document_service)
):
    """
    解析上传的文档
    
    Args:
        file: 上传的文件
        extract_metadata: 是否提取元数据
        perform_ocr: 是否执行OCR
        service: 文档服务实例
    
    Returns:
        DocumentResponse: 解析结果
    """
    try:
        logger.info(f"开始解析文档: {file.filename}")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 解析文档
        result = await service.parse_document(
            file_content=file_content,
            filename=file.filename,
            extract_metadata=extract_metadata,
            perform_ocr=perform_ocr
        )
        
        logger.info(f"文档解析完成: {file.filename}")
        return result
        
    except Exception as e:
        logger.error(f"文档解析失败: {file.filename}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"文档解析失败: {str(e)}")


@app.post("/api/v1/documents/chunk", response_model=List[ChunkResponse])
async def chunk_document(
    document_id: str,
    strategy: str = "semantic",
    max_length: int = 512,
    overlap: int = 64,
    service: DocumentService = Depends(get_document_service)
):
    """
    对文档进行智能分块
    
    Args:
        document_id: 文档ID
        strategy: 分块策略 (sentence, paragraph, semantic, sliding_window)
        max_length: 最大分块长度
        overlap: 重叠长度
        service: 文档服务实例
    
    Returns:
        List[ChunkResponse]: 分块结果列表
    """
    try:
        logger.info(f"开始分块文档: {document_id}, 策略: {strategy}")
        
        chunks = await service.chunk_document(
            document_id=document_id,
            strategy=strategy,
            max_length=max_length,
            overlap=overlap
        )
        
        logger.info(f"文档分块完成: {document_id}, 分块数量: {len(chunks)}")
        return chunks
        
    except Exception as e:
        logger.error(f"文档分块失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"文档分块失败: {str(e)}")


@app.post("/api/v1/documents/index")
async def index_document(
    document_id: str,
    index_type: str = "hybrid",
    service: DocumentService = Depends(get_document_service)
):
    """
    为文档构建索引
    
    Args:
        document_id: 文档ID
        index_type: 索引类型 (fulltext, vector, hybrid)
        service: 文档服务实例
    
    Returns:
        dict: 索引构建结果
    """
    try:
        logger.info(f"开始构建索引: {document_id}, 类型: {index_type}")
        
        result = await service.index_document(
            document_id=document_id,
            index_type=index_type
        )
        
        logger.info(f"索引构建完成: {document_id}")
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"索引构建失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"索引构建失败: {str(e)}")


@app.get("/api/v1/documents/{document_id}/versions")
async def get_document_versions(
    document_id: str,
    service: DocumentService = Depends(get_document_service)
):
    """
    获取文档版本历史
    
    Args:
        document_id: 文档ID
        service: 文档服务实例
    
    Returns:
        dict: 版本历史信息
    """
    try:
        versions = await service.get_document_versions(document_id)
        return {"document_id": document_id, "versions": versions}
        
    except Exception as e:
        logger.error(f"获取文档版本失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=404, detail=f"文档版本获取失败: {str(e)}")


@app.post("/api/v1/documents/batch/process")
async def batch_process_documents(
    document_ids: List[str],
    operations: List[str] = ["parse", "chunk", "index"],
    service: DocumentService = Depends(get_document_service)
):
    """
    批量处理文档
    
    Args:
        document_ids: 文档ID列表
        operations: 操作列表
        service: 文档服务实例
    
    Returns:
        dict: 批处理结果
    """
    try:
        logger.info(f"开始批量处理文档: {len(document_ids)}个文档")
        
        result = await service.batch_process_documents(
            document_ids=document_ids,
            operations=operations
        )
        
        logger.info(f"批量处理完成: 成功{result['success_count']}个, 失败{result['error_count']}个")
        return result
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise HTTPException(status_code=400, detail=f"批量处理失败: {str(e)}")


@app.get("/api/v1/documents")
async def list_documents(
    page: int = 1,
    size: int = 20,
    search: Optional[str] = None,
    service: DocumentService = Depends(get_document_service)
):
    """
    获取文档列表

    Args:
        page: 页码
        size: 每页大小
        search: 搜索关键词
        service: 文档服务实例

    Returns:
        dict: 文档列表
    """
    try:
        result = await service.list_documents(
            page=page,
            size=size,
            search=search
        )
        return result
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@app.get("/api/v1/documents/{document_id}")
async def get_document(
    document_id: str,
    service: DocumentService = Depends(get_document_service)
):
    """
    获取文档详情

    Args:
        document_id: 文档ID
        service: 文档服务实例

    Returns:
        DocumentResponse: 文档详情
    """
    try:
        result = await service.get_document(document_id)
        if not result:
            raise HTTPException(status_code=404, detail="文档不存在")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详情失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取文档详情失败: {str(e)}")


@app.delete("/api/v1/documents/{document_id}")
async def delete_document(
    document_id: str,
    service: DocumentService = Depends(get_document_service)
):
    """
    删除文档

    Args:
        document_id: 文档ID
        service: 文档服务实例

    Returns:
        dict: 删除结果
    """
    try:
        result = await service.delete_document(document_id)
        logger.info(f"文档删除成功: {document_id}")
        return result
    except Exception as e:
        logger.error(f"文档删除失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"文档删除失败: {str(e)}")


@app.post("/api/v1/documents/{document_id}/reprocess")
async def reprocess_document(
    document_id: str,
    operations: List[str] = ["chunk", "index"],
    service: DocumentService = Depends(get_document_service)
):
    """
    重新处理文档

    Args:
        document_id: 文档ID
        operations: 处理操作列表
        service: 文档服务实例

    Returns:
        dict: 处理结果
    """
    try:
        result = await service.reprocess_document(document_id, operations)
        logger.info(f"文档重新处理完成: {document_id}")
        return result
    except Exception as e:
        logger.error(f"文档重新处理失败: {document_id}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"文档重新处理失败: {str(e)}")


@app.get("/api/v1/search")
async def search_documents(
    query: str,
    search_type: str = "hybrid",
    top_k: int = 10,
    document_ids: Optional[List[str]] = None,
    service: DocumentService = Depends(get_document_service)
):
    """
    搜索文档

    Args:
        query: 搜索查询
        search_type: 搜索类型 (fulltext, vector, hybrid)
        top_k: 返回结果数量
        document_ids: 限制搜索的文档ID列表
        service: 文档服务实例

    Returns:
        dict: 搜索结果
    """
    try:
        result = await service.search_documents(
            query=query,
            search_type=search_type,
            top_k=top_k,
            document_ids=document_ids
        )
        logger.info(f"文档搜索完成: 查询={query}, 结果数={len(result.get('results', []))}")
        return result
    except Exception as e:
        logger.error(f"文档搜索失败: {query}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"文档搜索失败: {str(e)}")


@app.get("/api/v1/statistics")
async def get_statistics(
    service: DocumentService = Depends(get_document_service)
):
    """
    获取系统统计信息

    Args:
        service: 文档服务实例

    Returns:
        dict: 统计信息
    """
    try:
        stats = await service.get_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@app.get("/metrics")
async def get_metrics():
    """获取Prometheus监控指标"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    return JSONResponse(
        content=generate_latest().decode('utf-8'),
        media_type=CONTENT_TYPE_LATEST
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
