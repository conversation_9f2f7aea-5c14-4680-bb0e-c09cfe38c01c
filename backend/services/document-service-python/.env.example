# 文档服务环境配置示例

# 基础配置
DEBUG=true
HOST=0.0.0.0
PORT=8001
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/rag_db

# Redis配置
REDIS_URL=redis://localhost:6379

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=rag-documents
MINIO_USE_SSL=false

# 文档处理配置
MAX_FILE_SIZE=104857600  # 100MB
DEFAULT_CHUNK_SIZE=512
DEFAULT_OVERLAP=64
MAX_CHUNK_SIZE=2048

# OCR配置
OCR_ENABLED=true
OCR_LANGUAGE=chi_sim+eng
OCR_CONFIDENCE_THRESHOLD=0.8

# 嵌入模型配置
DEFAULT_EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
EMBEDDING_DIMENSION=384
DEVICE=auto

# PDF解析配置
PDF_EXTRACT_IMAGES=true
PDF_EXTRACT_TABLES=true
PDF_DPI=300

# Word文档配置
DOCX_EXTRACT_TABLES=true
DOCX_PRESERVE_FORMATTING=true

# HTML解析配置
HTML_EXTRACT_TEXT_ONLY=false
HTML_REMOVE_SCRIPTS=true

# 分块配置
SENTENCE_MAX_LENGTH=512
SENTENCE_OVERLAP=0
PARAGRAPH_MIN_LENGTH=100
PARAGRAPH_MAX_LENGTH=1024
SEMANTIC_SIMILARITY_THRESHOLD=0.7
SEMANTIC_MAX_LENGTH=800
SLIDING_WINDOW_SIZE=256
SLIDING_WINDOW_OVERLAP=64

# Elasticsearch配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=rag_documents

# 索引配置
FULLTEXT_ANALYZER=ik_max_word
FULLTEXT_SEARCH_ANALYZER=ik_smart
VECTOR_INDEX_TYPE=hnsw
VECTOR_SIMILARITY_METRIC=cosine
HNSW_M=16
HNSW_EF_CONSTRUCTION=200
HNSW_EF_SEARCH=100

# 任务队列配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=8002

# 性能配置
MAX_WORKERS=4
BATCH_SIZE=32
TIMEOUT=300

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
