"""
文档解析器测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
import io

from app.core.document_parser import DocumentParser
from app.models.document import DocumentContent


class TestDocumentParser:
    """文档解析器测试类"""
    
    @pytest.fixture
    def parser(self):
        """创建解析器实例"""
        return DocumentParser()
    
    @pytest.mark.asyncio
    async def test_parse_text_document(self, parser):
        """测试文本文档解析"""
        content = "这是一个测试文档。\n包含多行文本。"
        filename = "test.txt"
        
        result = await parser.parse_document(
            file_content=content.encode('utf-8'),
            filename=filename,
            extract_metadata=True,
            perform_ocr=False
        )
        
        assert isinstance(result, DocumentContent)
        assert result.text == content
        assert result.filename == filename
        assert result.format == "text/plain"
    
    @pytest.mark.asyncio
    async def test_parse_markdown_document(self, parser):
        """测试Markdown文档解析"""
        content = "# 标题\n\n这是一个**粗体**文本。"
        filename = "test.md"
        
        result = await parser.parse_document(
            file_content=content.encode('utf-8'),
            filename=filename,
            extract_metadata=True,
            perform_ocr=False
        )
        
        assert isinstance(result, DocumentContent)
        assert "标题" in result.text
        assert "粗体" in result.text
        assert result.filename == filename
        assert result.format == "text/markdown"
    
    @pytest.mark.asyncio
    async def test_parse_html_document(self, parser):
        """测试HTML文档解析"""
        content = "<html><body><h1>标题</h1><p>段落内容</p></body></html>"
        filename = "test.html"
        
        result = await parser.parse_document(
            file_content=content.encode('utf-8'),
            filename=filename,
            extract_metadata=True,
            perform_ocr=False
        )
        
        assert isinstance(result, DocumentContent)
        assert "标题" in result.text
        assert "段落内容" in result.text
        assert result.filename == filename
        assert result.format == "text/html"
    
    def test_detect_mime_type(self, parser):
        """测试MIME类型检测"""
        # 测试PDF
        pdf_content = b'%PDF-1.4'
        mime_type = parser._detect_mime_type("test.pdf", pdf_content)
        assert mime_type == "application/pdf"
        
        # 测试文本
        text_content = b'Hello World'
        mime_type = parser._detect_mime_type("test.txt", text_content)
        assert mime_type == "text/plain"
        
        # 测试Word文档
        mime_type = parser._detect_mime_type("test.docx", b'PK\x03\x04')
        assert mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    
    @pytest.mark.asyncio
    async def test_unsupported_format(self, parser):
        """测试不支持的文件格式"""
        content = b"binary content"
        filename = "test.xyz"
        
        with pytest.raises(ValueError, match="不支持的文件格式"):
            await parser.parse_document(
                file_content=content,
                filename=filename,
                extract_metadata=True,
                perform_ocr=False
            )
    
    @pytest.mark.asyncio
    async def test_extract_metadata(self, parser):
        """测试元数据提取"""
        content = "测试文档内容"
        filename = "test.txt"
        
        result = await parser.parse_document(
            file_content=content.encode('utf-8'),
            filename=filename,
            extract_metadata=True,
            perform_ocr=False
        )
        
        assert result.metadata is not None
        assert result.metadata.filename == filename
        assert result.metadata.file_size == len(content.encode('utf-8'))
        assert result.metadata.mime_type == "text/plain"
    
    @pytest.mark.asyncio
    async def test_cleanup(self, parser):
        """测试资源清理"""
        await parser.cleanup()
        # 验证线程池已关闭
        assert parser.executor._shutdown


if __name__ == "__main__":
    pytest.main([__file__])
