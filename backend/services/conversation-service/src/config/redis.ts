/**
 * Redis连接配置和管理
 */

import Redis from 'ioredis';
import { config } from './index';
import { logger } from '../utils/logger';

let redisClient: Redis | null = null;
let redisSubscriber: Redis | null = null;
let redisPublisher: Redis | null = null;

/**
 * 创建Redis客户端
 */
export function createRedisClient(role: string = 'client'): Redis {
  const client = new Redis({
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    db: config.redis.db,
    keyPrefix: config.redis.keyPrefix,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: config.redis.maxRetries,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
  });

  // 连接事件监听
  client.on('connect', () => {
    logger.info(`Redis ${role} 连接建立`);
  });

  client.on('ready', () => {
    logger.info(`Redis ${role} 连接就绪`);
  });

  client.on('error', (error) => {
    logger.error(`Redis ${role} 连接错误:`, error);
  });

  client.on('close', () => {
    logger.warn(`Redis ${role} 连接关闭`);
  });

  client.on('reconnecting', () => {
    logger.info(`Redis ${role} 重新连接中...`);
  });

  return client;
}

/**
 * 连接Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    // 创建主客户端
    redisClient = createRedisClient('client');
    await redisClient.connect();
    
    // 创建发布者客户端
    redisPublisher = createRedisClient('publisher');
    await redisPublisher.connect();
    
    // 创建订阅者客户端
    redisSubscriber = createRedisClient('subscriber');
    await redisSubscriber.connect();
    
    // 测试连接
    await redisClient.ping();
    logger.info('Redis连接测试成功');
  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 获取Redis客户端
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    throw new Error('Redis客户端未初始化');
  }
  return redisClient;
}

/**
 * 获取Redis发布者
 */
export function getRedisPublisher(): Redis {
  if (!redisPublisher) {
    throw new Error('Redis发布者未初始化');
  }
  return redisPublisher;
}

/**
 * 获取Redis订阅者
 */
export function getRedisSubscriber(): Redis {
  if (!redisSubscriber) {
    throw new Error('Redis订阅者未初始化');
  }
  return redisSubscriber;
}

/**
 * 关闭Redis连接
 */
export async function closeRedis(): Promise<void> {
  const clients = [
    { client: redisClient, name: 'client' },
    { client: redisPublisher, name: 'publisher' },
    { client: redisSubscriber, name: 'subscriber' },
  ];

  for (const { client, name } of clients) {
    if (client) {
      await client.quit();
      logger.info(`Redis ${name} 连接已关闭`);
    }
  }

  redisClient = null;
  redisPublisher = null;
  redisSubscriber = null;
}

/**
 * Redis缓存操作类
 */
export class RedisCache {
  private client: Redis;

  constructor(client: Redis) {
    this.client = client;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    
    if (ttl) {
      await this.client.setex(key, ttl, serializedValue);
    } else {
      await this.client.set(key, serializedValue);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    const value = await this.client.get(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('缓存值解析失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    await this.client.expire(key, ttl);
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    return await this.client.ttl(key);
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const values = await this.client.mget(...keys);
    
    return values.map(value => {
      if (!value) {
        return null;
      }

      try {
        return JSON.parse(value) as T;
      } catch (error) {
        logger.error('批量缓存值解析失败:', error);
        return null;
      }
    });
  }

  /**
   * 批量设置
   */
  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<void> {
    const pipeline = this.client.pipeline();
    
    for (const [key, value] of Object.entries(keyValuePairs)) {
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        pipeline.setex(key, ttl, serializedValue);
      } else {
        pipeline.set(key, serializedValue);
      }
    }
    
    await pipeline.exec();
  }

  /**
   * 增加计数器
   */
  async incr(key: string, increment: number = 1): Promise<number> {
    if (increment === 1) {
      return await this.client.incr(key);
    } else {
      return await this.client.incrby(key, increment);
    }
  }

  /**
   * 减少计数器
   */
  async decr(key: string, decrement: number = 1): Promise<number> {
    if (decrement === 1) {
      return await this.client.decr(key);
    } else {
      return await this.client.decrby(key, decrement);
    }
  }

  /**
   * 列表操作 - 左推入
   */
  async lpush(key: string, ...values: any[]): Promise<number> {
    const serializedValues = values.map(v => JSON.stringify(v));
    return await this.client.lpush(key, ...serializedValues);
  }

  /**
   * 列表操作 - 右推入
   */
  async rpush(key: string, ...values: any[]): Promise<number> {
    const serializedValues = values.map(v => JSON.stringify(v));
    return await this.client.rpush(key, ...serializedValues);
  }

  /**
   * 列表操作 - 左弹出
   */
  async lpop<T>(key: string): Promise<T | null> {
    const value = await this.client.lpop(key);
    
    if (!value) {
      return null;
    }

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('列表值解析失败:', error);
      return null;
    }
  }

  /**
   * 列表操作 - 获取范围
   */
  async lrange<T>(key: string, start: number, stop: number): Promise<T[]> {
    const values = await this.client.lrange(key, start, stop);
    
    return values.map(value => {
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        logger.error('列表值解析失败:', error);
        return null;
      }
    }).filter(v => v !== null) as T[];
  }

  /**
   * 集合操作 - 添加成员
   */
  async sadd(key: string, ...members: any[]): Promise<number> {
    const serializedMembers = members.map(m => JSON.stringify(m));
    return await this.client.sadd(key, ...serializedMembers);
  }

  /**
   * 集合操作 - 获取所有成员
   */
  async smembers<T>(key: string): Promise<T[]> {
    const members = await this.client.smembers(key);
    
    return members.map(member => {
      try {
        return JSON.parse(member) as T;
      } catch (error) {
        logger.error('集合成员解析失败:', error);
        return null;
      }
    }).filter(m => m !== null) as T[];
  }

  /**
   * 有序集合操作 - 添加成员
   */
  async zadd(key: string, score: number, member: any): Promise<number> {
    const serializedMember = JSON.stringify(member);
    return await this.client.zadd(key, score, serializedMember);
  }

  /**
   * 有序集合操作 - 获取范围
   */
  async zrange<T>(key: string, start: number, stop: number): Promise<T[]> {
    const members = await this.client.zrange(key, start, stop);
    
    return members.map(member => {
      try {
        return JSON.parse(member) as T;
      } catch (error) {
        logger.error('有序集合成员解析失败:', error);
        return null;
      }
    }).filter(m => m !== null) as T[];
  }
}

/**
 * 创建缓存实例
 */
export function createCache(): RedisCache {
  return new RedisCache(getRedisClient());
}

/**
 * Redis发布订阅管理器
 */
export class RedisPubSub {
  private publisher: Redis;
  private subscriber: Redis;
  private listeners: Map<string, Set<Function>> = new Map();

  constructor(publisher: Redis, subscriber: Redis) {
    this.publisher = publisher;
    this.subscriber = subscriber;
    
    // 监听消息
    this.subscriber.on('message', (channel, message) => {
      const listeners = this.listeners.get(channel);
      if (listeners) {
        try {
          const data = JSON.parse(message);
          listeners.forEach(listener => listener(data));
        } catch (error) {
          logger.error('解析发布订阅消息失败:', error);
        }
      }
    });
  }

  /**
   * 发布消息
   */
  async publish(channel: string, data: any): Promise<void> {
    const message = JSON.stringify(data);
    await this.publisher.publish(channel, message);
  }

  /**
   * 订阅频道
   */
  async subscribe(channel: string, listener: Function): Promise<void> {
    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, new Set());
      await this.subscriber.subscribe(channel);
    }
    
    this.listeners.get(channel)!.add(listener);
  }

  /**
   * 取消订阅
   */
  async unsubscribe(channel: string, listener?: Function): Promise<void> {
    const listeners = this.listeners.get(channel);
    
    if (!listeners) {
      return;
    }

    if (listener) {
      listeners.delete(listener);
      
      if (listeners.size === 0) {
        this.listeners.delete(channel);
        await this.subscriber.unsubscribe(channel);
      }
    } else {
      this.listeners.delete(channel);
      await this.subscriber.unsubscribe(channel);
    }
  }
}

/**
 * 创建发布订阅实例
 */
export function createPubSub(): RedisPubSub {
  return new RedisPubSub(getRedisPublisher(), getRedisSubscriber());
}
