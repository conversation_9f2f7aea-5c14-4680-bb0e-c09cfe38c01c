/**
 * 对话服务配置模块
 */

import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

export interface Config {
  // 服务配置
  host: string;
  port: number;
  nodeEnv: string;
  
  // 数据库配置
  database: {
    url: string;
    poolSize: number;
    maxOverflow: number;
    timeout: number;
  };
  
  // Redis配置
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
    maxRetries: number;
    retryDelay: number;
  };
  
  // 外部服务配置
  services: {
    // 生成服务
    generation: {
      url: string;
      timeout: number;
      retries: number;
    };
    
    // 检索服务
    retrieval: {
      url: string;
      timeout: number;
      retries: number;
    };
    
    // 文档服务
    document: {
      url: string;
      timeout: number;
      retries: number;
    };
  };
  
  // WebSocket配置
  websocket: {
    pingTimeout: number;
    pingInterval: number;
    maxConnections: number;
    connectionTimeout: number;
    messageRateLimit: number;
  };
  
  // 对话配置
  conversation: {
    maxHistoryLength: number;
    maxMessageLength: number;
    maxConcurrentConversations: number;
    sessionTimeout: number;
    autoSaveInterval: number;
  };
  
  // 消息配置
  message: {
    maxRetries: number;
    retryDelay: number;
    batchSize: number;
    processingTimeout: number;
  };
  
  // 缓存配置
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
    compressionEnabled: boolean;
  };
  
  // 队列配置
  queue: {
    concurrency: number;
    maxRetries: number;
    retryDelay: number;
    removeOnComplete: number;
    removeOnFail: number;
  };
  
  // 限流配置
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  
  // 监控配置
  monitoring: {
    enableMetrics: boolean;
    metricsPort: number;
    enableTracing: boolean;
    healthCheckInterval: number;
  };
  
  // 安全配置
  security: {
    enableAuth: boolean;
    jwtSecret: string;
    jwtExpiration: string;
    allowedOrigins: string[];
    maxRequestSize: string;
    enableEncryption: boolean;
  };
  
  // 日志配置
  logging: {
    level: string;
    enableFileLogging: boolean;
    logDirectory: string;
    maxFileSize: string;
    maxFiles: number;
  };
}

/**
 * 获取配置
 */
export const config: Config = {
  // 服务配置
  host: process.env.HOST || '0.0.0.0',
  port: parseInt(process.env.PORT || '3002'),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // 数据库配置
  database: {
    url: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/conversation_service',
    poolSize: parseInt(process.env.DB_POOL_SIZE || '20'),
    maxOverflow: parseInt(process.env.DB_MAX_OVERFLOW || '30'),
    timeout: parseInt(process.env.DB_TIMEOUT || '30000'),
  },
  
  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'conversation:',
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.REDIS_RETRY_DELAY || '1000'),
  },
  
  // 外部服务配置
  services: {
    generation: {
      url: process.env.GENERATION_SERVICE_URL || 'http://localhost:3000',
      timeout: parseInt(process.env.GENERATION_SERVICE_TIMEOUT || '30000'),
      retries: parseInt(process.env.GENERATION_SERVICE_RETRIES || '3'),
    },
    retrieval: {
      url: process.env.RETRIEVAL_SERVICE_URL || 'http://localhost:8002',
      timeout: parseInt(process.env.RETRIEVAL_SERVICE_TIMEOUT || '30000'),
      retries: parseInt(process.env.RETRIEVAL_SERVICE_RETRIES || '3'),
    },
    document: {
      url: process.env.DOCUMENT_SERVICE_URL || 'http://localhost:3001',
      timeout: parseInt(process.env.DOCUMENT_SERVICE_TIMEOUT || '30000'),
      retries: parseInt(process.env.DOCUMENT_SERVICE_RETRIES || '3'),
    },
  },
  
  // WebSocket配置
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT || '60000'),
    pingInterval: parseInt(process.env.WS_PING_INTERVAL || '25000'),
    maxConnections: parseInt(process.env.WS_MAX_CONNECTIONS || '1000'),
    connectionTimeout: parseInt(process.env.WS_CONNECTION_TIMEOUT || '30000'),
    messageRateLimit: parseInt(process.env.WS_MESSAGE_RATE_LIMIT || '10'),
  },
  
  // 对话配置
  conversation: {
    maxHistoryLength: parseInt(process.env.CONVERSATION_MAX_HISTORY || '100'),
    maxMessageLength: parseInt(process.env.CONVERSATION_MAX_MESSAGE_LENGTH || '10000'),
    maxConcurrentConversations: parseInt(process.env.CONVERSATION_MAX_CONCURRENT || '10'),
    sessionTimeout: parseInt(process.env.CONVERSATION_SESSION_TIMEOUT || '3600000'), // 1小时
    autoSaveInterval: parseInt(process.env.CONVERSATION_AUTO_SAVE_INTERVAL || '30000'), // 30秒
  },
  
  // 消息配置
  message: {
    maxRetries: parseInt(process.env.MESSAGE_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.MESSAGE_RETRY_DELAY || '1000'),
    batchSize: parseInt(process.env.MESSAGE_BATCH_SIZE || '10'),
    processingTimeout: parseInt(process.env.MESSAGE_PROCESSING_TIMEOUT || '60000'),
  },
  
  // 缓存配置
  cache: {
    enabled: process.env.CACHE_ENABLED !== 'false',
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
    compressionEnabled: process.env.CACHE_COMPRESSION_ENABLED === 'true',
  },
  
  // 队列配置
  queue: {
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '5'),
    maxRetries: parseInt(process.env.QUEUE_MAX_RETRIES || '3'),
    retryDelay: parseInt(process.env.QUEUE_RETRY_DELAY || '5000'),
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE || '100'),
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL || '50'),
  },
  
  // 限流配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true',
  },
  
  // 监控配置
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090'),
    enableTracing: process.env.ENABLE_TRACING === 'true',
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
  },
  
  // 安全配置
  security: {
    enableAuth: process.env.ENABLE_AUTH !== 'false',
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key-here',
    jwtExpiration: process.env.JWT_EXPIRATION || '24h',
    allowedOrigins: (process.env.ALLOWED_ORIGINS || 'http://localhost:3100').split(','),
    maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
    enableEncryption: process.env.ENABLE_ENCRYPTION === 'true',
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableFileLogging: process.env.ENABLE_FILE_LOGGING === 'true',
    logDirectory: process.env.LOG_DIRECTORY || './logs',
    maxFileSize: process.env.LOG_MAX_FILE_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
  },
};

/**
 * 验证配置
 */
export function validateConfig(): void {
  const errors: string[] = [];
  
  // 验证必需的环境变量
  if (!config.database.url) {
    errors.push('DATABASE_URL 环境变量未设置');
  }
  
  if (config.security.enableAuth && !config.security.jwtSecret) {
    errors.push('启用认证但未设置 JWT_SECRET');
  }
  
  if (config.security.jwtSecret === 'your-secret-key-here' && config.nodeEnv === 'production') {
    errors.push('生产环境必须设置安全的 JWT_SECRET');
  }
  
  // 验证数值范围
  if (config.port < 1 || config.port > 65535) {
    errors.push('端口号必须在1-65535之间');
  }
  
  if (config.conversation.maxHistoryLength < 1) {
    errors.push('对话历史长度必须大于0');
  }
  
  if (config.conversation.maxMessageLength < 1) {
    errors.push('消息最大长度必须大于0');
  }
  
  if (config.websocket.maxConnections < 1) {
    errors.push('WebSocket最大连接数必须大于0');
  }
  
  // 验证URL格式
  const urlFields = [
    { name: 'GENERATION_SERVICE_URL', value: config.services.generation.url },
    { name: 'RETRIEVAL_SERVICE_URL', value: config.services.retrieval.url },
    { name: 'DOCUMENT_SERVICE_URL', value: config.services.document.url },
  ];
  
  for (const field of urlFields) {
    try {
      new URL(field.value);
    } catch (error) {
      errors.push(`${field.name} 不是有效的URL: ${field.value}`);
    }
  }
  
  if (errors.length > 0) {
    throw new Error(`配置验证失败:\n${errors.join('\n')}`);
  }
}

/**
 * 是否为开发环境
 */
export function isDevelopment(): boolean {
  return config.nodeEnv === 'development';
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return config.nodeEnv === 'production';
}

/**
 * 获取Redis连接URL
 */
export function getRedisUrl(): string {
  const { host, port, password, db } = config.redis;
  const auth = password ? `:${password}@` : '';
  return `redis://${auth}${host}:${port}/${db}`;
}
