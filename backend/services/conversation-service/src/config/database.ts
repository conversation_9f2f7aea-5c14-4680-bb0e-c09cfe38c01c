/**
 * 数据库配置和连接管理
 */

import { Pool, PoolClient } from 'pg';
import { config } from './index';
import { logger } from '../utils/logger';

let pool: Pool | null = null;

/**
 * 创建数据库连接池
 */
export function createPool(): Pool {
  return new Pool({
    connectionString: config.database.url,
    max: config.database.poolSize,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: config.database.timeout,
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000,
  });
}

/**
 * 连接数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    pool = createPool();
    
    // 测试连接
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    
    logger.info('数据库连接成功');
    
    // 监听连接池事件
    pool.on('connect', (client) => {
      logger.debug('新的数据库连接建立');
    });
    
    pool.on('error', (err, client) => {
      logger.error('数据库连接池错误', err);
    });
    
    pool.on('remove', (client) => {
      logger.debug('数据库连接被移除');
    });
    
  } catch (error) {
    logger.error('数据库连接失败', error);
    throw error;
  }
}

/**
 * 获取数据库连接池
 */
export function getPool(): Pool {
  if (!pool) {
    throw new Error('数据库连接池未初始化');
  }
  return pool;
}

/**
 * 获取数据库客户端
 */
export async function getClient(): Promise<PoolClient> {
  const pool = getPool();
  return await pool.connect();
}

/**
 * 执行查询
 */
export async function query(text: string, params?: any[]): Promise<any> {
  const pool = getPool();
  const start = Date.now();
  
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('数据库查询执行', {
      query: text,
      duration,
      rows: result.rowCount,
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    logger.error('数据库查询失败', {
      query: text,
      duration,
      error,
    });
    throw error;
  }
}

/**
 * 执行事务
 */
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    logger.info('数据库连接已关闭');
  }
}

/**
 * 数据库健康检查
 */
export async function healthCheck(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health');
    return result.rows[0].health === 1;
  } catch (error) {
    logger.error('数据库健康检查失败', error);
    return false;
  }
}

/**
 * 初始化数据库表
 */
export async function initializeTables(): Promise<void> {
  try {
    // 创建对话表
    await query(`
      CREATE TABLE IF NOT EXISTS conversations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        title VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active',
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_message_at TIMESTAMP WITH TIME ZONE,
        message_count INTEGER DEFAULT 0
      )
    `);
    
    // 创建消息表
    await query(`
      CREATE TABLE IF NOT EXISTS messages (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
        role VARCHAR(20) NOT NULL,
        content TEXT NOT NULL,
        content_type VARCHAR(50) DEFAULT 'text',
        metadata JSONB DEFAULT '{}',
        parent_message_id UUID REFERENCES messages(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        is_deleted BOOLEAN DEFAULT FALSE
      )
    `);
    
    // 创建会话表
    await query(`
      CREATE TABLE IF NOT EXISTS sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        socket_id VARCHAR(255),
        status VARCHAR(20) DEFAULT 'active',
        metadata JSONB DEFAULT '{}',
        last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE
      )
    `);
    
    // 创建消息反馈表
    await query(`
      CREATE TABLE IF NOT EXISTS message_feedback (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
        user_id UUID NOT NULL,
        feedback_type VARCHAR(20) NOT NULL,
        rating INTEGER,
        comment TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    
    // 创建索引
    await query(`
      CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
      CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
      CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
      CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON conversations(last_message_at);
    `);
    
    await query(`
      CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
      CREATE INDEX IF NOT EXISTS idx_messages_role ON messages(role);
      CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
      CREATE INDEX IF NOT EXISTS idx_messages_parent_id ON messages(parent_message_id);
      CREATE INDEX IF NOT EXISTS idx_messages_deleted ON messages(is_deleted);
    `);
    
    await query(`
      CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
      CREATE INDEX IF NOT EXISTS idx_sessions_socket_id ON sessions(socket_id);
      CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
      CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON sessions(last_activity);
    `);
    
    await query(`
      CREATE INDEX IF NOT EXISTS idx_feedback_message_id ON message_feedback(message_id);
      CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON message_feedback(user_id);
      CREATE INDEX IF NOT EXISTS idx_feedback_type ON message_feedback(feedback_type);
    `);
    
    // 创建触发器函数
    await query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);
    
    // 创建触发器
    await query(`
      DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
      CREATE TRIGGER update_conversations_updated_at
        BEFORE UPDATE ON conversations
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    `);
    
    await query(`
      DROP TRIGGER IF EXISTS update_messages_updated_at ON messages;
      CREATE TRIGGER update_messages_updated_at
        BEFORE UPDATE ON messages
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    `);
    
    // 创建更新对话统计的函数
    await query(`
      CREATE OR REPLACE FUNCTION update_conversation_stats()
      RETURNS TRIGGER AS $$
      BEGIN
        IF TG_OP = 'INSERT' THEN
          UPDATE conversations 
          SET 
            message_count = message_count + 1,
            last_message_at = NEW.created_at
          WHERE id = NEW.conversation_id;
          RETURN NEW;
        ELSIF TG_OP = 'DELETE' THEN
          UPDATE conversations 
          SET message_count = message_count - 1
          WHERE id = OLD.conversation_id;
          RETURN OLD;
        END IF;
        RETURN NULL;
      END;
      $$ language 'plpgsql';
    `);
    
    // 创建消息统计触发器
    await query(`
      DROP TRIGGER IF EXISTS update_conversation_stats_trigger ON messages;
      CREATE TRIGGER update_conversation_stats_trigger
        AFTER INSERT OR DELETE ON messages
        FOR EACH ROW
        EXECUTE FUNCTION update_conversation_stats();
    `);
    
    logger.info('数据库表初始化完成');
  } catch (error) {
    logger.error('数据库表初始化失败', error);
    throw error;
  }
}
