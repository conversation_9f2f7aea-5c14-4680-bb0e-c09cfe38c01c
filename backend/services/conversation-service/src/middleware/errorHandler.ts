/**
 * 错误处理中间件
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 业务错误类
 */
export class BusinessError extends AppError {
  constructor(message: string, code: string = 'BUSINESS_ERROR', details?: any) {
    super(message, 400, code, true, details);
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', true, details);
  }
}

/**
 * 认证错误类
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR', true);
  }
}

/**
 * 授权错误类
 */
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR', true);
  }
}

/**
 * 资源未找到错误类
 */
export class NotFoundError extends AppError {
  constructor(message: string = '资源不存在') {
    super(message, 404, 'NOT_FOUND', true);
  }
}

/**
 * 冲突错误类
 */
export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT_ERROR', true, details);
  }
}

/**
 * 限流错误类
 */
export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_ERROR', true);
  }
}

/**
 * 外部服务错误类
 */
export class ExternalServiceError extends AppError {
  constructor(message: string, service: string, details?: any) {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR', true, { service, ...details });
  }
}

/**
 * 超时错误类
 */
export class TimeoutError extends AppError {
  constructor(message: string = '请求超时', operation?: string) {
    super(message, 408, 'TIMEOUT_ERROR', true, { operation });
  }
}

/**
 * 对话错误类
 */
export class ConversationError extends AppError {
  constructor(message: string, code: string = 'CONVERSATION_ERROR', details?: any) {
    super(message, 400, code, true, details);
  }
}

/**
 * 消息错误类
 */
export class MessageError extends AppError {
  constructor(message: string, code: string = 'MESSAGE_ERROR', details?: any) {
    super(message, 400, code, true, details);
  }
}

/**
 * WebSocket错误类
 */
export class WebSocketError extends AppError {
  constructor(message: string, code: string = 'WEBSOCKET_ERROR', details?: any) {
    super(message, 400, code, true, details);
  }
}

/**
 * 错误响应格式
 */
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  requestId?: string;
  path: string;
}

/**
 * 格式化错误响应
 */
function formatErrorResponse(
  error: AppError | Error,
  req: Request,
  includeStack: boolean = false
): ErrorResponse {
  const isAppError = error instanceof AppError;
  
  const response: ErrorResponse = {
    success: false,
    error: {
      code: isAppError ? error.code : 'INTERNAL_ERROR',
      message: error.message || '内部服务器错误',
      details: isAppError ? error.details : undefined
    },
    timestamp: new Date().toISOString(),
    requestId: (req as any).requestId,
    path: req.path
  };

  // 开发环境包含堆栈信息
  if (includeStack && error.stack) {
    response.error.stack = error.stack;
  }

  return response;
}

/**
 * 错误处理中间件
 */
export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  const isAppError = error instanceof AppError;
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // 确定状态码
  let statusCode = 500;
  if (isAppError) {
    statusCode = error.statusCode;
  } else if (error.name === 'ValidationError') {
    statusCode = 400;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
  } else if (error.name === 'CastError') {
    statusCode = 400;
  }

  // 记录错误日志
  const logContext = {
    requestId: (req as any).requestId,
    method: req.method,
    url: req.url,
    statusCode,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  };

  if (statusCode >= 500) {
    // 服务器错误
    logger.error('服务器错误', error, logContext);
  } else if (statusCode >= 400) {
    // 客户端错误
    logger.warn('客户端错误', logContext);
  }

  // 格式化错误响应
  const errorResponse = formatErrorResponse(error, req, isDevelopment);

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

/**
 * 404错误处理中间件
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new NotFoundError(`路由 ${req.method} ${req.path} 不存在`);
  next(error);
}

/**
 * 异步路由错误捕获装饰器
 */
export function catchAsync(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 验证错误处理
 */
export function handleValidationError(errors: any[]): ValidationError {
  const details = errors.map(error => ({
    field: error.param || error.path,
    message: error.msg || error.message,
    value: error.value
  }));

  return new ValidationError('请求参数验证失败', details);
}

/**
 * 数据库错误处理
 */
export function handleDatabaseError(error: any): AppError {
  let message = '数据库操作错误';
  let statusCode = 500;

  // PostgreSQL错误处理
  if (error.code) {
    switch (error.code) {
      case '23505': // 唯一约束违反
        message = '数据已存在';
        statusCode = 409;
        return new ConflictError(message, { constraint: error.constraint });
      
      case '23503': // 外键约束违反
        message = '关联数据不存在';
        statusCode = 400;
        return new ValidationError(message, { constraint: error.constraint });
      
      case '23502': // 非空约束违反
        message = '必需字段不能为空';
        statusCode = 400;
        return new ValidationError(message, { column: error.column });
      
      case '42P01': // 表不存在
        message = '数据表不存在';
        statusCode = 500;
        break;
      
      case '42703': // 列不存在
        message = '数据列不存在';
        statusCode = 500;
        break;
    }
  }

  return new AppError(message, statusCode, 'DATABASE_ERROR', true, {
    code: error.code,
    detail: error.detail
  });
}

/**
 * Redis错误处理
 */
export function handleRedisError(error: any): ExternalServiceError {
  let message = 'Redis服务错误';

  if (error.code === 'ECONNREFUSED') {
    message = 'Redis服务连接失败';
  } else if (error.code === 'ETIMEDOUT') {
    message = 'Redis服务请求超时';
  } else if (error.message) {
    message = error.message;
  }

  return new ExternalServiceError(message, 'redis', {
    code: error.code,
    errno: error.errno
  });
}

/**
 * 外部服务错误处理
 */
export function handleExternalServiceError(error: any, serviceName: string): ExternalServiceError {
  let message = `${serviceName}服务错误`;

  if (error.response) {
    // HTTP响应错误
    const status = error.response.status;
    const data = error.response.data;
    
    if (status >= 400 && status < 500) {
      message = data?.message || `${serviceName}服务请求错误`;
    } else if (status >= 500) {
      message = `${serviceName}服务内部错误`;
    }
    
    return new ExternalServiceError(message, serviceName, {
      status,
      data: data?.error || data
    });
  } else if (error.code === 'ECONNREFUSED') {
    message = `${serviceName}服务连接失败`;
  } else if (error.code === 'ETIMEDOUT') {
    message = `${serviceName}服务请求超时`;
  }

  return new ExternalServiceError(message, serviceName, {
    code: error.code,
    message: error.message
  });
}

/**
 * 全局未捕获异常处理
 */
export function setupGlobalErrorHandlers(): void {
  // 未捕获的异常
  process.on('uncaughtException', (error: Error) => {
    logger.error('未捕获的异常', error);
    
    // 优雅关闭
    process.exit(1);
  });

  // 未处理的Promise拒绝
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('未处理的Promise拒绝', new Error(reason), {
      promise: promise.toString()
    });
    
    // 优雅关闭
    process.exit(1);
  });

  // 警告处理
  process.on('warning', (warning: any) => {
    logger.warn('进程警告', {
      name: warning.name,
      message: warning.message,
      stack: warning.stack
    });
  });
}
