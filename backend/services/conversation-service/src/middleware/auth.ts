/**
 * 认证中间件
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { AuthenticationError, AuthorizationError } from './errorHandler';
import { logger } from '../utils/logger';

/**
 * 用户信息接口
 */
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
  metadata?: Record<string, any>;
}

/**
 * JWT载荷接口
 */
interface JWTPayload {
  userId: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
  iat: number;
  exp: number;
}

/**
 * 扩展Request接口
 */
declare global {
  namespace Express {
    interface Request {
      user?: User;
      requestId?: string;
      logger?: any;
    }
  }
}

/**
 * 验证JWT令牌
 */
function verifyToken(token: string): JWTPayload {
  try {
    return jwt.verify(token, config.security.jwtSecret) as JWTPayload;
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      throw new AuthenticationError('令牌已过期');
    } else if (error.name === 'JsonWebTokenError') {
      throw new AuthenticationError('无效的令牌');
    } else {
      throw new AuthenticationError('令牌验证失败');
    }
  }
}

/**
 * 从请求中提取令牌
 */
function extractToken(req: Request): string | null {
  // 从Authorization头提取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从查询参数提取（用于WebSocket连接）
  const tokenFromQuery = req.query.token as string;
  if (tokenFromQuery) {
    return tokenFromQuery;
  }

  // 从Cookie提取
  const tokenFromCookie = req.cookies?.token;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

/**
 * 认证中间件
 */
export function authMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    // 如果禁用认证，跳过验证
    if (!config.security.enableAuth) {
      // 创建默认用户
      req.user = {
        id: 'anonymous',
        email: '<EMAIL>',
        name: 'Anonymous User',
        role: 'user',
        permissions: ['read', 'write'],
      };
      return next();
    }

    // 提取令牌
    const token = extractToken(req);
    if (!token) {
      throw new AuthenticationError('缺少认证令牌');
    }

    // 验证令牌
    const payload = verifyToken(token);

    // 创建用户对象
    req.user = {
      id: payload.userId,
      email: payload.email,
      name: payload.name,
      role: payload.role,
      permissions: payload.permissions,
    };

    // 记录认证成功
    if (req.logger) {
      req.logger.debug('用户认证成功', {
        userId: req.user.id,
        role: req.user.role,
      });
    }

    next();
  } catch (error) {
    // 记录认证失败
    if (req.logger) {
      req.logger.warn('用户认证失败', { error: (error as Error).message });
    }
    
    next(error);
  }
}

/**
 * 可选认证中间件（不强制要求认证）
 */
export function optionalAuthMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    // 如果禁用认证，跳过验证
    if (!config.security.enableAuth) {
      return next();
    }

    // 提取令牌
    const token = extractToken(req);
    if (!token) {
      return next(); // 没有令牌，继续处理
    }

    // 验证令牌
    const payload = verifyToken(token);

    // 创建用户对象
    req.user = {
      id: payload.userId,
      email: payload.email,
      name: payload.name,
      role: payload.role,
      permissions: payload.permissions,
    };

    next();
  } catch (error) {
    // 认证失败但不阻止请求
    if (req.logger) {
      req.logger.warn('可选认证失败', { error: (error as Error).message });
    }
    
    next();
  }
}

/**
 * 权限检查中间件工厂
 */
export function requirePermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('用户未认证');
      }

      if (!req.user.permissions.includes(permission)) {
        throw new AuthorizationError(`缺少权限: ${permission}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 角色检查中间件工厂
 */
export function requireRole(role: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('用户未认证');
      }

      if (req.user.role !== role) {
        throw new AuthorizationError(`需要角色: ${role}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 多角色检查中间件工厂
 */
export function requireAnyRole(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('用户未认证');
      }

      if (!roles.includes(req.user.role)) {
        throw new AuthorizationError(`需要以下角色之一: ${roles.join(', ')}`);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * 资源所有者检查中间件工厂
 */
export function requireOwnership(resourceUserIdField: string = 'userId') {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('用户未认证');
      }

      // 管理员可以访问所有资源
      if (req.user.role === 'admin') {
        return next();
      }

      // 从请求参数、查询参数或请求体中获取资源用户ID
      const resourceUserId = req.params[resourceUserIdField] || 
                           req.query[resourceUserIdField] || 
                           req.body[resourceUserIdField];

      if (!resourceUserId) {
        throw new AuthorizationError('无法确定资源所有者');
      }

      if (req.user.id !== resourceUserId) {
        throw new AuthorizationError('只能访问自己的资源');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * WebSocket认证函数
 */
export function authenticateSocket(token: string): User {
  if (!config.security.enableAuth) {
    // 返回默认用户
    return {
      id: 'anonymous',
      email: '<EMAIL>',
      name: 'Anonymous User',
      role: 'user',
      permissions: ['read', 'write'],
    };
  }

  if (!token) {
    throw new AuthenticationError('缺少认证令牌');
  }

  const payload = verifyToken(token);

  return {
    id: payload.userId,
    email: payload.email,
    name: payload.name,
    role: payload.role,
    permissions: payload.permissions,
  };
}

/**
 * 生成JWT令牌（用于测试）
 */
export function generateToken(user: Partial<User>): string {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id || 'test-user',
    email: user.email || '<EMAIL>',
    name: user.name || 'Test User',
    role: user.role || 'user',
    permissions: user.permissions || ['read', 'write'],
  };

  return jwt.sign(payload, config.security.jwtSecret, {
    expiresIn: config.security.jwtExpiration,
  });
}

/**
 * 刷新令牌
 */
export function refreshToken(oldToken: string): string {
  try {
    const payload = jwt.verify(oldToken, config.security.jwtSecret, {
      ignoreExpiration: true, // 忽略过期时间
    }) as JWTPayload;

    // 生成新令牌
    const newPayload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: payload.userId,
      email: payload.email,
      name: payload.name,
      role: payload.role,
      permissions: payload.permissions,
    };

    return jwt.sign(newPayload, config.security.jwtSecret, {
      expiresIn: config.security.jwtExpiration,
    });
  } catch (error) {
    throw new AuthenticationError('令牌刷新失败');
  }
}

/**
 * 验证令牌是否即将过期
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 30): boolean {
  try {
    const payload = jwt.verify(token, config.security.jwtSecret) as JWTPayload;
    const now = Math.floor(Date.now() / 1000);
    const threshold = thresholdMinutes * 60;
    
    return (payload.exp - now) < threshold;
  } catch (error) {
    return true; // 如果验证失败，认为需要刷新
  }
}
