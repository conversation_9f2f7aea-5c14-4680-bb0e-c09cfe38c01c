/**
 * 日志工具模块
 */

import winston from 'winston';
import { config, isDevelopment } from '../config';

// 自定义日志格式
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 添加堆栈信息
    if (stack) {
      log += `\n${stack}`;
    }
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// 开发环境格式
const devFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// 创建传输器
const transports: winston.transport[] = [
  // 控制台输出
  new winston.transports.Console({
    format: isDevelopment() ? devFormat : customFormat,
    level: isDevelopment() ? 'debug' : config.logging.level
  })
];

// 文件日志（如果启用）
if (config.logging.enableFileLogging) {
  // 错误日志文件
  transports.push(
    new winston.transports.File({
      filename: `${config.logging.logDirectory}/error.log`,
      level: 'error',
      format: customFormat,
      maxsize: parseInt(config.logging.maxFileSize) * 1024 * 1024,
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  );
  
  // 组合日志文件
  transports.push(
    new winston.transports.File({
      filename: `${config.logging.logDirectory}/combined.log`,
      format: customFormat,
      maxsize: parseInt(config.logging.maxFileSize) * 1024 * 1024,
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  );
}

// 创建logger实例
export const logger = winston.createLogger({
  level: config.logging.level,
  format: customFormat,
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: `${config.logging.logDirectory}/exceptions.log`,
      maxsize: parseInt(config.logging.maxFileSize) * 1024 * 1024,
      maxFiles: 3
    })
  ],
  // 处理未处理的Promise拒绝
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: `${config.logging.logDirectory}/rejections.log`,
      maxsize: parseInt(config.logging.maxFileSize) * 1024 * 1024,
      maxFiles: 3
    })
  ],
  exitOnError: false
});

/**
 * 结构化日志记录器
 */
export class StructuredLogger {
  private context: Record<string, any>;

  constructor(context: Record<string, any> = {}) {
    this.context = context;
  }

  /**
   * 添加上下文
   */
  withContext(context: Record<string, any>): StructuredLogger {
    return new StructuredLogger({ ...this.context, ...context });
  }

  /**
   * 记录信息日志
   */
  info(message: string, meta?: Record<string, any>): void {
    logger.info(message, { ...this.context, ...meta });
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error | any, meta?: Record<string, any>): void {
    const errorMeta = error instanceof Error ? {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : { error };

    logger.error(message, { ...this.context, ...errorMeta, ...meta });
  }

  /**
   * 记录警告日志
   */
  warn(message: string, meta?: Record<string, any>): void {
    logger.warn(message, { ...this.context, ...meta });
  }

  /**
   * 记录调试日志
   */
  debug(message: string, meta?: Record<string, any>): void {
    logger.debug(message, { ...this.context, ...meta });
  }

  /**
   * 记录详细日志
   */
  verbose(message: string, meta?: Record<string, any>): void {
    logger.verbose(message, { ...this.context, ...meta });
  }
}

/**
 * 创建带上下文的日志记录器
 */
export function createLogger(context: Record<string, any>): StructuredLogger {
  return new StructuredLogger(context);
}

/**
 * 请求日志中间件
 */
export function createRequestLogger(serviceName: string = 'conversation-service') {
  return (req: any, res: any, next: any) => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 添加请求ID到请求对象
    req.requestId = requestId;
    
    // 创建请求日志记录器
    req.logger = createLogger({
      service: serviceName,
      requestId,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress
    });

    // 记录请求开始
    req.logger.info('请求开始', {
      headers: req.headers,
      query: req.query,
      body: req.method !== 'GET' ? req.body : undefined
    });

    // 监听响应结束
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const statusCode = res.statusCode;
      
      req.logger.info('请求完成', {
        statusCode,
        duration,
        contentLength: res.get('content-length')
      });
    });

    // 监听响应错误
    res.on('error', (error: Error) => {
      const duration = Date.now() - startTime;
      
      req.logger.error('请求错误', error, {
        duration,
        statusCode: res.statusCode
      });
    });

    next();
  };
}

/**
 * 性能监控日志
 */
export class PerformanceLogger {
  private startTime: number;
  private logger: StructuredLogger;
  private operation: string;

  constructor(operation: string, context?: Record<string, any>) {
    this.operation = operation;
    this.startTime = Date.now();
    this.logger = createLogger({ operation, ...context });
    
    this.logger.debug('操作开始');
  }

  /**
   * 记录检查点
   */
  checkpoint(name: string, meta?: Record<string, any>): void {
    const elapsed = Date.now() - this.startTime;
    this.logger.debug(`检查点: ${name}`, { elapsed, ...meta });
  }

  /**
   * 完成操作
   */
  finish(meta?: Record<string, any>): void {
    const duration = Date.now() - this.startTime;
    this.logger.info('操作完成', { duration, ...meta });
  }

  /**
   * 操作失败
   */
  fail(error: Error | string, meta?: Record<string, any>): void {
    const duration = Date.now() - this.startTime;
    
    if (typeof error === 'string') {
      this.logger.error('操作失败', new Error(error), { duration, ...meta });
    } else {
      this.logger.error('操作失败', error, { duration, ...meta });
    }
  }
}

/**
 * 创建性能监控器
 */
export function createPerformanceLogger(operation: string, context?: Record<string, any>): PerformanceLogger {
  return new PerformanceLogger(operation, context);
}

/**
 * WebSocket日志记录器
 */
export class WebSocketLogger {
  private logger: StructuredLogger;

  constructor(socketId: string, userId?: string) {
    this.logger = createLogger({
      component: 'websocket',
      socketId,
      userId
    });
  }

  /**
   * 连接事件
   */
  connect(meta?: Record<string, any>): void {
    this.logger.info('WebSocket连接建立', meta);
  }

  /**
   * 断开连接事件
   */
  disconnect(reason: string, meta?: Record<string, any>): void {
    this.logger.info('WebSocket连接断开', { reason, ...meta });
  }

  /**
   * 消息发送
   */
  messageSent(event: string, data?: any): void {
    this.logger.debug('WebSocket消息发送', {
      event,
      dataSize: data ? JSON.stringify(data).length : 0
    });
  }

  /**
   * 消息接收
   */
  messageReceived(event: string, data?: any): void {
    this.logger.debug('WebSocket消息接收', {
      event,
      dataSize: data ? JSON.stringify(data).length : 0
    });
  }

  /**
   * 错误事件
   */
  error(message: string, error?: Error, meta?: Record<string, any>): void {
    this.logger.error(message, error, meta);
  }

  /**
   * 警告事件
   */
  warn(message: string, meta?: Record<string, any>): void {
    this.logger.warn(message, meta);
  }
}

/**
 * 创建WebSocket日志记录器
 */
export function createWebSocketLogger(socketId: string, userId?: string): WebSocketLogger {
  return new WebSocketLogger(socketId, userId);
}
