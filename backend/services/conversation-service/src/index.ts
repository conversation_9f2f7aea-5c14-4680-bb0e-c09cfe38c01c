/**
 * 对话服务主入口文件
 * 负责对话管理、历史记录和实时通信
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

import { config, validateConfig } from './config';
import { logger, createRequestLogger } from './utils/logger';
import { errorHandler, notFoundHandler, setupGlobalErrorHandlers } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';
import { setupSocketIO } from './socket/socketHandler';
import { ConversationManager } from './services/conversationManager';
import { MessageProcessor } from './services/messageProcessor';

// 导入路由
import conversationRoutes from './routes/conversations';
import messageRoutes from './routes/messages';
import healthRoutes from './routes/health';

/**
 * 应用程序类
 */
class ConversationService {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private conversationManager: ConversationManager;
  private messageProcessor: MessageProcessor;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.security.allowedOrigins,
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });
  }

  /**
   * 初始化应用程序
   */
  async initialize(): Promise<void> {
    try {
      // 验证配置
      validateConfig();
      logger.info('配置验证通过');

      // 设置全局错误处理
      setupGlobalErrorHandlers();

      // 连接数据库
      await connectDatabase();
      logger.info('数据库连接成功');

      // 连接Redis
      await connectRedis();
      logger.info('Redis连接成功');

      // 初始化服务
      this.conversationManager = ConversationManager.getInstance();
      this.messageProcessor = MessageProcessor.getInstance();
      await this.conversationManager.initialize();
      await this.messageProcessor.initialize();

      // 设置中间件
      this.setupMiddleware();

      // 设置路由
      this.setupRoutes();

      // 设置Socket.IO
      setupSocketIO(this.io, this.conversationManager, this.messageProcessor);

      // 设置错误处理
      this.setupErrorHandling();

      logger.info('对话服务初始化完成');
    } catch (error) {
      logger.error('对话服务初始化失败', error);
      throw error;
    }
  }

  /**
   * 设置中间件
   */
  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS配置
    this.app.use(cors({
      origin: config.security.allowedOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // 压缩中间件
    this.app.use(compression());

    // 请求日志
    this.app.use(createRequestLogger('conversation-service'));

    // HTTP日志
    if (config.nodeEnv === 'development') {
      this.app.use(morgan('dev'));
    } else {
      this.app.use(morgan('combined'));
    }

    // 限流中间件
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: '请求过于频繁，请稍后再试',
        },
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // 解析中间件
    this.app.use(express.json({ limit: config.security.maxRequestSize }));
    this.app.use(express.urlencoded({ extended: true, limit: config.security.maxRequestSize }));

    // 认证中间件（除了健康检查）
    this.app.use('/api/', (req, res, next) => {
      if (req.path.startsWith('/health')) {
        return next();
      }
      return authMiddleware(req, res, next);
    });
  }

  /**
   * 设置路由
   */
  private setupRoutes(): void {
    // API路由
    this.app.use('/api/conversations', conversationRoutes);
    this.app.use('/api/messages', messageRoutes);
    this.app.use('/api/health', healthRoutes);

    // 根路径
    this.app.get('/', (req, res) => {
      res.json({
        service: 'conversation-service',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
      });
    });

    // API文档
    this.app.get('/api', (req, res) => {
      res.json({
        service: 'RAG对话服务',
        version: '1.0.0',
        description: '提供对话管理、历史记录和实时通信功能',
        endpoints: {
          conversations: '/api/conversations',
          messages: '/api/messages',
          health: '/api/health',
          websocket: '/socket.io',
        },
        documentation: 'https://docs.rag-system.com/conversation-service',
      });
    });
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 404处理
    this.app.use(notFoundHandler);

    // 全局错误处理
    this.app.use(errorHandler);
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(config.port, config.host, () => {
          logger.info(`对话服务启动成功`, {
            host: config.host,
            port: config.port,
            env: config.nodeEnv,
            pid: process.pid,
          });
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            logger.error(`端口 ${config.port} 已被占用`);
          } else {
            logger.error('服务器启动失败', error);
          }
          reject(error);
        });
      } catch (error) {
        logger.error('启动服务器时发生错误', error);
        reject(error);
      }
    });
  }

  /**
   * 优雅关闭
   */
  async shutdown(): Promise<void> {
    logger.info('开始优雅关闭对话服务...');

    try {
      // 关闭Socket.IO
      this.io.close();
      logger.info('Socket.IO已关闭');

      // 关闭HTTP服务器
      await new Promise<void>((resolve) => {
        this.server.close(() => {
          logger.info('HTTP服务器已关闭');
          resolve();
        });
      });

      // 关闭数据库连接
      // await closeDatabase();
      logger.info('数据库连接已关闭');

      // 关闭Redis连接
      // await closeRedis();
      logger.info('Redis连接已关闭');

      logger.info('对话服务优雅关闭完成');
    } catch (error) {
      logger.error('优雅关闭过程中发生错误', error);
      throw error;
    }
  }

  /**
   * 获取应用实例
   */
  getApp(): express.Application {
    return this.app;
  }

  /**
   * 获取Socket.IO实例
   */
  getIO(): SocketIOServer {
    return this.io;
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const service = new ConversationService();

  try {
    // 初始化服务
    await service.initialize();

    // 启动服务
    await service.start();

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
      
      try {
        await service.shutdown();
        process.exit(0);
      } catch (error) {
        logger.error('优雅关闭失败', error);
        process.exit(1);
      }
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('服务启动失败', error);
    process.exit(1);
  }
}

// 启动服务
if (require.main === module) {
  main().catch((error) => {
    console.error('启动失败:', error);
    process.exit(1);
  });
}

export default ConversationService;
