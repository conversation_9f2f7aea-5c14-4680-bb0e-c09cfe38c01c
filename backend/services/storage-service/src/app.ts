/**
 * 对象存储服务主应用
 * 提供文件上传、下载、管理等功能
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import multer from 'multer';
import { config } from './config';
import { logger, createRequestLogger } from './utils/logger';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { metricsMiddleware, prometheusMetrics } from './middleware/metrics';
import { healthRouter } from './routes/health';
import { uploadRouter } from './routes/upload';
import { downloadRouter } from './routes/download';
import { fileRouter } from './routes/files';
import { bucketRouter } from './routes/buckets';
import { adminRouter } from './routes/admin';

const app = express();

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: false, // 允许文件预览
}));

app.use(cors({
  origin: config.cors.origins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

app.use(compression());

// 请求日志
app.use(createRequestLogger('storage-service'));

// 指标收集
app.use(metricsMiddleware);

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// 上传限流（更严格）
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次上传
  message: {
    error: '上传过于频繁，请稍后再试',
    code: 'UPLOAD_RATE_LIMIT_EXCEEDED'
  },
});

// JSON解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/static', express.static('public', {
  maxAge: '1d',
  etag: true,
  lastModified: true,
}));

// 健康检查路由（无需认证）
app.use('/health', healthRouter);

// Prometheus指标路由（无需认证）
app.get('/metrics', prometheusMetrics);

// API路由（需要认证）
app.use('/api/v1/upload', uploadLimiter, authMiddleware, uploadRouter);
app.use('/api/v1/download', authMiddleware, downloadRouter);
app.use('/api/v1/files', authMiddleware, fileRouter);
app.use('/api/v1/buckets', authMiddleware, bucketRouter);
app.use('/api/v1/admin', authMiddleware, adminRouter);

// 根路径
app.get('/', (req, res) => {
  res.json({
    service: 'RAG Storage Service',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      metrics: '/metrics',
      upload: '/api/v1/upload',
      download: '/api/v1/download',
      files: '/api/v1/files',
      buckets: '/api/v1/buckets',
      admin: '/api/v1/admin',
    }
  });
});

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 优雅关闭处理
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  
  // 这里可以添加清理逻辑
  // 比如关闭数据库连接、完成正在处理的请求等
  
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', { reason, promise });
  process.exit(1);
});

export default app;
