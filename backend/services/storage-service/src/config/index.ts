/**
 * 存储服务配置
 */

import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 环境检查
export const isDevelopment = () => process.env.NODE_ENV === 'development';
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isTest = () => process.env.NODE_ENV === 'test';

// 主配置对象
export const config = {
  // 服务配置
  server: {
    port: parseInt(process.env.PORT || '3008'),
    host: process.env.HOST || '0.0.0.0',
    env: process.env.NODE_ENV || 'development',
  },

  // CORS配置
  cors: {
    origins: process.env.CORS_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
    ],
  },

  // 对象存储配置
  storage: {
    // 存储提供商 (minio, aws, gcp, azure)
    provider: process.env.STORAGE_PROVIDER || 'minio',
    
    // MinIO配置
    minio: {
      endpoint: process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(process.env.MINIO_PORT || '9000'),
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: process.env.MINIO_ACCESS_KEY || 'rag-app',
      secretKey: process.env.MINIO_SECRET_KEY || 'rag-app-secret-2024',
      region: process.env.MINIO_REGION || 'us-east-1',
    },

    // AWS S3配置
    aws: {
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
      endpoint: process.env.AWS_ENDPOINT, // 用于S3兼容服务
    },

    // Google Cloud Storage配置
    gcp: {
      projectId: process.env.GCP_PROJECT_ID || '',
      keyFilename: process.env.GCP_KEY_FILENAME || '',
      credentials: process.env.GCP_CREDENTIALS ? JSON.parse(process.env.GCP_CREDENTIALS) : undefined,
    },

    // Azure Blob Storage配置
    azure: {
      accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME || '',
      accountKey: process.env.AZURE_STORAGE_ACCOUNT_KEY || '',
      connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING || '',
    },

    // 存储桶配置
    buckets: {
      documents: process.env.BUCKET_DOCUMENTS || 'documents',
      images: process.env.BUCKET_IMAGES || 'images',
      videos: process.env.BUCKET_VIDEOS || 'videos',
      audio: process.env.BUCKET_AUDIO || 'audio',
      archives: process.env.BUCKET_ARCHIVES || 'archives',
      temp: process.env.BUCKET_TEMP || 'temp',
      backups: process.env.BUCKET_BACKUPS || 'backups',
      static: process.env.BUCKET_STATIC || 'static',
      avatars: process.env.BUCKET_AVATARS || 'avatars',
      thumbnails: process.env.BUCKET_THUMBNAILS || 'thumbnails',
    },
  },

  // 文件上传配置
  upload: {
    // 最大文件大小 (字节)
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '*********'), // 100MB
    
    // 最大文件数量
    maxFiles: parseInt(process.env.MAX_FILES || '10'),
    
    // 允许的文件类型
    allowedMimeTypes: process.env.ALLOWED_MIME_TYPES?.split(',') || [
      // 文档类型
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/markdown',
      'text/csv',
      'application/json',
      'application/xml',
      
      // 图片类型
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/bmp',
      'image/tiff',
      
      // 视频类型
      'video/mp4',
      'video/avi',
      'video/mov',
      'video/wmv',
      'video/flv',
      'video/webm',
      
      // 音频类型
      'audio/mp3',
      'audio/wav',
      'audio/flac',
      'audio/aac',
      'audio/ogg',
      
      // 压缩文件
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      'application/x-tar',
      'application/gzip',
    ],
    
    // 文件名规则
    filenamePattern: process.env.FILENAME_PATTERN || '^[a-zA-Z0-9._-]+$',
    
    // 临时目录
    tempDir: process.env.TEMP_DIR || '/tmp/uploads',
    
    // 是否启用病毒扫描
    enableVirusScan: process.env.ENABLE_VIRUS_SCAN === 'true',
    
    // 是否启用重复检测
    enableDuplicateDetection: process.env.ENABLE_DUPLICATE_DETECTION === 'true',
  },

  // 图片处理配置
  imageProcessing: {
    // 是否启用图片处理
    enabled: process.env.IMAGE_PROCESSING_ENABLED !== 'false',
    
    // 缩略图尺寸
    thumbnailSizes: [
      { name: 'small', width: 150, height: 150 },
      { name: 'medium', width: 300, height: 300 },
      { name: 'large', width: 600, height: 600 },
    ],
    
    // 图片质量
    quality: parseInt(process.env.IMAGE_QUALITY || '80'),
    
    // 支持的图片格式
    supportedFormats: ['jpeg', 'png', 'webp'],
    
    // 最大图片尺寸
    maxWidth: parseInt(process.env.MAX_IMAGE_WIDTH || '4096'),
    maxHeight: parseInt(process.env.MAX_IMAGE_HEIGHT || '4096'),
  },

  // Redis配置（用于缓存）
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6380'),
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'storage:',
    maxRetries: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
    timeout: parseInt(process.env.REDIS_TIMEOUT || '5000'),
  },

  // 安全配置
  security: {
    // JWT密钥
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    
    // JWT过期时间
    jwtExpiration: process.env.JWT_EXPIRATION || '24h',
    
    // 是否启用认证
    enableAuth: process.env.ENABLE_AUTH !== 'false',
    
    // 文件访问令牌过期时间
    fileTokenExpiration: process.env.FILE_TOKEN_EXPIRATION || '1h',
    
    // 是否启用文件加密
    enableEncryption: process.env.ENABLE_ENCRYPTION === 'true',
    
    // 加密密钥
    encryptionKey: process.env.ENCRYPTION_KEY || '',
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || (isDevelopment() ? 'debug' : 'info'),
    enableFileLogging: process.env.ENABLE_FILE_LOGGING === 'true',
    logDirectory: process.env.LOG_DIRECTORY || './logs',
    maxFileSize: process.env.LOG_MAX_FILE_SIZE || '10',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
  },

  // 监控配置
  monitoring: {
    // 是否启用指标收集
    enableMetrics: process.env.ENABLE_METRICS !== 'false',
    
    // 指标端点路径
    metricsPath: process.env.METRICS_PATH || '/metrics',
    
    // 健康检查配置
    healthCheck: {
      timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000'),
      interval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
    },
  },

  // 外部服务配置
  services: {
    // 病毒扫描服务
    clamav: {
      host: process.env.CLAMAV_HOST || 'localhost',
      port: parseInt(process.env.CLAMAV_PORT || '3310'),
      timeout: parseInt(process.env.CLAMAV_TIMEOUT || '30000'),
    },
    
    // 文件预览服务
    onlyoffice: {
      url: process.env.ONLYOFFICE_URL || 'http://localhost:8082',
      secret: process.env.ONLYOFFICE_SECRET || 'your-jwt-secret',
    },
    
    // 文件转换服务
    gotenberg: {
      url: process.env.GOTENBERG_URL || 'http://localhost:3000',
      timeout: parseInt(process.env.GOTENBERG_TIMEOUT || '30000'),
    },
    
    // 图片处理服务
    imageproxy: {
      url: process.env.IMAGEPROXY_URL || 'http://localhost:8081',
      secret: process.env.IMAGEPROXY_SECRET || '',
    },
  },

  // CDN配置
  cdn: {
    // 是否启用CDN
    enabled: process.env.CDN_ENABLED === 'true',
    
    // CDN域名
    domain: process.env.CDN_DOMAIN || '',
    
    // CDN路径前缀
    pathPrefix: process.env.CDN_PATH_PREFIX || '/files',
    
    // 缓存控制
    cacheControl: process.env.CDN_CACHE_CONTROL || 'public, max-age=31536000',
  },
};

// 配置验证
export function validateConfig(): void {
  const requiredEnvVars = [
    'MINIO_ACCESS_KEY',
    'MINIO_SECRET_KEY',
    'JWT_SECRET',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
  }

  // 验证存储配置
  if (config.storage.provider === 'aws' && (!config.storage.aws.accessKeyId || !config.storage.aws.secretAccessKey)) {
    throw new Error('AWS存储配置不完整');
  }

  if (config.storage.provider === 'gcp' && !config.storage.gcp.projectId) {
    throw new Error('GCP存储配置不完整');
  }

  if (config.storage.provider === 'azure' && !config.storage.azure.accountName) {
    throw new Error('Azure存储配置不完整');
  }
}

export default config;
