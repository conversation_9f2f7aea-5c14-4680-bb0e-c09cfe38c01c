# RAG检索系统先进技术配置文件
# 包含Self-RAG、CoT检索、多向量检索等先进技术的配置参数

# 全局配置
global:
  # 启用的技术列表
  enabled_techniques:
    - "self_rag"
    - "hard_negative_mining"
    - "multi_vector_retrieval"
    - "chinese_optimization"
    - "temporal_aware_retrieval"
    # - "chain_of_thought"  # 需要LLM集成
    # - "corrective_rag"    # 需要高级模型
    # - "knowledge_graph"   # 需要知识图谱
  
  # 性能配置
  performance:
    max_concurrent_requests: 200
    request_timeout: 30
    batch_size: 32
    cache_enabled: true
    cache_ttl: 3600

# Self-RAG自我反思检索配置
self_rag:
  enabled: true
  max_iterations: 3
  confidence_threshold: 0.8
  min_confidence_threshold: 0.3
  iteration_timeout: 10  # 每次迭代超时时间（秒）
  
  # 评估配置
  critique:
    relevance_weight: 0.4
    completeness_weight: 0.3
    confidence_weight: 0.3
    min_relevance_score: 0.6
    min_completeness_score: 0.5
  
  # 查询精化配置
  query_refinement:
    max_refinement_length: 200
    synonym_expansion: true
    context_enhancement: true
    concept_addition: true

# Chain-of-Thought检索配置
chain_of_thought:
  enabled: false  # 需要LLM集成后启用
  max_steps: 3
  step_timeout: 15
  
  # LLM配置
  llm:
    model: "gpt-3.5-turbo"
    temperature: 0.3
    max_tokens: 500
    api_timeout: 10
  
  # 推理步骤配置
  reasoning:
    step_weights: [1.0, 0.8, 0.6]  # 步骤权重递减
    min_step_confidence: 0.5
    domain_specific_prompts: true

# 困难负样本挖掘配置
hard_negative_mining:
  enabled: true
  similarity_threshold_low: 0.6
  similarity_threshold_high: 0.85
  relevance_threshold: 0.3
  max_candidates: 100
  max_hard_negatives: 10
  
  # 对比学习配置
  contrastive_learning:
    temperature: 0.07
    margin: 0.2
    batch_size: 32
    learning_rate: 0.001
    weight_decay: 0.01
  
  # 负样本质量控制
  quality_control:
    min_content_length: 50
    max_content_length: 2000
    diversity_threshold: 0.8

# 多向量检索配置
multi_vector_retrieval:
  enabled: true
  
  # 方面配置
  aspects:
    semantic:
      enabled: true
      weight: 0.3
      description: "语义方面"
    factual:
      enabled: true
      weight: 0.25
      description: "事实方面"
    temporal:
      enabled: true
      weight: 0.15
      description: "时间方面"
    entity:
      enabled: true
      weight: 0.2
      description: "实体方面"
    procedural:
      enabled: true
      weight: 0.1
      description: "程序方面"
  
  # 融合配置
  fusion:
    diversity_bonus: 0.1  # 多样性奖励
    aspect_threshold: 0.1  # 方面最低权重
    max_aspects_per_query: 5

# 中文优化配置
chinese_optimization:
  enabled: true
  
  # 分词配置
  segmentation:
    # 分词器权重
    ensemble_weights:
      jieba: 0.4
      pkuseg: 0.3
      thulac: 0.3
    
    # 领域词汇
    domain_vocabulary:
      technology:
        - "机器学习"
        - "深度学习"
        - "神经网络"
        - "算法"
        - "数据结构"
      business:
        - "市场营销"
        - "商业模式"
        - "企业管理"
        - "财务分析"
      science:
        - "物理学"
        - "化学"
        - "生物学"
        - "数学"
    
    # 质量控制
    min_confidence: 0.5
    max_word_length: 10
    cache_enabled: true
  
  # 语义角色标注配置
  semantic_role_labeling:
    enabled: true
    role_weights:
      A0: 1.0   # 施事
      A1: 0.9   # 受事
      A2: 0.7   # 与事
      TMP: 0.6  # 时间
      LOC: 0.6  # 地点
      MNR: 0.5  # 方式
    
    min_confidence: 0.3
    max_roles_per_sentence: 6

# 时效性感知检索配置
temporal_aware_retrieval:
  enabled: true
  
  # 时效性评分
  freshness_scoring:
    decay_factor: 0.1
    max_age_days: 365
    recent_boost: 1.5  # 最近内容加权
    
  # 时间权重
  temporal_weights:
    very_recent: 1.0    # 1天内
    recent: 0.8         # 1周内
    moderate: 0.6       # 1月内
    old: 0.4           # 1年内
    very_old: 0.2      # 1年以上
  
  # 时间感知查询
  time_aware_query:
    extract_time_expressions: true
    boost_temporal_relevance: true
    temporal_query_expansion: true

# Corrective RAG配置
corrective_rag:
  enabled: false  # 需要高级模型后启用
  
  # 相关性检测
  relevance_detection:
    threshold: 0.6
    model: "cross-encoder/ms-marco-MiniLM-L-6-v2"
    batch_size: 16
  
  # 纠错策略
  correction_strategy:
    max_correction_attempts: 2
    correction_query_templates:
      - "更准确的 {query}"
      - "{query} 详细信息"
      - "{query} 相关内容"
  
  # 结果融合
  result_fusion:
    original_weight: 0.6
    corrected_weight: 0.4
    max_total_results: 20

# 知识图谱增强配置
knowledge_graph_enhancement:
  enabled: false  # 需要知识图谱后启用
  
  # 图谱配置
  graph_config:
    neo4j_uri: "bolt://localhost:7687"
    neo4j_user: "neo4j"
    neo4j_password: "password"
    max_hop_distance: 3
  
  # 实体链接
  entity_linking:
    confidence_threshold: 0.7
    max_entities_per_query: 5
    entity_expansion: true
  
  # 关系推理
  relation_reasoning:
    max_relations: 10
    relation_weights:
      "is_a": 1.0
      "part_of": 0.8
      "related_to": 0.6
      "similar_to": 0.5

# A/B测试配置
ab_testing:
  enabled: true
  
  # 实验配置
  experiments:
    self_rag_test:
      enabled: true
      traffic_split: 0.3
      control_group: "enhanced_search"
      treatment_group: "self_rag_search"
      metrics:
        - "accuracy"
        - "latency"
        - "user_satisfaction"
    
    multi_vector_test:
      enabled: true
      traffic_split: 0.2
      control_group: "single_vector_search"
      treatment_group: "multi_vector_search"
      metrics:
        - "recall"
        - "precision"
        - "diversity"
    
    chinese_optimization_test:
      enabled: true
      traffic_split: 0.4
      control_group: "basic_chinese_search"
      treatment_group: "optimized_chinese_search"
      metrics:
        - "chinese_accuracy"
        - "segmentation_quality"

# 监控和日志配置
monitoring:
  # 性能监控
  performance_metrics:
    enabled: true
    collection_interval: 60  # 秒
    retention_days: 30
    
    # 关键指标
    key_metrics:
      - "search_latency_p95"
      - "search_latency_p99"
      - "qps"
      - "error_rate"
      - "cache_hit_rate"
  
  # 质量监控
  quality_metrics:
    enabled: true
    
    # 技术特定指标
    technique_metrics:
      self_rag:
        - "iteration_count"
        - "confidence_score"
        - "refinement_success_rate"
      
      multi_vector:
        - "aspect_coverage"
        - "fusion_score"
        - "diversity_score"
      
      chinese_optimization:
        - "segmentation_confidence"
        - "srl_confidence"
        - "optimization_success_rate"
  
  # 告警配置
  alerts:
    latency_threshold: 3.0  # 秒
    error_rate_threshold: 0.05  # 5%
    confidence_threshold: 0.6
    
    # 告警通道
    notification_channels:
      - "email"
      - "slack"
      - "webhook"

# 缓存配置
caching:
  # 多级缓存
  levels:
    l1_memory:
      enabled: true
      max_size: 1000
      ttl: 300  # 5分钟
    
    l2_redis:
      enabled: true
      ttl: 3600  # 1小时
      max_memory: "2gb"
    
    l3_database:
      enabled: true
      ttl: 86400  # 24小时
  
  # 缓存策略
  strategies:
    query_results: "l1_memory,l2_redis"
    embeddings: "l2_redis,l3_database"
    user_profiles: "l1_memory,l2_redis"
    negative_samples: "l2_redis"

# 安全配置
security:
  # API安全
  api_security:
    rate_limiting:
      enabled: true
      requests_per_minute: 100
      burst_size: 20
    
    authentication:
      enabled: true
      token_expiry: 3600
    
    input_validation:
      max_query_length: 1000
      allowed_characters: "unicode"
      sanitization: true
  
  # 数据安全
  data_security:
    encryption_at_rest: true
    encryption_in_transit: true
    pii_detection: true
    data_retention_days: 90
