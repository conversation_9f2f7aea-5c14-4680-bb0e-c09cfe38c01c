"""
系统性能测试套件
测试并发性能、延迟、内存使用等关键指标
"""

import asyncio
import time
import psutil
import statistics
from typing import List, Dict, Any, Callable
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import aiohttp
import pytest

from loguru import logger


@dataclass
class PerformanceMetrics:
    """性能指标"""
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_response_time: float
    min_response_time: float
    qps: float
    success_rate: float
    error_count: int
    memory_usage_mb: float
    cpu_usage_percent: float


@dataclass
class LoadTestConfig:
    """负载测试配置"""
    concurrent_users: int
    test_duration_seconds: int
    ramp_up_seconds: int
    target_qps: int
    endpoint_url: str
    test_data: List[Dict[str, Any]]


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.results = []
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def single_request_test(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """单个请求测试"""
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                response_data = await response.json()
                end_time = time.time()
                
                return {
                    "success": response.status == 200,
                    "response_time": (end_time - start_time) * 1000,  # 毫秒
                    "status_code": response.status,
                    "response_size": len(str(response_data)),
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            end_time = time.time()
            logger.error(f"请求失败: {e}")
            return {
                "success": False,
                "response_time": (end_time - start_time) * 1000,
                "status_code": 0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def concurrent_load_test(self, config: LoadTestConfig) -> PerformanceMetrics:
        """并发负载测试"""
        logger.info(f"开始负载测试: {config.concurrent_users} 并发用户, {config.test_duration_seconds}秒")
        
        # 记录系统资源使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 测试结果收集
        test_results = []
        start_time = time.time()
        
        # 创建并发任务
        tasks = []
        for i in range(config.concurrent_users):
            task = asyncio.create_task(
                self._user_simulation(config, i)
            )
            tasks.append(task)
            
            # 渐进式增加负载
            if config.ramp_up_seconds > 0:
                await asyncio.sleep(config.ramp_up_seconds / config.concurrent_users)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集所有结果
        for result in results:
            if isinstance(result, list):
                test_results.extend(result)
            elif isinstance(result, Exception):
                logger.error(f"任务执行异常: {result}")
        
        # 计算性能指标
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage = final_memory - initial_memory
        
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 计算响应时间指标
        response_times = [r["response_time"] for r in test_results if r.get("response_time")]
        successful_requests = [r for r in test_results if r.get("success", False)]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
            max_response_time = min_response_time = 0
        
        # 计算QPS和成功率
        total_requests = len(test_results)
        qps = total_requests / total_duration if total_duration > 0 else 0
        success_rate = len(successful_requests) / total_requests if total_requests > 0 else 0
        error_count = total_requests - len(successful_requests)
        
        metrics = PerformanceMetrics(
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            max_response_time=max_response_time,
            min_response_time=min_response_time,
            qps=qps,
            success_rate=success_rate,
            error_count=error_count,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage
        )
        
        logger.info(f"负载测试完成: QPS={qps:.2f}, P95延迟={p95_response_time:.2f}ms, 成功率={success_rate:.2%}")
        return metrics
    
    async def _user_simulation(self, config: LoadTestConfig, user_id: int) -> List[Dict[str, Any]]:
        """模拟单个用户的行为"""
        user_results = []
        start_time = time.time()
        
        while time.time() - start_time < config.test_duration_seconds:
            # 随机选择测试数据
            import random
            test_data = random.choice(config.test_data)
            
            # 执行请求
            result = await self.single_request_test(config.endpoint_url, test_data)
            result["user_id"] = user_id
            user_results.append(result)
            
            # 控制请求频率
            if config.target_qps > 0:
                delay = 1.0 / config.target_qps * config.concurrent_users
                await asyncio.sleep(delay)
        
        return user_results
    
    async def latency_test(self, endpoint: str, payload: Dict[str, Any], iterations: int = 100) -> Dict[str, float]:
        """延迟测试"""
        logger.info(f"开始延迟测试: {iterations} 次迭代")
        
        response_times = []
        
        for i in range(iterations):
            result = await self.single_request_test(endpoint, payload)
            if result.get("success"):
                response_times.append(result["response_time"])
        
        if response_times:
            return {
                "avg_latency": statistics.mean(response_times),
                "median_latency": statistics.median(response_times),
                "p95_latency": statistics.quantiles(response_times, n=20)[18],
                "p99_latency": statistics.quantiles(response_times, n=100)[98],
                "min_latency": min(response_times),
                "max_latency": max(response_times),
                "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0
            }
        else:
            return {"error": "所有请求都失败了"}
    
    async def memory_stress_test(self, endpoint: str, payload: Dict[str, Any], 
                                duration_seconds: int = 60) -> Dict[str, Any]:
        """内存压力测试"""
        logger.info(f"开始内存压力测试: {duration_seconds} 秒")
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_samples = []
        start_time = time.time()
        
        # 持续发送请求并监控内存
        while time.time() - start_time < duration_seconds:
            # 发送请求
            await self.single_request_test(endpoint, payload)
            
            # 记录内存使用
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_samples.append(current_memory)
            
            await asyncio.sleep(0.1)  # 100ms间隔
        
        final_memory = process.memory_info().rss / 1024 / 1024
        
        return {
            "initial_memory_mb": initial_memory,
            "final_memory_mb": final_memory,
            "memory_increase_mb": final_memory - initial_memory,
            "peak_memory_mb": max(memory_samples),
            "avg_memory_mb": statistics.mean(memory_samples),
            "memory_samples": len(memory_samples)
        }


# 测试用例
class TestPerformance:
    """性能测试用例"""
    
    @pytest.fixture
    def performance_tester(self):
        """性能测试器fixture"""
        return PerformanceTester()
    
    @pytest.fixture
    def test_queries(self):
        """测试查询数据"""
        return [
            {"query": "人工智能的发展历史", "top_k": 10},
            {"query": "机器学习算法比较", "top_k": 5},
            {"query": "深度学习在自然语言处理中的应用", "top_k": 15},
            {"query": "计算机视觉技术", "top_k": 8},
            {"query": "大数据分析方法", "top_k": 12}
        ]
    
    @pytest.mark.asyncio
    async def test_basic_search_latency(self, performance_tester, test_queries):
        """测试基础搜索延迟"""
        async with performance_tester:
            for query_data in test_queries:
                latency_stats = await performance_tester.latency_test(
                    "/search", query_data, iterations=50
                )
                
                # 验证延迟要求
                assert latency_stats.get("p95_latency", float('inf')) < 2000, \
                    f"P95延迟超过2秒: {latency_stats.get('p95_latency')}ms"
                
                logger.info(f"查询 '{query_data['query'][:20]}...' P95延迟: {latency_stats.get('p95_latency', 0):.2f}ms")
    
    @pytest.mark.asyncio
    async def test_advanced_search_latency(self, performance_tester, test_queries):
        """测试高级搜索延迟"""
        async with performance_tester:
            advanced_queries = [
                {
                    "query": query["query"],
                    "search_methods": ["self_rag", "multi_vector"],
                    "top_k": query["top_k"]
                }
                for query in test_queries
            ]
            
            for query_data in advanced_queries:
                latency_stats = await performance_tester.latency_test(
                    "/advanced/search/advanced", query_data, iterations=30
                )
                
                # 高级搜索允许更高的延迟
                assert latency_stats.get("p95_latency", float('inf')) < 5000, \
                    f"高级搜索P95延迟超过5秒: {latency_stats.get('p95_latency')}ms"
                
                logger.info(f"高级查询 P95延迟: {latency_stats.get('p95_latency', 0):.2f}ms")
    
    @pytest.mark.asyncio
    async def test_concurrent_load(self, performance_tester, test_queries):
        """测试并发负载"""
        async with performance_tester:
            config = LoadTestConfig(
                concurrent_users=20,
                test_duration_seconds=30,
                ramp_up_seconds=5,
                target_qps=50,
                endpoint_url="/search",
                test_data=test_queries
            )
            
            metrics = await performance_tester.concurrent_load_test(config)
            
            # 验证性能要求
            assert metrics.qps >= 100, f"QPS低于要求: {metrics.qps}"
            assert metrics.p95_response_time < 2000, f"P95延迟超过2秒: {metrics.p95_response_time}ms"
            assert metrics.success_rate >= 0.99, f"成功率低于99%: {metrics.success_rate}"
            
            logger.info(f"并发测试结果: QPS={metrics.qps:.2f}, P95={metrics.p95_response_time:.2f}ms, 成功率={metrics.success_rate:.2%}")
    
    @pytest.mark.asyncio
    async def test_memory_usage(self, performance_tester, test_queries):
        """测试内存使用"""
        async with performance_tester:
            memory_stats = await performance_tester.memory_stress_test(
                "/search", test_queries[0], duration_seconds=60
            )
            
            # 验证内存使用合理
            assert memory_stats["memory_increase_mb"] < 500, \
                f"内存增长过多: {memory_stats['memory_increase_mb']}MB"
            
            logger.info(f"内存测试结果: 增长{memory_stats['memory_increase_mb']:.2f}MB, "
                       f"峰值{memory_stats['peak_memory_mb']:.2f}MB")
    
    @pytest.mark.asyncio
    async def test_chinese_optimization_performance(self, performance_tester):
        """测试中文优化性能"""
        async with performance_tester:
            chinese_queries = [
                {"text": "这是一个中文分词测试句子，包含了多种词汇类型。", "optimization_type": "segmentation"},
                {"text": "人工智能技术在医疗领域的应用前景如何？", "optimization_type": "semantic_role"},
                {"text": "深度学习模型的训练过程需要大量的计算资源。", "optimization_type": "segmentation"}
            ]
            
            for query_data in chinese_queries:
                latency_stats = await performance_tester.latency_test(
                    "/advanced/chinese/optimize", query_data, iterations=20
                )
                
                # 中文优化延迟要求
                assert latency_stats.get("p95_latency", float('inf')) < 1000, \
                    f"中文优化P95延迟超过1秒: {latency_stats.get('p95_latency')}ms"
                
                logger.info(f"中文优化延迟: {latency_stats.get('p95_latency', 0):.2f}ms")


# 性能测试报告生成
def generate_performance_report(metrics: PerformanceMetrics, test_name: str) -> Dict[str, Any]:
    """生成性能测试报告"""
    return {
        "test_name": test_name,
        "timestamp": datetime.now().isoformat(),
        "metrics": {
            "response_time": {
                "average_ms": metrics.avg_response_time,
                "p95_ms": metrics.p95_response_time,
                "p99_ms": metrics.p99_response_time,
                "max_ms": metrics.max_response_time,
                "min_ms": metrics.min_response_time
            },
            "throughput": {
                "qps": metrics.qps,
                "success_rate": metrics.success_rate,
                "error_count": metrics.error_count
            },
            "resources": {
                "memory_usage_mb": metrics.memory_usage_mb,
                "cpu_usage_percent": metrics.cpu_usage_percent
            }
        },
        "requirements_met": {
            "p95_under_2s": metrics.p95_response_time < 2000,
            "qps_over_100": metrics.qps >= 100,
            "success_rate_over_99": metrics.success_rate >= 0.99
        }
    }


if __name__ == "__main__":
    # 运行性能测试
    async def main():
        async with PerformanceTester() as tester:
            # 基础延迟测试
            test_query = {"query": "测试查询", "top_k": 10}
            latency_stats = await tester.latency_test("/search", test_query)
            print(f"延迟测试结果: {latency_stats}")
            
            # 负载测试
            config = LoadTestConfig(
                concurrent_users=10,
                test_duration_seconds=30,
                ramp_up_seconds=5,
                target_qps=20,
                endpoint_url="/search",
                test_data=[test_query]
            )
            
            metrics = await tester.concurrent_load_test(config)
            report = generate_performance_report(metrics, "基础负载测试")
            print(f"负载测试报告: {report}")
    
    asyncio.run(main())
