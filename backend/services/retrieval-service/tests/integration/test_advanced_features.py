"""
高级功能集成测试
测试Self-RAG、多向量检索、中文优化等先进技术的集成效果
"""

import pytest
import asyncio
from typing import List, Dict, Any
from datetime import datetime

from app.advanced_retrieval_techniques import self_rag_retriever
from app.negative_mining_and_multivector import multi_vector_retriever, hard_negative_miner
from app.chinese_optimization import chinese_segmentation_optimizer, chinese_semantic_role_labeler
from app.temporal_scorer import temporal_scorer
from app.result_fusion_optimizer import result_fusion_optimizer
from app.ab_testing_framework import ab_testing_framework
from app.enhanced_monitoring import enhanced_monitoring


class TestAdvancedFeatures:
    """高级功能集成测试"""
    
    @pytest.fixture
    def test_queries(self):
        """测试查询数据"""
        return [
            "人工智能的发展历史和未来趋势",
            "机器学习算法的分类和应用场景",
            "深度学习在自然语言处理中的应用",
            "计算机视觉技术的原理和实现方法",
            "大数据分析的工具和技术栈"
        ]
    
    @pytest.fixture
    def chinese_test_texts(self):
        """中文测试文本"""
        return [
            "人工智能技术在医疗领域的应用前景如何？",
            "深度学习模型的训练过程需要大量的计算资源。",
            "自然语言处理技术可以帮助我们更好地理解文本内容。",
            "机器学习算法的性能评估需要使用多种指标。",
            "数据挖掘技术在商业智能中发挥着重要作用。"
        ]
    
    @pytest.mark.asyncio
    async def test_self_rag_search(self, test_queries):
        """测试Self-RAG检索功能"""
        for query in test_queries[:3]:  # 测试前3个查询
            results = await self_rag_retriever.self_rag_search(
                query=query,
                top_k=10
            )
            
            # 验证结果
            assert len(results) > 0, f"Self-RAG检索应该返回结果: {query}"
            assert all(hasattr(r, 'content') for r in results), "结果应该包含content字段"
            assert all(hasattr(r, 'score') for r in results), "结果应该包含score字段"
            assert all(r.score >= 0 for r in results), "分数应该非负"
            
            # 验证结果按分数降序排列
            scores = [r.score for r in results]
            assert scores == sorted(scores, reverse=True), "结果应该按分数降序排列"
    
    @pytest.mark.asyncio
    async def test_multi_vector_search(self, test_queries):
        """测试多向量检索功能"""
        for query in test_queries[:3]:
            results = await multi_vector_retriever.multi_vector_search(
                query=query,
                top_k=10
            )
            
            # 验证结果
            assert len(results) > 0, f"多向量检索应该返回结果: {query}"
            assert all(hasattr(r, 'content') for r in results), "结果应该包含content字段"
            assert all(hasattr(r, 'score') for r in results), "结果应该包含score字段"
            assert all(hasattr(r, 'metadata') for r in results), "结果应该包含metadata字段"
            
            # 验证方面分数
            for result in results:
                if hasattr(result, 'aspect_scores'):
                    assert isinstance(result.aspect_scores, dict), "方面分数应该是字典"
                    assert len(result.aspect_scores) > 0, "应该包含方面分数"
    
    @pytest.mark.asyncio
    async def test_chinese_segmentation(self, chinese_test_texts):
        """测试中文分词优化"""
        for text in chinese_test_texts:
            result = await chinese_segmentation_optimizer.optimize_segmentation(text)
            
            # 验证分词结果
            assert hasattr(result, 'words'), "应该包含分词结果"
            assert len(result.words) > 0, "分词结果不应为空"
            assert hasattr(result, 'confidence'), "应该包含置信度"
            assert 0 <= result.confidence <= 1, "置信度应该在0-1之间"
            assert hasattr(result, 'method'), "应该包含使用的方法"
            
            # 验证分词质量
            original_length = len(text)
            segmented_length = len(''.join(result.words))
            assert abs(original_length - segmented_length) <= 2, "分词后长度应该基本一致"
    
    @pytest.mark.asyncio
    async def test_chinese_semantic_role_labeling(self, chinese_test_texts):
        """测试中文语义角色标注"""
        for text in chinese_test_texts:
            result = await chinese_semantic_role_labeler.analyze_semantic_roles(text)
            
            # 验证SRL结果
            assert isinstance(result, dict), "SRL结果应该是字典"
            assert 'confidence' in result, "应该包含置信度"
            assert 0 <= result['confidence'] <= 1, "置信度应该在0-1之间"
            
            if 'predicate' in result and result['predicate']:
                assert isinstance(result['predicate'], str), "谓词应该是字符串"
                assert len(result['predicate']) > 0, "谓词不应为空"
            
            if 'roles' in result:
                assert isinstance(result['roles'], dict), "角色应该是字典"
    
    @pytest.mark.asyncio
    async def test_temporal_scoring(self):
        """测试时效性评分"""
        test_cases = [
            {
                "content": "2023年人工智能技术的最新发展",
                "query": "人工智能最新进展",
                "metadata": {"created_at": "2023-12-01T10:00:00Z"}
            },
            {
                "content": "历史上的重要科学发现",
                "query": "科学发现历史",
                "metadata": {"created_at": "2020-01-01T10:00:00Z"}
            }
        ]
        
        for case in test_cases:
            score = await temporal_scorer.calculate_temporal_score(
                content=case["content"],
                query=case["query"],
                metadata=case["metadata"]
            )
            
            # 验证时效性评分
            assert hasattr(score, 'freshness_score'), "应该包含新鲜度分数"
            assert hasattr(score, 'relevance_score'), "应该包含相关性分数"
            assert hasattr(score, 'final_score'), "应该包含最终分数"
            assert 0 <= score.final_score <= 1, "最终分数应该在0-1之间"
            
            # 验证时间特征
            assert hasattr(score, 'features'), "应该包含时间特征"
            assert hasattr(score.features, 'creation_time'), "应该包含创建时间"
    
    @pytest.mark.asyncio
    async def test_result_fusion(self, test_queries):
        """测试结果融合优化"""
        # 模拟多种检索结果
        search_results = {
            "semantic_search": [
                {"content": f"语义检索结果 {i}", "score": 0.8 - i*0.1, "metadata": {}}
                for i in range(5)
            ],
            "multi_vector_search": [
                {"content": f"多向量检索结果 {i}", "score": 0.9 - i*0.1, "metadata": {}}
                for i in range(5)
            ],
            "temporal_aware_search": [
                {"content": f"时效性检索结果 {i}", "score": 0.7 - i*0.1, "metadata": {}}
                for i in range(5)
            ]
        }
        
        query = test_queries[0]
        fused_results = await result_fusion_optimizer.optimize_fusion(
            search_results, query, {"user_id": "test_user"}
        )
        
        # 验证融合结果
        assert len(fused_results) > 0, "融合结果不应为空"
        assert all(hasattr(r, 'content') for r in fused_results), "应该包含content字段"
        assert all(hasattr(r, 'final_score') for r in fused_results), "应该包含最终分数"
        assert all(hasattr(r, 'fusion_method') for r in fused_results), "应该包含融合方法"
        assert all(hasattr(r, 'confidence') for r in fused_results), "应该包含置信度"
        
        # 验证分数排序
        scores = [r.final_score for r in fused_results]
        assert scores == sorted(scores, reverse=True), "融合结果应该按分数降序排列"
    
    @pytest.mark.asyncio
    async def test_hard_negative_mining(self):
        """测试困难负样本挖掘"""
        # 模拟正样本和候选文档
        query = "机器学习算法"
        positive_docs = [
            {"id": "pos1", "content": "机器学习是人工智能的重要分支", "metadata": {}},
            {"id": "pos2", "content": "监督学习和无监督学习是主要类型", "metadata": {}}
        ]
        candidate_docs = [
            {"id": "cand1", "content": "深度学习神经网络结构", "metadata": {}},
            {"id": "cand2", "content": "计算机视觉图像识别", "metadata": {}},
            {"id": "cand3", "content": "自然语言处理文本分析", "metadata": {}},
            {"id": "cand4", "content": "数据挖掘统计分析", "metadata": {}},
            {"id": "cand5", "content": "天气预报气象数据", "metadata": {}}
        ]
        
        negative_samples = await hard_negative_miner.mine_hard_negatives(
            query=query,
            positive_docs=positive_docs,
            candidate_docs=candidate_docs,
            top_k=3
        )
        
        # 验证困难负样本
        assert len(negative_samples) > 0, "应该挖掘到困难负样本"
        assert len(negative_samples) <= 3, "负样本数量不应超过top_k"
        
        for sample in negative_samples:
            assert hasattr(sample, 'doc_id'), "应该包含文档ID"
            assert hasattr(sample, 'similarity_score'), "应该包含相似度分数"
            assert hasattr(sample, 'difficulty_level'), "应该包含困难度级别"
            assert 0 <= sample.similarity_score <= 1, "相似度分数应该在0-1之间"
    
    @pytest.mark.asyncio
    async def test_ab_testing_framework(self):
        """测试A/B测试框架"""
        # 创建测试实验
        experiment_config = {
            "experiment_name": "test_experiment",
            "description": "测试实验",
            "control_group": "control",
            "treatment_group": "treatment",
            "traffic_split": 0.5,
            "start_date": datetime.now().isoformat(),
            "status": "active"
        }
        
        success = await ab_testing_framework.create_experiment(experiment_config)
        assert success, "应该成功创建A/B测试实验"
        
        # 测试用户分组
        user_assignment = await ab_testing_framework.assign_user_to_group(
            "test_experiment", "test_user_123"
        )
        
        assert user_assignment is not None, "应该成功分配用户组"
        assert "group_name" in user_assignment, "应该包含组名"
        assert user_assignment["group_name"] in ["control", "treatment"], "组名应该有效"
        
        # 测试实验结果记录
        result_recorded = await ab_testing_framework.record_experiment_result(
            experiment_name="test_experiment",
            user_id="test_user_123",
            group_name=user_assignment["group_name"],
            metric_name="response_time",
            metric_value=150.0
        )
        
        assert result_recorded, "应该成功记录实验结果"
    
    @pytest.mark.asyncio
    async def test_monitoring_metrics(self):
        """测试监控指标收集"""
        # 记录搜索指标
        enhanced_monitoring.record_search_metrics(
            response_time=120.5,
            result_count=10,
            search_type="advanced_search"
        )
        
        # 记录Self-RAG指标
        enhanced_monitoring.record_self_rag_metrics(
            iterations=2,
            confidence=0.85,
            query_type="complex"
        )
        
        # 记录多向量指标
        enhanced_monitoring.record_multi_vector_metrics(
            aspect_count=5,
            fusion_score=0.92,
            fusion_method="adaptive",
            diversity_score=0.75
        )
        
        # 获取指标摘要
        metrics_summary = await enhanced_monitoring.get_metrics_summary()
        
        # 验证指标摘要
        assert isinstance(metrics_summary, dict), "指标摘要应该是字典"
        assert "search_metrics" in metrics_summary, "应该包含搜索指标"
        assert "self_rag_metrics" in metrics_summary, "应该包含Self-RAG指标"
        assert "multi_vector_metrics" in metrics_summary, "应该包含多向量指标"
    
    @pytest.mark.asyncio
    async def test_end_to_end_advanced_search(self, test_queries):
        """端到端高级搜索测试"""
        query = test_queries[0]
        
        # 模拟高级搜索流程
        # 1. 中文优化
        if any(ord(char) > 127 for char in query):  # 包含中文字符
            segmentation_result = await chinese_segmentation_optimizer.optimize_segmentation(query)
            optimized_query = " ".join(segmentation_result.words)
        else:
            optimized_query = query
        
        # 2. 执行多种检索
        self_rag_results = await self_rag_retriever.self_rag_search(optimized_query, top_k=5)
        multi_vector_results = await multi_vector_retriever.multi_vector_search(optimized_query, top_k=5)
        
        # 3. 构建搜索结果字典
        search_results = {
            "self_rag_search": [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in self_rag_results
            ],
            "multi_vector_search": [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in multi_vector_results
            ]
        }
        
        # 4. 结果融合
        fused_results = await result_fusion_optimizer.optimize_fusion(
            search_results, optimized_query, {"user_id": "test_user"}
        )
        
        # 验证端到端结果
        assert len(fused_results) > 0, "端到端搜索应该返回结果"
        assert all(hasattr(r, 'content') for r in fused_results), "结果应该包含content"
        assert all(hasattr(r, 'final_score') for r in fused_results), "结果应该包含最终分数"
        assert all(r.final_score > 0 for r in fused_results), "最终分数应该大于0"
        
        # 验证结果质量
        top_result = fused_results[0]
        assert top_result.final_score >= 0.1, "顶部结果分数应该合理"
        assert len(top_result.content) > 10, "顶部结果内容应该有意义"
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self, test_queries):
        """测试性能要求"""
        import time
        
        query = test_queries[0]
        
        # 测试响应时间
        start_time = time.time()
        results = await self_rag_retriever.self_rag_search(query, top_k=10)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 毫秒
        
        # 验证性能要求
        assert response_time < 5000, f"Self-RAG检索响应时间应该小于5秒，实际: {response_time:.2f}ms"
        assert len(results) > 0, "应该返回检索结果"
        
        # 测试并发性能
        concurrent_tasks = []
        for i in range(5):  # 5个并发请求
            task = asyncio.create_task(
                self_rag_retriever.self_rag_search(f"{query} {i}", top_k=5)
            )
            concurrent_tasks.append(task)
        
        start_time = time.time()
        concurrent_results = await asyncio.gather(*concurrent_tasks)
        end_time = time.time()
        
        concurrent_time = (end_time - start_time) * 1000
        
        # 验证并发性能
        assert concurrent_time < 10000, f"并发检索时间应该小于10秒，实际: {concurrent_time:.2f}ms"
        assert all(len(r) > 0 for r in concurrent_results), "所有并发请求都应该返回结果"
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        # 测试空查询
        empty_results = await self_rag_retriever.self_rag_search("", top_k=10)
        assert len(empty_results) == 0, "空查询应该返回空结果"
        
        # 测试无效参数
        invalid_results = await multi_vector_retriever.multi_vector_search("test", top_k=0)
        assert len(invalid_results) == 0, "无效top_k应该返回空结果"
        
        # 测试异常文本
        try:
            await chinese_segmentation_optimizer.optimize_segmentation(None)
            assert False, "None文本应该抛出异常"
        except (ValueError, TypeError):
            pass  # 预期的异常
        
        # 测试无效时间格式
        invalid_temporal_score = await temporal_scorer.calculate_temporal_score(
            content="test content",
            query="test query",
            metadata={"created_at": "invalid_date"}
        )
        assert invalid_temporal_score.final_score >= 0, "无效日期应该有默认处理"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
