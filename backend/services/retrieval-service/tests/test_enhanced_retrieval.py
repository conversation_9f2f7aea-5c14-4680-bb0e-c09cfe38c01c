"""
增强检索功能测试用例
测试HyDE、查询重写、重排序等功能
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from app.enhanced_retrieval import (
    QueryRewriter, 
    HyDEGenerator, 
    EnhancedRetrievalEngine,
    SearchResult
)
from app.advanced_reranker import (
    AdvancedReranker,
    CrossEncoderReranker,
    UserBehaviorAnalyzer,
    FreshnessScorer,
    AuthorityScorer,
    RerankingResult
)


class TestQueryRewriter:
    """查询重写器测试"""
    
    @pytest.fixture
    def query_rewriter(self):
        return QueryRewriter()
    
    @pytest.mark.asyncio
    async def test_basic_query_rewrite(self, query_rewriter):
        """测试基础查询重写"""
        query = "如何优化系统性能"
        
        rewritten_queries = await query_rewriter.rewrite_query(query)
        
        assert isinstance(rewritten_queries, list)
        assert len(rewritten_queries) >= 1
        assert query in rewritten_queries  # 原始查询应该包含在内
        
        # 检查是否生成了变体
        unique_queries = set(rewritten_queries)
        assert len(unique_queries) >= 1
    
    @pytest.mark.asyncio
    async def test_synonym_expansion(self, query_rewriter):
        """测试同义词扩展"""
        query = "解决问题的方法"
        
        expanded_queries = await query_rewriter._expand_with_synonyms(query)
        
        assert isinstance(expanded_queries, list)
        # 应该包含同义词替换的变体
        assert any("处理" in q or "方式" in q for q in expanded_queries)
    
    @pytest.mark.asyncio
    async def test_context_enhancement(self, query_rewriter):
        """测试上下文增强"""
        query = "优化性能"
        context = {
            "conversation_history": [
                {"role": "user", "content": "我在使用数据库查询"},
                {"role": "assistant", "content": "数据库查询优化很重要"},
                {"role": "user", "content": "优化性能"}
            ]
        }
        
        enhanced_queries = await query_rewriter._enhance_with_context(query, context)
        
        assert isinstance(enhanced_queries, list)
        # 应该包含上下文关键词
        if enhanced_queries:
            assert any("数据库" in q for q in enhanced_queries)
    
    @pytest.mark.asyncio
    async def test_query_simplification(self, query_rewriter):
        """测试查询简化"""
        query = "请问如何在复杂的系统环境中优化数据库查询性能"
        
        simplified = await query_rewriter._simplify_query(query)
        
        if simplified:
            assert len(simplified) < len(query)
            assert "优化" in simplified
            assert "数据库" in simplified


class TestHyDEGenerator:
    """HyDE生成器测试"""
    
    @pytest.fixture
    def hyde_generator(self):
        return HyDEGenerator()
    
    @pytest.mark.asyncio
    async def test_hypothetical_document_generation(self, hyde_generator):
        """测试假设性文档生成"""
        query = "什么是机器学习"
        
        with patch.object(hyde_generator, '_call_llm_for_hyde') as mock_llm:
            mock_llm.return_value = "机器学习是一种人工智能技术，通过算法让计算机从数据中学习模式..."
            
            hyp_doc = await hyde_generator.generate_hypothetical_document(query)
            
            assert isinstance(hyp_doc, str)
            assert len(hyp_doc) > 0
            mock_llm.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_hyde_search(self, hyde_generator):
        """测试HyDE检索"""
        query = "Python编程技巧"
        
        # Mock依赖
        with patch('app.enhanced_retrieval.embedding_client') as mock_embedding, \
             patch('app.enhanced_retrieval.retrieval_engine') as mock_engine:
            
            mock_embedding.embed_text.return_value = [0.1] * 1536
            mock_engine.semantic_search.return_value = [
                {
                    "id": "doc1",
                    "content": "Python是一种编程语言",
                    "score": 0.9,
                    "metadata": {}
                }
            ]
            
            results = await hyde_generator.hyde_search(query, top_k=5)
            
            assert isinstance(results, list)
            assert len(results) <= 5
            if results:
                assert isinstance(results[0], SearchResult)
    
    def test_merge_hyde_results(self, hyde_generator):
        """测试HyDE结果融合"""
        hyp_results = [
            {"id": "doc1", "content": "内容1", "score": 0.9, "metadata": {}}
        ]
        query_results = [
            {"id": "doc1", "content": "内容1", "score": 0.8, "metadata": {}},
            {"id": "doc2", "content": "内容2", "score": 0.7, "metadata": {}}
        ]
        
        merged = hyde_generator._merge_hyde_results(hyp_results, query_results, top_k=5)
        
        assert isinstance(merged, list)
        assert len(merged) <= 5
        # doc1应该有更高的综合分数（HyDE + 原始查询）
        if len(merged) >= 2:
            assert merged[0].id == "doc1"


class TestAdvancedReranker:
    """高级重排序器测试"""
    
    @pytest.fixture
    def reranker(self):
        return AdvancedReranker()
    
    @pytest.fixture
    def sample_results(self):
        return [
            {
                "id": "doc1",
                "content": "Python是一种编程语言",
                "score": 0.8,
                "metadata": {"timestamp": "2024-01-01T00:00:00"}
            },
            {
                "id": "doc2", 
                "content": "机器学习算法介绍",
                "score": 0.7,
                "metadata": {"timestamp": "2024-02-01T00:00:00"}
            }
        ]
    
    @pytest.mark.asyncio
    async def test_rerank_basic(self, reranker, sample_results):
        """测试基础重排序"""
        query = "Python编程"
        
        # Mock各种评分器
        with patch.object(reranker, '_calculate_semantic_scores') as mock_semantic, \
             patch.object(reranker, '_calculate_behavior_scores') as mock_behavior, \
             patch.object(reranker, '_calculate_freshness_scores') as mock_freshness, \
             patch.object(reranker, '_calculate_authority_scores') as mock_authority:
            
            mock_semantic.return_value = [0.9, 0.7]
            mock_behavior.return_value = [0.6, 0.8]
            mock_freshness.return_value = [0.5, 0.9]
            mock_authority.return_value = [0.7, 0.6]
            
            results = await reranker.rerank(query, sample_results)
            
            assert isinstance(results, list)
            assert len(results) == 2
            assert all(isinstance(r, RerankingResult) for r in results)
            
            # 检查分数计算
            for result in results:
                assert hasattr(result, 'rerank_score')
                assert hasattr(result, 'factors')
                assert 'semantic' in result.factors
                assert 'behavior' in result.factors
                assert 'freshness' in result.factors
                assert 'authority' in result.factors


class TestCrossEncoderReranker:
    """交叉编码器重排序器测试"""
    
    @pytest.fixture
    def cross_encoder(self):
        return CrossEncoderReranker()
    
    @pytest.mark.asyncio
    async def test_score_pairs_without_model(self, cross_encoder):
        """测试无模型时的评分"""
        query = "测试查询"
        documents = ["文档1", "文档2"]
        
        # 模型未加载时应返回默认分数
        scores = await cross_encoder.score_pairs(query, documents)
        
        assert isinstance(scores, list)
        assert len(scores) == 2
        assert all(isinstance(s, float) for s in scores)
        assert all(0.0 <= s <= 1.0 for s in scores)


class TestUserBehaviorAnalyzer:
    """用户行为分析器测试"""
    
    @pytest.fixture
    def behavior_analyzer(self):
        return UserBehaviorAnalyzer()
    
    @pytest.mark.asyncio
    async def test_get_user_preferences(self, behavior_analyzer):
        """测试获取用户偏好"""
        user_id = "test_user"
        
        with patch('app.advanced_reranker.retrieval_cache') as mock_cache:
            mock_cache.get.return_value = None  # 缓存未命中
            
            prefs = await behavior_analyzer.get_user_preferences(user_id)
            
            assert isinstance(prefs, dict)
            assert "preferred_content_types" in prefs
            assert "interaction_patterns" in prefs
    
    @pytest.mark.asyncio
    async def test_score_user_relevance(self, behavior_analyzer):
        """测试用户相关性评分"""
        document = {
            "content": "Python编程教程",
            "metadata": {
                "content_type": "tutorial",
                "topics": ["programming", "python"]
            }
        }
        
        user_prefs = {
            "preferred_content_types": ["tutorial"],
            "topic_interests": ["programming"],
            "preferred_content_length": "medium"
        }
        
        score = await behavior_analyzer.score_user_relevance(document, user_prefs)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # 应该有较高分数，因为匹配用户偏好


class TestFreshnessScorer:
    """时效性评分器测试"""
    
    @pytest.fixture
    def freshness_scorer(self):
        return FreshnessScorer()
    
    def test_score_freshness_recent(self, freshness_scorer):
        """测试最近内容的时效性评分"""
        from datetime import datetime, timedelta
        
        recent_time = datetime.now() - timedelta(days=1)
        score = freshness_scorer.score_freshness(recent_time)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # 最近内容应该有高分
    
    def test_score_freshness_old(self, freshness_scorer):
        """测试旧内容的时效性评分"""
        from datetime import datetime, timedelta
        
        old_time = datetime.now() - timedelta(days=365)
        score = freshness_scorer.score_freshness(old_time)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # 旧内容应该有低分
    
    def test_score_freshness_none(self, freshness_scorer):
        """测试无时间戳的时效性评分"""
        score = freshness_scorer.score_freshness(None)
        
        assert isinstance(score, float)
        assert score == 0.5  # 无时间戳应返回中等分数


class TestAuthorityScorer:
    """权威性评分器测试"""
    
    @pytest.fixture
    def authority_scorer(self):
        return AuthorityScorer()
    
    def test_score_authority_official(self, authority_scorer):
        """测试官方文档的权威性评分"""
        document = {
            "metadata": {
                "source_type": "official_doc",
                "citation_count": 100,
                "author_authority": 0.9,
                "quality_score": 0.8
            }
        }
        
        score = authority_scorer.score_authority(document)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # 官方文档应该有高权威性
    
    def test_score_authority_unknown(self, authority_scorer):
        """测试未知来源的权威性评分"""
        document = {
            "metadata": {
                "source_type": "unknown"
            }
        }
        
        score = authority_scorer.score_authority(document)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score == 0.5  # 未知来源应该有中等权威性


class TestEnhancedRetrievalEngine:
    """增强检索引擎测试"""
    
    @pytest.fixture
    def enhanced_engine(self):
        return EnhancedRetrievalEngine()
    
    @pytest.mark.asyncio
    async def test_enhanced_search_baseline(self, enhanced_engine):
        """测试基线检索"""
        query = "测试查询"
        
        with patch('app.enhanced_retrieval.retrieval_engine') as mock_engine:
            mock_engine.search.return_value = [
                {
                    "id": "doc1",
                    "content": "测试内容",
                    "score": 0.8,
                    "metadata": {}
                }
            ]
            
            results = await enhanced_engine.enhanced_search(
                query=query,
                search_type="baseline",
                top_k=5
            )
            
            assert isinstance(results, list)
            assert len(results) <= 5
            if results:
                assert isinstance(results[0], SearchResult)
                assert results[0].source_type == "baseline"
    
    @pytest.mark.asyncio
    async def test_enhanced_search_full(self, enhanced_engine):
        """测试全功能增强检索"""
        query = "测试查询"
        
        # Mock所有依赖
        with patch.object(enhanced_engine.hyde_generator, 'hyde_search') as mock_hyde, \
             patch.object(enhanced_engine, '_rewrite_search') as mock_rewrite, \
             patch.object(enhanced_engine, '_baseline_search') as mock_baseline:
            
            mock_hyde.return_value = [SearchResult("1", "内容1", 0.9, {}, "hyde")]
            mock_rewrite.return_value = [SearchResult("2", "内容2", 0.8, {}, "rewrite")]
            mock_baseline.return_value = [SearchResult("3", "内容3", 0.7, {}, "baseline")]
            
            results = await enhanced_engine.enhanced_search(
                query=query,
                search_type="enhanced",
                top_k=5
            )
            
            assert isinstance(results, list)
            assert len(results) <= 5
            
            # 验证各种检索方法都被调用
            mock_hyde.assert_called_once()
            mock_rewrite.assert_called_once()
            mock_baseline.assert_called_once()
    
    def test_deduplicate_and_rank(self, enhanced_engine):
        """测试去重和排序"""
        results = [
            SearchResult("1", "内容1", 0.9, {}, "source1"),
            SearchResult("1", "内容1", 0.8, {}, "source2"),  # 重复ID
            SearchResult("2", "内容2", 0.7, {}, "source1")
        ]
        
        deduplicated = enhanced_engine._deduplicate_and_rank(results, top_k=5)
        
        assert len(deduplicated) == 2  # 去重后应该只有2个
        assert deduplicated[0].id == "1"  # 最高分的应该排在前面
        assert deduplicated[0].score == 0.9  # 应该保留最高分数


# 集成测试
class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_enhanced_search(self):
        """端到端增强检索测试"""
        # 这里可以添加完整的端到端测试
        # 包括从API请求到最终结果的完整流程
        pass


# 性能测试
class TestPerformance:
    """性能测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """并发请求测试"""
        # 测试系统在高并发下的表现
        pass
    
    @pytest.mark.asyncio
    async def test_large_result_set(self):
        """大结果集测试"""
        # 测试处理大量检索结果的性能
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
