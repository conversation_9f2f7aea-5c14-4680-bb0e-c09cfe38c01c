#!/usr/bin/env python3
"""
生产环境部署脚本
实现灰度发布、全量上线、回滚等部署功能
"""

import asyncio
import json
import time
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass

import aiohttp
from loguru import logger


@dataclass
class DeploymentConfig:
    """部署配置"""
    environment: str  # staging, production
    version: str
    image_tag: str
    replicas: int
    canary_percentage: int  # 灰度发布百分比
    health_check_url: str
    rollback_on_failure: bool
    monitoring_duration_minutes: int


@dataclass
class DeploymentStatus:
    """部署状态"""
    phase: str  # preparing, deploying, monitoring, completed, failed
    progress: int  # 0-100
    message: str
    start_time: datetime
    current_time: datetime
    errors: List[str]


class ProductionDeployer:
    """生产环境部署器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.status = DeploymentStatus(
            phase="preparing",
            progress=0,
            message="准备部署",
            start_time=datetime.now(),
            current_time=datetime.now(),
            errors=[]
        )
        
        # 部署步骤
        self.deployment_steps = [
            ("pre_deployment_checks", "部署前检查", 10),
            ("build_and_push_image", "构建和推送镜像", 20),
            ("update_configuration", "更新配置", 30),
            ("canary_deployment", "灰度部署", 50),
            ("health_monitoring", "健康监控", 70),
            ("full_deployment", "全量部署", 90),
            ("post_deployment_verification", "部署后验证", 100)
        ]
    
    async def deploy(self) -> bool:
        """执行部署"""
        logger.info(f"开始部署到 {self.config.environment} 环境")
        
        try:
            for step_name, step_desc, progress in self.deployment_steps:
                self._update_status("deploying", progress, f"执行: {step_desc}")
                
                step_method = getattr(self, step_name)
                success = await step_method()
                
                if not success:
                    self._update_status("failed", progress, f"步骤失败: {step_desc}")
                    if self.config.rollback_on_failure:
                        await self.rollback()
                    return False
                
                logger.info(f"步骤完成: {step_desc}")
            
            self._update_status("completed", 100, "部署成功完成")
            logger.info("生产环境部署成功")
            return True
            
        except Exception as e:
            error_msg = f"部署过程中发生异常: {e}"
            self.status.errors.append(error_msg)
            self._update_status("failed", self.status.progress, error_msg)
            logger.error(error_msg)
            
            if self.config.rollback_on_failure:
                await self.rollback()
            
            return False
    
    async def pre_deployment_checks(self) -> bool:
        """部署前检查"""
        logger.info("执行部署前检查...")
        
        checks = [
            ("check_docker_registry", "检查Docker镜像仓库连接"),
            ("check_kubernetes_cluster", "检查Kubernetes集群状态"),
            ("check_database_connection", "检查数据库连接"),
            ("check_dependencies", "检查依赖服务"),
            ("validate_configuration", "验证配置文件")
        ]
        
        for check_method, check_desc in checks:
            logger.info(f"执行检查: {check_desc}")
            
            try:
                method = getattr(self, check_method)
                success = await method()
                
                if not success:
                    error_msg = f"检查失败: {check_desc}"
                    self.status.errors.append(error_msg)
                    logger.error(error_msg)
                    return False
                    
            except Exception as e:
                error_msg = f"检查异常 {check_desc}: {e}"
                self.status.errors.append(error_msg)
                logger.error(error_msg)
                return False
        
        logger.info("部署前检查全部通过")
        return True
    
    async def check_docker_registry(self) -> bool:
        """检查Docker镜像仓库"""
        try:
            # 检查镜像是否存在
            result = subprocess.run(
                ["docker", "manifest", "inspect", f"retrieval-service:{self.config.image_tag}"],
                capture_output=True, text=True
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Docker镜像仓库检查失败: {e}")
            return False
    
    async def check_kubernetes_cluster(self) -> bool:
        """检查Kubernetes集群状态"""
        try:
            result = subprocess.run(
                ["kubectl", "cluster-info"],
                capture_output=True, text=True
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Kubernetes集群检查失败: {e}")
            return False
    
    async def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            # 这里应该实际连接数据库进行检查
            # 简化实现，假设检查通过
            await asyncio.sleep(1)
            return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False
    
    async def check_dependencies(self) -> bool:
        """检查依赖服务"""
        try:
            # 检查Redis、Elasticsearch等依赖服务
            dependencies = [
                "redis-service",
                "elasticsearch-service",
                "prometheus-service"
            ]
            
            for service in dependencies:
                result = subprocess.run(
                    ["kubectl", "get", "service", service],
                    capture_output=True, text=True
                )
                if result.returncode != 0:
                    logger.error(f"依赖服务不可用: {service}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"依赖服务检查失败: {e}")
            return False
    
    async def validate_configuration(self) -> bool:
        """验证配置文件"""
        try:
            config_files = [
                "deploy/k8s/deployment.yaml",
                "deploy/k8s/service.yaml",
                "deploy/k8s/configmap.yaml"
            ]
            
            for config_file in config_files:
                if not Path(config_file).exists():
                    logger.error(f"配置文件不存在: {config_file}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"配置文件验证失败: {e}")
            return False
    
    async def build_and_push_image(self) -> bool:
        """构建和推送镜像"""
        logger.info("构建Docker镜像...")
        
        try:
            # 构建镜像
            build_cmd = [
                "docker", "build",
                "-t", f"retrieval-service:{self.config.image_tag}",
                "-f", "Dockerfile",
                "."
            ]
            
            result = subprocess.run(build_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"镜像构建失败: {result.stderr}")
                return False
            
            # 推送镜像
            push_cmd = [
                "docker", "push", f"retrieval-service:{self.config.image_tag}"
            ]
            
            result = subprocess.run(push_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"镜像推送失败: {result.stderr}")
                return False
            
            logger.info("镜像构建和推送成功")
            return True
            
        except Exception as e:
            logger.error(f"镜像构建推送失败: {e}")
            return False
    
    async def update_configuration(self) -> bool:
        """更新配置"""
        logger.info("更新Kubernetes配置...")
        
        try:
            # 更新ConfigMap
            result = subprocess.run(
                ["kubectl", "apply", "-f", "deploy/k8s/configmap.yaml"],
                capture_output=True, text=True
            )
            if result.returncode != 0:
                logger.error(f"ConfigMap更新失败: {result.stderr}")
                return False
            
            # 更新Secret
            result = subprocess.run(
                ["kubectl", "apply", "-f", "deploy/k8s/secret.yaml"],
                capture_output=True, text=True
            )
            if result.returncode != 0:
                logger.error(f"Secret更新失败: {result.stderr}")
                return False
            
            logger.info("配置更新成功")
            return True
            
        except Exception as e:
            logger.error(f"配置更新失败: {e}")
            return False
    
    async def canary_deployment(self) -> bool:
        """灰度部署"""
        logger.info(f"开始灰度部署，流量比例: {self.config.canary_percentage}%")
        
        try:
            # 创建灰度版本的Deployment
            canary_deployment = self._generate_canary_deployment_yaml()
            
            with open("deploy/k8s/canary-deployment.yaml", "w") as f:
                f.write(canary_deployment)
            
            # 应用灰度部署
            result = subprocess.run(
                ["kubectl", "apply", "-f", "deploy/k8s/canary-deployment.yaml"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                logger.error(f"灰度部署失败: {result.stderr}")
                return False
            
            # 等待Pod就绪
            await self._wait_for_pods_ready("app=retrieval-service,version=canary")
            
            logger.info("灰度部署成功")
            return True
            
        except Exception as e:
            logger.error(f"灰度部署失败: {e}")
            return False
    
    async def health_monitoring(self) -> bool:
        """健康监控"""
        logger.info(f"开始健康监控，持续 {self.config.monitoring_duration_minutes} 分钟")
        
        monitoring_start = time.time()
        monitoring_duration = self.config.monitoring_duration_minutes * 60
        
        while time.time() - monitoring_start < monitoring_duration:
            try:
                # 检查Pod健康状态
                pod_health = await self._check_pod_health()
                if not pod_health:
                    logger.error("Pod健康检查失败")
                    return False
                
                # 检查应用健康状态
                app_health = await self._check_application_health()
                if not app_health:
                    logger.error("应用健康检查失败")
                    return False
                
                # 检查关键指标
                metrics_ok = await self._check_key_metrics()
                if not metrics_ok:
                    logger.error("关键指标检查失败")
                    return False
                
                logger.info("健康检查通过，继续监控...")
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"健康监控异常: {e}")
                return False
        
        logger.info("健康监控完成，所有检查通过")
        return True
    
    async def full_deployment(self) -> bool:
        """全量部署"""
        logger.info("开始全量部署...")
        
        try:
            # 更新主Deployment
            deployment_yaml = self._generate_full_deployment_yaml()
            
            with open("deploy/k8s/deployment.yaml", "w") as f:
                f.write(deployment_yaml)
            
            # 应用全量部署
            result = subprocess.run(
                ["kubectl", "apply", "-f", "deploy/k8s/deployment.yaml"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                logger.error(f"全量部署失败: {result.stderr}")
                return False
            
            # 等待滚动更新完成
            result = subprocess.run(
                ["kubectl", "rollout", "status", "deployment/retrieval-service"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                logger.error(f"滚动更新失败: {result.stderr}")
                return False
            
            # 清理灰度部署
            subprocess.run(
                ["kubectl", "delete", "-f", "deploy/k8s/canary-deployment.yaml"],
                capture_output=True, text=True
            )
            
            logger.info("全量部署成功")
            return True
            
        except Exception as e:
            logger.error(f"全量部署失败: {e}")
            return False
    
    async def post_deployment_verification(self) -> bool:
        """部署后验证"""
        logger.info("执行部署后验证...")
        
        try:
            # 验证服务可用性
            service_available = await self._verify_service_availability()
            if not service_available:
                logger.error("服务可用性验证失败")
                return False
            
            # 验证API功能
            api_functional = await self._verify_api_functionality()
            if not api_functional:
                logger.error("API功能验证失败")
                return False
            
            # 验证数据库连接
            db_connected = await self._verify_database_connectivity()
            if not db_connected:
                logger.error("数据库连接验证失败")
                return False
            
            # 验证监控指标
            monitoring_ok = await self._verify_monitoring_metrics()
            if not monitoring_ok:
                logger.error("监控指标验证失败")
                return False
            
            logger.info("部署后验证全部通过")
            return True
            
        except Exception as e:
            logger.error(f"部署后验证失败: {e}")
            return False
    
    async def rollback(self) -> bool:
        """回滚部署"""
        logger.info("开始回滚部署...")
        
        try:
            # 回滚到上一个版本
            result = subprocess.run(
                ["kubectl", "rollout", "undo", "deployment/retrieval-service"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                logger.error(f"回滚失败: {result.stderr}")
                return False
            
            # 等待回滚完成
            result = subprocess.run(
                ["kubectl", "rollout", "status", "deployment/retrieval-service"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                logger.error(f"回滚状态检查失败: {result.stderr}")
                return False
            
            # 清理灰度部署
            subprocess.run(
                ["kubectl", "delete", "-f", "deploy/k8s/canary-deployment.yaml"],
                capture_output=True, text=True
            )
            
            logger.info("回滚成功")
            return True
            
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            return False
    
    def _update_status(self, phase: str, progress: int, message: str):
        """更新部署状态"""
        self.status.phase = phase
        self.status.progress = progress
        self.status.message = message
        self.status.current_time = datetime.now()
        
        logger.info(f"部署状态: {phase} ({progress}%) - {message}")
    
    def _generate_canary_deployment_yaml(self) -> str:
        """生成灰度部署YAML"""
        canary_replicas = max(1, int(self.config.replicas * self.config.canary_percentage / 100))
        
        return f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: retrieval-service-canary
  labels:
    app: retrieval-service
    version: canary
spec:
  replicas: {canary_replicas}
  selector:
    matchLabels:
      app: retrieval-service
      version: canary
  template:
    metadata:
      labels:
        app: retrieval-service
        version: canary
    spec:
      containers:
      - name: retrieval-service
        image: retrieval-service:{self.config.image_tag}
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "{self.config.environment}"
        - name: VERSION
          value: "{self.config.version}"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
"""
    
    def _generate_full_deployment_yaml(self) -> str:
        """生成全量部署YAML"""
        return f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: retrieval-service
  labels:
    app: retrieval-service
    version: stable
spec:
  replicas: {self.config.replicas}
  selector:
    matchLabels:
      app: retrieval-service
      version: stable
  template:
    metadata:
      labels:
        app: retrieval-service
        version: stable
    spec:
      containers:
      - name: retrieval-service
        image: retrieval-service:{self.config.image_tag}
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "{self.config.environment}"
        - name: VERSION
          value: "{self.config.version}"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
"""
    
    async def _wait_for_pods_ready(self, label_selector: str, timeout: int = 300) -> bool:
        """等待Pod就绪"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = subprocess.run(
                ["kubectl", "get", "pods", "-l", label_selector, "-o", "json"],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                pods_data = json.loads(result.stdout)
                pods = pods_data.get("items", [])
                
                if pods:
                    all_ready = True
                    for pod in pods:
                        status = pod.get("status", {})
                        conditions = status.get("conditions", [])
                        
                        ready_condition = next(
                            (c for c in conditions if c["type"] == "Ready"), None
                        )
                        
                        if not ready_condition or ready_condition["status"] != "True":
                            all_ready = False
                            break
                    
                    if all_ready:
                        logger.info("所有Pod已就绪")
                        return True
            
            logger.info("等待Pod就绪...")
            await asyncio.sleep(10)
        
        logger.error("等待Pod就绪超时")
        return False
    
    async def _check_pod_health(self) -> bool:
        """检查Pod健康状态"""
        try:
            result = subprocess.run(
                ["kubectl", "get", "pods", "-l", "app=retrieval-service", "-o", "json"],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                return False
            
            pods_data = json.loads(result.stdout)
            pods = pods_data.get("items", [])
            
            for pod in pods:
                status = pod.get("status", {})
                phase = status.get("phase", "")
                
                if phase != "Running":
                    logger.warning(f"Pod状态异常: {phase}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Pod健康检查异常: {e}")
            return False
    
    async def _check_application_health(self) -> bool:
        """检查应用健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.config.health_check_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"应用健康检查异常: {e}")
            return False
    
    async def _check_key_metrics(self) -> bool:
        """检查关键指标"""
        try:
            # 这里应该检查Prometheus指标
            # 简化实现，假设检查通过
            await asyncio.sleep(1)
            return True
            
        except Exception as e:
            logger.error(f"关键指标检查异常: {e}")
            return False
    
    async def _verify_service_availability(self) -> bool:
        """验证服务可用性"""
        return await self._check_application_health()
    
    async def _verify_api_functionality(self) -> bool:
        """验证API功能"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试基础搜索API
                test_query = {"query": "测试查询", "top_k": 5}
                async with session.post(
                    f"{self.config.health_check_url.replace('/health', '/search')}",
                    json=test_query,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"API功能验证异常: {e}")
            return False
    
    async def _verify_database_connectivity(self) -> bool:
        """验证数据库连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.config.health_check_url.replace('/health', '/advanced/database/health')}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("connection_status") == "healthy"
                    return False
                    
        except Exception as e:
            logger.error(f"数据库连接验证异常: {e}")
            return False
    
    async def _verify_monitoring_metrics(self) -> bool:
        """验证监控指标"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.config.health_check_url.replace('/health', '/advanced/monitoring/metrics')}",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"监控指标验证异常: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取部署状态"""
        return {
            "phase": self.status.phase,
            "progress": self.status.progress,
            "message": self.status.message,
            "start_time": self.status.start_time.isoformat(),
            "current_time": self.status.current_time.isoformat(),
            "duration_minutes": (self.status.current_time - self.status.start_time).total_seconds() / 60,
            "errors": self.status.errors,
            "config": {
                "environment": self.config.environment,
                "version": self.config.version,
                "image_tag": self.config.image_tag,
                "replicas": self.config.replicas,
                "canary_percentage": self.config.canary_percentage
            }
        }


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生产环境部署脚本")
    parser.add_argument("--environment", choices=["staging", "production"], 
                       default="staging", help="部署环境")
    parser.add_argument("--version", required=True, help="版本号")
    parser.add_argument("--image-tag", required=True, help="镜像标签")
    parser.add_argument("--replicas", type=int, default=3, help="副本数量")
    parser.add_argument("--canary-percentage", type=int, default=10, help="灰度发布百分比")
    parser.add_argument("--health-url", default="http://retrieval-service:8000/health", 
                       help="健康检查URL")
    parser.add_argument("--no-rollback", action="store_true", help="失败时不自动回滚")
    parser.add_argument("--monitoring-duration", type=int, default=10, 
                       help="监控持续时间（分钟）")
    
    args = parser.parse_args()
    
    config = DeploymentConfig(
        environment=args.environment,
        version=args.version,
        image_tag=args.image_tag,
        replicas=args.replicas,
        canary_percentage=args.canary_percentage,
        health_check_url=args.health_url,
        rollback_on_failure=not args.no_rollback,
        monitoring_duration_minutes=args.monitoring_duration
    )
    
    deployer = ProductionDeployer(config)
    
    # 执行部署
    success = await deployer.deploy()
    
    # 输出最终状态
    final_status = deployer.get_status()
    print(json.dumps(final_status, indent=2, ensure_ascii=False))
    
    if success:
        logger.info("部署成功完成")
        exit(0)
    else:
        logger.error("部署失败")
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
