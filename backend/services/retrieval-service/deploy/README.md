# RAG检索系统生产环境部署指南

本文档详细说明了如何将RAG检索系统部署到生产环境，包括灰度发布、监控配置和运维管理。

## 目录

1. [部署前准备](#部署前准备)
2. [环境配置](#环境配置)
3. [部署流程](#部署流程)
4. [监控和告警](#监控和告警)
5. [运维管理](#运维管理)
6. [故障排除](#故障排除)

## 部署前准备

### 1. 基础设施要求

- **Kubernetes集群**: v1.20+
- **节点配置**: 
  - 计算节点: 4核8GB内存，最少3个节点
  - 存储: SSD存储，至少100GB可用空间
- **网络**: 支持LoadBalancer和Ingress
- **证书**: SSL/TLS证书用于HTTPS访问

### 2. 依赖服务

- **PostgreSQL**: v13+ (主数据库)
- **Redis**: v6+ (缓存和会话存储)
- **Elasticsearch**: v7.10+ (向量搜索)
- **Prometheus**: v2.30+ (监控指标)
- **Grafana**: v8.0+ (监控面板)

### 3. 工具准备

```bash
# 安装必要工具
kubectl version --client
docker version
helm version

# 验证集群连接
kubectl cluster-info
kubectl get nodes
```

## 环境配置

### 1. 创建命名空间

```bash
kubectl create namespace retrieval-service
kubectl label namespace retrieval-service name=retrieval-service
```

### 2. 配置Secret

```bash
# 复制Secret模板
cp deploy/k8s/secret.yaml.template deploy/k8s/secret.yaml

# 编辑Secret文件，填入实际的密钥值
vim deploy/k8s/secret.yaml

# 或者使用命令行创建
kubectl create secret generic retrieval-service-secrets \
  --from-literal=database-url="****************************************/retrieval_db" \
  --from-literal=redis-url="redis://redis:6379/0" \
  --from-literal=elasticsearch-url="http://elasticsearch:9200" \
  --from-literal=openai-api-key="sk-your-api-key" \
  --namespace=retrieval-service
```

### 3. 应用配置

```bash
# 应用ConfigMap
kubectl apply -f deploy/k8s/configmap.yaml -n retrieval-service

# 应用Secret
kubectl apply -f deploy/k8s/secret.yaml -n retrieval-service

# 验证配置
kubectl get configmap -n retrieval-service
kubectl get secret -n retrieval-service
```

## 部署流程

### 1. 自动化部署

使用提供的部署脚本进行自动化部署：

```bash
# 灰度部署到staging环境
python deploy/production_deploy.py \
  --environment staging \
  --version v1.0.0 \
  --image-tag latest \
  --replicas 2 \
  --canary-percentage 20 \
  --monitoring-duration 10

# 生产环境部署
python deploy/production_deploy.py \
  --environment production \
  --version v1.0.0 \
  --image-tag v1.0.0 \
  --replicas 5 \
  --canary-percentage 10 \
  --monitoring-duration 30
```

### 2. 手动部署步骤

如果需要手动控制部署过程：

#### 步骤1: 构建和推送镜像

```bash
# 构建Docker镜像
docker build -t retrieval-service:v1.0.0 .

# 标记镜像
docker tag retrieval-service:v1.0.0 your-registry/retrieval-service:v1.0.0

# 推送镜像
docker push your-registry/retrieval-service:v1.0.0
```

#### 步骤2: 数据库迁移

```bash
# 运行数据库迁移
kubectl run migration-job \
  --image=your-registry/retrieval-service:v1.0.0 \
  --restart=Never \
  --command -- python -m app.database_manager run_migrations
```

#### 步骤3: 灰度部署

```bash
# 创建灰度版本
kubectl apply -f deploy/k8s/canary-deployment.yaml -n retrieval-service

# 验证灰度部署
kubectl get pods -l version=canary -n retrieval-service
kubectl logs -l version=canary -n retrieval-service
```

#### 步骤4: 流量切换

```bash
# 更新Service选择器，逐步切换流量
kubectl patch service retrieval-service \
  -p '{"spec":{"selector":{"version":"canary"}}}' \
  -n retrieval-service
```

#### 步骤5: 全量部署

```bash
# 应用主部署
kubectl apply -f deploy/k8s/deployment.yaml -n retrieval-service

# 等待滚动更新完成
kubectl rollout status deployment/retrieval-service -n retrieval-service

# 清理灰度部署
kubectl delete -f deploy/k8s/canary-deployment.yaml -n retrieval-service
```

### 3. 验证部署

```bash
# 检查Pod状态
kubectl get pods -n retrieval-service

# 检查服务状态
kubectl get svc -n retrieval-service

# 检查Ingress状态
kubectl get ingress -n retrieval-service

# 健康检查
curl -f http://your-domain/health

# API功能测试
curl -X POST http://your-domain/search \
  -H "Content-Type: application/json" \
  -d '{"query": "测试查询", "top_k": 5}'
```

## 监控和告警

### 1. Prometheus监控

```bash
# 部署Prometheus
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace

# 配置ServiceMonitor
kubectl apply -f deploy/k8s/servicemonitor.yaml -n retrieval-service
```

### 2. Grafana面板

导入预配置的Grafana面板：

- 系统性能面板: `grafana/system-dashboard.json`
- 应用指标面板: `grafana/application-dashboard.json`
- 业务指标面板: `grafana/business-dashboard.json`

### 3. 告警规则

关键告警指标：

- **响应时间**: P95 > 2秒
- **错误率**: > 5%
- **QPS**: < 10 requests/second
- **内存使用**: > 90%
- **Pod重启**: 频繁重启

## 运维管理

### 1. 日常运维命令

```bash
# 查看Pod日志
kubectl logs -f deployment/retrieval-service -n retrieval-service

# 进入Pod调试
kubectl exec -it deployment/retrieval-service -n retrieval-service -- /bin/bash

# 扩缩容
kubectl scale deployment retrieval-service --replicas=10 -n retrieval-service

# 重启服务
kubectl rollout restart deployment/retrieval-service -n retrieval-service
```

### 2. 性能调优

```bash
# 查看资源使用情况
kubectl top pods -n retrieval-service
kubectl top nodes

# 调整资源限制
kubectl patch deployment retrieval-service \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"retrieval-service","resources":{"limits":{"memory":"4Gi","cpu":"2000m"}}}]}}}}' \
  -n retrieval-service
```

### 3. 备份和恢复

```bash
# 数据库备份
kubectl run backup-job \
  --image=postgres:13 \
  --restart=Never \
  --command -- pg_dump -h postgres -U user -d retrieval_db > backup.sql

# 配置备份
kubectl get configmap retrieval-service-config -o yaml > config-backup.yaml
kubectl get secret retrieval-service-secrets -o yaml > secret-backup.yaml
```

## 故障排除

### 1. 常见问题

#### Pod启动失败

```bash
# 查看Pod事件
kubectl describe pod <pod-name> -n retrieval-service

# 查看容器日志
kubectl logs <pod-name> -c retrieval-service -n retrieval-service

# 常见原因：
# - 镜像拉取失败
# - 配置错误
# - 资源不足
# - 健康检查失败
```

#### 服务不可访问

```bash
# 检查Service
kubectl get svc retrieval-service -n retrieval-service

# 检查Endpoints
kubectl get endpoints retrieval-service -n retrieval-service

# 检查Ingress
kubectl describe ingress retrieval-service-ingress -n retrieval-service

# 测试内部连接
kubectl run test-pod --image=curlimages/curl --rm -it -- \
  curl http://retrieval-service:8000/health
```

#### 性能问题

```bash
# 查看资源使用
kubectl top pods -n retrieval-service

# 查看HPA状态
kubectl get hpa -n retrieval-service

# 查看监控指标
curl http://retrieval-service:9090/metrics
```

### 2. 回滚操作

```bash
# 查看部署历史
kubectl rollout history deployment/retrieval-service -n retrieval-service

# 回滚到上一版本
kubectl rollout undo deployment/retrieval-service -n retrieval-service

# 回滚到指定版本
kubectl rollout undo deployment/retrieval-service --to-revision=2 -n retrieval-service
```

### 3. 紧急处理

```bash
# 紧急扩容
kubectl scale deployment retrieval-service --replicas=20 -n retrieval-service

# 紧急下线
kubectl scale deployment retrieval-service --replicas=0 -n retrieval-service

# 流量切换到备用服务
kubectl patch service retrieval-service \
  -p '{"spec":{"selector":{"app":"backup-service"}}}' \
  -n retrieval-service
```

## 安全最佳实践

1. **网络安全**
   - 使用NetworkPolicy限制Pod间通信
   - 配置Ingress SSL终止
   - 启用Pod安全策略

2. **访问控制**
   - 使用RBAC控制API访问
   - 定期轮换Secret
   - 启用审计日志

3. **镜像安全**
   - 使用非root用户运行容器
   - 定期扫描镜像漏洞
   - 使用只读根文件系统

## 联系信息

如有问题，请联系：
- 开发团队: <EMAIL>
- 运维团队: <EMAIL>
- 紧急联系: <EMAIL>
