apiVersion: apps/v1
kind: Deployment
metadata:
  name: retrieval-service
  namespace: default
  labels:
    app: retrieval-service
    version: stable
    component: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: retrieval-service
      version: stable
  template:
    metadata:
      labels:
        app: retrieval-service
        version: stable
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: retrieval-service
      containers:
      - name: retrieval-service
        image: retrieval-service:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: retrieval-service-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: retrieval-service-secrets
              key: redis-url
        - name: ELASTICSEARCH_URL
          valueFrom:
            secretKeyRef:
              name: retrieval-service-secrets
              key: elasticsearch-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: retrieval-service-secrets
              key: openai-api-key
        - name: PROMETHEUS_MULTIPROC_DIR
          value: "/tmp/prometheus_multiproc"
        envFrom:
        - configMapRef:
            name: retrieval-service-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            ephemeral-storage: "2Gi"
          limits:
            memory: "2Gi"
            cpu: "1000m"
            ephemeral-storage: "4Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 30
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi
      - name: data-volume
        persistentVolumeClaim:
          claimName: retrieval-service-data
      - name: logs-volume
        emptyDir:
          sizeLimit: 500Mi
      nodeSelector:
        node-type: compute
      tolerations:
      - key: "compute-node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - retrieval-service
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      restartPolicy: Always

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: retrieval-service
  namespace: default
  labels:
    app: retrieval-service

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: retrieval-service
  namespace: default
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: retrieval-service
  namespace: default
subjects:
- kind: ServiceAccount
  name: retrieval-service
  namespace: default
roleRef:
  kind: Role
  name: retrieval-service
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: retrieval-service-data
  namespace: default
  labels:
    app: retrieval-service
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: retrieval-service-pdb
  namespace: default
  labels:
    app: retrieval-service
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: retrieval-service
      version: stable
