"""
RAG系统检索服务主入口
负责语义检索、混合检索和结果排序
"""

import os
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from loguru import logger
import uvicorn

from app.config import settings
from app.database import init_db, close_db
from app.redis_client import init_redis, close_redis
from app.vector_store import init_vector_store, close_vector_store
from app.search_engine import init_search_engine, close_search_engine
from app.routers import retrieval, health
from app.middleware import LoggingMiddleware, ErrorHandlerMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("🚀 检索服务启动中...")
    
    try:
        # 初始化数据库连接
        await init_db()
        logger.info("✅ 数据库连接成功")
        
        # 初始化Redis连接
        await init_redis()
        logger.info("✅ Redis连接成功")
        
        # 初始化向量数据库
        await init_vector_store()
        logger.info("✅ 向量数据库连接成功")
        
        # 初始化搜索引擎
        await init_search_engine()
        logger.info("✅ 搜索引擎初始化成功")
        
        logger.info("🎉 检索服务启动完成")
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    
    yield
    
    # 关闭时清理
    logger.info("🔄 检索服务关闭中...")
    
    try:
        await close_search_engine()
        await close_vector_store()
        await close_redis()
        await close_db()
        logger.info("✅ 服务关闭完成")
    except Exception as e:
        logger.error(f"❌ 服务关闭异常: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="RAG检索服务",
    description="负责语义检索、混合检索和结果排序的微服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(LoggingMiddleware)
app.add_middleware(ErrorHandlerMiddleware)

# 注册路由
app.include_router(health.router, prefix="/health", tags=["健康检查"])
app.include_router(retrieval.router, prefix="/api/v1", tags=["检索"])


@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "RAG检索服务",
        "version": "1.0.0",
        "status": "运行中",
        "docs": "/docs"
    }


if __name__ == "__main__":
    # 配置日志
    logger.add(
        "logs/retrieval_service.log",
        rotation="1 day",
        retention="30 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8"
    )
    
    # 启动服务
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )
