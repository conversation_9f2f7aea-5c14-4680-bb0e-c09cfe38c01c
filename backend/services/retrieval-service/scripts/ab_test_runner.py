#!/usr/bin/env python3
"""
A/B测试执行和数据分析脚本
自动化执行A/B测试并生成分析报告
"""

import asyncio
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass

import aiohttp
import pandas as pd
import numpy as np
from scipy import stats
from loguru import logger


@dataclass
class ABTestConfig:
    """A/B测试配置"""
    experiment_name: str
    description: str
    control_group: str
    treatment_group: str
    traffic_split: float
    test_duration_days: int
    success_metrics: List[str]
    test_queries: List[str]


@dataclass
class ABTestResult:
    """A/B测试结果"""
    experiment_name: str
    control_metrics: Dict[str, float]
    treatment_metrics: Dict[str, float]
    statistical_significance: Dict[str, Dict[str, Any]]
    confidence_level: float
    recommendation: str
    effect_size: Dict[str, float]


class ABTestRunner:
    """A/B测试执行器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def create_experiment(self, config: ABTestConfig) -> bool:
        """创建A/B测试实验"""
        try:
            experiment_data = {
                "experiment_name": config.experiment_name,
                "description": config.description,
                "control_group": config.control_group,
                "treatment_group": config.treatment_group,
                "traffic_split": config.traffic_split,
                "start_date": datetime.now().isoformat(),
                "end_date": (datetime.now() + timedelta(days=config.test_duration_days)).isoformat(),
                "status": "active"
            }
            
            async with self.session.post(
                f"{self.base_url}/ab-testing/experiments",
                json=experiment_data
            ) as response:
                if response.status == 200:
                    logger.info(f"A/B测试实验创建成功: {config.experiment_name}")
                    return True
                else:
                    logger.error(f"创建实验失败: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"创建A/B测试实验失败: {e}")
            return False
    
    async def run_test_queries(self, config: ABTestConfig, user_count: int = 100) -> List[Dict[str, Any]]:
        """运行测试查询"""
        logger.info(f"开始执行A/B测试查询，用户数: {user_count}")
        
        results = []
        
        for user_id in range(user_count):
            # 为用户分配组别
            group_assignment = await self._assign_user_group(
                config.experiment_name, f"user_{user_id}"
            )
            
            if not group_assignment:
                continue
            
            group_name = group_assignment["group_name"]
            
            # 根据组别执行不同的搜索
            for query in config.test_queries:
                result = await self._execute_search_for_group(
                    query, group_name, f"user_{user_id}", config
                )
                
                if result:
                    result["user_id"] = f"user_{user_id}"
                    result["group"] = group_name
                    result["query"] = query
                    result["experiment"] = config.experiment_name
                    results.append(result)
        
        logger.info(f"A/B测试查询执行完成，收集到 {len(results)} 个结果")
        return results
    
    async def _assign_user_group(self, experiment_name: str, user_id: str) -> Optional[Dict[str, Any]]:
        """为用户分配组别"""
        try:
            async with self.session.post(
                f"{self.base_url}/ab-testing/assign",
                json={
                    "experiment_name": experiment_name,
                    "user_id": user_id
                }
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"用户分组失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"用户分组失败: {e}")
            return None
    
    async def _execute_search_for_group(self, query: str, group: str, user_id: str, 
                                      config: ABTestConfig) -> Optional[Dict[str, Any]]:
        """为特定组执行搜索"""
        try:
            start_time = datetime.now()
            
            # 根据组别选择不同的搜索端点
            if group == config.control_group:
                # 控制组：使用基础搜索
                search_data = {"query": query, "top_k": 10}
                endpoint = "/search"
            else:
                # 实验组：使用高级搜索
                search_data = {
                    "query": query,
                    "search_methods": ["self_rag", "multi_vector", "semantic"],
                    "top_k": 10,
                    "user_id": user_id,
                    "enable_chinese_optimization": True,
                    "enable_temporal_scoring": True
                }
                endpoint = "/advanced/search/advanced"
            
            async with self.session.post(
                f"{self.base_url}{endpoint}",
                json=search_data
            ) as response:
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds() * 1000
                
                if response.status == 200:
                    response_data = await response.json()
                    
                    # 模拟用户行为指标
                    result_count = len(response_data.get("results", []))
                    
                    # 模拟点击率（基于结果质量）
                    avg_score = np.mean([r.get("score", 0) for r in response_data.get("results", [])])
                    click_through_rate = min(0.8, avg_score * 0.6 + np.random.normal(0, 0.1))
                    click_through_rate = max(0.1, click_through_rate)
                    
                    # 模拟用户满意度（1-5分）
                    satisfaction = min(5, max(1, avg_score * 3 + np.random.normal(0, 0.5)))
                    
                    return {
                        "response_time_ms": response_time,
                        "result_count": result_count,
                        "avg_score": avg_score,
                        "click_through_rate": click_through_rate,
                        "user_satisfaction": satisfaction,
                        "success": True,
                        "timestamp": start_time.isoformat()
                    }
                else:
                    logger.warning(f"搜索请求失败: {response.status}")
                    return {
                        "response_time_ms": response_time,
                        "success": False,
                        "timestamp": start_time.isoformat()
                    }
                    
        except Exception as e:
            logger.error(f"执行搜索失败: {e}")
            return None
    
    async def analyze_results(self, results: List[Dict[str, Any]], 
                            config: ABTestConfig) -> ABTestResult:
        """分析A/B测试结果"""
        logger.info("开始分析A/B测试结果")
        
        # 分组数据
        control_data = [r for r in results if r["group"] == config.control_group]
        treatment_data = [r for r in results if r["group"] == config.treatment_group]
        
        if not control_data or not treatment_data:
            raise ValueError("控制组或实验组数据为空")
        
        # 计算各组指标
        control_metrics = self._calculate_group_metrics(control_data)
        treatment_metrics = self._calculate_group_metrics(treatment_data)
        
        # 统计显著性检验
        significance_tests = self._perform_significance_tests(
            control_data, treatment_data
        )
        
        # 计算效应大小
        effect_sizes = self._calculate_effect_sizes(
            control_metrics, treatment_metrics
        )
        
        # 生成建议
        recommendation = self._generate_recommendation(
            control_metrics, treatment_metrics, significance_tests
        )
        
        return ABTestResult(
            experiment_name=config.experiment_name,
            control_metrics=control_metrics,
            treatment_metrics=treatment_metrics,
            statistical_significance=significance_tests,
            confidence_level=0.95,
            recommendation=recommendation,
            effect_size=effect_sizes
        )
    
    def _calculate_group_metrics(self, group_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算组别指标"""
        if not group_data:
            return {}
        
        # 成功的请求
        successful_requests = [r for r in group_data if r.get("success", False)]
        
        if not successful_requests:
            return {"success_rate": 0.0}
        
        metrics = {
            "sample_size": len(group_data),
            "success_rate": len(successful_requests) / len(group_data),
            "avg_response_time": statistics.mean([r["response_time_ms"] for r in successful_requests]),
            "p95_response_time": statistics.quantiles([r["response_time_ms"] for r in successful_requests], n=20)[18],
            "avg_result_count": statistics.mean([r["result_count"] for r in successful_requests]),
            "avg_score": statistics.mean([r["avg_score"] for r in successful_requests]),
            "avg_click_through_rate": statistics.mean([r["click_through_rate"] for r in successful_requests]),
            "avg_user_satisfaction": statistics.mean([r["user_satisfaction"] for r in successful_requests])
        }
        
        return metrics
    
    def _perform_significance_tests(self, control_data: List[Dict[str, Any]], 
                                  treatment_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """执行统计显著性检验"""
        tests = {}
        
        # 过滤成功的请求
        control_success = [r for r in control_data if r.get("success", False)]
        treatment_success = [r for r in treatment_data if r.get("success", False)]
        
        if not control_success or not treatment_success:
            return tests
        
        # 响应时间t检验
        control_response_times = [r["response_time_ms"] for r in control_success]
        treatment_response_times = [r["response_time_ms"] for r in treatment_success]
        
        t_stat, p_value = stats.ttest_ind(control_response_times, treatment_response_times)
        tests["response_time"] = {
            "test": "t-test",
            "statistic": t_stat,
            "p_value": p_value,
            "significant": p_value < 0.05
        }
        
        # 用户满意度t检验
        control_satisfaction = [r["user_satisfaction"] for r in control_success]
        treatment_satisfaction = [r["user_satisfaction"] for r in treatment_success]
        
        t_stat, p_value = stats.ttest_ind(control_satisfaction, treatment_satisfaction)
        tests["user_satisfaction"] = {
            "test": "t-test",
            "statistic": t_stat,
            "p_value": p_value,
            "significant": p_value < 0.05
        }
        
        # 点击率t检验
        control_ctr = [r["click_through_rate"] for r in control_success]
        treatment_ctr = [r["click_through_rate"] for r in treatment_success]
        
        t_stat, p_value = stats.ttest_ind(control_ctr, treatment_ctr)
        tests["click_through_rate"] = {
            "test": "t-test",
            "statistic": t_stat,
            "p_value": p_value,
            "significant": p_value < 0.05
        }
        
        return tests
    
    def _calculate_effect_sizes(self, control_metrics: Dict[str, float], 
                              treatment_metrics: Dict[str, float]) -> Dict[str, float]:
        """计算效应大小"""
        effect_sizes = {}
        
        for metric in ["avg_response_time", "avg_user_satisfaction", "avg_click_through_rate"]:
            if metric in control_metrics and metric in treatment_metrics:
                control_value = control_metrics[metric]
                treatment_value = treatment_metrics[metric]
                
                if control_value != 0:
                    # 计算相对变化百分比
                    effect_sizes[metric] = ((treatment_value - control_value) / control_value) * 100
                else:
                    effect_sizes[metric] = 0
        
        return effect_sizes
    
    def _generate_recommendation(self, control_metrics: Dict[str, float], 
                               treatment_metrics: Dict[str, float], 
                               significance_tests: Dict[str, Dict[str, Any]]) -> str:
        """生成测试建议"""
        recommendations = []
        
        # 检查用户满意度
        if "user_satisfaction" in significance_tests:
            test = significance_tests["user_satisfaction"]
            if test["significant"]:
                control_satisfaction = control_metrics.get("avg_user_satisfaction", 0)
                treatment_satisfaction = treatment_metrics.get("avg_user_satisfaction", 0)
                
                if treatment_satisfaction > control_satisfaction:
                    recommendations.append("实验组用户满意度显著提升，建议采用新技术")
                else:
                    recommendations.append("实验组用户满意度显著下降，建议保持现有技术")
        
        # 检查响应时间
        if "response_time" in significance_tests:
            test = significance_tests["response_time"]
            if test["significant"]:
                control_time = control_metrics.get("avg_response_time", 0)
                treatment_time = treatment_metrics.get("avg_response_time", 0)
                
                if treatment_time < control_time:
                    recommendations.append("实验组响应时间显著改善")
                else:
                    recommendations.append("实验组响应时间显著增加，需要优化性能")
        
        # 检查点击率
        if "click_through_rate" in significance_tests:
            test = significance_tests["click_through_rate"]
            if test["significant"]:
                control_ctr = control_metrics.get("avg_click_through_rate", 0)
                treatment_ctr = treatment_metrics.get("avg_click_through_rate", 0)
                
                if treatment_ctr > control_ctr:
                    recommendations.append("实验组点击率显著提升")
                else:
                    recommendations.append("实验组点击率显著下降")
        
        if not recommendations:
            recommendations.append("各项指标无显著差异，建议延长测试时间或增加样本量")
        
        return "; ".join(recommendations)
    
    async def generate_report(self, result: ABTestResult, output_path: str = None):
        """生成A/B测试报告"""
        report = {
            "experiment_name": result.experiment_name,
            "analysis_timestamp": datetime.now().isoformat(),
            "summary": {
                "control_group_metrics": result.control_metrics,
                "treatment_group_metrics": result.treatment_metrics,
                "effect_sizes": result.effect_size
            },
            "statistical_analysis": result.statistical_significance,
            "recommendation": result.recommendation,
            "confidence_level": result.confidence_level
        }
        
        # 保存报告
        if output_path:
            output_file = Path(output_path)
        else:
            reports_dir = Path("data/ab_test_reports")
            reports_dir.mkdir(exist_ok=True)
            output_file = reports_dir / f"ab_test_report_{result.experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"A/B测试报告已保存到: {output_file}")
        
        # 打印摘要
        print("\n" + "="*60)
        print(f"A/B测试报告: {result.experiment_name}")
        print("="*60)
        print(f"建议: {result.recommendation}")
        print("\n控制组指标:")
        for metric, value in result.control_metrics.items():
            print(f"  {metric}: {value:.4f}")
        print("\n实验组指标:")
        for metric, value in result.treatment_metrics.items():
            print(f"  {metric}: {value:.4f}")
        print("\n效应大小:")
        for metric, effect in result.effect_size.items():
            print(f"  {metric}: {effect:+.2f}%")
        print("="*60)


# 预定义的A/B测试配置
PREDEFINED_TESTS = {
    "self_rag_vs_basic": ABTestConfig(
        experiment_name="self_rag_vs_basic_search",
        description="Self-RAG检索 vs 基础检索对比测试",
        control_group="basic_search",
        treatment_group="self_rag_search",
        traffic_split=0.5,
        test_duration_days=7,
        success_metrics=["response_time", "user_satisfaction", "click_through_rate"],
        test_queries=[
            "人工智能的发展历史",
            "机器学习算法比较",
            "深度学习在自然语言处理中的应用",
            "计算机视觉技术原理",
            "大数据分析方法"
        ]
    ),
    "multi_vector_vs_semantic": ABTestConfig(
        experiment_name="multi_vector_vs_semantic_search",
        description="多向量检索 vs 语义检索对比测试",
        control_group="semantic_search",
        treatment_group="multi_vector_search",
        traffic_split=0.5,
        test_duration_days=7,
        success_metrics=["response_time", "user_satisfaction", "click_through_rate"],
        test_queries=[
            "如何实现推荐系统",
            "什么是区块链技术",
            "云计算的优势和挑战",
            "物联网应用场景",
            "网络安全防护措施"
        ]
    ),
    "chinese_optimization_test": ABTestConfig(
        experiment_name="chinese_optimization_test",
        description="中文优化功能效果测试",
        control_group="no_chinese_optimization",
        treatment_group="with_chinese_optimization",
        traffic_split=0.5,
        test_duration_days=5,
        success_metrics=["response_time", "user_satisfaction", "click_through_rate"],
        test_queries=[
            "自然语言处理技术发展趋势",
            "人工智能在医疗领域的应用前景",
            "机器学习模型的评估指标有哪些",
            "深度学习框架对比分析",
            "数据挖掘算法的分类和特点"
        ]
    )
}


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="A/B测试执行脚本")
    parser.add_argument("--test", choices=list(PREDEFINED_TESTS.keys()), 
                       default="self_rag_vs_basic", help="选择测试配置")
    parser.add_argument("--url", default="http://localhost:8000", help="服务URL")
    parser.add_argument("--users", type=int, default=100, help="测试用户数量")
    parser.add_argument("--output", help="报告输出路径")
    
    args = parser.parse_args()
    
    config = PREDEFINED_TESTS[args.test]
    
    async with ABTestRunner(base_url=args.url) as runner:
        # 创建实验
        success = await runner.create_experiment(config)
        if not success:
            logger.error("创建A/B测试实验失败")
            return
        
        # 执行测试
        results = await runner.run_test_queries(config, user_count=args.users)
        
        if not results:
            logger.error("没有收集到测试结果")
            return
        
        # 分析结果
        analysis_result = await runner.analyze_results(results, config)
        
        # 生成报告
        await runner.generate_report(analysis_result, args.output)


if __name__ == "__main__":
    asyncio.run(main())
