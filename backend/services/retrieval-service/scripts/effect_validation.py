#!/usr/bin/env python3
"""
效果验证脚本
验证RAG检索系统各项技术的整体效果，包括召回率、准确率等关键指标
"""

import asyncio
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
from pathlib import Path
from dataclasses import dataclass

import aiohttp
import pandas as pd
import numpy as np
from loguru import logger


@dataclass
class ValidationMetrics:
    """验证指标"""
    recall_improvement: float  # 召回率提升
    precision_improvement: float  # 准确率提升
    response_time_p95: float  # P95响应时间
    qps: float  # 每秒查询数
    user_satisfaction: float  # 用户满意度
    click_through_rate: float  # 点击率
    coverage_rate: float  # 覆盖率


@dataclass
class TechnologyEffect:
    """技术效果"""
    technology_name: str
    baseline_metrics: Dict[str, float]
    enhanced_metrics: Dict[str, float]
    improvement_percentage: Dict[str, float]
    statistical_significance: Dict[str, bool]


class EffectValidator:
    """效果验证器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
        # 验证目标
        self.target_metrics = {
            "recall_improvement": 40.0,  # 召回率提升40%
            "precision_improvement": 32.5,  # 准确率提升32.5%
            "response_time_p95": 2000,  # P95响应时间<2秒
            "qps": 100,  # QPS>100
            "user_satisfaction": 4.0,  # 用户满意度>4.0
            "click_through_rate": 0.3,  # 点击率>30%
            "coverage_rate": 0.85  # 覆盖率>85%
        }
        
        # 测试查询集
        self.test_queries = [
            "人工智能的发展历史和未来趋势",
            "机器学习算法的分类和应用场景",
            "深度学习在自然语言处理中的应用",
            "计算机视觉技术的原理和实现",
            "大数据分析的方法和工具",
            "云计算架构设计和最佳实践",
            "区块链技术的核心概念和应用",
            "物联网系统的构建和部署",
            "网络安全防护策略和技术",
            "软件工程的开发流程和管理"
        ]
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def validate_overall_effect(self) -> ValidationMetrics:
        """验证整体效果"""
        logger.info("开始验证RAG检索系统整体效果...")
        
        # 1. 基线测试
        baseline_results = await self._run_baseline_tests()
        
        # 2. 增强技术测试
        enhanced_results = await self._run_enhanced_tests()
        
        # 3. 性能测试
        performance_metrics = await self._run_performance_tests()
        
        # 4. 用户体验测试
        user_experience_metrics = await self._run_user_experience_tests()
        
        # 5. 计算改进指标
        validation_metrics = self._calculate_validation_metrics(
            baseline_results, enhanced_results, performance_metrics, user_experience_metrics
        )
        
        # 6. 生成验证报告
        await self._generate_validation_report(validation_metrics)
        
        logger.info("效果验证完成")
        return validation_metrics
    
    async def _run_baseline_tests(self) -> Dict[str, Any]:
        """运行基线测试"""
        logger.info("执行基线测试...")
        
        baseline_results = []
        
        for query in self.test_queries:
            # 使用基础搜索
            result = await self._execute_search(query, "baseline")
            if result:
                baseline_results.append(result)
        
        # 计算基线指标
        baseline_metrics = self._calculate_search_metrics(baseline_results)
        
        logger.info(f"基线测试完成，平均相关性: {baseline_metrics.get('avg_relevance', 0):.3f}")
        return baseline_metrics
    
    async def _run_enhanced_tests(self) -> Dict[str, Any]:
        """运行增强技术测试"""
        logger.info("执行增强技术测试...")
        
        enhanced_results = []
        
        for query in self.test_queries:
            # 使用高级搜索
            result = await self._execute_search(query, "advanced")
            if result:
                enhanced_results.append(result)
        
        # 计算增强指标
        enhanced_metrics = self._calculate_search_metrics(enhanced_results)
        
        logger.info(f"增强技术测试完成，平均相关性: {enhanced_metrics.get('avg_relevance', 0):.3f}")
        return enhanced_metrics
    
    async def _execute_search(self, query: str, search_type: str) -> Dict[str, Any]:
        """执行搜索"""
        try:
            start_time = datetime.now()
            
            if search_type == "baseline":
                # 基础搜索
                search_data = {"query": query, "top_k": 10}
                endpoint = "/search"
            else:
                # 高级搜索
                search_data = {
                    "query": query,
                    "search_methods": ["self_rag", "multi_vector", "semantic", "temporal"],
                    "fusion_method": "adaptive",
                    "top_k": 10,
                    "enable_chinese_optimization": True,
                    "enable_temporal_scoring": True
                }
                endpoint = "/advanced/search/advanced"
            
            async with self.session.post(
                f"{self.base_url}{endpoint}",
                json=search_data,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds() * 1000
                
                if response.status == 200:
                    response_data = await response.json()
                    
                    return {
                        "query": query,
                        "search_type": search_type,
                        "response_time_ms": response_time,
                        "results": response_data.get("results", []),
                        "total_results": len(response_data.get("results", [])),
                        "success": True,
                        "timestamp": start_time.isoformat()
                    }
                else:
                    logger.warning(f"搜索请求失败: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"搜索执行失败: {e}")
            return None
    
    def _calculate_search_metrics(self, search_results: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算搜索指标"""
        if not search_results:
            return {}
        
        # 响应时间指标
        response_times = [r["response_time_ms"] for r in search_results]
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0]
        
        # 结果数量指标
        result_counts = [r["total_results"] for r in search_results]
        avg_result_count = statistics.mean(result_counts)
        
        # 模拟相关性评分（实际应用中需要人工标注或更复杂的评估）
        relevance_scores = []
        for result in search_results:
            results = result.get("results", [])
            if results:
                # 基于结果分数计算相关性
                scores = [r.get("score", 0) for r in results]
                avg_score = statistics.mean(scores) if scores else 0
                relevance_scores.append(min(1.0, avg_score))
            else:
                relevance_scores.append(0.0)
        
        avg_relevance = statistics.mean(relevance_scores) if relevance_scores else 0
        
        # 覆盖率（有结果的查询比例）
        successful_queries = sum(1 for r in search_results if r["total_results"] > 0)
        coverage_rate = successful_queries / len(search_results)
        
        return {
            "avg_response_time": avg_response_time,
            "p95_response_time": p95_response_time,
            "avg_result_count": avg_result_count,
            "avg_relevance": avg_relevance,
            "coverage_rate": coverage_rate,
            "success_rate": sum(1 for r in search_results if r["success"]) / len(search_results)
        }
    
    async def _run_performance_tests(self) -> Dict[str, float]:
        """运行性能测试"""
        logger.info("执行性能测试...")
        
        # 并发测试
        concurrent_users = 20
        test_duration = 60  # 秒
        
        start_time = datetime.now()
        tasks = []
        
        # 创建并发任务
        for i in range(concurrent_users):
            task = asyncio.create_task(
                self._user_load_simulation(test_duration, i)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集性能数据
        all_response_times = []
        total_requests = 0
        successful_requests = 0
        
        for result in results:
            if isinstance(result, list):
                for req in result:
                    total_requests += 1
                    if req.get("success"):
                        successful_requests += 1
                        all_response_times.append(req["response_time_ms"])
        
        # 计算性能指标
        total_duration = (datetime.now() - start_time).total_seconds()
        qps = total_requests / total_duration if total_duration > 0 else 0
        
        if all_response_times:
            p95_response_time = statistics.quantiles(all_response_times, n=20)[18]
            avg_response_time = statistics.mean(all_response_times)
        else:
            p95_response_time = 0
            avg_response_time = 0
        
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        
        logger.info(f"性能测试完成: QPS={qps:.2f}, P95={p95_response_time:.2f}ms")
        
        return {
            "qps": qps,
            "p95_response_time": p95_response_time,
            "avg_response_time": avg_response_time,
            "success_rate": success_rate,
            "total_requests": total_requests,
            "concurrent_users": concurrent_users
        }
    
    async def _user_load_simulation(self, duration: int, user_id: int) -> List[Dict[str, Any]]:
        """模拟用户负载"""
        user_results = []
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < duration:
            # 随机选择查询
            import random
            query = random.choice(self.test_queries)
            
            # 执行搜索
            result = await self._execute_search(query, "advanced")
            if result:
                result["user_id"] = user_id
                user_results.append(result)
            
            # 模拟用户思考时间
            await asyncio.sleep(random.uniform(1, 3))
        
        return user_results
    
    async def _run_user_experience_tests(self) -> Dict[str, float]:
        """运行用户体验测试"""
        logger.info("执行用户体验测试...")
        
        # 模拟用户行为和满意度
        user_sessions = []
        
        for i in range(50):  # 模拟50个用户会话
            session_results = []
            
            # 每个会话执行3-5个查询
            import random
            query_count = random.randint(3, 5)
            
            for _ in range(query_count):
                query = random.choice(self.test_queries)
                result = await self._execute_search(query, "advanced")
                
                if result and result["results"]:
                    # 模拟用户满意度（基于结果质量）
                    avg_score = statistics.mean([r.get("score", 0) for r in result["results"]])
                    satisfaction = min(5.0, max(1.0, avg_score * 3 + random.gauss(0, 0.5)))
                    
                    # 模拟点击行为
                    click_probability = min(0.8, avg_score * 0.6 + 0.2)
                    clicked = random.random() < click_probability
                    
                    session_results.append({
                        "satisfaction": satisfaction,
                        "clicked": clicked,
                        "result_count": len(result["results"]),
                        "response_time": result["response_time_ms"]
                    })
            
            if session_results:
                user_sessions.append(session_results)
        
        # 计算用户体验指标
        all_satisfactions = []
        all_clicks = []
        all_sessions_with_clicks = 0
        
        for session in user_sessions:
            session_satisfaction = statistics.mean([r["satisfaction"] for r in session])
            all_satisfactions.append(session_satisfaction)
            
            session_clicks = sum(1 for r in session if r["clicked"])
            session_queries = len(session)
            
            if session_clicks > 0:
                all_sessions_with_clicks += 1
            
            if session_queries > 0:
                all_clicks.append(session_clicks / session_queries)
        
        avg_satisfaction = statistics.mean(all_satisfactions) if all_satisfactions else 0
        avg_click_rate = statistics.mean(all_clicks) if all_clicks else 0
        session_success_rate = all_sessions_with_clicks / len(user_sessions) if user_sessions else 0
        
        logger.info(f"用户体验测试完成: 满意度={avg_satisfaction:.2f}, 点击率={avg_click_rate:.2%}")
        
        return {
            "user_satisfaction": avg_satisfaction,
            "click_through_rate": avg_click_rate,
            "session_success_rate": session_success_rate,
            "total_sessions": len(user_sessions)
        }
    
    def _calculate_validation_metrics(self, baseline: Dict[str, Any], 
                                    enhanced: Dict[str, Any],
                                    performance: Dict[str, Any],
                                    user_experience: Dict[str, Any]) -> ValidationMetrics:
        """计算验证指标"""
        
        # 召回率改进（基于覆盖率）
        baseline_coverage = baseline.get("coverage_rate", 0)
        enhanced_coverage = enhanced.get("coverage_rate", 0)
        recall_improvement = ((enhanced_coverage - baseline_coverage) / baseline_coverage * 100) if baseline_coverage > 0 else 0
        
        # 准确率改进（基于相关性）
        baseline_relevance = baseline.get("avg_relevance", 0)
        enhanced_relevance = enhanced.get("avg_relevance", 0)
        precision_improvement = ((enhanced_relevance - baseline_relevance) / baseline_relevance * 100) if baseline_relevance > 0 else 0
        
        return ValidationMetrics(
            recall_improvement=recall_improvement,
            precision_improvement=precision_improvement,
            response_time_p95=performance.get("p95_response_time", 0),
            qps=performance.get("qps", 0),
            user_satisfaction=user_experience.get("user_satisfaction", 0),
            click_through_rate=user_experience.get("click_through_rate", 0),
            coverage_rate=enhanced.get("coverage_rate", 0)
        )
    
    async def _generate_validation_report(self, metrics: ValidationMetrics):
        """生成验证报告"""
        
        # 检查是否达到目标
        targets_met = {
            "recall_improvement": metrics.recall_improvement >= self.target_metrics["recall_improvement"],
            "precision_improvement": metrics.precision_improvement >= self.target_metrics["precision_improvement"],
            "response_time_p95": metrics.response_time_p95 <= self.target_metrics["response_time_p95"],
            "qps": metrics.qps >= self.target_metrics["qps"],
            "user_satisfaction": metrics.user_satisfaction >= self.target_metrics["user_satisfaction"],
            "click_through_rate": metrics.click_through_rate >= self.target_metrics["click_through_rate"],
            "coverage_rate": metrics.coverage_rate >= self.target_metrics["coverage_rate"]
        }
        
        overall_success = all(targets_met.values())
        success_rate = sum(targets_met.values()) / len(targets_met)
        
        report = {
            "validation_timestamp": datetime.now().isoformat(),
            "overall_success": overall_success,
            "success_rate": success_rate,
            "metrics": {
                "recall_improvement": {
                    "actual": metrics.recall_improvement,
                    "target": self.target_metrics["recall_improvement"],
                    "met": targets_met["recall_improvement"],
                    "unit": "%"
                },
                "precision_improvement": {
                    "actual": metrics.precision_improvement,
                    "target": self.target_metrics["precision_improvement"],
                    "met": targets_met["precision_improvement"],
                    "unit": "%"
                },
                "response_time_p95": {
                    "actual": metrics.response_time_p95,
                    "target": self.target_metrics["response_time_p95"],
                    "met": targets_met["response_time_p95"],
                    "unit": "ms"
                },
                "qps": {
                    "actual": metrics.qps,
                    "target": self.target_metrics["qps"],
                    "met": targets_met["qps"],
                    "unit": "requests/second"
                },
                "user_satisfaction": {
                    "actual": metrics.user_satisfaction,
                    "target": self.target_metrics["user_satisfaction"],
                    "met": targets_met["user_satisfaction"],
                    "unit": "score (1-5)"
                },
                "click_through_rate": {
                    "actual": metrics.click_through_rate,
                    "target": self.target_metrics["click_through_rate"],
                    "met": targets_met["click_through_rate"],
                    "unit": "rate (0-1)"
                },
                "coverage_rate": {
                    "actual": metrics.coverage_rate,
                    "target": self.target_metrics["coverage_rate"],
                    "met": targets_met["coverage_rate"],
                    "unit": "rate (0-1)"
                }
            },
            "summary": {
                "targets_met": sum(targets_met.values()),
                "total_targets": len(targets_met),
                "success_percentage": success_rate * 100
            }
        }
        
        # 保存报告
        reports_dir = Path("data/validation_reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / f"effect_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印报告摘要
        print("\n" + "="*80)
        print("RAG检索系统效果验证报告")
        print("="*80)
        print(f"整体成功: {'✅ 是' if overall_success else '❌ 否'}")
        print(f"成功率: {success_rate:.1%} ({sum(targets_met.values())}/{len(targets_met)})")
        print("\n关键指标:")
        print(f"  召回率提升: {metrics.recall_improvement:+.1f}% (目标: +{self.target_metrics['recall_improvement']:.1f}%) {'✅' if targets_met['recall_improvement'] else '❌'}")
        print(f"  准确率提升: {metrics.precision_improvement:+.1f}% (目标: +{self.target_metrics['precision_improvement']:.1f}%) {'✅' if targets_met['precision_improvement'] else '❌'}")
        print(f"  P95响应时间: {metrics.response_time_p95:.0f}ms (目标: <{self.target_metrics['response_time_p95']:.0f}ms) {'✅' if targets_met['response_time_p95'] else '❌'}")
        print(f"  QPS: {metrics.qps:.1f} (目标: >{self.target_metrics['qps']:.0f}) {'✅' if targets_met['qps'] else '❌'}")
        print(f"  用户满意度: {metrics.user_satisfaction:.2f} (目标: >{self.target_metrics['user_satisfaction']:.1f}) {'✅' if targets_met['user_satisfaction'] else '❌'}")
        print(f"  点击率: {metrics.click_through_rate:.1%} (目标: >{self.target_metrics['click_through_rate']:.0%}) {'✅' if targets_met['click_through_rate'] else '❌'}")
        print(f"  覆盖率: {metrics.coverage_rate:.1%} (目标: >{self.target_metrics['coverage_rate']:.0%}) {'✅' if targets_met['coverage_rate'] else '❌'}")
        print(f"\n报告已保存到: {report_file}")
        print("="*80)
        
        logger.info(f"验证报告已生成: {report_file}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG检索系统效果验证")
    parser.add_argument("--url", default="http://localhost:8000", help="服务URL")
    parser.add_argument("--output", help="报告输出路径")
    
    args = parser.parse_args()
    
    async with EffectValidator(base_url=args.url) as validator:
        metrics = await validator.validate_overall_effect()
        
        if args.output:
            # 保存详细指标到指定路径
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump({
                    "recall_improvement": metrics.recall_improvement,
                    "precision_improvement": metrics.precision_improvement,
                    "response_time_p95": metrics.response_time_p95,
                    "qps": metrics.qps,
                    "user_satisfaction": metrics.user_satisfaction,
                    "click_through_rate": metrics.click_through_rate,
                    "coverage_rate": metrics.coverage_rate
                }, f, indent=2)


if __name__ == "__main__":
    asyncio.run(main())
