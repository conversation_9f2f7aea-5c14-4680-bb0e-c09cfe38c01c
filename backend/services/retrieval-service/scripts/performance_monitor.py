#!/usr/bin/env python3
"""
性能监控脚本
持续监控系统性能指标并生成报告
"""

import asyncio
import time
import json
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any
from pathlib import Path

import aiohttp
from loguru import logger


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 monitoring_interval: int = 60):
        self.base_url = base_url
        self.monitoring_interval = monitoring_interval
        self.metrics_history = []
        self.alerts = []
        
        # 性能阈值
        self.thresholds = {
            "max_response_time_ms": 2000,
            "min_qps": 100,
            "max_memory_usage_mb": 2048,
            "max_cpu_usage_percent": 80,
            "min_success_rate": 0.99
        }
    
    async def start_monitoring(self, duration_hours: int = 24):
        """开始性能监控"""
        logger.info(f"开始性能监控，持续 {duration_hours} 小时")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)
        
        while datetime.now() < end_time:
            try:
                # 收集性能指标
                metrics = await self._collect_metrics()
                
                # 检查阈值
                alerts = self._check_thresholds(metrics)
                
                # 记录指标
                self.metrics_history.append(metrics)
                self.alerts.extend(alerts)
                
                # 生成报告
                if len(self.metrics_history) % 10 == 0:  # 每10次监控生成一次报告
                    await self._generate_report()
                
                # 等待下次监控
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                await asyncio.sleep(self.monitoring_interval)
        
        # 生成最终报告
        await self._generate_final_report()
        logger.info("性能监控完成")
    
    async def _collect_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": await self._collect_system_metrics(),
            "application": await self._collect_application_metrics(),
            "database": await self._collect_database_metrics()
        }
        
        return metrics
    
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用
            memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            
            # 网络IO
            network = psutil.net_io_counters()
            
            return {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_mb": memory.total / 1024 / 1024,
                    "used_mb": memory.used / 1024 / 1024,
                    "available_mb": memory.available / 1024 / 1024,
                    "percent": memory.percent
                },
                "disk": {
                    "total_gb": disk.total / 1024 / 1024 / 1024,
                    "used_gb": disk.used / 1024 / 1024 / 1024,
                    "free_gb": disk.free / 1024 / 1024 / 1024,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            }
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return {}
    
    async def _collect_application_metrics(self) -> Dict[str, Any]:
        """收集应用指标"""
        try:
            async with aiohttp.ClientSession() as session:
                # 健康检查
                health_response = await self._safe_request(
                    session, f"{self.base_url}/health"
                )
                
                # 监控指标
                metrics_response = await self._safe_request(
                    session, f"{self.base_url}/advanced/monitoring/metrics"
                )
                
                # 数据库健康状态
                db_health_response = await self._safe_request(
                    session, f"{self.base_url}/advanced/database/health"
                )
                
                # 性能测试
                test_query = {"query": "性能监控测试查询", "top_k": 5}
                start_time = time.time()
                search_response = await self._safe_request(
                    session, f"{self.base_url}/search", method="POST", json=test_query
                )
                response_time = (time.time() - start_time) * 1000
                
                return {
                    "health": health_response,
                    "metrics": metrics_response,
                    "database_health": db_health_response,
                    "test_response_time_ms": response_time,
                    "test_success": search_response is not None
                }
                
        except Exception as e:
            logger.error(f"收集应用指标失败: {e}")
            return {}
    
    async def _collect_database_metrics(self) -> Dict[str, Any]:
        """收集数据库指标"""
        try:
            async with aiohttp.ClientSession() as session:
                db_health = await self._safe_request(
                    session, f"{self.base_url}/advanced/database/health"
                )
                
                if db_health:
                    return {
                        "connection_status": db_health.get("connection_status", "unknown"),
                        "table_count": len(db_health.get("table_statistics", [])),
                        "index_count": db_health.get("index_statistics", {}).get("total_indexes", 0),
                        "performance": db_health.get("performance", {}),
                        "disk_usage": db_health.get("disk_usage", {})
                    }
                else:
                    return {"connection_status": "error"}
                    
        except Exception as e:
            logger.error(f"收集数据库指标失败: {e}")
            return {"connection_status": "error"}
    
    async def _safe_request(self, session: aiohttp.ClientSession, url: str, 
                          method: str = "GET", **kwargs) -> Dict[str, Any]:
        """安全的HTTP请求"""
        try:
            if method.upper() == "GET":
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        return await response.json()
            elif method.upper() == "POST":
                async with session.post(url, timeout=aiohttp.ClientTimeout(total=10), **kwargs) as response:
                    if response.status == 200:
                        return await response.json()
            
            return None
            
        except Exception as e:
            logger.warning(f"请求失败 {url}: {e}")
            return None
    
    def _check_thresholds(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查性能阈值"""
        alerts = []
        timestamp = metrics["timestamp"]
        
        # 检查响应时间
        response_time = metrics.get("application", {}).get("test_response_time_ms", 0)
        if response_time > self.thresholds["max_response_time_ms"]:
            alerts.append({
                "type": "response_time",
                "severity": "warning",
                "message": f"响应时间过高: {response_time:.2f}ms",
                "threshold": self.thresholds["max_response_time_ms"],
                "actual": response_time,
                "timestamp": timestamp
            })
        
        # 检查内存使用
        memory_used = metrics.get("system", {}).get("memory", {}).get("used_mb", 0)
        if memory_used > self.thresholds["max_memory_usage_mb"]:
            alerts.append({
                "type": "memory_usage",
                "severity": "critical",
                "message": f"内存使用过高: {memory_used:.2f}MB",
                "threshold": self.thresholds["max_memory_usage_mb"],
                "actual": memory_used,
                "timestamp": timestamp
            })
        
        # 检查CPU使用率
        cpu_percent = metrics.get("system", {}).get("cpu_percent", 0)
        if cpu_percent > self.thresholds["max_cpu_usage_percent"]:
            alerts.append({
                "type": "cpu_usage",
                "severity": "warning",
                "message": f"CPU使用率过高: {cpu_percent:.2f}%",
                "threshold": self.thresholds["max_cpu_usage_percent"],
                "actual": cpu_percent,
                "timestamp": timestamp
            })
        
        # 检查数据库连接
        db_status = metrics.get("database", {}).get("connection_status", "unknown")
        if db_status != "healthy":
            alerts.append({
                "type": "database_connection",
                "severity": "critical",
                "message": f"数据库连接异常: {db_status}",
                "timestamp": timestamp
            })
        
        return alerts
    
    async def _generate_report(self):
        """生成监控报告"""
        if not self.metrics_history:
            return
        
        # 计算统计信息
        recent_metrics = self.metrics_history[-10:]  # 最近10次监控
        
        response_times = [
            m.get("application", {}).get("test_response_time_ms", 0)
            for m in recent_metrics
        ]
        
        memory_usage = [
            m.get("system", {}).get("memory", {}).get("used_mb", 0)
            for m in recent_metrics
        ]
        
        cpu_usage = [
            m.get("system", {}).get("cpu_percent", 0)
            for m in recent_metrics
        ]
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "monitoring_period": f"最近 {len(recent_metrics)} 次监控",
            "statistics": {
                "response_time": {
                    "avg_ms": sum(response_times) / len(response_times) if response_times else 0,
                    "max_ms": max(response_times) if response_times else 0,
                    "min_ms": min(response_times) if response_times else 0
                },
                "memory_usage": {
                    "avg_mb": sum(memory_usage) / len(memory_usage) if memory_usage else 0,
                    "max_mb": max(memory_usage) if memory_usage else 0,
                    "min_mb": min(memory_usage) if memory_usage else 0
                },
                "cpu_usage": {
                    "avg_percent": sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
                    "max_percent": max(cpu_usage) if cpu_usage else 0,
                    "min_percent": min(cpu_usage) if cpu_usage else 0
                }
            },
            "alerts_count": len([a for a in self.alerts if a["timestamp"] in [m["timestamp"] for m in recent_metrics]]),
            "health_status": "healthy" if not any(a["severity"] == "critical" for a in self.alerts[-10:]) else "unhealthy"
        }
        
        logger.info(f"监控报告: {report}")
        
        # 保存报告到文件
        reports_dir = Path("data/monitoring_reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
    
    async def _generate_final_report(self):
        """生成最终监控报告"""
        if not self.metrics_history:
            logger.warning("没有监控数据，无法生成最终报告")
            return
        
        # 计算整体统计
        all_response_times = [
            m.get("application", {}).get("test_response_time_ms", 0)
            for m in self.metrics_history
        ]
        
        all_memory_usage = [
            m.get("system", {}).get("memory", {}).get("used_mb", 0)
            for m in self.metrics_history
        ]
        
        all_cpu_usage = [
            m.get("system", {}).get("cpu_percent", 0)
            for m in self.metrics_history
        ]
        
        # 按严重程度统计告警
        critical_alerts = [a for a in self.alerts if a["severity"] == "critical"]
        warning_alerts = [a for a in self.alerts if a["severity"] == "warning"]
        
        final_report = {
            "monitoring_summary": {
                "start_time": self.metrics_history[0]["timestamp"],
                "end_time": self.metrics_history[-1]["timestamp"],
                "total_samples": len(self.metrics_history),
                "monitoring_duration_hours": len(self.metrics_history) * self.monitoring_interval / 3600
            },
            "performance_summary": {
                "response_time": {
                    "avg_ms": sum(all_response_times) / len(all_response_times) if all_response_times else 0,
                    "max_ms": max(all_response_times) if all_response_times else 0,
                    "min_ms": min(all_response_times) if all_response_times else 0,
                    "p95_ms": sorted(all_response_times)[int(len(all_response_times) * 0.95)] if all_response_times else 0
                },
                "memory_usage": {
                    "avg_mb": sum(all_memory_usage) / len(all_memory_usage) if all_memory_usage else 0,
                    "max_mb": max(all_memory_usage) if all_memory_usage else 0,
                    "min_mb": min(all_memory_usage) if all_memory_usage else 0
                },
                "cpu_usage": {
                    "avg_percent": sum(all_cpu_usage) / len(all_cpu_usage) if all_cpu_usage else 0,
                    "max_percent": max(all_cpu_usage) if all_cpu_usage else 0,
                    "min_percent": min(all_cpu_usage) if all_cpu_usage else 0
                }
            },
            "alerts_summary": {
                "total_alerts": len(self.alerts),
                "critical_alerts": len(critical_alerts),
                "warning_alerts": len(warning_alerts),
                "alert_rate": len(self.alerts) / len(self.metrics_history) if self.metrics_history else 0
            },
            "sla_compliance": {
                "response_time_sla": sum(1 for rt in all_response_times if rt < 2000) / len(all_response_times) if all_response_times else 0,
                "availability_sla": sum(1 for m in self.metrics_history if m.get("application", {}).get("test_success", False)) / len(self.metrics_history) if self.metrics_history else 0
            },
            "recommendations": self._generate_recommendations()
        }
        
        logger.info(f"最终监控报告: {final_report}")
        
        # 保存最终报告
        reports_dir = Path("data/monitoring_reports")
        reports_dir.mkdir(exist_ok=True)
        
        final_report_file = reports_dir / f"final_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(final_report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"最终报告已保存到: {final_report_file}")
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if not self.metrics_history:
            return recommendations
        
        # 分析响应时间
        response_times = [
            m.get("application", {}).get("test_response_time_ms", 0)
            for m in self.metrics_history
        ]
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            if avg_response_time > 1000:
                recommendations.append("响应时间较高，建议优化查询算法或增加缓存")
        
        # 分析内存使用
        memory_usage = [
            m.get("system", {}).get("memory", {}).get("used_mb", 0)
            for m in self.metrics_history
        ]
        
        if memory_usage:
            max_memory = max(memory_usage)
            if max_memory > 1500:
                recommendations.append("内存使用较高，建议优化内存管理或增加内存容量")
        
        # 分析告警频率
        if len(self.alerts) > len(self.metrics_history) * 0.1:
            recommendations.append("告警频率较高，建议检查系统配置和资源分配")
        
        # 分析数据库连接
        db_errors = [
            m for m in self.metrics_history
            if m.get("database", {}).get("connection_status") != "healthy"
        ]
        
        if len(db_errors) > 0:
            recommendations.append("数据库连接不稳定，建议检查数据库配置和网络连接")
        
        return recommendations


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="性能监控脚本")
    parser.add_argument("--url", default="http://localhost:8000", help="服务URL")
    parser.add_argument("--duration", type=int, default=1, help="监控持续时间（小时）")
    parser.add_argument("--interval", type=int, default=60, help="监控间隔（秒）")
    
    args = parser.parse_args()
    
    monitor = PerformanceMonitor(
        base_url=args.url,
        monitoring_interval=args.interval
    )
    
    await monitor.start_monitoring(duration_hours=args.duration)


if __name__ == "__main__":
    asyncio.run(main())
