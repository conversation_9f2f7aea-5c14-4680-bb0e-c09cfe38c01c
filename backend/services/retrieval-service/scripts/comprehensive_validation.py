#!/usr/bin/env python3
"""
综合验证脚本
全面验证RAG检索系统的功能、性能和质量指标
"""

import asyncio
import json
import time
import statistics
from datetime import datetime
from typing import Dict, List, Any, Tuple
from pathlib import Path
from dataclasses import dataclass

import aiohttp
from loguru import logger


@dataclass
class ValidationResult:
    """验证结果"""
    test_name: str
    passed: bool
    score: float
    details: Dict[str, Any]
    error_message: str = ""


@dataclass
class ComprehensiveReport:
    """综合报告"""
    overall_score: float
    passed_tests: int
    total_tests: int
    functional_tests: List[ValidationResult]
    performance_tests: List[ValidationResult]
    quality_tests: List[ValidationResult]
    recommendations: List[str]


class ComprehensiveValidator:
    """综合验证器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
        # 验证标准
        self.standards = {
            "response_time_threshold": 2000,  # 2秒
            "accuracy_threshold": 0.7,  # 70%
            "recall_threshold": 0.6,  # 60%
            "qps_threshold": 50,  # 50 QPS
            "error_rate_threshold": 0.05,  # 5%
            "availability_threshold": 0.99  # 99%
        }
        
        # 测试数据
        self.test_queries = [
            "人工智能的发展历史和未来趋势",
            "机器学习算法的分类和应用场景",
            "深度学习在自然语言处理中的应用",
            "计算机视觉技术的原理和实现",
            "大数据分析的方法和工具",
            "云计算架构设计和最佳实践",
            "区块链技术的核心概念",
            "物联网系统的构建方法",
            "网络安全防护策略",
            "软件工程开发流程"
        ]
        
        self.chinese_queries = [
            "人工智能技术在医疗领域的应用前景如何？",
            "深度学习模型的训练过程需要哪些步骤？",
            "自然语言处理技术可以解决什么问题？",
            "机器学习算法的性能如何评估？",
            "数据挖掘在商业分析中的作用是什么？"
        ]
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def run_comprehensive_validation(self) -> ComprehensiveReport:
        """运行综合验证"""
        logger.info("开始综合验证...")
        
        # 功能测试
        functional_tests = await self._run_functional_tests()
        
        # 性能测试
        performance_tests = await self._run_performance_tests()
        
        # 质量测试
        quality_tests = await self._run_quality_tests()
        
        # 生成综合报告
        report = self._generate_comprehensive_report(
            functional_tests, performance_tests, quality_tests
        )
        
        # 保存报告
        await self._save_report(report)
        
        logger.info("综合验证完成")
        return report
    
    async def _run_functional_tests(self) -> List[ValidationResult]:
        """运行功能测试"""
        logger.info("执行功能测试...")
        
        tests = []
        
        # 1. 基础搜索功能测试
        tests.append(await self._test_basic_search())
        
        # 2. Self-RAG功能测试
        tests.append(await self._test_self_rag())
        
        # 3. 多向量检索测试
        tests.append(await self._test_multi_vector())
        
        # 4. 中文优化测试
        tests.append(await self._test_chinese_optimization())
        
        # 5. 时效性评分测试
        tests.append(await self._test_temporal_scoring())
        
        # 6. 结果融合测试
        tests.append(await self._test_result_fusion())
        
        # 7. A/B测试框架测试
        tests.append(await self._test_ab_testing())
        
        # 8. 监控系统测试
        tests.append(await self._test_monitoring())
        
        return tests
    
    async def _test_basic_search(self) -> ValidationResult:
        """测试基础搜索功能"""
        try:
            success_count = 0
            total_count = len(self.test_queries)
            
            for query in self.test_queries:
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 10}
                )
                
                if response and response.get("results"):
                    success_count += 1
            
            success_rate = success_count / total_count
            passed = success_rate >= 0.9
            
            return ValidationResult(
                test_name="基础搜索功能",
                passed=passed,
                score=success_rate,
                details={
                    "success_count": success_count,
                    "total_count": total_count,
                    "success_rate": success_rate
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="基础搜索功能",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_self_rag(self) -> ValidationResult:
        """测试Self-RAG功能"""
        try:
            success_count = 0
            total_count = 5
            
            for query in self.test_queries[:5]:
                response = await self._make_request(
                    "POST", "/self-rag/search", 
                    {"query": query, "max_iterations": 3}
                )
                
                if response and response.get("results"):
                    # 检查是否有迭代信息
                    if response.get("iterations", 0) > 0:
                        success_count += 1
            
            success_rate = success_count / total_count
            passed = success_rate >= 0.8
            
            return ValidationResult(
                test_name="Self-RAG功能",
                passed=passed,
                score=success_rate,
                details={
                    "success_count": success_count,
                    "total_count": total_count,
                    "success_rate": success_rate
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="Self-RAG功能",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_multi_vector(self) -> ValidationResult:
        """测试多向量检索"""
        try:
            success_count = 0
            total_count = 5
            
            for query in self.test_queries[:5]:
                response = await self._make_request(
                    "POST", "/advanced/search/advanced",
                    {
                        "query": query,
                        "search_methods": ["multi_vector"],
                        "top_k": 10
                    }
                )
                
                if response and response.get("results"):
                    # 检查是否有方面信息
                    results = response.get("results", [])
                    if results and any("aspect" in str(r) for r in results):
                        success_count += 1
            
            success_rate = success_count / total_count
            passed = success_rate >= 0.6
            
            return ValidationResult(
                test_name="多向量检索",
                passed=passed,
                score=success_rate,
                details={
                    "success_count": success_count,
                    "total_count": total_count,
                    "success_rate": success_rate
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="多向量检索",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_chinese_optimization(self) -> ValidationResult:
        """测试中文优化"""
        try:
            success_count = 0
            total_count = len(self.chinese_queries)
            
            for text in self.chinese_queries:
                response = await self._make_request(
                    "POST", "/advanced/chinese/optimize",
                    {"text": text, "optimization_type": "segmentation"}
                )
                
                if response and response.get("optimized_text"):
                    success_count += 1
            
            success_rate = success_count / total_count
            passed = success_rate >= 0.8
            
            return ValidationResult(
                test_name="中文优化",
                passed=passed,
                score=success_rate,
                details={
                    "success_count": success_count,
                    "total_count": total_count,
                    "success_rate": success_rate
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="中文优化",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_temporal_scoring(self) -> ValidationResult:
        """测试时效性评分"""
        try:
            response = await self._make_request(
                "POST", "/advanced/temporal/score",
                {
                    "content": "2023年人工智能技术的最新发展",
                    "query": "人工智能最新进展",
                    "metadata": {"created_at": "2023-12-01T10:00:00Z"}
                }
            )
            
            if response and "final_score" in response:
                final_score = response["final_score"]
                passed = 0 <= final_score <= 1
                
                return ValidationResult(
                    test_name="时效性评分",
                    passed=passed,
                    score=final_score,
                    details=response
                )
            else:
                return ValidationResult(
                    test_name="时效性评分",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="未返回有效的时效性分数"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="时效性评分",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_result_fusion(self) -> ValidationResult:
        """测试结果融合"""
        try:
            response = await self._make_request(
                "POST", "/advanced/search/advanced",
                {
                    "query": "人工智能发展",
                    "search_methods": ["semantic", "multi_vector"],
                    "fusion_method": "adaptive",
                    "top_k": 10
                }
            )
            
            if response and response.get("results"):
                results = response["results"]
                fusion_method = response.get("fusion_method", "")
                
                # 检查是否有融合信息
                has_fusion_info = any(
                    "fusion" in str(r).lower() for r in results
                ) or fusion_method
                
                passed = has_fusion_info and len(results) > 0
                score = len(results) / 10.0  # 基于结果数量评分
                
                return ValidationResult(
                    test_name="结果融合",
                    passed=passed,
                    score=score,
                    details={
                        "result_count": len(results),
                        "fusion_method": fusion_method,
                        "has_fusion_info": has_fusion_info
                    }
                )
            else:
                return ValidationResult(
                    test_name="结果融合",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="未返回融合结果"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="结果融合",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_ab_testing(self) -> ValidationResult:
        """测试A/B测试框架"""
        try:
            # 检查A/B测试实验列表
            response = await self._make_request("GET", "/advanced/ab-testing/experiments")
            
            if response and "experiments" in response:
                experiments = response["experiments"]
                passed = isinstance(experiments, list)
                score = 1.0 if passed else 0.0
                
                return ValidationResult(
                    test_name="A/B测试框架",
                    passed=passed,
                    score=score,
                    details={
                        "experiment_count": len(experiments),
                        "experiments": experiments
                    }
                )
            else:
                return ValidationResult(
                    test_name="A/B测试框架",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="无法获取A/B测试实验列表"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="A/B测试框架",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_monitoring(self) -> ValidationResult:
        """测试监控系统"""
        try:
            # 检查监控指标
            response = await self._make_request("GET", "/advanced/monitoring/metrics")
            
            if response and isinstance(response, dict):
                has_metrics = len(response) > 0
                passed = has_metrics
                score = 1.0 if passed else 0.0
                
                return ValidationResult(
                    test_name="监控系统",
                    passed=passed,
                    score=score,
                    details={
                        "metrics_count": len(response),
                        "available_metrics": list(response.keys())
                    }
                )
            else:
                return ValidationResult(
                    test_name="监控系统",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="无法获取监控指标"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="监控系统",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _run_performance_tests(self) -> List[ValidationResult]:
        """运行性能测试"""
        logger.info("执行性能测试...")
        
        tests = []
        
        # 1. 响应时间测试
        tests.append(await self._test_response_time())
        
        # 2. 并发性能测试
        tests.append(await self._test_concurrent_performance())
        
        # 3. 吞吐量测试
        tests.append(await self._test_throughput())
        
        return tests
    
    async def _test_response_time(self) -> ValidationResult:
        """测试响应时间"""
        try:
            response_times = []
            
            for query in self.test_queries[:5]:
                start_time = time.time()
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 10}
                )
                end_time = time.time()
                
                if response:
                    response_time = (end_time - start_time) * 1000
                    response_times.append(response_time)
            
            if response_times:
                avg_response_time = statistics.mean(response_times)
                p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0]
                
                passed = p95_response_time < self.standards["response_time_threshold"]
                score = max(0, 1 - (p95_response_time / self.standards["response_time_threshold"]))
                
                return ValidationResult(
                    test_name="响应时间",
                    passed=passed,
                    score=score,
                    details={
                        "avg_response_time": avg_response_time,
                        "p95_response_time": p95_response_time,
                        "threshold": self.standards["response_time_threshold"],
                        "sample_count": len(response_times)
                    }
                )
            else:
                return ValidationResult(
                    test_name="响应时间",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="无法获取响应时间数据"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="响应时间",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_concurrent_performance(self) -> ValidationResult:
        """测试并发性能"""
        try:
            concurrent_users = 10
            tasks = []
            
            start_time = time.time()
            
            for i in range(concurrent_users):
                query = self.test_queries[i % len(self.test_queries)]
                task = asyncio.create_task(
                    self._make_request("POST", "/search", {"query": query, "top_k": 5})
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_time = end_time - start_time
            successful_requests = sum(1 for r in results if not isinstance(r, Exception) and r)
            success_rate = successful_requests / concurrent_users
            
            passed = success_rate >= 0.9 and total_time < 10
            score = success_rate * (1 - min(total_time / 10, 1))
            
            return ValidationResult(
                test_name="并发性能",
                passed=passed,
                score=score,
                details={
                    "concurrent_users": concurrent_users,
                    "successful_requests": successful_requests,
                    "success_rate": success_rate,
                    "total_time": total_time
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="并发性能",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_throughput(self) -> ValidationResult:
        """测试吞吐量"""
        try:
            test_duration = 30  # 30秒
            request_count = 0
            start_time = time.time()
            
            while time.time() - start_time < test_duration:
                query = self.test_queries[request_count % len(self.test_queries)]
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 5}
                )
                
                if response:
                    request_count += 1
                
                # 控制请求频率
                await asyncio.sleep(0.1)
            
            actual_duration = time.time() - start_time
            qps = request_count / actual_duration
            
            passed = qps >= self.standards["qps_threshold"]
            score = min(qps / self.standards["qps_threshold"], 1.0)
            
            return ValidationResult(
                test_name="吞吐量",
                passed=passed,
                score=score,
                details={
                    "qps": qps,
                    "request_count": request_count,
                    "duration": actual_duration,
                    "threshold": self.standards["qps_threshold"]
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="吞吐量",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _run_quality_tests(self) -> List[ValidationResult]:
        """运行质量测试"""
        logger.info("执行质量测试...")
        
        tests = []
        
        # 1. 结果相关性测试
        tests.append(await self._test_result_relevance())
        
        # 2. 结果多样性测试
        tests.append(await self._test_result_diversity())
        
        # 3. 系统稳定性测试
        tests.append(await self._test_system_stability())
        
        return tests
    
    async def _test_result_relevance(self) -> ValidationResult:
        """测试结果相关性"""
        try:
            relevance_scores = []
            
            for query in self.test_queries[:5]:
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 10}
                )
                
                if response and response.get("results"):
                    results = response["results"]
                    # 基于分数评估相关性
                    scores = [r.get("score", 0) for r in results if isinstance(r, dict)]
                    if scores:
                        avg_score = statistics.mean(scores)
                        relevance_scores.append(avg_score)
            
            if relevance_scores:
                avg_relevance = statistics.mean(relevance_scores)
                passed = avg_relevance >= self.standards["accuracy_threshold"]
                score = avg_relevance
                
                return ValidationResult(
                    test_name="结果相关性",
                    passed=passed,
                    score=score,
                    details={
                        "avg_relevance": avg_relevance,
                        "threshold": self.standards["accuracy_threshold"],
                        "sample_count": len(relevance_scores)
                    }
                )
            else:
                return ValidationResult(
                    test_name="结果相关性",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="无法获取相关性数据"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="结果相关性",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_result_diversity(self) -> ValidationResult:
        """测试结果多样性"""
        try:
            diversity_scores = []
            
            for query in self.test_queries[:3]:
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 10}
                )
                
                if response and response.get("results"):
                    results = response["results"]
                    # 简单的多样性评估：检查内容长度差异
                    contents = [r.get("content", "") for r in results if isinstance(r, dict)]
                    if len(contents) > 1:
                        lengths = [len(c) for c in contents]
                        diversity = statistics.stdev(lengths) / statistics.mean(lengths) if statistics.mean(lengths) > 0 else 0
                        diversity_scores.append(min(diversity, 1.0))
            
            if diversity_scores:
                avg_diversity = statistics.mean(diversity_scores)
                passed = avg_diversity >= 0.3  # 30%的多样性阈值
                score = avg_diversity
                
                return ValidationResult(
                    test_name="结果多样性",
                    passed=passed,
                    score=score,
                    details={
                        "avg_diversity": avg_diversity,
                        "threshold": 0.3,
                        "sample_count": len(diversity_scores)
                    }
                )
            else:
                return ValidationResult(
                    test_name="结果多样性",
                    passed=False,
                    score=0.0,
                    details={},
                    error_message="无法计算多样性"
                )
                
        except Exception as e:
            return ValidationResult(
                test_name="结果多样性",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _test_system_stability(self) -> ValidationResult:
        """测试系统稳定性"""
        try:
            # 连续请求测试
            success_count = 0
            total_requests = 20
            
            for i in range(total_requests):
                query = self.test_queries[i % len(self.test_queries)]
                response = await self._make_request(
                    "POST", "/search", {"query": query, "top_k": 5}
                )
                
                if response and response.get("results"):
                    success_count += 1
                
                # 短暂延迟
                await asyncio.sleep(0.1)
            
            stability_rate = success_count / total_requests
            passed = stability_rate >= self.standards["availability_threshold"]
            score = stability_rate
            
            return ValidationResult(
                test_name="系统稳定性",
                passed=passed,
                score=score,
                details={
                    "success_count": success_count,
                    "total_requests": total_requests,
                    "stability_rate": stability_rate,
                    "threshold": self.standards["availability_threshold"]
                }
            )
            
        except Exception as e:
            return ValidationResult(
                test_name="系统稳定性",
                passed=False,
                score=0.0,
                details={},
                error_message=str(e)
            )
    
    async def _make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送HTTP请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == "GET":
                async with self.session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        return await response.json()
            
            return None
            
        except Exception as e:
            logger.warning(f"请求失败 {method} {endpoint}: {e}")
            return None
    
    def _generate_comprehensive_report(self, functional_tests: List[ValidationResult],
                                     performance_tests: List[ValidationResult],
                                     quality_tests: List[ValidationResult]) -> ComprehensiveReport:
        """生成综合报告"""
        all_tests = functional_tests + performance_tests + quality_tests
        
        passed_tests = sum(1 for test in all_tests if test.passed)
        total_tests = len(all_tests)
        
        # 计算加权总分
        functional_weight = 0.4
        performance_weight = 0.3
        quality_weight = 0.3
        
        functional_score = statistics.mean([t.score for t in functional_tests]) if functional_tests else 0
        performance_score = statistics.mean([t.score for t in performance_tests]) if performance_tests else 0
        quality_score = statistics.mean([t.score for t in quality_tests]) if quality_tests else 0
        
        overall_score = (
            functional_score * functional_weight +
            performance_score * performance_weight +
            quality_score * quality_weight
        )
        
        # 生成建议
        recommendations = self._generate_recommendations(all_tests)
        
        return ComprehensiveReport(
            overall_score=overall_score,
            passed_tests=passed_tests,
            total_tests=total_tests,
            functional_tests=functional_tests,
            performance_tests=performance_tests,
            quality_tests=quality_tests,
            recommendations=recommendations
        )
    
    def _generate_recommendations(self, tests: List[ValidationResult]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 分析失败的测试
        failed_tests = [t for t in tests if not t.passed]
        
        for test in failed_tests:
            if "响应时间" in test.test_name:
                recommendations.append("优化查询性能，考虑增加缓存或优化数据库索引")
            elif "并发" in test.test_name:
                recommendations.append("提升并发处理能力，考虑增加服务实例或优化异步处理")
            elif "相关性" in test.test_name:
                recommendations.append("改进检索算法，优化向量模型或调整相似度阈值")
            elif "多样性" in test.test_name:
                recommendations.append("增强结果多样性，优化重排序算法或增加多样性因子")
            elif "稳定性" in test.test_name:
                recommendations.append("提升系统稳定性，检查错误处理和资源管理")
        
        # 分析低分测试
        low_score_tests = [t for t in tests if t.score < 0.7]
        
        if len(low_score_tests) > len(tests) * 0.3:
            recommendations.append("整体性能需要改进，建议进行全面的系统优化")
        
        if not recommendations:
            recommendations.append("系统表现良好，建议继续监控和定期优化")
        
        return recommendations
    
    async def _save_report(self, report: ComprehensiveReport):
        """保存验证报告"""
        reports_dir = Path("data/validation_reports")
        reports_dir.mkdir(exist_ok=True)
        
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "overall_score": report.overall_score,
            "passed_tests": report.passed_tests,
            "total_tests": report.total_tests,
            "success_rate": report.passed_tests / report.total_tests if report.total_tests > 0 else 0,
            "functional_tests": [
                {
                    "test_name": t.test_name,
                    "passed": t.passed,
                    "score": t.score,
                    "details": t.details,
                    "error_message": t.error_message
                }
                for t in report.functional_tests
            ],
            "performance_tests": [
                {
                    "test_name": t.test_name,
                    "passed": t.passed,
                    "score": t.score,
                    "details": t.details,
                    "error_message": t.error_message
                }
                for t in report.performance_tests
            ],
            "quality_tests": [
                {
                    "test_name": t.test_name,
                    "passed": t.passed,
                    "score": t.score,
                    "details": t.details,
                    "error_message": t.error_message
                }
                for t in report.quality_tests
            ],
            "recommendations": report.recommendations
        }
        
        report_file = reports_dir / f"comprehensive_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 打印报告摘要
        self._print_report_summary(report)
        
        logger.info(f"综合验证报告已保存到: {report_file}")
    
    def _print_report_summary(self, report: ComprehensiveReport):
        """打印报告摘要"""
        print("\n" + "="*80)
        print("RAG检索系统综合验证报告")
        print("="*80)
        print(f"整体评分: {report.overall_score:.2f}/1.00")
        print(f"测试通过率: {report.passed_tests}/{report.total_tests} ({report.passed_tests/report.total_tests:.1%})")
        
        print(f"\n功能测试 ({len(report.functional_tests)}项):")
        for test in report.functional_tests:
            status = "✅" if test.passed else "❌"
            print(f"  {status} {test.test_name}: {test.score:.2f}")
        
        print(f"\n性能测试 ({len(report.performance_tests)}项):")
        for test in report.performance_tests:
            status = "✅" if test.passed else "❌"
            print(f"  {status} {test.test_name}: {test.score:.2f}")
        
        print(f"\n质量测试 ({len(report.quality_tests)}项):")
        for test in report.quality_tests:
            status = "✅" if test.passed else "❌"
            print(f"  {status} {test.test_name}: {test.score:.2f}")
        
        if report.recommendations:
            print(f"\n优化建议:")
            for i, rec in enumerate(report.recommendations, 1):
                print(f"  {i}. {rec}")
        
        print("="*80)


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG检索系统综合验证")
    parser.add_argument("--url", default="http://localhost:8000", help="服务URL")
    
    args = parser.parse_args()
    
    async with ComprehensiveValidator(base_url=args.url) as validator:
        report = await validator.run_comprehensive_validation()
        
        # 根据结果设置退出码
        if report.overall_score >= 0.8 and report.passed_tests / report.total_tests >= 0.8:
            exit(0)  # 成功
        else:
            exit(1)  # 失败


if __name__ == "__main__":
    asyncio.run(main())
