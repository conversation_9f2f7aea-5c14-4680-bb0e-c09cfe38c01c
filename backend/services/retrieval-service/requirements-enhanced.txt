# RAG检索系统增强技术依赖包
# 核心深度学习框架
torch>=1.9.0
transformers>=4.20.0
sentence-transformers>=2.2.0
accelerate>=0.20.0

# 向量检索和相似度计算
faiss-cpu>=1.7.0
numpy>=1.21.0
scipy>=1.7.0

# 中文自然语言处理
jieba>=0.42.1
pkuseg>=0.0.25
thulac>=0.2.1

# 机器学习和数据处理
scikit-learn>=1.0.0
pandas>=1.3.0
datasets>=2.0.0

# 异步和并发处理
asyncio
aiohttp>=3.8.0
asyncpg>=0.25.0

# 缓存和数据库
redis>=4.0.0
psycopg2-binary>=2.9.0

# API框架
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0

# 监控和日志
prometheus-client>=0.11.0
loguru>=0.5.3

# 配置管理
pyyaml>=5.4.0
python-dotenv>=0.19.0

# 测试框架
pytest>=6.2.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0

# 数据可视化和分析
matplotlib>=3.4.0
seaborn>=0.11.0

# 文本处理和正则表达式
regex>=2021.8.3
nltk>=3.6.0

# HTTP客户端
httpx>=0.24.0
requests>=2.25.0

# 时间处理
python-dateutil>=2.8.0

# 数学计算
sympy>=1.8.0

# 图像处理（多模态支持）
Pillow>=8.3.0

# 知识图谱支持
neo4j>=4.3.0
rdflib>=6.0.0

# 模型量化和优化
optimum>=1.8.0
onnx>=1.12.0

# 分布式计算
ray>=2.0.0

# 数据验证
marshmallow>=3.13.0

# 加密和安全
cryptography>=3.4.0

# 开发工具
black>=21.7.0
flake8>=3.9.0
mypy>=0.910
