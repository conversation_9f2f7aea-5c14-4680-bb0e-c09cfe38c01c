-- 高级功能数据库迁移脚本
-- 创建方面向量表、用户行为表、查询历史表等

-- 创建方面向量表
CREATE TABLE IF NOT EXISTS aspect_vectors (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    chunk_id VARCHAR(255),
    aspect_name VARCHAR(50) NOT NULL,
    vector VECTOR(384),
    content TEXT NOT NULL,
    weight FLOAT DEFAULT 1.0,
    confidence FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建方面向量索引
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_document_id ON aspect_vectors(document_id);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_aspect_name ON aspect_vectors(aspect_name);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_confidence ON aspect_vectors(confidence);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_created_at ON aspect_vectors(created_at);

-- 创建向量相似度索引
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_cosine 
ON aspect_vectors USING ivfflat (vector vector_cosine_ops) 
WITH (lists = 100);

CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_l2 
ON aspect_vectors USING ivfflat (vector vector_l2_ops) 
WITH (lists = 100);

-- 创建困难负样本表
CREATE TABLE IF NOT EXISTS hard_negative_samples (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    positive_doc_id VARCHAR(255) NOT NULL,
    negative_doc_id VARCHAR(255) NOT NULL,
    similarity_score FLOAT NOT NULL,
    relevance_score FLOAT NOT NULL,
    difficulty_level VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建困难负样本索引
CREATE INDEX IF NOT EXISTS idx_hard_negative_query ON hard_negative_samples(query);
CREATE INDEX IF NOT EXISTS idx_hard_negative_difficulty ON hard_negative_samples(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_hard_negative_similarity ON hard_negative_samples(similarity_score);

-- 创建A/B测试实验表
CREATE TABLE IF NOT EXISTS ab_experiments (
    id SERIAL PRIMARY KEY,
    experiment_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    control_group VARCHAR(50) NOT NULL,
    treatment_group VARCHAR(50) NOT NULL,
    traffic_split FLOAT NOT NULL DEFAULT 0.5,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    metrics JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建A/B测试用户分配表
CREATE TABLE IF NOT EXISTS ab_user_assignments (
    id SERIAL PRIMARY KEY,
    experiment_id INTEGER REFERENCES ab_experiments(id),
    user_id VARCHAR(255) NOT NULL,
    group_name VARCHAR(50) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(experiment_id, user_id)
);

-- 创建A/B测试索引
CREATE INDEX IF NOT EXISTS idx_ab_experiments_status ON ab_experiments(status);
CREATE INDEX IF NOT EXISTS idx_ab_experiments_start_date ON ab_experiments(start_date);
CREATE INDEX IF NOT EXISTS idx_ab_user_assignments_user_id ON ab_user_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_ab_user_assignments_experiment_id ON ab_user_assignments(experiment_id);

-- 创建查询历史表
CREATE TABLE IF NOT EXISTS query_history (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255),
    original_query TEXT NOT NULL,
    refined_query TEXT,
    search_type VARCHAR(50) NOT NULL,
    response_time_ms INTEGER NOT NULL,
    result_count INTEGER NOT NULL,
    user_feedback INTEGER, -- 1-5评分
    click_positions INTEGER[], -- 点击位置数组
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建查询历史索引
CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_query_history_search_type ON query_history(search_type);
CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at);
CREATE INDEX IF NOT EXISTS idx_query_history_response_time ON query_history(response_time_ms);

-- 创建用户行为表
CREATE TABLE IF NOT EXISTS user_behavior (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    action_type VARCHAR(50) NOT NULL, -- search, click, feedback, etc.
    target_id VARCHAR(255), -- document_id, query_id, etc.
    action_data JSONB,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户行为索引
CREATE INDEX IF NOT EXISTS idx_user_behavior_user_id ON user_behavior(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_action_type ON user_behavior(action_type);
CREATE INDEX IF NOT EXISTS idx_user_behavior_created_at ON user_behavior(created_at);
CREATE INDEX IF NOT EXISTS idx_user_behavior_session_id ON user_behavior(session_id);

-- 创建性能指标表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value FLOAT NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- latency, accuracy, qps, etc.
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建性能指标索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_created_at ON performance_metrics(created_at);

-- 创建文档块表（如果不存在）
CREATE TABLE IF NOT EXISTS document_chunks (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    chunk_weight FLOAT DEFAULT 1.0,
    quality_score FLOAT DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档块索引
CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_document_chunks_quality_score ON document_chunks(quality_score);

-- 创建时效性评分表
CREATE TABLE IF NOT EXISTS temporal_scores (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    freshness_score FLOAT NOT NULL,
    relevance_score FLOAT NOT NULL,
    decay_score FLOAT NOT NULL,
    final_score FLOAT NOT NULL,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建时效性评分索引
CREATE INDEX IF NOT EXISTS idx_temporal_scores_document_id ON temporal_scores(document_id);
CREATE INDEX IF NOT EXISTS idx_temporal_scores_final_score ON temporal_scores(final_score);
CREATE INDEX IF NOT EXISTS idx_temporal_scores_calculated_at ON temporal_scores(calculated_at);

-- 创建中文分词结果表
CREATE TABLE IF NOT EXISTS chinese_segmentation_results (
    id SERIAL PRIMARY KEY,
    text_hash VARCHAR(64) NOT NULL,
    original_text TEXT NOT NULL,
    segmented_words TEXT[] NOT NULL,
    pos_tags TEXT[],
    confidence FLOAT NOT NULL,
    method VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建中文分词索引
CREATE INDEX IF NOT EXISTS idx_chinese_segmentation_hash ON chinese_segmentation_results(text_hash);
CREATE INDEX IF NOT EXISTS idx_chinese_segmentation_method ON chinese_segmentation_results(method);

-- 创建语义角色标注结果表
CREATE TABLE IF NOT EXISTS semantic_role_results (
    id SERIAL PRIMARY KEY,
    text_hash VARCHAR(64) NOT NULL,
    original_text TEXT NOT NULL,
    predicate VARCHAR(100),
    roles JSONB NOT NULL,
    semantic_focus TEXT,
    query_intent VARCHAR(50),
    confidence FLOAT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建语义角色标注索引
CREATE INDEX IF NOT EXISTS idx_semantic_role_hash ON semantic_role_results(text_hash);
CREATE INDEX IF NOT EXISTS idx_semantic_role_intent ON semantic_role_results(query_intent);

-- 创建Self-RAG评估历史表
CREATE TABLE IF NOT EXISTS self_rag_evaluations (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    iteration INTEGER NOT NULL,
    confidence FLOAT NOT NULL,
    relevance_score FLOAT NOT NULL,
    completeness_score FLOAT NOT NULL,
    suggestions TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建Self-RAG评估索引
CREATE INDEX IF NOT EXISTS idx_self_rag_query ON self_rag_evaluations(query);
CREATE INDEX IF NOT EXISTS idx_self_rag_confidence ON self_rag_evaluations(confidence);
CREATE INDEX IF NOT EXISTS idx_self_rag_created_at ON self_rag_evaluations(created_at);

-- 更新现有表的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
DROP TRIGGER IF EXISTS update_aspect_vectors_updated_at ON aspect_vectors;
CREATE TRIGGER update_aspect_vectors_updated_at 
    BEFORE UPDATE ON aspect_vectors 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_ab_experiments_updated_at ON ab_experiments;
CREATE TRIGGER update_ab_experiments_updated_at 
    BEFORE UPDATE ON ab_experiments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_document_chunks_updated_at ON document_chunks;
CREATE TRIGGER update_document_chunks_updated_at 
    BEFORE UPDATE ON document_chunks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：查询性能统计
CREATE OR REPLACE VIEW query_performance_stats AS
SELECT 
    search_type,
    DATE_TRUNC('hour', created_at) as hour,
    COUNT(*) as query_count,
    AVG(response_time_ms) as avg_response_time,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY response_time_ms) as p95_response_time,
    AVG(result_count) as avg_result_count,
    AVG(user_feedback) as avg_user_feedback
FROM query_history 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY search_type, DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

-- 创建视图：A/B测试结果统计
CREATE OR REPLACE VIEW ab_test_results AS
SELECT 
    e.experiment_name,
    ua.group_name,
    COUNT(DISTINCT ua.user_id) as user_count,
    COUNT(qh.id) as query_count,
    AVG(qh.response_time_ms) as avg_response_time,
    AVG(qh.user_feedback) as avg_satisfaction,
    COUNT(CASE WHEN array_length(qh.click_positions, 1) > 0 THEN 1 END)::FLOAT / COUNT(qh.id) as click_through_rate
FROM ab_experiments e
JOIN ab_user_assignments ua ON e.id = ua.experiment_id
LEFT JOIN query_history qh ON ua.user_id = qh.user_id 
    AND qh.created_at >= ua.assigned_at
WHERE e.status = 'active'
GROUP BY e.experiment_name, ua.group_name;

-- 分析表统计信息
ANALYZE aspect_vectors;
ANALYZE hard_negative_samples;
ANALYZE ab_experiments;
ANALYZE ab_user_assignments;
ANALYZE query_history;
ANALYZE user_behavior;
ANALYZE performance_metrics;
ANALYZE document_chunks;
ANALYZE temporal_scores;
ANALYZE chinese_segmentation_results;
ANALYZE semantic_role_results;
ANALYZE self_rag_evaluations;
