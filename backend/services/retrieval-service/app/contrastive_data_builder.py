"""
对比学习训练数据构建器
基于困难负样本挖掘构建高质量的对比学习数据集
"""

import asyncio
import json
import random
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from loguru import logger
from .negative_mining_and_multivector import HardNegativeMiner, ContrastiveExample
from .database_client import database_client


@dataclass
class DatasetStats:
    """数据集统计信息"""
    total_examples: int
    queries_count: int
    avg_positives_per_query: float
    avg_negatives_per_query: float
    difficulty_distribution: Dict[str, int]
    quality_score: float


class ContrastiveDataBuilder:
    """对比学习数据构建器"""
    
    def __init__(self):
        self.hard_negative_miner = HardNegativeMiner()
        self.min_examples_per_query = 5
        self.max_examples_per_query = 20
        self.train_test_split = 0.8
        
        # 数据增强参数
        self.augmentation_enabled = True
        self.synonym_replacement_prob = 0.1
        self.random_deletion_prob = 0.05
    
    async def build_dataset(self, training_queries: List[str], 
                          positive_docs: Dict[str, List[str]],
                          output_dir: str = "data/contrastive") -> DatasetStats:
        """构建完整的对比学习数据集"""
        try:
            logger.info("开始构建对比学习数据集...")
            
            # 1. 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 2. 生成对比学习样本
            contrastive_examples = await self._generate_contrastive_examples(
                training_queries, positive_docs
            )
            
            if not contrastive_examples:
                logger.error("未生成任何对比学习样本")
                return DatasetStats(0, 0, 0, 0, {}, 0)
            
            # 3. 数据增强
            if self.augmentation_enabled:
                augmented_examples = await self._augment_data(contrastive_examples)
                contrastive_examples.extend(augmented_examples)
            
            # 4. 数据质量检查和过滤
            filtered_examples = await self._quality_filter(contrastive_examples)
            
            # 5. 数据集分割
            train_examples, test_examples = self._split_dataset(filtered_examples)
            
            # 6. 保存数据集
            await self._save_dataset(train_examples, test_examples, output_path)
            
            # 7. 生成统计信息
            stats = self._calculate_stats(filtered_examples, training_queries)
            
            # 8. 保存统计信息
            await self._save_stats(stats, output_path)
            
            logger.info(f"数据集构建完成，共 {stats.total_examples} 个样本")
            return stats
            
        except Exception as e:
            logger.error(f"数据集构建失败: {e}")
            return DatasetStats(0, 0, 0, 0, {}, 0)
    
    async def _generate_contrastive_examples(self, training_queries: List[str], 
                                           positive_docs: Dict[str, List[str]]) -> List[ContrastiveExample]:
        """生成对比学习样本"""
        try:
            logger.info("生成对比学习样本...")
            
            all_examples = []
            
            for query in training_queries:
                if query not in positive_docs:
                    logger.warning(f"查询 {query} 没有正样本文档")
                    continue
                
                query_positives = positive_docs[query]
                
                # 挖掘困难负样本
                hard_negatives = await self.hard_negative_miner.mine_hard_negatives(
                    query, query_positives
                )
                
                if not hard_negatives:
                    logger.warning(f"查询 {query} 未找到困难负样本")
                    continue
                
                # 创建对比样本
                query_examples = await self._create_query_examples(
                    query, query_positives, hard_negatives
                )
                
                all_examples.extend(query_examples)
            
            logger.info(f"生成了 {len(all_examples)} 个对比学习样本")
            return all_examples
            
        except Exception as e:
            logger.error(f"生成对比学习样本失败: {e}")
            return []
    
    async def _create_query_examples(self, query: str, positives: List[str], 
                                   negatives: List[str]) -> List[ContrastiveExample]:
        """为单个查询创建对比样本"""
        examples = []
        
        try:
            # 限制样本数量
            max_examples = min(self.max_examples_per_query, len(positives) * len(negatives))
            
            # 创建所有可能的正负样本对
            pairs = []
            for positive in positives:
                for negative in negatives:
                    pairs.append((positive, negative))
            
            # 随机选择样本对
            if len(pairs) > max_examples:
                pairs = random.sample(pairs, max_examples)
            
            # 创建对比样本
            for positive, negative in pairs:
                # 计算困难度
                similarity = await self.hard_negative_miner._calculate_single_similarity(query, negative)
                relevance = await self.hard_negative_miner._calculate_single_relevance(query, negative)
                difficulty_score = self.hard_negative_miner._calculate_difficulty(similarity, relevance)
                
                # 确定困难度等级和权重
                if difficulty_score > 0.7:
                    difficulty = "hard"
                    weight = 1.5
                elif difficulty_score > 0.4:
                    difficulty = "medium"
                    weight = 1.0
                else:
                    difficulty = "easy"
                    weight = 0.5
                
                example = ContrastiveExample(
                    query=query,
                    positive=positive,
                    negative=negative,
                    difficulty=difficulty,
                    weight=weight
                )
                
                examples.append(example)
            
            return examples
            
        except Exception as e:
            logger.error(f"创建查询样本失败: {e}")
            return []
    
    async def _augment_data(self, examples: List[ContrastiveExample]) -> List[ContrastiveExample]:
        """数据增强"""
        try:
            logger.info("执行数据增强...")
            
            augmented_examples = []
            
            for example in examples:
                # 查询增强
                augmented_queries = await self._augment_query(example.query)
                
                for aug_query in augmented_queries:
                    aug_example = ContrastiveExample(
                        query=aug_query,
                        positive=example.positive,
                        negative=example.negative,
                        difficulty=example.difficulty,
                        weight=example.weight * 0.8  # 增强样本权重稍低
                    )
                    augmented_examples.append(aug_example)
            
            logger.info(f"数据增强生成了 {len(augmented_examples)} 个额外样本")
            return augmented_examples
            
        except Exception as e:
            logger.error(f"数据增强失败: {e}")
            return []
    
    async def _augment_query(self, query: str) -> List[str]:
        """查询增强"""
        try:
            augmented_queries = []
            words = query.split()
            
            # 同义词替换
            if random.random() < self.synonym_replacement_prob and len(words) > 2:
                # 简化实现：随机替换一个词
                new_words = words.copy()
                replace_idx = random.randint(0, len(words) - 1)
                # 这里应该使用同义词库，简化处理
                new_words[replace_idx] = f"{words[replace_idx]}_syn"
                augmented_queries.append(" ".join(new_words))
            
            # 随机删除
            if random.random() < self.random_deletion_prob and len(words) > 3:
                new_words = words.copy()
                del_idx = random.randint(0, len(words) - 1)
                del new_words[del_idx]
                augmented_queries.append(" ".join(new_words))
            
            return augmented_queries
            
        except Exception as e:
            logger.error(f"查询增强失败: {e}")
            return []
    
    async def _quality_filter(self, examples: List[ContrastiveExample]) -> List[ContrastiveExample]:
        """质量过滤"""
        try:
            logger.info("执行质量过滤...")
            
            filtered_examples = []
            
            for example in examples:
                if self._is_quality_example(example):
                    filtered_examples.append(example)
            
            logger.info(f"质量过滤后保留 {len(filtered_examples)} 个样本")
            return filtered_examples
            
        except Exception as e:
            logger.error(f"质量过滤失败: {e}")
            return examples
    
    def _is_quality_example(self, example: ContrastiveExample) -> bool:
        """判断样本质量"""
        try:
            # 1. 长度检查
            if len(example.query) < 5 or len(example.positive) < 20 or len(example.negative) < 20:
                return False
            
            # 2. 内容重复检查
            if example.positive == example.negative:
                return False
            
            # 3. 查询与正样本的相关性检查（简化）
            query_words = set(example.query.lower().split())
            positive_words = set(example.positive.lower().split())
            overlap = len(query_words & positive_words) / len(query_words)
            
            if overlap < 0.1:  # 正样本应该与查询有一定相关性
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"质量检查失败: {e}")
            return True
    
    def _split_dataset(self, examples: List[ContrastiveExample]) -> Tuple[List[ContrastiveExample], List[ContrastiveExample]]:
        """分割数据集"""
        try:
            # 按查询分组
            query_groups = {}
            for example in examples:
                if example.query not in query_groups:
                    query_groups[example.query] = []
                query_groups[example.query].append(example)
            
            # 按查询分割，确保同一查询的样本在同一集合中
            queries = list(query_groups.keys())
            random.shuffle(queries)
            
            split_idx = int(len(queries) * self.train_test_split)
            train_queries = queries[:split_idx]
            test_queries = queries[split_idx:]
            
            train_examples = []
            test_examples = []
            
            for query in train_queries:
                train_examples.extend(query_groups[query])
            
            for query in test_queries:
                test_examples.extend(query_groups[query])
            
            logger.info(f"数据集分割: 训练集 {len(train_examples)} 样本，测试集 {len(test_examples)} 样本")
            return train_examples, test_examples
            
        except Exception as e:
            logger.error(f"数据集分割失败: {e}")
            return examples, []
    
    async def _save_dataset(self, train_examples: List[ContrastiveExample], 
                          test_examples: List[ContrastiveExample], 
                          output_path: Path):
        """保存数据集"""
        try:
            # 保存训练集
            train_file = output_path / "train.jsonl"
            with open(train_file, 'w', encoding='utf-8') as f:
                for example in train_examples:
                    f.write(json.dumps(asdict(example), ensure_ascii=False) + '\n')
            
            # 保存测试集
            test_file = output_path / "test.jsonl"
            with open(test_file, 'w', encoding='utf-8') as f:
                for example in test_examples:
                    f.write(json.dumps(asdict(example), ensure_ascii=False) + '\n')
            
            logger.info(f"数据集保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存数据集失败: {e}")
    
    def _calculate_stats(self, examples: List[ContrastiveExample], 
                        queries: List[str]) -> DatasetStats:
        """计算数据集统计信息"""
        try:
            # 按查询分组
            query_groups = {}
            for example in examples:
                if example.query not in query_groups:
                    query_groups[example.query] = []
                query_groups[example.query].append(example)
            
            # 计算统计信息
            total_examples = len(examples)
            queries_count = len(query_groups)
            
            positives_per_query = []
            negatives_per_query = []
            difficulty_dist = {"easy": 0, "medium": 0, "hard": 0}
            
            for query, query_examples in query_groups.items():
                unique_positives = set(ex.positive for ex in query_examples)
                unique_negatives = set(ex.negative for ex in query_examples)
                
                positives_per_query.append(len(unique_positives))
                negatives_per_query.append(len(unique_negatives))
                
                for example in query_examples:
                    difficulty_dist[example.difficulty] += 1
            
            avg_positives = sum(positives_per_query) / len(positives_per_query) if positives_per_query else 0
            avg_negatives = sum(negatives_per_query) / len(negatives_per_query) if negatives_per_query else 0
            
            # 计算质量分数（简化）
            hard_ratio = difficulty_dist["hard"] / total_examples if total_examples > 0 else 0
            quality_score = min(1.0, hard_ratio * 2 + 0.5)  # 困难样本比例越高质量越好
            
            return DatasetStats(
                total_examples=total_examples,
                queries_count=queries_count,
                avg_positives_per_query=avg_positives,
                avg_negatives_per_query=avg_negatives,
                difficulty_distribution=difficulty_dist,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return DatasetStats(0, 0, 0, 0, {}, 0)
    
    async def _save_stats(self, stats: DatasetStats, output_path: Path):
        """保存统计信息"""
        try:
            stats_file = output_path / "dataset_stats.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(stats), f, indent=2, ensure_ascii=False)
            
            logger.info(f"统计信息保存到: {stats_file}")
            
        except Exception as e:
            logger.error(f"保存统计信息失败: {e}")


# 创建全局实例
contrastive_data_builder = ContrastiveDataBuilder()
