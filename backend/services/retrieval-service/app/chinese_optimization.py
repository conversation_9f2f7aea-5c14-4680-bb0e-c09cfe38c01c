"""
中文特定优化模块
包含中文分词优化、语义角色标注、实体消歧等功能
"""

import asyncio
import re
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
import jieba
import jieba.posseg as pseg

# 尝试导入其他中文处理库
try:
    import pkuseg
    HAS_PKUSEG = True
except ImportError:
    HAS_PKUSEG = False
    logger.warning("pkuseg未安装，将使用jieba作为备选")

try:
    import thulac
    HAS_THULAC = True
except ImportError:
    HAS_THULAC = False
    logger.warning("thulac未安装，将使用jieba作为备选")

from .config import settings
from .enhanced_retrieval import SearchResult
from .redis_client import retrieval_cache


@dataclass
class SegmentationResult:
    """分词结果"""
    words: List[str]
    pos_tags: List[str]  # 词性标注
    confidence: float
    method: str


@dataclass
class SemanticRole:
    """语义角色"""
    role: str  # A0, A1, A2, TMP, LOC, MNR等
    content: str
    weight: float


@dataclass
class EntityCandidate:
    """实体候选"""
    entity: str
    candidate_id: str
    confidence: float
    context_match: float
    popularity: float


class ChineseSegmentationOptimizer:
    """中文分词优化器"""

    def __init__(self):
        # 集成权重配置
        self.ensemble_weights = {
            "jieba": 0.4,
            "pkuseg": 0.3,
            "thulac": 0.3
        }

        # 领域词汇
        self.domain_vocabulary = {
            "technology": [
                "机器学习", "深度学习", "神经网络", "算法", "数据结构",
                "人工智能", "自然语言处理", "计算机视觉", "大数据", "云计算"
            ],
            "business": [
                "市场营销", "商业模式", "企业管理", "财务分析", "供应链",
                "客户关系", "品牌建设", "战略规划", "风险管理", "投资回报"
            ],
            "science": [
                "物理学", "化学", "生物学", "数学", "统计学",
                "实验设计", "数据分析", "科学方法", "研究方法", "学术论文"
            ]
        }

        # 质量控制参数
        self.min_confidence = 0.5
        self.max_word_length = 10
        self.cache_enabled = True

        # 初始化分词器
        self.segmenters = {}
        self._initialize_segmenters()

    def _initialize_segmenters(self):
        """初始化各种分词器"""
        try:
            # 初始化jieba
            jieba.initialize()
            self.segmenters["jieba"] = "jieba"
            logger.info("jieba分词器初始化成功")

            # 初始化pkuseg
            if HAS_PKUSEG:
                try:
                    self.segmenters["pkuseg"] = pkuseg.pkuseg()
                    logger.info("pkuseg分词器初始化成功")
                except Exception as e:
                    logger.warning(f"pkuseg初始化失败: {e}")

            # 初始化thulac
            if HAS_THULAC:
                try:
                    self.segmenters["thulac"] = thulac.thulac(seg_only=True)
                    logger.info("thulac分词器初始化成功")
                except Exception as e:
                    logger.warning(f"thulac初始化失败: {e}")

            # 加载领域词汇到jieba
            self._load_domain_vocabulary()

        except Exception as e:
            logger.error(f"分词器初始化失败: {e}")

    def _load_domain_vocabulary(self):
        """加载领域词汇到jieba"""
        try:
            for domain, words in self.domain_vocabulary.items():
                for word in words:
                    jieba.add_word(word, freq=1000, tag=f"domain_{domain}")

            logger.info("领域词汇加载完成")

        except Exception as e:
            logger.error(f"领域词汇加载失败: {e}")

    async def optimized_segmentation(self, text: str, context: str = "") -> SegmentationResult:
        """
        优化的中文分词

        Args:
            text: 待分词文本
            context: 上下文信息

        Returns:
            分词结果
        """
        try:
            logger.debug(f"开始中文分词优化: {text[:50]}...")

            # 检查缓存
            if self.cache_enabled:
                cache_key = f"seg:{hash(text)}:{hash(context)}"
                cached_result = await retrieval_cache.get(cache_key)
                if cached_result:
                    import json
                    data = json.loads(cached_result)
                    return SegmentationResult(**data)

            # 预处理
            cleaned_text = self._preprocess_text(text)

            # 多分词器结果
            segmentation_results = {}

            # jieba分词
            jieba_result = await self._jieba_segment(cleaned_text, context)
            segmentation_results["jieba"] = jieba_result

            # pkuseg分词
            if "pkuseg" in self.segmenters:
                pkuseg_result = await self._pkuseg_segment(cleaned_text)
                segmentation_results["pkuseg"] = pkuseg_result

            # thulac分词
            if "thulac" in self.segmenters:
                thulac_result = await self._thulac_segment(cleaned_text)
                segmentation_results["thulac"] = thulac_result

            # 集成分词结果
            final_result = await self._ensemble_segmentation(segmentation_results, context)

            # 后处理
            final_result = await self._postprocess_segmentation(final_result, text)

            # 缓存结果
            if self.cache_enabled:
                import json
                await retrieval_cache.set(
                    cache_key,
                    json.dumps(final_result.__dict__),
                    ttl=3600
                )

            # 记录监控指标
            from .enhanced_monitoring import enhanced_monitoring
            enhanced_monitoring.record_chinese_optimization_metrics(
                final_result.confidence, 0.0, final_result.method
            )

            logger.debug(f"中文分词完成，方法: {final_result.method}，置信度: {final_result.confidence:.3f}")
            return final_result

        except Exception as e:
            logger.error(f"中文分词优化失败: {e}")
            # 返回简单分词结果
            words = text.split()
            return SegmentationResult(
                words=words,
                pos_tags=["n"] * len(words),
                confidence=0.1,
                method="fallback"
            )

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        try:
            # 1. 去除多余空白
            text = re.sub(r'\s+', ' ', text.strip())

            # 2. 处理标点符号
            text = re.sub(r'[，。！？；：""''（）【】《》]', ' ', text)

            # 3. 处理英文和数字
            text = re.sub(r'[a-zA-Z0-9]+', ' ', text)

            # 4. 去除过短的文本
            if len(text.strip()) < 2:
                return text

            return text.strip()

        except Exception as e:
            logger.error(f"文本预处理失败: {e}")
            return text

    async def _jieba_segment(self, text: str, context: str = "") -> SegmentationResult:
        """jieba分词"""
        try:
            # 使用精确模式分词
            words = list(jieba.cut(text, cut_all=False))

            # 词性标注
            pos_result = list(pseg.cut(text))
            pos_tags = [item.flag for item in pos_result]

            # 确保词和词性数量一致
            if len(words) != len(pos_tags):
                pos_tags = ["n"] * len(words)

            # 计算置信度
            confidence = self._calculate_jieba_confidence(words, context)

            return SegmentationResult(
                words=words,
                pos_tags=pos_tags,
                confidence=confidence,
                method="jieba"
            )

        except Exception as e:
            logger.error(f"jieba分词失败: {e}")
            return SegmentationResult([], [], 0.0, "jieba")

    async def _pkuseg_segment(self, text: str) -> SegmentationResult:
        """pkuseg分词"""
        try:
            if "pkuseg" not in self.segmenters:
                return SegmentationResult([], [], 0.0, "pkuseg")

            segmenter = self.segmenters["pkuseg"]
            words = segmenter.cut(text)

            # pkuseg没有直接的词性标注，使用默认
            pos_tags = ["n"] * len(words)

            # 计算置信度
            confidence = self._calculate_pkuseg_confidence(words)

            return SegmentationResult(
                words=words,
                pos_tags=pos_tags,
                confidence=confidence,
                method="pkuseg"
            )

        except Exception as e:
            logger.error(f"pkuseg分词失败: {e}")
            return SegmentationResult([], [], 0.0, "pkuseg")

    async def _thulac_segment(self, text: str) -> SegmentationResult:
        """thulac分词"""
        try:
            if "thulac" not in self.segmenters:
                return SegmentationResult([], [], 0.0, "thulac")

            segmenter = self.segmenters["thulac"]
            result = segmenter.cut(text, text=True)

            # 解析结果
            words = []
            pos_tags = []

            for item in result.split():
                if '_' in item:
                    word, pos = item.rsplit('_', 1)
                    words.append(word)
                    pos_tags.append(pos)
                else:
                    words.append(item)
                    pos_tags.append("n")

            # 计算置信度
            confidence = self._calculate_thulac_confidence(words)

            return SegmentationResult(
                words=words,
                pos_tags=pos_tags,
                confidence=confidence,
                method="thulac"
            )

        except Exception as e:
            logger.error(f"thulac分词失败: {e}")
            return SegmentationResult([], [], 0.0, "thulac")

    async def _ensemble_segmentation(self, results: Dict[str, SegmentationResult],
                                   context: str = "") -> SegmentationResult:
        """集成多个分词结果"""
        try:
            if not results:
                return SegmentationResult([], [], 0.0, "ensemble")

            # 过滤有效结果
            valid_results = {k: v for k, v in results.items() if v.words and v.confidence > 0}

            if not valid_results:
                # 使用第一个可用结果
                first_result = list(results.values())[0]
                return first_result

            # 选择最佳结果作为基础
            best_method = max(valid_results.keys(),
                            key=lambda k: valid_results[k].confidence * self.ensemble_weights.get(k, 0.1))
            base_result = valid_results[best_method]

            # 如果只有一个有效结果，直接返回
            if len(valid_results) == 1:
                base_result.method = f"ensemble_{best_method}"
                return base_result

            # 投票融合
            final_words = await self._vote_fusion(valid_results)

            # 生成词性标注
            final_pos_tags = await self._generate_pos_tags(final_words, valid_results)

            # 计算集成置信度
            ensemble_confidence = self._calculate_ensemble_confidence(valid_results)

            return SegmentationResult(
                words=final_words,
                pos_tags=final_pos_tags,
                confidence=ensemble_confidence,
                method=f"ensemble_{'+'.join(valid_results.keys())}"
            )

        except Exception as e:
            logger.error(f"集成分词失败: {e}")
            # 返回最佳单一结果
            if results:
                best_result = max(results.values(), key=lambda x: x.confidence)
                return best_result
            return SegmentationResult([], [], 0.0, "ensemble")

    async def _vote_fusion(self, results: Dict[str, SegmentationResult]) -> List[str]:
        """投票融合分词结果"""
        try:
            # 收集所有词
            all_words = []
            for method, result in results.items():
                weight = self.ensemble_weights.get(method, 0.1)
                for word in result.words:
                    all_words.append((word, weight * result.confidence))

            # 按词频和权重排序
            from collections import defaultdict
            word_scores = defaultdict(float)

            for word, score in all_words:
                word_scores[word] += score

            # 选择高分词汇
            sorted_words = sorted(word_scores.items(), key=lambda x: x[1], reverse=True)

            # 重新组合成合理的分词序列
            final_words = []
            used_chars = set()

            for word, score in sorted_words:
                # 检查是否与已选词重叠
                word_chars = set(word)
                if not (word_chars & used_chars):
                    final_words.append(word)
                    used_chars.update(word_chars)

            return final_words if final_words else [word for word, _ in sorted_words[:10]]

        except Exception as e:
            logger.error(f"投票融合失败: {e}")
            # 返回第一个结果的词
            first_result = list(results.values())[0]
            return first_result.words

    async def _generate_pos_tags(self, words: List[str],
                               results: Dict[str, SegmentationResult]) -> List[str]:
        """生成词性标注"""
        try:
            pos_tags = []

            for word in words:
                # 从各个结果中查找该词的词性
                word_pos_votes = []

                for method, result in results.items():
                    if word in result.words:
                        idx = result.words.index(word)
                        if idx < len(result.pos_tags):
                            pos = result.pos_tags[idx]
                            weight = self.ensemble_weights.get(method, 0.1)
                            word_pos_votes.append((pos, weight))

                if word_pos_votes:
                    # 选择权重最高的词性
                    best_pos = max(word_pos_votes, key=lambda x: x[1])[0]
                    pos_tags.append(best_pos)
                else:
                    # 默认词性
                    pos_tags.append("n")

            return pos_tags

        except Exception as e:
            logger.error(f"词性标注生成失败: {e}")
            return ["n"] * len(words)

    def _calculate_ensemble_confidence(self, results: Dict[str, SegmentationResult]) -> float:
        """计算集成置信度"""
        try:
            if not results:
                return 0.0

            # 加权平均置信度
            total_weight = 0
            weighted_confidence = 0

            for method, result in results.items():
                weight = self.ensemble_weights.get(method, 0.1)
                weighted_confidence += result.confidence * weight
                total_weight += weight

            if total_weight > 0:
                base_confidence = weighted_confidence / total_weight
            else:
                base_confidence = sum(r.confidence for r in results.values()) / len(results)

            # 多样性奖励：如果多个分词器结果一致，提高置信度
            consistency_bonus = self._calculate_consistency_bonus(results)

            final_confidence = min(1.0, base_confidence + consistency_bonus)
            return final_confidence

        except Exception as e:
            logger.error(f"集成置信度计算失败: {e}")
            return 0.5

    def _calculate_consistency_bonus(self, results: Dict[str, SegmentationResult]) -> float:
        """计算一致性奖励"""
        try:
            if len(results) < 2:
                return 0.0

            # 计算词汇重叠度
            all_words = [set(result.words) for result in results.values()]

            total_overlap = 0
            comparisons = 0

            for i in range(len(all_words)):
                for j in range(i + 1, len(all_words)):
                    overlap = len(all_words[i] & all_words[j])
                    union = len(all_words[i] | all_words[j])
                    if union > 0:
                        total_overlap += overlap / union
                        comparisons += 1

            if comparisons > 0:
                avg_overlap = total_overlap / comparisons
                return avg_overlap * 0.1  # 最多10%的奖励

            return 0.0

        except Exception as e:
            logger.error(f"一致性奖励计算失败: {e}")
            return 0.0

    def _calculate_jieba_confidence(self, words: List[str], context: str = "") -> float:
        """计算jieba分词置信度"""
        try:
            if not words:
                return 0.0

            confidence_factors = []

            # 1. 词长度分布
            avg_word_length = sum(len(word) for word in words) / len(words)
            length_score = min(1.0, avg_word_length / 2.0)  # 理想词长约2字
            confidence_factors.append(length_score)

            # 2. 领域词汇匹配
            domain_match_score = self._calculate_domain_match_score(words)
            confidence_factors.append(domain_match_score)

            # 3. 上下文一致性
            if context:
                context_score = self._calculate_context_consistency(words, context)
                confidence_factors.append(context_score)

            # 4. 词汇合理性
            rationality_score = self._calculate_word_rationality(words)
            confidence_factors.append(rationality_score)

            # 综合评分
            base_confidence = sum(confidence_factors) / len(confidence_factors)

            # jieba特定调整
            jieba_bonus = 0.1  # jieba是主要分词器，给予小幅奖励

            return min(1.0, base_confidence + jieba_bonus)

        except Exception as e:
            logger.error(f"jieba置信度计算失败: {e}")
            return 0.5

    def _calculate_pkuseg_confidence(self, words: List[str]) -> float:
        """计算pkuseg分词置信度"""
        try:
            if not words:
                return 0.0

            # pkuseg在学术文本上表现较好
            academic_keywords = ["研究", "分析", "方法", "理论", "实验", "数据", "模型"]
            academic_score = sum(1 for word in words if any(kw in word for kw in academic_keywords))
            academic_score = min(1.0, academic_score / len(words) * 5)

            # 基础评分
            base_score = 0.7  # pkuseg基础分数

            return min(1.0, base_score + academic_score * 0.2)

        except Exception as e:
            logger.error(f"pkuseg置信度计算失败: {e}")
            return 0.7

    def _calculate_thulac_confidence(self, words: List[str]) -> float:
        """计算thulac分词置信度"""
        try:
            if not words:
                return 0.0

            # thulac在新闻文本上表现较好
            news_keywords = ["报道", "消息", "新闻", "发布", "宣布", "表示", "认为"]
            news_score = sum(1 for word in words if any(kw in word for kw in news_keywords))
            news_score = min(1.0, news_score / len(words) * 5)

            # 基础评分
            base_score = 0.6  # thulac基础分数

            return min(1.0, base_score + news_score * 0.3)

        except Exception as e:
            logger.error(f"thulac置信度计算失败: {e}")
            return 0.6

    def _calculate_domain_match_score(self, words: List[str]) -> float:
        """计算领域词汇匹配分数"""
        try:
            if not words:
                return 0.0

            total_domain_words = 0
            for domain_words in self.domain_vocabulary.values():
                total_domain_words += len(domain_words)

            matched_words = 0
            for word in words:
                for domain_words in self.domain_vocabulary.values():
                    if word in domain_words:
                        matched_words += 1
                        break

            if total_domain_words > 0:
                match_ratio = matched_words / len(words)
                return min(1.0, match_ratio * 3)  # 放大匹配效果

            return 0.5  # 默认分数

        except Exception as e:
            logger.error(f"领域匹配分数计算失败: {e}")
            return 0.5

    def _calculate_context_consistency(self, words: List[str], context: str) -> float:
        """计算上下文一致性"""
        try:
            if not context or not words:
                return 0.5

            context_words = set(jieba.cut(context))
            word_set = set(words)

            # 计算词汇重叠
            overlap = len(word_set & context_words)
            union = len(word_set | context_words)

            if union > 0:
                consistency = overlap / union
                return consistency

            return 0.5

        except Exception as e:
            logger.error(f"上下文一致性计算失败: {e}")
            return 0.5

    def _calculate_word_rationality(self, words: List[str]) -> float:
        """计算词汇合理性"""
        try:
            if not words:
                return 0.0

            rational_count = 0

            for word in words:
                # 检查词长度
                if 1 <= len(word) <= self.max_word_length:
                    rational_count += 1

                # 检查是否包含合理字符
                if re.match(r'^[\u4e00-\u9fff]+$', word):  # 只包含中文字符
                    rational_count += 0.5

            rationality = rational_count / len(words)
            return min(1.0, rationality)

        except Exception as e:
            logger.error(f"词汇合理性计算失败: {e}")
            return 0.5

    async def _postprocess_segmentation(self, result: SegmentationResult,
                                      original_text: str) -> SegmentationResult:
        """后处理分词结果"""
        try:
            # 1. 过滤过短和过长的词
            filtered_words = []
            filtered_pos = []

            for i, word in enumerate(result.words):
                if 1 <= len(word) <= self.max_word_length:
                    filtered_words.append(word)
                    if i < len(result.pos_tags):
                        filtered_pos.append(result.pos_tags[i])
                    else:
                        filtered_pos.append("n")

            # 2. 去重（保持顺序）
            seen = set()
            unique_words = []
            unique_pos = []

            for i, word in enumerate(filtered_words):
                if word not in seen:
                    seen.add(word)
                    unique_words.append(word)
                    if i < len(filtered_pos):
                        unique_pos.append(filtered_pos[i])
                    else:
                        unique_pos.append("n")

            # 3. 质量检查
            if len(unique_words) == 0:
                # 如果没有有效词，使用字符级分割
                unique_words = list(original_text.replace(" ", ""))
                unique_pos = ["n"] * len(unique_words)
                result.confidence = 0.1

            # 4. 置信度调整
            if result.confidence < self.min_confidence:
                result.confidence = self.min_confidence

            return SegmentationResult(
                words=unique_words,
                pos_tags=unique_pos,
                confidence=result.confidence,
                method=result.method
            )

        except Exception as e:
            logger.error(f"分词后处理失败: {e}")
            return result
    
    def __init__(self):
        # 初始化分词器
        self.segmenters = {}
        self._init_segmenters()
        
        # 集成权重
        self.ensemble_weights = {
            "jieba": 0.4,
            "pkuseg": 0.3 if HAS_PKUSEG else 0.0,
            "thulac": 0.3 if HAS_THULAC else 0.0
        }
        
        # 归一化权重
        total_weight = sum(self.ensemble_weights.values())
        if total_weight > 0:
            self.ensemble_weights = {
                k: v / total_weight for k, v in self.ensemble_weights.items()
            }
        
        # 领域词典
        self.domain_vocab = self._load_domain_vocabulary()
        
        # 缓存
        self.cache_ttl = 3600  # 1小时
    
    def _init_segmenters(self):
        """初始化分词器"""
        try:
            # jieba分词器
            jieba.initialize()
            self.segmenters["jieba"] = jieba
            
            # pkuseg分词器
            if HAS_PKUSEG:
                self.segmenters["pkuseg"] = pkuseg.pkuseg()
            
            # thulac分词器
            if HAS_THULAC:
                self.segmenters["thulac"] = thulac.thulac(seg_only=True)
            
            logger.info(f"初始化了 {len(self.segmenters)} 个分词器")
            
        except Exception as e:
            logger.error(f"分词器初始化失败: {e}")
    
    def _load_domain_vocabulary(self) -> Dict[str, Set[str]]:
        """加载领域词汇"""
        # 简化的领域词汇，实际应该从文件或数据库加载
        return {
            "technology": {
                "机器学习", "深度学习", "神经网络", "算法", "数据结构",
                "编程语言", "软件工程", "系统架构", "数据库", "云计算"
            },
            "business": {
                "市场营销", "商业模式", "企业管理", "财务分析", "投资理财",
                "供应链", "客户关系", "品牌建设", "战略规划", "风险管理"
            },
            "science": {
                "物理学", "化学", "生物学", "数学", "天文学",
                "地理学", "心理学", "医学", "环境科学", "材料科学"
            }
        }
    
    async def optimized_segmentation(self, text: str, context: str = "") -> SegmentationResult:
        """
        优化的中文分词
        
        Args:
            text: 待分词文本
            context: 上下文信息
            
        Returns:
            分词结果
        """
        try:
            # 检查缓存
            cache_key = f"seg:{hash(text + context) % 1000000}"
            cached_result = await retrieval_cache.get(cache_key)
            if cached_result:
                return SegmentationResult(**cached_result)
            
            logger.debug(f"开始优化分词: {text[:50]}...")
            
            # 1. 多分词器结果
            seg_results = await self._multi_segmenter_results(text)
            
            # 2. 上下文感知调整
            if context:
                seg_results = await self._context_aware_adjustment(
                    text, seg_results, context
                )
            
            # 3. 投票融合
            final_words = self._ensemble_segmentation(text, seg_results)
            
            # 4. 词性标注
            pos_tags = await self._pos_tagging(final_words)
            
            # 5. 计算置信度
            confidence = self._calculate_confidence(seg_results, final_words)
            
            result = SegmentationResult(
                words=final_words,
                pos_tags=pos_tags,
                confidence=confidence,
                method="ensemble"
            )
            
            # 缓存结果
            await retrieval_cache.set(
                cache_key, 
                result.__dict__, 
                ttl=self.cache_ttl
            )
            
            return result
            
        except Exception as e:
            logger.error(f"优化分词失败: {e}")
            # 回退到jieba分词
            words = list(jieba.cut(text))
            return SegmentationResult(
                words=words,
                pos_tags=["n"] * len(words),  # 默认词性
                confidence=0.5,
                method="fallback"
            )
    
    async def _multi_segmenter_results(self, text: str) -> Dict[str, List[str]]:
        """获取多分词器结果"""
        results = {}
        
        for name, segmenter in self.segmenters.items():
            try:
                if name == "jieba":
                    words = list(segmenter.cut(text))
                elif name == "pkuseg":
                    words = segmenter.cut(text)
                elif name == "thulac":
                    words = [word for word, _ in segmenter.cut(text)]
                else:
                    continue
                
                results[name] = words
                
            except Exception as e:
                logger.error(f"{name}分词失败: {e}")
                continue
        
        return results
    
    async def _context_aware_adjustment(self, text: str, seg_results: Dict[str, List[str]], 
                                      context: str) -> Dict[str, List[str]]:
        """上下文感知调整"""
        try:
            # 1. 分析上下文领域
            domain = self._identify_domain(context)
            
            # 2. 获取领域词汇
            domain_words = self.domain_vocab.get(domain, set())
            
            # 3. 调整分词结果
            adjusted_results = {}
            
            for method, words in seg_results.items():
                adjusted_words = self._adjust_with_domain_vocab(text, words, domain_words)
                adjusted_results[method] = adjusted_words
            
            return adjusted_results
            
        except Exception as e:
            logger.error(f"上下文调整失败: {e}")
            return seg_results
    
    def _identify_domain(self, context: str) -> str:
        """识别文本领域"""
        domain_scores = {}
        
        for domain, vocab in self.domain_vocab.items():
            score = 0
            for word in vocab:
                if word in context:
                    score += 1
            
            if vocab:  # 避免除零
                domain_scores[domain] = score / len(vocab)
        
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        
        return "general"
    
    def _adjust_with_domain_vocab(self, text: str, words: List[str], 
                                 domain_words: Set[str]) -> List[str]:
        """使用领域词汇调整分词"""
        adjusted_words = []
        i = 0
        
        while i < len(words):
            # 检查是否可以合并成领域词汇
            merged = False
            
            for length in range(min(4, len(words) - i), 0, -1):  # 最多合并4个词
                candidate = "".join(words[i:i+length])
                
                if candidate in domain_words:
                    adjusted_words.append(candidate)
                    i += length
                    merged = True
                    break
            
            if not merged:
                adjusted_words.append(words[i])
                i += 1
        
        return adjusted_words
    
    def _ensemble_segmentation(self, text: str, seg_results: Dict[str, List[str]]) -> List[str]:
        """集成分词结果"""
        if not seg_results:
            return list(jieba.cut(text))
        
        # 1. 收集所有可能的词
        word_votes = {}
        
        for method, words in seg_results.items():
            weight = self.ensemble_weights.get(method, 0.0)
            
            # 重构词的位置信息
            pos = 0
            for word in words:
                start_pos = text.find(word, pos)
                if start_pos != -1:
                    end_pos = start_pos + len(word)
                    word_key = (start_pos, end_pos, word)
                    
                    if word_key not in word_votes:
                        word_votes[word_key] = 0
                    word_votes[word_key] += weight
                    
                    pos = end_pos
        
        # 2. 基于投票结果重构分词
        sorted_words = sorted(word_votes.items(), key=lambda x: (x[0][0], -x[1]))
        
        final_words = []
        last_end = 0
        
        for (start, end, word), score in sorted_words:
            if start >= last_end:  # 无重叠
                final_words.append(word)
                last_end = end
        
        return final_words if final_words else list(jieba.cut(text))
    
    async def _pos_tagging(self, words: List[str]) -> List[str]:
        """词性标注"""
        try:
            # 使用jieba的词性标注
            pos_tags = []
            for word in words:
                # 简化的词性标注
                tagged = list(pseg.cut(word))
                if tagged:
                    pos_tags.append(tagged[0].flag)
                else:
                    pos_tags.append("n")  # 默认名词
            
            return pos_tags
            
        except Exception as e:
            logger.error(f"词性标注失败: {e}")
            return ["n"] * len(words)  # 默认都是名词
    
    def _calculate_confidence(self, seg_results: Dict[str, List[str]], 
                            final_words: List[str]) -> float:
        """计算分词置信度"""
        if not seg_results or not final_words:
            return 0.5
        
        # 计算与各分词器结果的一致性
        consistency_scores = []
        
        for method, words in seg_results.items():
            # 简化的一致性计算：词汇重叠率
            final_set = set(final_words)
            method_set = set(words)
            
            if final_set or method_set:
                overlap = len(final_set.intersection(method_set))
                union = len(final_set.union(method_set))
                consistency = overlap / union if union > 0 else 0
                
                weight = self.ensemble_weights.get(method, 0.0)
                consistency_scores.append(consistency * weight)
        
        return sum(consistency_scores) if consistency_scores else 0.5


class ChineseSemanticRoleLabeler:
    """中文语义角色标注器"""

    def __init__(self):
        # 角色权重配置
        self.role_weights = {
            "A0": 1.0,   # 施事（主语）
            "A1": 0.9,   # 受事（宾语）
            "A2": 0.7,   # 与事（间接宾语）
            "TMP": 0.6,  # 时间
            "LOC": 0.6,  # 地点
            "MNR": 0.5,  # 方式
            "PRP": 0.5,  # 目的
            "CAU": 0.5,  # 原因
            "EXT": 0.4,  # 程度
            "DIR": 0.4   # 方向
        }

        # 质量控制参数
        self.min_confidence = 0.3
        self.max_roles_per_sentence = 6

        # 语义角色模式
        self.role_patterns = self._initialize_role_patterns()

        # 谓词词典
        self.predicate_dict = self._initialize_predicate_dict()

    def _initialize_role_patterns(self) -> Dict[str, List[str]]:
        """初始化语义角色模式"""
        return {
            "A0": [  # 施事模式
                r"(.+)(?:进行|执行|完成|实现|开展)",
                r"(.+)(?:研究|分析|探讨|调查)",
                r"(.+)(?:提出|建议|认为|表示)",
                r"(.+)(?:发现|发明|创造|开发)",
                r"(.+)(?:管理|领导|指导|负责)"
            ],
            "A1": [  # 受事模式
                r"(?:进行|执行|完成|实现|开展)(.+)",
                r"(?:研究|分析|探讨|调查)(.+)",
                r"(?:解决|处理|应对|克服)(.+)",
                r"(?:获得|取得|达到|实现)(.+)",
                r"(?:使用|采用|运用|应用)(.+)"
            ],
            "TMP": [  # 时间模式
                r"(?:在|于|当|自|从|到|至)(.+?)(?:时|期间|之前|之后|以来)",
                r"(.+?)(?:年|月|日|时|分|秒)",
                r"(?:今天|昨天|明天|现在|过去|未来|最近|目前)",
                r"(.+?)(?:阶段|时期|期间|过程中)"
            ],
            "LOC": [  # 地点模式
                r"(?:在|于|到|从|向)(.+?)(?:地方|地区|区域|位置)",
                r"(.+?)(?:市|省|县|区|镇|村|街|路|号)",
                r"(?:国内|国外|海外|境内|境外|本地|外地)",
                r"(.+?)(?:公司|学校|医院|银行|商店|工厂)"
            ],
            "MNR": [  # 方式模式
                r"(?:通过|采用|使用|运用|借助|依靠)(.+?)(?:方式|方法|手段|途径)",
                r"(?:以|用|凭借)(.+?)(?:进行|实现|完成)",
                r"(.+?)(?:地|的方式|的方法|的手段)"
            ],
            "PRP": [  # 目的模式
                r"(?:为了|为|以便|以期|旨在)(.+)",
                r"(.+?)(?:目的|目标|意图|用途)",
                r"(?:希望|期望|打算|计划)(.+)"
            ]
        }

    def _initialize_predicate_dict(self) -> Dict[str, List[str]]:
        """初始化谓词词典"""
        return {
            "action": [  # 动作类谓词
                "进行", "执行", "完成", "实现", "开展", "实施", "推进",
                "研究", "分析", "探讨", "调查", "考察", "观察", "检查",
                "解决", "处理", "应对", "克服", "改善", "优化", "提升",
                "创建", "建立", "构建", "开发", "设计", "制作", "生产"
            ],
            "cognitive": [  # 认知类谓词
                "认为", "觉得", "相信", "怀疑", "猜测", "推测", "判断",
                "理解", "明白", "知道", "了解", "掌握", "学习", "记住",
                "思考", "考虑", "琢磨", "反思", "总结", "归纳", "推理"
            ],
            "communication": [  # 交流类谓词
                "说", "讲", "谈", "聊", "讨论", "交流", "沟通",
                "告诉", "通知", "报告", "汇报", "介绍", "说明", "解释",
                "询问", "请教", "咨询", "问", "回答", "答复", "回应"
            ],
            "change": [  # 变化类谓词
                "变化", "改变", "转变", "变成", "成为", "变为",
                "增加", "减少", "提高", "降低", "上升", "下降",
                "扩大", "缩小", "增长", "衰减", "发展", "退化"
            ]
        }

    async def analyze_semantic_roles(self, text: str) -> Dict[str, Any]:
        """
        分析语义角色

        Args:
            text: 待分析文本

        Returns:
            语义角色分析结果
        """
        try:
            logger.debug(f"开始语义角色分析: {text[:50]}...")

            # 1. 预处理和分句
            sentences = self._split_sentences(text)

            # 2. 分析每个句子
            sentence_analyses = []
            for sentence in sentences:
                analysis = await self._analyze_sentence_roles(sentence)
                if analysis:
                    sentence_analyses.append(analysis)

            # 3. 合并分析结果
            merged_analysis = await self._merge_sentence_analyses(sentence_analyses)

            # 4. 提取语义焦点
            semantic_focus = await self._extract_semantic_focus(merged_analysis)

            # 5. 推断查询意图
            query_intent = await self._infer_query_intent(merged_analysis, text)

            # 6. 计算置信度
            confidence = self._calculate_srl_confidence(merged_analysis)

            result = {
                "predicate": merged_analysis.get("predicate", ""),
                "roles": merged_analysis.get("roles", {}),
                "semantic_focus": semantic_focus,
                "query_intent": query_intent,
                "confidence": confidence,
                "sentence_count": len(sentences)
            }

            # 记录监控指标
            from .enhanced_monitoring import enhanced_monitoring
            enhanced_monitoring.record_chinese_optimization_metrics(
                0.0, confidence, "srl", query_intent
            )

            logger.debug(f"语义角色分析完成，置信度: {confidence:.3f}")
            return result

        except Exception as e:
            logger.error(f"语义角色分析失败: {e}")
            return {
                "predicate": "",
                "roles": {},
                "semantic_focus": "",
                "query_intent": "unknown",
                "confidence": 0.0,
                "sentence_count": 0
            }

    def _split_sentences(self, text: str) -> List[str]:
        """分句"""
        try:
            # 简单分句规则
            sentences = re.split(r'[。！？；]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            return sentences

        except Exception as e:
            logger.error(f"分句失败: {e}")
            return [text]

    async def _analyze_sentence_roles(self, sentence: str) -> Dict[str, Any]:
        """分析单个句子的语义角色"""
        try:
            if len(sentence) < 3:
                return {}

            # 1. 识别谓词
            predicate = self._identify_predicate(sentence)
            if not predicate:
                return {}

            # 2. 基于模式匹配识别角色
            roles = {}

            for role_type, patterns in self.role_patterns.items():
                role_content = self._match_role_patterns(sentence, patterns)
                if role_content:
                    roles[role_type] = {
                        "content": role_content,
                        "weight": self.role_weights.get(role_type, 0.5),
                        "confidence": self._calculate_role_confidence(role_content, role_type)
                    }

            # 3. 基于依存关系的角色识别（简化版）
            dependency_roles = await self._identify_dependency_roles(sentence, predicate)

            # 4. 合并角色
            merged_roles = self._merge_roles(roles, dependency_roles)

            return {
                "sentence": sentence,
                "predicate": predicate,
                "roles": merged_roles
            }

        except Exception as e:
            logger.error(f"句子角色分析失败: {e}")
            return {}

    def _identify_predicate(self, sentence: str) -> str:
        """识别谓词"""
        try:
            # 分词
            words = list(jieba.cut(sentence))

            # 查找谓词
            for word in words:
                for pred_type, pred_list in self.predicate_dict.items():
                    if word in pred_list:
                        return word

            # 如果没找到，使用词性标注查找动词
            pos_result = list(pseg.cut(sentence))
            for word, pos in pos_result:
                if pos.startswith('v'):  # 动词
                    return word

            return ""

        except Exception as e:
            logger.error(f"谓词识别失败: {e}")
            return ""

    def _match_role_patterns(self, sentence: str, patterns: List[str]) -> str:
        """匹配角色模式"""
        try:
            for pattern in patterns:
                match = re.search(pattern, sentence)
                if match:
                    # 返回第一个捕获组
                    if match.groups():
                        content = match.group(1).strip()
                        if content and len(content) > 0:
                            return content

            return ""

        except Exception as e:
            logger.error(f"角色模式匹配失败: {e}")
            return ""

    async def _identify_dependency_roles(self, sentence: str, predicate: str) -> Dict[str, Any]:
        """基于依存关系识别角色（简化版）"""
        try:
            if not predicate:
                return {}

            # 简化的依存关系分析
            words = list(jieba.cut(sentence))

            if predicate not in words:
                return {}

            pred_index = words.index(predicate)
            roles = {}

            # 简单规则：谓词前的词可能是施事，谓词后的词可能是受事
            if pred_index > 0:
                # 施事（A0）
                agent_words = words[:pred_index]
                if agent_words:
                    agent_content = "".join(agent_words[-2:])  # 取前面最多2个词
                    if len(agent_content) > 0:
                        roles["A0"] = {
                            "content": agent_content,
                            "weight": self.role_weights.get("A0", 1.0),
                            "confidence": 0.6
                        }

            if pred_index < len(words) - 1:
                # 受事（A1）
                patient_words = words[pred_index + 1:]
                if patient_words:
                    patient_content = "".join(patient_words[:2])  # 取后面最多2个词
                    if len(patient_content) > 0:
                        roles["A1"] = {
                            "content": patient_content,
                            "weight": self.role_weights.get("A1", 0.9),
                            "confidence": 0.6
                        }

            return roles

        except Exception as e:
            logger.error(f"依存关系角色识别失败: {e}")
            return {}

    def _merge_roles(self, pattern_roles: Dict[str, Any],
                    dependency_roles: Dict[str, Any]) -> Dict[str, Any]:
        """合并角色识别结果"""
        try:
            merged = {}

            # 优先使用模式匹配结果
            for role_type, role_info in pattern_roles.items():
                merged[role_type] = role_info

            # 补充依存关系结果
            for role_type, role_info in dependency_roles.items():
                if role_type not in merged:
                    merged[role_type] = role_info
                else:
                    # 如果已存在，比较置信度
                    if role_info["confidence"] > merged[role_type]["confidence"]:
                        merged[role_type] = role_info

            return merged

        except Exception as e:
            logger.error(f"角色合并失败: {e}")
            return pattern_roles

    def _calculate_role_confidence(self, content: str, role_type: str) -> float:
        """计算角色置信度"""
        try:
            if not content:
                return 0.0

            # 基础置信度
            base_confidence = 0.5

            # 长度因子
            length_factor = min(1.0, len(content) / 5.0)

            # 角色类型因子
            type_factor = self.role_weights.get(role_type, 0.5)

            # 内容质量因子
            quality_factor = self._assess_content_quality(content, role_type)

            confidence = base_confidence * length_factor * type_factor * quality_factor
            return min(1.0, confidence)

        except Exception as e:
            logger.error(f"角色置信度计算失败: {e}")
            return 0.5

    def _assess_content_quality(self, content: str, role_type: str) -> float:
        """评估内容质量"""
        try:
            quality_score = 1.0

            # 检查是否包含无意义字符
            if re.search(r'[^\u4e00-\u9fff\w\s]', content):
                quality_score *= 0.8

            # 检查长度合理性
            if len(content) < 1 or len(content) > 20:
                quality_score *= 0.7

            # 角色特定检查
            if role_type == "TMP":  # 时间角色
                if re.search(r'[年月日时分秒]|时间|期间|阶段', content):
                    quality_score *= 1.2
            elif role_type == "LOC":  # 地点角色
                if re.search(r'[市省县区镇村街路号]|地方|地区|位置', content):
                    quality_score *= 1.2

            return min(1.0, quality_score)

        except Exception as e:
            logger.error(f"内容质量评估失败: {e}")
            return 1.0

    async def _merge_sentence_analyses(self, analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并句子分析结果"""
        try:
            if not analyses:
                return {}

            # 选择最重要的谓词
            main_predicate = ""
            max_importance = 0

            for analysis in analyses:
                predicate = analysis.get("predicate", "")
                if predicate:
                    importance = self._calculate_predicate_importance(predicate)
                    if importance > max_importance:
                        max_importance = importance
                        main_predicate = predicate

            # 合并所有角色
            merged_roles = {}

            for analysis in analyses:
                roles = analysis.get("roles", {})
                for role_type, role_info in roles.items():
                    if role_type not in merged_roles:
                        merged_roles[role_type] = role_info
                    else:
                        # 选择置信度更高的
                        if role_info["confidence"] > merged_roles[role_type]["confidence"]:
                            merged_roles[role_type] = role_info

            return {
                "predicate": main_predicate,
                "roles": merged_roles
            }

        except Exception as e:
            logger.error(f"句子分析合并失败: {e}")
            return {}

    def _calculate_predicate_importance(self, predicate: str) -> float:
        """计算谓词重要性"""
        try:
            # 基于谓词类型计算重要性
            importance_scores = {
                "action": 1.0,
                "cognitive": 0.8,
                "communication": 0.7,
                "change": 0.9
            }

            for pred_type, pred_list in self.predicate_dict.items():
                if predicate in pred_list:
                    return importance_scores.get(pred_type, 0.5)

            return 0.5  # 默认重要性

        except Exception as e:
            logger.error(f"谓词重要性计算失败: {e}")
            return 0.5

    async def _extract_semantic_focus(self, analysis: Dict[str, Any]) -> str:
        """提取语义焦点"""
        try:
            if not analysis:
                return ""

            predicate = analysis.get("predicate", "")
            roles = analysis.get("roles", {})

            # 构建语义焦点
            focus_parts = []

            # 添加施事
            if "A0" in roles:
                focus_parts.append(roles["A0"]["content"])

            # 添加谓词
            if predicate:
                focus_parts.append(predicate)

            # 添加受事
            if "A1" in roles:
                focus_parts.append(roles["A1"]["content"])

            # 添加重要的附加角色
            for role_type in ["TMP", "LOC", "MNR"]:
                if role_type in roles and roles[role_type]["weight"] > 0.6:
                    focus_parts.append(roles[role_type]["content"])

            semantic_focus = " ".join(focus_parts)
            return semantic_focus.strip()

        except Exception as e:
            logger.error(f"语义焦点提取失败: {e}")
            return ""

    async def _infer_query_intent(self, analysis: Dict[str, Any], original_text: str) -> str:
        """推断查询意图"""
        try:
            predicate = analysis.get("predicate", "")
            roles = analysis.get("roles", {})

            # 基于谓词类型推断意图
            if predicate:
                for pred_type, pred_list in self.predicate_dict.items():
                    if predicate in pred_list:
                        if pred_type == "action":
                            return "action_inquiry"  # 行动查询
                        elif pred_type == "cognitive":
                            return "knowledge_seeking"  # 知识寻求
                        elif pred_type == "communication":
                            return "information_exchange"  # 信息交换
                        elif pred_type == "change":
                            return "status_inquiry"  # 状态查询

            # 基于角色推断意图
            if "TMP" in roles:
                return "temporal_inquiry"  # 时间查询
            elif "LOC" in roles:
                return "location_inquiry"  # 地点查询
            elif "MNR" in roles:
                return "method_inquiry"  # 方法查询

            # 基于疑问词推断
            question_words = ["什么", "怎么", "如何", "为什么", "哪里", "什么时候", "谁"]
            for word in question_words:
                if word in original_text:
                    if word in ["什么"]:
                        return "definition_inquiry"
                    elif word in ["怎么", "如何"]:
                        return "method_inquiry"
                    elif word in ["为什么"]:
                        return "reason_inquiry"
                    elif word in ["哪里"]:
                        return "location_inquiry"
                    elif word in ["什么时候"]:
                        return "temporal_inquiry"
                    elif word in ["谁"]:
                        return "entity_inquiry"

            return "general_inquiry"  # 一般查询

        except Exception as e:
            logger.error(f"查询意图推断失败: {e}")
            return "unknown"

    def _calculate_srl_confidence(self, analysis: Dict[str, Any]) -> float:
        """计算语义角色标注置信度"""
        try:
            if not analysis:
                return 0.0

            predicate = analysis.get("predicate", "")
            roles = analysis.get("roles", {})

            # 基础置信度
            base_confidence = 0.3

            # 谓词存在奖励
            if predicate:
                base_confidence += 0.3

            # 角色数量和质量
            if roles:
                role_score = 0
                total_weight = 0

                for role_type, role_info in roles.items():
                    role_confidence = role_info.get("confidence", 0.5)
                    role_weight = role_info.get("weight", 0.5)

                    role_score += role_confidence * role_weight
                    total_weight += role_weight

                if total_weight > 0:
                    avg_role_score = role_score / total_weight
                    base_confidence += avg_role_score * 0.4

            return min(1.0, base_confidence)

        except Exception as e:
            logger.error(f"SRL置信度计算失败: {e}")
            return 0.3
    
    def __init__(self):
        self.role_weights = {
            "A0": 1.0,  # 施事（主语）
            "A1": 0.9,  # 受事（宾语）
            "A2": 0.7,  # 与事（间接宾语）
            "TMP": 0.6, # 时间
            "LOC": 0.6, # 地点
            "MNR": 0.5, # 方式
            "PRP": 0.4, # 目的
            "CND": 0.4  # 条件
        }
        
        # 简化的角色识别模式
        self.role_patterns = {
            "A0": ["主体", "执行者", "发起者"],
            "A1": ["对象", "目标", "结果"],
            "TMP": ["时间", "何时", "什么时候"],
            "LOC": ["地点", "哪里", "位置"],
            "MNR": ["方式", "如何", "怎么样"],
            "PRP": ["目的", "为了", "目标"]
        }
    
    async def analyze_semantic_roles(self, sentence: str) -> Dict[str, Any]:
        """
        分析语义角色
        
        Args:
            sentence: 输入句子
            
        Returns:
            语义角色分析结果
        """
        try:
            logger.debug(f"分析语义角色: {sentence[:50]}...")
            
            # 1. 分词和词性标注
            segmenter = ChineseSegmentationOptimizer()
            seg_result = await segmenter.optimized_segmentation(sentence)
            
            # 2. 识别谓词
            predicate = self._identify_predicate(seg_result.words, seg_result.pos_tags)
            
            # 3. 识别语义角色
            roles = await self._identify_semantic_roles(sentence, seg_result.words, predicate)
            
            # 4. 提取语义焦点
            semantic_focus = self._extract_semantic_focus(roles)
            
            # 5. 推断查询意图
            query_intent = await self._infer_query_intent(sentence, roles)
            
            return {
                "predicate": predicate,
                "roles": roles,
                "semantic_focus": semantic_focus,
                "query_intent": query_intent,
                "confidence": self._calculate_srl_confidence(roles)
            }
            
        except Exception as e:
            logger.error(f"语义角色分析失败: {e}")
            return {
                "predicate": "",
                "roles": {},
                "semantic_focus": [],
                "query_intent": "unknown",
                "confidence": 0.0
            }
    
    def _identify_predicate(self, words: List[str], pos_tags: List[str]) -> str:
        """识别谓词"""
        # 寻找动词作为谓词
        for word, pos in zip(words, pos_tags):
            if pos.startswith('v'):  # 动词
                return word
        
        # 如果没有动词，寻找形容词
        for word, pos in zip(words, pos_tags):
            if pos.startswith('a'):  # 形容词
                return word
        
        return ""
    
    async def _identify_semantic_roles(self, sentence: str, words: List[str], 
                                     predicate: str) -> Dict[str, SemanticRole]:
        """识别语义角色"""
        roles = {}
        
        # 简化的角色识别
        sentence_lower = sentence.lower()
        
        for role, patterns in self.role_patterns.items():
            for pattern in patterns:
                if pattern in sentence_lower:
                    # 提取该角色的内容
                    content = self._extract_role_content(sentence, pattern, predicate)
                    if content:
                        roles[role] = SemanticRole(
                            role=role,
                            content=content,
                            weight=self.role_weights.get(role, 0.5)
                        )
                        break
        
        return roles
    
    def _extract_role_content(self, sentence: str, pattern: str, predicate: str) -> str:
        """提取角色内容"""
        # 简化的内容提取
        # 实际应该使用更复杂的句法分析
        
        pattern_pos = sentence.find(pattern)
        if pattern_pos == -1:
            return ""
        
        # 提取模式周围的词汇
        words = sentence.split()
        pattern_words = pattern.split()
        
        for i, word in enumerate(words):
            if word in pattern_words:
                # 提取前后的词汇作为角色内容
                start = max(0, i - 2)
                end = min(len(words), i + 3)
                content_words = words[start:end]
                
                # 过滤掉模式词本身
                filtered_words = [w for w in content_words if w not in pattern_words]
                return " ".join(filtered_words)
        
        return ""
    
    def _extract_semantic_focus(self, roles: Dict[str, SemanticRole]) -> List[str]:
        """提取语义焦点"""
        focus = []
        
        # 按权重排序，提取高权重的角色内容
        sorted_roles = sorted(
            roles.values(), 
            key=lambda x: x.weight, 
            reverse=True
        )
        
        for role in sorted_roles:
            if role.weight > 0.7:  # 高权重阈值
                focus.append(role.content)
        
        return focus
    
    async def _infer_query_intent(self, sentence: str, 
                                roles: Dict[str, SemanticRole]) -> str:
        """推断查询意图"""
        sentence_lower = sentence.lower()
        
        # 基于疑问词推断意图
        if any(word in sentence_lower for word in ["什么", "是什么", "定义"]):
            return "definition"
        elif any(word in sentence_lower for word in ["如何", "怎么", "方法"]):
            return "procedure"
        elif any(word in sentence_lower for word in ["为什么", "原因"]):
            return "explanation"
        elif any(word in sentence_lower for word in ["哪里", "地点"]):
            return "location"
        elif any(word in sentence_lower for word in ["什么时候", "时间"]):
            return "temporal"
        elif any(word in sentence_lower for word in ["谁", "人物"]):
            return "entity"
        else:
            return "general"
    
    def _calculate_srl_confidence(self, roles: Dict[str, SemanticRole]) -> float:
        """计算语义角色标注置信度"""
        if not roles:
            return 0.0
        
        # 基于识别的角色数量和权重计算置信度
        total_weight = sum(role.weight for role in roles.values())
        role_count = len(roles)
        
        # 归一化置信度
        confidence = min(total_weight / 3.0, 1.0)  # 假设3个高权重角色为满分
        confidence *= min(role_count / 2.0, 1.0)   # 假设2个角色为合理数量
        
        return confidence
    
    async def enhance_query_with_srl(self, query: str) -> str:
        """使用语义角色标注增强查询"""
        try:
            srl_info = await self.analyze_semantic_roles(query)
            
            # 构建增强查询
            enhanced_parts = [query]  # 原始查询
            
            # 添加语义焦点
            enhanced_parts.extend(srl_info["semantic_focus"])
            
            # 添加谓词信息
            if srl_info["predicate"]:
                enhanced_parts.append(srl_info["predicate"])
            
            # 基于查询意图添加相关词汇
            intent_keywords = self._get_intent_keywords(srl_info["query_intent"])
            enhanced_parts.extend(intent_keywords)
            
            return " ".join(enhanced_parts)
            
        except Exception as e:
            logger.error(f"SRL查询增强失败: {e}")
            return query
    
    def _get_intent_keywords(self, intent: str) -> List[str]:
        """获取意图相关关键词"""
        intent_keywords = {
            "definition": ["概念", "含义", "解释"],
            "procedure": ["步骤", "流程", "实现"],
            "explanation": ["原理", "机制", "原因"],
            "location": ["位置", "地址", "场所"],
            "temporal": ["时间", "日期", "历史"],
            "entity": ["人物", "组织", "实体"]
        }
        
        return intent_keywords.get(intent, [])


# 创建全局实例
chinese_segmenter = ChineseSegmentationOptimizer()
chinese_srl = ChineseSemanticRoleLabeler()
