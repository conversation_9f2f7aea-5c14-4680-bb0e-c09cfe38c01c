"""
模型性能验证和优化器
评估对比学习模型效果，进行超参数调优
"""

import torch
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from sklearn.metrics.pairwise import cosine_similarity

from loguru import logger
from .contrastive_trainer import ContrastiveModel, ContrastiveDataset, TrainingConfig
from .contrastive_data_builder import ContrastiveExample
from .enhanced_monitoring import enhanced_monitoring


@dataclass
class EvaluationMetrics:
    """评估指标"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    mrr: float  # Mean Reciprocal Rank
    ndcg_at_k: float  # NDCG@K
    embedding_quality: float
    improvement_over_baseline: float


@dataclass
class HyperparameterConfig:
    """超参数配置"""
    learning_rates: List[float]
    batch_sizes: List[int]
    temperatures: List[float]
    margins: List[float]
    epochs: List[int]


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.baseline_model = None
        self.evaluation_results = {}
        
    def load_baseline_model(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        """加载基线模型"""
        try:
            from sentence_transformers import SentenceTransformer
            self.baseline_model = SentenceTransformer(model_name)
            logger.info(f"基线模型加载成功: {model_name}")
        except Exception as e:
            logger.error(f"基线模型加载失败: {e}")
    
    def evaluate_model(self, model_path: str, test_dataset: ContrastiveDataset,
                      output_dir: str = "evaluation_results") -> EvaluationMetrics:
        """评估模型性能"""
        try:
            logger.info("开始模型性能评估...")
            
            # 加载训练好的模型
            trained_model = self._load_trained_model(model_path)
            
            # 评估各项指标
            metrics = self._compute_metrics(trained_model, test_dataset)
            
            # 与基线模型对比
            if self.baseline_model:
                baseline_metrics = self._evaluate_baseline(test_dataset)
                metrics.improvement_over_baseline = self._calculate_improvement(metrics, baseline_metrics)
            
            # 保存评估结果
            self._save_evaluation_results(metrics, output_dir)
            
            # 生成评估报告
            self._generate_evaluation_report(metrics, output_dir)
            
            logger.info(f"模型评估完成，准确率: {metrics.accuracy:.4f}")
            return metrics
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            raise
    
    def _load_trained_model(self, model_path: str) -> ContrastiveModel:
        """加载训练好的模型"""
        try:
            model_dir = Path(model_path)
            
            # 加载模型状态
            checkpoint = torch.load(model_dir / "pytorch_model.bin", map_location=self.device)
            config = checkpoint['config']
            
            # 创建模型
            model = ContrastiveModel(config.model_name, config.temperature)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            logger.info(f"训练模型加载成功: {model_path}")
            return model
            
        except Exception as e:
            logger.error(f"加载训练模型失败: {e}")
            raise
    
    def _compute_metrics(self, model: ContrastiveModel, test_dataset: ContrastiveDataset) -> EvaluationMetrics:
        """计算评估指标"""
        try:
            # 准备数据
            queries, positives, negatives, labels = self._prepare_evaluation_data(test_dataset)
            
            # 计算嵌入
            with torch.no_grad():
                query_embeddings = self._encode_texts(model, queries)
                positive_embeddings = self._encode_texts(model, positives)
                negative_embeddings = self._encode_texts(model, negatives)
            
            # 计算相似度
            pos_similarities = self._compute_similarities(query_embeddings, positive_embeddings)
            neg_similarities = self._compute_similarities(query_embeddings, negative_embeddings)
            
            # 计算分类指标
            predictions = (pos_similarities > neg_similarities).astype(int)
            true_labels = np.ones(len(predictions))  # 正样本应该更相似
            
            accuracy = accuracy_score(true_labels, predictions)
            precision, recall, f1, _ = precision_recall_fscore_support(
                true_labels, predictions, average='binary'
            )
            
            # 计算排序指标
            mrr = self._compute_mrr(pos_similarities, neg_similarities)
            ndcg = self._compute_ndcg(pos_similarities, neg_similarities)
            
            # 计算嵌入质量
            embedding_quality = self._compute_embedding_quality(
                query_embeddings, positive_embeddings, negative_embeddings
            )
            
            return EvaluationMetrics(
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                mrr=mrr,
                ndcg_at_k=ndcg,
                embedding_quality=embedding_quality,
                improvement_over_baseline=0.0  # 稍后计算
            )
            
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            raise
    
    def _prepare_evaluation_data(self, test_dataset: ContrastiveDataset) -> Tuple[List[str], List[str], List[str], List[int]]:
        """准备评估数据"""
        queries, positives, negatives, labels = [], [], [], []
        
        for i in range(len(test_dataset)):
            example = test_dataset.examples[i]
            queries.append(example.query)
            positives.append(example.positive)
            negatives.append(example.negative)
            labels.append(1)  # 正样本标签
        
        return queries, positives, negatives, labels
    
    def _encode_texts(self, model: ContrastiveModel, texts: List[str]) -> np.ndarray:
        """编码文本"""
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained("sentence-transformers/all-MiniLM-L6-v2")
            
            embeddings = []
            batch_size = 32
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                # 编码
                encoded = tokenizer(
                    batch_texts,
                    truncation=True,
                    padding=True,
                    max_length=512,
                    return_tensors='pt'
                )
                
                # 移动到设备
                input_ids = encoded['input_ids'].to(self.device)
                attention_mask = encoded['attention_mask'].to(self.device)
                
                # 获取嵌入
                with torch.no_grad():
                    batch_embeddings = model(input_ids, attention_mask)
                    embeddings.append(batch_embeddings.cpu().numpy())
            
            return np.vstack(embeddings)
            
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            raise
    
    def _compute_similarities(self, embeddings1: np.ndarray, embeddings2: np.ndarray) -> np.ndarray:
        """计算相似度"""
        # 计算对角线相似度（对应样本之间的相似度）
        similarities = np.sum(embeddings1 * embeddings2, axis=1)
        return similarities
    
    def _compute_mrr(self, pos_similarities: np.ndarray, neg_similarities: np.ndarray) -> float:
        """计算Mean Reciprocal Rank"""
        try:
            reciprocal_ranks = []
            
            for pos_sim, neg_sim in zip(pos_similarities, neg_similarities):
                # 创建排序列表（正样本和负样本）
                scores = [pos_sim, neg_sim]
                sorted_indices = np.argsort(scores)[::-1]  # 降序排序
                
                # 找到正样本的排名（索引0是正样本）
                rank = np.where(sorted_indices == 0)[0][0] + 1
                reciprocal_ranks.append(1.0 / rank)
            
            return np.mean(reciprocal_ranks)
            
        except Exception as e:
            logger.error(f"计算MRR失败: {e}")
            return 0.0
    
    def _compute_ndcg(self, pos_similarities: np.ndarray, neg_similarities: np.ndarray, k: int = 2) -> float:
        """计算NDCG@K"""
        try:
            ndcg_scores = []
            
            for pos_sim, neg_sim in zip(pos_similarities, neg_similarities):
                # 创建相关性分数（正样本相关性为1，负样本为0）
                relevance = [1, 0]
                scores = [pos_sim, neg_sim]
                
                # 按分数排序
                sorted_indices = np.argsort(scores)[::-1]
                sorted_relevance = [relevance[i] for i in sorted_indices]
                
                # 计算DCG@K
                dcg = 0
                for i in range(min(k, len(sorted_relevance))):
                    dcg += sorted_relevance[i] / np.log2(i + 2)
                
                # 计算IDCG@K
                ideal_relevance = sorted(relevance, reverse=True)
                idcg = 0
                for i in range(min(k, len(ideal_relevance))):
                    idcg += ideal_relevance[i] / np.log2(i + 2)
                
                # 计算NDCG
                ndcg = dcg / idcg if idcg > 0 else 0
                ndcg_scores.append(ndcg)
            
            return np.mean(ndcg_scores)
            
        except Exception as e:
            logger.error(f"计算NDCG失败: {e}")
            return 0.0
    
    def _compute_embedding_quality(self, query_emb: np.ndarray, 
                                 pos_emb: np.ndarray, neg_emb: np.ndarray) -> float:
        """计算嵌入质量"""
        try:
            # 计算正样本相似度的平均值
            pos_similarities = np.sum(query_emb * pos_emb, axis=1)
            avg_pos_sim = np.mean(pos_similarities)
            
            # 计算负样本相似度的平均值
            neg_similarities = np.sum(query_emb * neg_emb, axis=1)
            avg_neg_sim = np.mean(neg_similarities)
            
            # 质量分数 = 正样本相似度 - 负样本相似度
            quality_score = avg_pos_sim - avg_neg_sim
            
            # 归一化到0-1范围
            normalized_score = (quality_score + 1) / 2
            
            return max(0, min(1, normalized_score))
            
        except Exception as e:
            logger.error(f"计算嵌入质量失败: {e}")
            return 0.0
    
    def _evaluate_baseline(self, test_dataset: ContrastiveDataset) -> EvaluationMetrics:
        """评估基线模型"""
        try:
            if not self.baseline_model:
                return EvaluationMetrics(0, 0, 0, 0, 0, 0, 0, 0)
            
            # 准备数据
            queries, positives, negatives, _ = self._prepare_evaluation_data(test_dataset)
            
            # 计算基线嵌入
            query_embeddings = self.baseline_model.encode(queries)
            positive_embeddings = self.baseline_model.encode(positives)
            negative_embeddings = self.baseline_model.encode(negatives)
            
            # 计算相似度
            pos_similarities = self._compute_similarities(query_embeddings, positive_embeddings)
            neg_similarities = self._compute_similarities(query_embeddings, negative_embeddings)
            
            # 计算指标
            predictions = (pos_similarities > neg_similarities).astype(int)
            true_labels = np.ones(len(predictions))
            
            accuracy = accuracy_score(true_labels, predictions)
            precision, recall, f1, _ = precision_recall_fscore_support(
                true_labels, predictions, average='binary'
            )
            
            mrr = self._compute_mrr(pos_similarities, neg_similarities)
            ndcg = self._compute_ndcg(pos_similarities, neg_similarities)
            embedding_quality = self._compute_embedding_quality(
                query_embeddings, positive_embeddings, negative_embeddings
            )
            
            return EvaluationMetrics(
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                mrr=mrr,
                ndcg_at_k=ndcg,
                embedding_quality=embedding_quality,
                improvement_over_baseline=0.0
            )
            
        except Exception as e:
            logger.error(f"基线模型评估失败: {e}")
            return EvaluationMetrics(0, 0, 0, 0, 0, 0, 0, 0)
    
    def _calculate_improvement(self, trained_metrics: EvaluationMetrics, 
                             baseline_metrics: EvaluationMetrics) -> float:
        """计算相对于基线的改进"""
        try:
            if baseline_metrics.accuracy == 0:
                return 0.0
            
            improvement = (trained_metrics.accuracy - baseline_metrics.accuracy) / baseline_metrics.accuracy * 100
            return improvement
            
        except Exception as e:
            logger.error(f"计算改进失败: {e}")
            return 0.0
    
    def _save_evaluation_results(self, metrics: EvaluationMetrics, output_dir: str):
        """保存评估结果"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            results_file = output_path / "evaluation_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(metrics), f, indent=2, ensure_ascii=False)
            
            logger.info(f"评估结果保存到: {results_file}")
            
        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
    
    def _generate_evaluation_report(self, metrics: EvaluationMetrics, output_dir: str):
        """生成评估报告"""
        try:
            output_path = Path(output_dir)
            report_file = output_path / "evaluation_report.md"
            
            report_content = f"""# 对比学习模型评估报告

## 评估时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 性能指标

### 分类指标
- **准确率**: {metrics.accuracy:.4f}
- **精确率**: {metrics.precision:.4f}
- **召回率**: {metrics.recall:.4f}
- **F1分数**: {metrics.f1_score:.4f}

### 排序指标
- **MRR**: {metrics.mrr:.4f}
- **NDCG@2**: {metrics.ndcg_at_k:.4f}

### 嵌入质量
- **嵌入质量分数**: {metrics.embedding_quality:.4f}

### 改进效果
- **相对基线改进**: {metrics.improvement_over_baseline:.2f}%

## 结论

{'✅ 模型性能达到预期目标' if metrics.improvement_over_baseline >= 10 else '⚠️ 模型性能需要进一步优化'}

预期改进目标：10-20%
实际改进：{metrics.improvement_over_baseline:.2f}%

## 建议

"""
            
            if metrics.improvement_over_baseline >= 20:
                report_content += "- 模型性能优秀，可以部署到生产环境\n"
            elif metrics.improvement_over_baseline >= 10:
                report_content += "- 模型性能良好，建议进行更多测试后部署\n"
            else:
                report_content += "- 模型性能需要改进，建议调整超参数或增加训练数据\n"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"评估报告生成: {report_file}")
            
        except Exception as e:
            logger.error(f"生成评估报告失败: {e}")
    
    def hyperparameter_tuning(self, train_dataset: ContrastiveDataset,
                            eval_dataset: ContrastiveDataset,
                            hyperparams: HyperparameterConfig,
                            output_dir: str = "hyperparameter_tuning") -> Dict[str, Any]:
        """超参数调优"""
        try:
            logger.info("开始超参数调优...")
            
            best_config = None
            best_score = 0
            all_results = []
            
            # 网格搜索
            for lr in hyperparams.learning_rates:
                for batch_size in hyperparams.batch_sizes:
                    for temperature in hyperparams.temperatures:
                        for margin in hyperparams.margins:
                            for epochs in hyperparams.epochs:
                                
                                config = TrainingConfig(
                                    learning_rate=lr,
                                    batch_size=batch_size,
                                    temperature=temperature,
                                    margin=margin,
                                    epochs=epochs
                                )
                                
                                # 训练和评估
                                score = self._train_and_evaluate(config, train_dataset, eval_dataset)
                                
                                result = {
                                    'config': asdict(config),
                                    'score': score,
                                    'timestamp': datetime.now().isoformat()
                                }
                                
                                all_results.append(result)
                                
                                if score > best_score:
                                    best_score = score
                                    best_config = config
                                
                                logger.info(f"配置评估完成 - 分数: {score:.4f}")
            
            # 保存调优结果
            tuning_results = {
                'best_config': asdict(best_config) if best_config else None,
                'best_score': best_score,
                'all_results': all_results
            }
            
            self._save_tuning_results(tuning_results, output_dir)
            
            logger.info(f"超参数调优完成，最佳分数: {best_score:.4f}")
            return tuning_results
            
        except Exception as e:
            logger.error(f"超参数调优失败: {e}")
            return {}
    
    def _train_and_evaluate(self, config: TrainingConfig, 
                          train_dataset: ContrastiveDataset,
                          eval_dataset: ContrastiveDataset) -> float:
        """训练并评估配置"""
        try:
            from .contrastive_trainer import ContrastiveTrainer
            
            # 创建训练器
            trainer = ContrastiveTrainer(config)
            
            # 快速训练（减少epoch用于调优）
            quick_config = TrainingConfig(**asdict(config))
            quick_config.epochs = 1  # 快速评估
            trainer.config = quick_config
            
            # 训练
            trainer.train(train_dataset, eval_dataset, "temp_model")
            
            # 评估
            metrics = self.evaluate_model("temp_model/final_model", eval_dataset)
            
            return metrics.accuracy
            
        except Exception as e:
            logger.error(f"训练评估失败: {e}")
            return 0.0
    
    def _save_tuning_results(self, results: Dict[str, Any], output_dir: str):
        """保存调优结果"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            results_file = output_path / "hyperparameter_tuning_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"调优结果保存到: {results_file}")
            
        except Exception as e:
            logger.error(f"保存调优结果失败: {e}")


# 创建全局实例
model_evaluator = ModelEvaluator()
