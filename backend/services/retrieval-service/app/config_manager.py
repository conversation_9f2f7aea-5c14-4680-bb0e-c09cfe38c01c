"""
配置管理系统
统一管理RAG检索系统的所有配置参数，支持动态调整和参数优化
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

from loguru import logger


@dataclass
class SelfRAGConfig:
    """Self-RAG配置"""
    max_iterations: int = 3
    confidence_threshold: float = 0.8
    min_confidence_threshold: float = 0.3
    relevance_weight: float = 0.4
    completeness_weight: float = 0.3
    confidence_weight: float = 0.3
    query_expansion_enabled: bool = True
    iteration_timeout: int = 30


@dataclass
class MultiVectorConfig:
    """多向量检索配置"""
    fusion_method: str = "adaptive"  # weighted_sum, rank_fusion, adaptive
    diversity_weight: float = 0.1
    min_aspect_confidence: float = 0.3
    aspect_weights: Dict[str, float] = None
    enable_aspect_detection: bool = True
    max_aspects: int = 5
    
    def __post_init__(self):
        if self.aspect_weights is None:
            self.aspect_weights = {
                "semantic": 0.3,
                "factual": 0.25,
                "temporal": 0.2,
                "entity": 0.15,
                "procedural": 0.1
            }


@dataclass
class ChineseOptimizationConfig:
    """中文优化配置"""
    segmentation_method: str = "ensemble"  # jieba, pkuseg, thulac, ensemble
    min_word_length: int = 1
    max_word_length: int = 10
    confidence_threshold: float = 0.5
    enable_pos_tagging: bool = True
    enable_ner: bool = True
    custom_dict_path: Optional[str] = None
    srl_model_path: Optional[str] = None


@dataclass
class TemporalConfig:
    """时效性配置"""
    freshness_decay_factor: float = 0.1
    max_age_days: int = 365
    relevance_weight: float = 0.6
    freshness_weight: float = 0.4
    enable_temporal_keywords: bool = True
    temporal_keyword_boost: float = 1.2
    default_freshness_score: float = 0.5


@dataclass
class FusionConfig:
    """结果融合配置"""
    default_method: str = "adaptive"
    diversity_threshold: float = 0.7
    max_results: int = 100
    min_score_threshold: float = 0.1
    enable_reranking: bool = True
    rerank_top_k: int = 50
    personalization_weight: float = 0.1


@dataclass
class CacheConfig:
    """缓存配置"""
    enable_caching: bool = True
    default_ttl: int = 3600  # 1小时
    max_cache_size: int = 10000
    cache_key_prefix: str = "rag_cache"
    enable_query_cache: bool = True
    enable_result_cache: bool = True
    enable_embedding_cache: bool = True


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_concurrent_requests: int = 1000
    request_timeout: int = 30
    batch_size: int = 100
    max_batch_size: int = 1000
    enable_async_processing: bool = True
    worker_count: int = 4
    connection_pool_size: int = 20


@dataclass
class MonitoringConfig:
    """监控配置"""
    enable_metrics: bool = True
    metrics_port: int = 9090
    enable_tracing: bool = True
    log_level: str = "INFO"
    enable_performance_logging: bool = True
    slow_query_threshold: float = 1.0  # 秒
    enable_user_behavior_tracking: bool = True


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/advanced_config.yaml"
        self.config_dir = Path(self.config_path).parent
        self.config_dir.mkdir(exist_ok=True)
        
        # 初始化配置
        self.self_rag = SelfRAGConfig()
        self.multi_vector = MultiVectorConfig()
        self.chinese_optimization = ChineseOptimizationConfig()
        self.temporal = TemporalConfig()
        self.fusion = FusionConfig()
        self.cache = CacheConfig()
        self.performance = PerformanceConfig()
        self.monitoring = MonitoringConfig()
        
        # 加载配置
        self.load_config()
        
        # 应用环境变量覆盖
        self._apply_env_overrides()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                # 更新配置对象
                if 'self_rag' in config_data:
                    self._update_config(self.self_rag, config_data['self_rag'])
                
                if 'multi_vector' in config_data:
                    self._update_config(self.multi_vector, config_data['multi_vector'])
                
                if 'chinese_optimization' in config_data:
                    self._update_config(self.chinese_optimization, config_data['chinese_optimization'])
                
                if 'temporal' in config_data:
                    self._update_config(self.temporal, config_data['temporal'])
                
                if 'fusion' in config_data:
                    self._update_config(self.fusion, config_data['fusion'])
                
                if 'cache' in config_data:
                    self._update_config(self.cache, config_data['cache'])
                
                if 'performance' in config_data:
                    self._update_config(self.performance, config_data['performance'])
                
                if 'monitoring' in config_data:
                    self._update_config(self.monitoring, config_data['monitoring'])
                
                logger.info(f"配置文件加载成功: {self.config_path}")
                return True
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置
                return False
                
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = {
                'self_rag': asdict(self.self_rag),
                'multi_vector': asdict(self.multi_vector),
                'chinese_optimization': asdict(self.chinese_optimization),
                'temporal': asdict(self.temporal),
                'fusion': asdict(self.fusion),
                'cache': asdict(self.cache),
                'performance': asdict(self.performance),
                'monitoring': asdict(self.monitoring),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")
            return False
    
    def _update_config(self, config_obj: Any, config_dict: Dict[str, Any]):
        """更新配置对象"""
        for key, value in config_dict.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        # Self-RAG配置
        if os.getenv('SELF_RAG_MAX_ITERATIONS'):
            self.self_rag.max_iterations = int(os.getenv('SELF_RAG_MAX_ITERATIONS'))
        if os.getenv('SELF_RAG_CONFIDENCE_THRESHOLD'):
            self.self_rag.confidence_threshold = float(os.getenv('SELF_RAG_CONFIDENCE_THRESHOLD'))
        
        # 多向量配置
        if os.getenv('MULTI_VECTOR_FUSION_METHOD'):
            self.multi_vector.fusion_method = os.getenv('MULTI_VECTOR_FUSION_METHOD')
        if os.getenv('MULTI_VECTOR_DIVERSITY_WEIGHT'):
            self.multi_vector.diversity_weight = float(os.getenv('MULTI_VECTOR_DIVERSITY_WEIGHT'))
        
        # 中文优化配置
        if os.getenv('CHINESE_SEGMENTATION_METHOD'):
            self.chinese_optimization.segmentation_method = os.getenv('CHINESE_SEGMENTATION_METHOD')
        if os.getenv('CHINESE_CONFIDENCE_THRESHOLD'):
            self.chinese_optimization.confidence_threshold = float(os.getenv('CHINESE_CONFIDENCE_THRESHOLD'))
        
        # 时效性配置
        if os.getenv('TEMPORAL_FRESHNESS_DECAY_FACTOR'):
            self.temporal.freshness_decay_factor = float(os.getenv('TEMPORAL_FRESHNESS_DECAY_FACTOR'))
        if os.getenv('TEMPORAL_MAX_AGE_DAYS'):
            self.temporal.max_age_days = int(os.getenv('TEMPORAL_MAX_AGE_DAYS'))
        
        # 性能配置
        if os.getenv('MAX_CONCURRENT_REQUESTS'):
            self.performance.max_concurrent_requests = int(os.getenv('MAX_CONCURRENT_REQUESTS'))
        if os.getenv('REQUEST_TIMEOUT'):
            self.performance.request_timeout = int(os.getenv('REQUEST_TIMEOUT'))
        
        # 缓存配置
        if os.getenv('CACHE_TTL'):
            self.cache.default_ttl = int(os.getenv('CACHE_TTL'))
        if os.getenv('ENABLE_CACHING'):
            self.cache.enable_caching = os.getenv('ENABLE_CACHING').lower() == 'true'
    
    def get_config(self, section: str) -> Any:
        """获取指定配置节"""
        return getattr(self, section, None)
    
    def update_config(self, section: str, updates: Dict[str, Any]) -> bool:
        """更新指定配置节"""
        try:
            config_obj = getattr(self, section, None)
            if config_obj is None:
                logger.error(f"配置节不存在: {section}")
                return False
            
            self._update_config(config_obj, updates)
            self.save_config()
            
            logger.info(f"配置更新成功: {section}")
            return True
            
        except Exception as e:
            logger.error(f"配置更新失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证Self-RAG配置
        if not (1 <= self.self_rag.max_iterations <= 10):
            validation_results["errors"].append("Self-RAG最大迭代次数应在1-10之间")
            validation_results["valid"] = False
        
        if not (0.1 <= self.self_rag.confidence_threshold <= 1.0):
            validation_results["errors"].append("Self-RAG置信度阈值应在0.1-1.0之间")
            validation_results["valid"] = False
        
        # 验证多向量配置
        if self.multi_vector.fusion_method not in ["weighted_sum", "rank_fusion", "adaptive"]:
            validation_results["errors"].append("多向量融合方法无效")
            validation_results["valid"] = False
        
        if not (0.0 <= self.multi_vector.diversity_weight <= 1.0):
            validation_results["errors"].append("多样性权重应在0.0-1.0之间")
            validation_results["valid"] = False
        
        # 验证中文优化配置
        if self.chinese_optimization.segmentation_method not in ["jieba", "pkuseg", "thulac", "ensemble"]:
            validation_results["errors"].append("中文分词方法无效")
            validation_results["valid"] = False
        
        # 验证时效性配置
        if not (0.0 <= self.temporal.freshness_decay_factor <= 1.0):
            validation_results["errors"].append("新鲜度衰减因子应在0.0-1.0之间")
            validation_results["valid"] = False
        
        if self.temporal.max_age_days <= 0:
            validation_results["errors"].append("最大年龄天数应大于0")
            validation_results["valid"] = False
        
        # 验证性能配置
        if self.performance.max_concurrent_requests <= 0:
            validation_results["errors"].append("最大并发请求数应大于0")
            validation_results["valid"] = False
        
        if self.performance.request_timeout <= 0:
            validation_results["errors"].append("请求超时时间应大于0")
            validation_results["valid"] = False
        
        # 性能警告
        if self.performance.max_concurrent_requests > 5000:
            validation_results["warnings"].append("并发请求数过高，可能影响系统稳定性")
        
        if self.cache.default_ttl < 60:
            validation_results["warnings"].append("缓存TTL过短，可能影响性能")
        
        return validation_results
    
    def get_optimized_config(self, workload_type: str = "balanced") -> Dict[str, Any]:
        """获取针对特定工作负载优化的配置"""
        optimized_configs = {
            "high_throughput": {
                "performance": {
                    "max_concurrent_requests": 2000,
                    "batch_size": 200,
                    "worker_count": 8
                },
                "cache": {
                    "enable_caching": True,
                    "default_ttl": 7200,
                    "max_cache_size": 50000
                },
                "self_rag": {
                    "max_iterations": 2,
                    "confidence_threshold": 0.7
                }
            },
            "high_accuracy": {
                "self_rag": {
                    "max_iterations": 5,
                    "confidence_threshold": 0.9,
                    "relevance_weight": 0.5
                },
                "multi_vector": {
                    "fusion_method": "adaptive",
                    "diversity_weight": 0.2
                },
                "fusion": {
                    "enable_reranking": True,
                    "rerank_top_k": 100
                }
            },
            "low_latency": {
                "performance": {
                    "request_timeout": 10,
                    "batch_size": 50
                },
                "self_rag": {
                    "max_iterations": 1,
                    "confidence_threshold": 0.6
                },
                "cache": {
                    "enable_caching": True,
                    "default_ttl": 1800
                }
            },
            "balanced": {
                "self_rag": {
                    "max_iterations": 3,
                    "confidence_threshold": 0.8
                },
                "performance": {
                    "max_concurrent_requests": 1000,
                    "batch_size": 100
                },
                "cache": {
                    "default_ttl": 3600
                }
            }
        }
        
        return optimized_configs.get(workload_type, optimized_configs["balanced"])
    
    def apply_optimized_config(self, workload_type: str = "balanced") -> bool:
        """应用优化配置"""
        try:
            optimized_config = self.get_optimized_config(workload_type)
            
            for section, updates in optimized_config.items():
                self.update_config(section, updates)
            
            logger.info(f"已应用{workload_type}工作负载优化配置")
            return True
            
        except Exception as e:
            logger.error(f"应用优化配置失败: {e}")
            return False
    
    def export_config(self, format: str = "yaml") -> str:
        """导出配置"""
        config_data = {
            'self_rag': asdict(self.self_rag),
            'multi_vector': asdict(self.multi_vector),
            'chinese_optimization': asdict(self.chinese_optimization),
            'temporal': asdict(self.temporal),
            'fusion': asdict(self.fusion),
            'cache': asdict(self.cache),
            'performance': asdict(self.performance),
            'monitoring': asdict(self.monitoring)
        }
        
        if format.lower() == "json":
            return json.dumps(config_data, indent=2, ensure_ascii=False)
        elif format.lower() == "yaml":
            return yaml.dump(config_data, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的格式: {format}")
    
    def import_config(self, config_str: str, format: str = "yaml") -> bool:
        """导入配置"""
        try:
            if format.lower() == "json":
                config_data = json.loads(config_str)
            elif format.lower() == "yaml":
                config_data = yaml.safe_load(config_str)
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            # 更新配置
            for section, updates in config_data.items():
                if hasattr(self, section):
                    self._update_config(getattr(self, section), updates)
            
            # 验证配置
            validation = self.validate_config()
            if not validation["valid"]:
                logger.error(f"导入的配置无效: {validation['errors']}")
                return False
            
            # 保存配置
            self.save_config()
            logger.info("配置导入成功")
            return True
            
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            return False


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> ConfigManager:
    """获取全局配置管理器"""
    return config_manager


def reload_config() -> bool:
    """重新加载配置"""
    return config_manager.load_config()


def validate_all_configs() -> Dict[str, Any]:
    """验证所有配置"""
    return config_manager.validate_config()


# 配置装饰器
def with_config(section: str):
    """配置装饰器，自动注入配置"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            config = config_manager.get_config(section)
            return func(*args, config=config, **kwargs)
        return wrapper
    return decorator
