"""
结果融合算法优化器
优化多种检索技术的结果融合策略
"""

import numpy as np
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import cosine_similarity

from loguru import logger
from .enhanced_monitoring import enhanced_monitoring
from .database_client import database_client


@dataclass
class FusionResult:
    """融合结果"""
    content: str
    final_score: float
    component_scores: Dict[str, float]
    fusion_method: str
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class FusionConfig:
    """融合配置"""
    method: str  # weighted_sum, rank_fusion, score_fusion, hybrid
    weights: Dict[str, float]
    normalization: str  # minmax, zscore, rank
    diversity_factor: float
    quality_threshold: float


class ResultFusionOptimizer:
    """结果融合算法优化器"""
    
    def __init__(self):
        # 融合方法配置
        self.fusion_methods = {
            "weighted_sum": self._weighted_sum_fusion,
            "rank_fusion": self._rank_fusion,
            "score_fusion": self._score_fusion,
            "hybrid": self._hybrid_fusion,
            "adaptive": self._adaptive_fusion
        }
        
        # 默认权重配置
        self.default_weights = {
            "semantic_search": 0.25,
            "self_rag_search": 0.30,
            "multi_vector_search": 0.25,
            "temporal_aware_search": 0.20
        }
        
        # 优化参数
        self.diversity_threshold = 0.8
        self.quality_threshold = 0.3
        self.max_results = 50
        self.learning_rate = 0.01
        
        # 历史性能数据
        self.performance_history = []
    
    async def optimize_fusion(self, search_results: Dict[str, List[Dict]], 
                            query: str, user_context: Dict[str, Any] = None) -> List[FusionResult]:
        """
        优化融合多种检索结果
        
        Args:
            search_results: 各种检索方法的结果
            query: 查询文本
            user_context: 用户上下文
            
        Returns:
            融合后的结果列表
        """
        try:
            logger.info(f"开始结果融合优化: {len(search_results)} 种检索方法")
            
            if not search_results:
                return []
            
            # 1. 预处理和标准化
            normalized_results = await self._preprocess_results(search_results)
            
            # 2. 选择最优融合策略
            fusion_config = await self._select_fusion_strategy(
                normalized_results, query, user_context
            )
            
            # 3. 执行融合
            fused_results = await self._execute_fusion(
                normalized_results, fusion_config, query
            )
            
            # 4. 后处理优化
            optimized_results = await self._postprocess_results(
                fused_results, query, user_context
            )
            
            # 5. 记录性能数据
            await self._record_fusion_performance(
                search_results, optimized_results, fusion_config
            )
            
            logger.info(f"结果融合完成，返回 {len(optimized_results)} 个结果")
            return optimized_results
            
        except Exception as e:
            logger.error(f"结果融合优化失败: {e}")
            return []
    
    async def _preprocess_results(self, search_results: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """预处理和标准化结果"""
        try:
            normalized_results = {}
            
            for method, results in search_results.items():
                if not results:
                    continue
                
                # 标准化分数
                normalized = await self._normalize_scores(results, method)
                
                # 质量过滤
                filtered = await self._quality_filter(normalized)
                
                # 去重
                deduplicated = await self._deduplicate_results(filtered)
                
                normalized_results[method] = deduplicated
            
            return normalized_results
            
        except Exception as e:
            logger.error(f"结果预处理失败: {e}")
            return search_results
    
    async def _normalize_scores(self, results: List[Dict], method: str) -> List[Dict]:
        """标准化分数"""
        try:
            if not results:
                return results
            
            scores = [r.get("score", 0.0) for r in results]
            
            if not scores or max(scores) == min(scores):
                return results
            
            # MinMax标准化
            scaler = MinMaxScaler()
            normalized_scores = scaler.fit_transform(np.array(scores).reshape(-1, 1)).flatten()
            
            # 更新结果
            for i, result in enumerate(results):
                result["normalized_score"] = float(normalized_scores[i])
                result["original_score"] = result.get("score", 0.0)
                result["method"] = method
            
            return results
            
        except Exception as e:
            logger.error(f"分数标准化失败: {e}")
            return results
    
    async def _quality_filter(self, results: List[Dict]) -> List[Dict]:
        """质量过滤"""
        try:
            filtered_results = []
            
            for result in results:
                score = result.get("normalized_score", 0.0)
                content = result.get("content", "")
                
                # 基本质量检查
                if (score >= self.quality_threshold and 
                    len(content) >= 20 and 
                    len(content) <= 2000):
                    filtered_results.append(result)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"质量过滤失败: {e}")
            return results
    
    async def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """去重"""
        try:
            if len(results) <= 1:
                return results
            
            # 基于内容相似度去重
            contents = [r.get("content", "") for r in results]
            
            # 简化去重：基于内容长度和前50个字符
            seen_signatures = set()
            deduplicated = []
            
            for result in results:
                content = result.get("content", "")
                signature = f"{len(content)}_{content[:50]}"
                
                if signature not in seen_signatures:
                    seen_signatures.add(signature)
                    deduplicated.append(result)
            
            return deduplicated
            
        except Exception as e:
            logger.error(f"去重失败: {e}")
            return results
    
    async def _select_fusion_strategy(self, results: Dict[str, List[Dict]], 
                                    query: str, user_context: Dict[str, Any] = None) -> FusionConfig:
        """选择融合策略"""
        try:
            # 分析查询特征
            query_features = await self._analyze_query_features(query)
            
            # 分析结果特征
            result_features = await self._analyze_result_features(results)
            
            # 基于特征选择策略
            if query_features.get("complexity", 0) > 0.7:
                # 复杂查询使用混合融合
                method = "hybrid"
                weights = self._adjust_weights_for_complex_query(query_features)
            elif result_features.get("diversity", 0) > 0.8:
                # 高多样性结果使用排序融合
                method = "rank_fusion"
                weights = self.default_weights
            elif result_features.get("quality_variance", 0) > 0.5:
                # 质量差异大使用分数融合
                method = "score_fusion"
                weights = self._adjust_weights_for_quality(result_features)
            else:
                # 默认使用加权求和
                method = "weighted_sum"
                weights = self.default_weights
            
            return FusionConfig(
                method=method,
                weights=weights,
                normalization="minmax",
                diversity_factor=0.1,
                quality_threshold=self.quality_threshold
            )
            
        except Exception as e:
            logger.error(f"融合策略选择失败: {e}")
            return FusionConfig("weighted_sum", self.default_weights, "minmax", 0.1, 0.3)
    
    async def _analyze_query_features(self, query: str) -> Dict[str, float]:
        """分析查询特征"""
        try:
            features = {}
            
            # 查询长度
            features["length"] = min(1.0, len(query) / 100.0)
            
            # 查询复杂度（基于词汇多样性）
            words = query.split()
            unique_words = set(words)
            features["complexity"] = len(unique_words) / len(words) if words else 0
            
            # 时间意图
            temporal_keywords = ["最近", "现在", "时间", "什么时候", "年", "月", "日"]
            temporal_count = sum(1 for kw in temporal_keywords if kw in query)
            features["temporal_intent"] = min(1.0, temporal_count / 3.0)
            
            # 实体意图
            entity_keywords = ["什么", "谁", "哪个", "哪些", "公司", "人"]
            entity_count = sum(1 for kw in entity_keywords if kw in query)
            features["entity_intent"] = min(1.0, entity_count / 3.0)
            
            return features
            
        except Exception as e:
            logger.error(f"查询特征分析失败: {e}")
            return {}
    
    async def _analyze_result_features(self, results: Dict[str, List[Dict]]) -> Dict[str, float]:
        """分析结果特征"""
        try:
            features = {}
            
            # 结果数量分布
            result_counts = [len(r) for r in results.values()]
            features["count_variance"] = np.var(result_counts) if result_counts else 0
            
            # 分数分布
            all_scores = []
            for method_results in results.values():
                scores = [r.get("normalized_score", 0) for r in method_results]
                all_scores.extend(scores)
            
            if all_scores:
                features["score_mean"] = np.mean(all_scores)
                features["score_variance"] = np.var(all_scores)
                features["quality_variance"] = features["score_variance"]
            else:
                features["score_mean"] = 0.5
                features["score_variance"] = 0.0
                features["quality_variance"] = 0.0
            
            # 多样性估计（简化）
            total_results = sum(len(r) for r in results.values())
            unique_results = len(set(
                r.get("content", "")[:50] for method_results in results.values() 
                for r in method_results
            ))
            features["diversity"] = unique_results / total_results if total_results > 0 else 0
            
            return features
            
        except Exception as e:
            logger.error(f"结果特征分析失败: {e}")
            return {}
    
    def _adjust_weights_for_complex_query(self, query_features: Dict[str, float]) -> Dict[str, float]:
        """为复杂查询调整权重"""
        weights = self.default_weights.copy()
        
        # 复杂查询增加Self-RAG权重
        if query_features.get("complexity", 0) > 0.7:
            weights["self_rag_search"] = min(0.4, weights["self_rag_search"] + 0.1)
            weights["semantic_search"] = max(0.15, weights["semantic_search"] - 0.05)
        
        # 时间意图增加时效性权重
        if query_features.get("temporal_intent", 0) > 0.5:
            weights["temporal_aware_search"] = min(0.3, weights["temporal_aware_search"] + 0.1)
            weights["semantic_search"] = max(0.15, weights["semantic_search"] - 0.05)
        
        # 实体意图增加多向量权重
        if query_features.get("entity_intent", 0) > 0.5:
            weights["multi_vector_search"] = min(0.35, weights["multi_vector_search"] + 0.1)
            weights["semantic_search"] = max(0.15, weights["semantic_search"] - 0.05)
        
        # 归一化权重
        total_weight = sum(weights.values())
        for key in weights:
            weights[key] /= total_weight
        
        return weights
    
    def _adjust_weights_for_quality(self, result_features: Dict[str, float]) -> Dict[str, float]:
        """基于质量调整权重"""
        weights = self.default_weights.copy()
        
        # 如果质量差异大，增加高质量方法的权重
        quality_variance = result_features.get("quality_variance", 0)
        
        if quality_variance > 0.5:
            # 假设Self-RAG和多向量检索质量更稳定
            weights["self_rag_search"] = min(0.35, weights["self_rag_search"] + 0.05)
            weights["multi_vector_search"] = min(0.3, weights["multi_vector_search"] + 0.05)
            weights["semantic_search"] = max(0.15, weights["semantic_search"] - 0.05)
            weights["temporal_aware_search"] = max(0.15, weights["temporal_aware_search"] - 0.05)
        
        # 归一化权重
        total_weight = sum(weights.values())
        for key in weights:
            weights[key] /= total_weight
        
        return weights
    
    async def _execute_fusion(self, results: Dict[str, List[Dict]], 
                            config: FusionConfig, query: str) -> List[FusionResult]:
        """执行融合"""
        try:
            fusion_method = self.fusion_methods.get(config.method, self._weighted_sum_fusion)
            
            fused_results = await fusion_method(results, config, query)
            
            return fused_results
            
        except Exception as e:
            logger.error(f"融合执行失败: {e}")
            return []
    
    async def _weighted_sum_fusion(self, results: Dict[str, List[Dict]], 
                                 config: FusionConfig, query: str) -> List[FusionResult]:
        """加权求和融合"""
        try:
            # 收集所有结果
            all_results = {}
            
            for method, method_results in results.items():
                weight = config.weights.get(method, 0.1)
                
                for result in method_results:
                    content = result.get("content", "")
                    score = result.get("normalized_score", 0.0)
                    
                    if content not in all_results:
                        all_results[content] = {
                            "content": content,
                            "component_scores": {},
                            "total_score": 0.0,
                            "method_count": 0,
                            "metadata": result.get("metadata", {})
                        }
                    
                    all_results[content]["component_scores"][method] = score
                    all_results[content]["total_score"] += score * weight
                    all_results[content]["method_count"] += 1
            
            # 转换为FusionResult
            fusion_results = []
            for content, data in all_results.items():
                confidence = data["method_count"] / len(results)  # 方法覆盖度作为置信度
                
                fusion_result = FusionResult(
                    content=content,
                    final_score=data["total_score"],
                    component_scores=data["component_scores"],
                    fusion_method="weighted_sum",
                    confidence=confidence,
                    metadata=data["metadata"]
                )
                
                fusion_results.append(fusion_result)
            
            # 按分数排序
            fusion_results.sort(key=lambda x: x.final_score, reverse=True)
            
            return fusion_results[:self.max_results]
            
        except Exception as e:
            logger.error(f"加权求和融合失败: {e}")
            return []
    
    async def _rank_fusion(self, results: Dict[str, List[Dict]], 
                         config: FusionConfig, query: str) -> List[FusionResult]:
        """排序融合（RRF - Reciprocal Rank Fusion）"""
        try:
            k = 60  # RRF参数
            
            # 收集所有结果的排序信息
            all_results = {}
            
            for method, method_results in results.items():
                weight = config.weights.get(method, 0.1)
                
                for rank, result in enumerate(method_results):
                    content = result.get("content", "")
                    
                    if content not in all_results:
                        all_results[content] = {
                            "content": content,
                            "component_scores": {},
                            "rrf_score": 0.0,
                            "method_count": 0,
                            "metadata": result.get("metadata", {})
                        }
                    
                    # RRF分数计算
                    rrf_score = weight / (k + rank + 1)
                    all_results[content]["component_scores"][method] = rrf_score
                    all_results[content]["rrf_score"] += rrf_score
                    all_results[content]["method_count"] += 1
            
            # 转换为FusionResult
            fusion_results = []
            for content, data in all_results.items():
                confidence = data["method_count"] / len(results)
                
                fusion_result = FusionResult(
                    content=content,
                    final_score=data["rrf_score"],
                    component_scores=data["component_scores"],
                    fusion_method="rank_fusion",
                    confidence=confidence,
                    metadata=data["metadata"]
                )
                
                fusion_results.append(fusion_result)
            
            # 按RRF分数排序
            fusion_results.sort(key=lambda x: x.final_score, reverse=True)
            
            return fusion_results[:self.max_results]
            
        except Exception as e:
            logger.error(f"排序融合失败: {e}")
            return []
    
    async def _score_fusion(self, results: Dict[str, List[Dict]], 
                          config: FusionConfig, query: str) -> List[FusionResult]:
        """分数融合（CombSUM）"""
        try:
            # 类似加权求和，但使用不同的归一化策略
            return await self._weighted_sum_fusion(results, config, query)
            
        except Exception as e:
            logger.error(f"分数融合失败: {e}")
            return []
    
    async def _hybrid_fusion(self, results: Dict[str, List[Dict]], 
                           config: FusionConfig, query: str) -> List[FusionResult]:
        """混合融合"""
        try:
            # 结合加权求和和排序融合
            weighted_results = await self._weighted_sum_fusion(results, config, query)
            rank_results = await self._rank_fusion(results, config, query)
            
            # 合并结果
            combined_results = {}
            
            # 加权求和结果
            for result in weighted_results:
                content = result.content
                combined_results[content] = {
                    "content": content,
                    "weighted_score": result.final_score,
                    "rank_score": 0.0,
                    "component_scores": result.component_scores,
                    "metadata": result.metadata
                }
            
            # 排序融合结果
            for result in rank_results:
                content = result.content
                if content in combined_results:
                    combined_results[content]["rank_score"] = result.final_score
                else:
                    combined_results[content] = {
                        "content": content,
                        "weighted_score": 0.0,
                        "rank_score": result.final_score,
                        "component_scores": result.component_scores,
                        "metadata": result.metadata
                    }
            
            # 计算混合分数
            fusion_results = []
            for content, data in combined_results.items():
                # 混合分数 = 0.6 * 加权分数 + 0.4 * 排序分数
                hybrid_score = 0.6 * data["weighted_score"] + 0.4 * data["rank_score"]
                
                fusion_result = FusionResult(
                    content=content,
                    final_score=hybrid_score,
                    component_scores=data["component_scores"],
                    fusion_method="hybrid",
                    confidence=0.8,  # 混合方法置信度较高
                    metadata=data["metadata"]
                )
                
                fusion_results.append(fusion_result)
            
            # 按混合分数排序
            fusion_results.sort(key=lambda x: x.final_score, reverse=True)
            
            return fusion_results[:self.max_results]
            
        except Exception as e:
            logger.error(f"混合融合失败: {e}")
            return []
    
    async def _adaptive_fusion(self, results: Dict[str, List[Dict]], 
                             config: FusionConfig, query: str) -> List[FusionResult]:
        """自适应融合"""
        try:
            # 基于历史性能选择最佳融合方法
            if not self.performance_history:
                # 没有历史数据，使用混合融合
                return await self._hybrid_fusion(results, config, query)
            
            # 分析历史性能，选择最佳方法
            method_performance = {}
            for record in self.performance_history[-100:]:  # 最近100次
                method = record.get("fusion_method", "weighted_sum")
                performance = record.get("performance_score", 0.5)
                
                if method not in method_performance:
                    method_performance[method] = []
                method_performance[method].append(performance)
            
            # 选择平均性能最好的方法
            best_method = "weighted_sum"
            best_performance = 0.0
            
            for method, performances in method_performance.items():
                avg_performance = sum(performances) / len(performances)
                if avg_performance > best_performance:
                    best_performance = avg_performance
                    best_method = method
            
            # 使用最佳方法
            fusion_method = self.fusion_methods.get(best_method, self._weighted_sum_fusion)
            return await fusion_method(results, config, query)
            
        except Exception as e:
            logger.error(f"自适应融合失败: {e}")
            return await self._weighted_sum_fusion(results, config, query)
    
    async def _postprocess_results(self, results: List[FusionResult], 
                                 query: str, user_context: Dict[str, Any] = None) -> List[FusionResult]:
        """后处理优化"""
        try:
            if not results:
                return results
            
            # 多样性优化
            diversified_results = await self._apply_diversity_optimization(results)
            
            # 个性化调整
            if user_context:
                personalized_results = await self._apply_personalization(
                    diversified_results, user_context
                )
            else:
                personalized_results = diversified_results
            
            return personalized_results
            
        except Exception as e:
            logger.error(f"后处理优化失败: {e}")
            return results
    
    async def _apply_diversity_optimization(self, results: List[FusionResult]) -> List[FusionResult]:
        """应用多样性优化"""
        try:
            if len(results) <= 1:
                return results
            
            # 贪心多样性选择
            selected_results = [results[0]]  # 选择得分最高的
            
            for result in results[1:]:
                # 计算与已选择结果的最大相似度
                max_similarity = 0.0
                
                for selected in selected_results:
                    # 简化相似度计算：基于内容长度和前100字符
                    content1 = result.content[:100]
                    content2 = selected.content[:100]
                    
                    # Jaccard相似度
                    set1 = set(content1.split())
                    set2 = set(content2.split())
                    
                    if set1 or set2:
                        similarity = len(set1 & set2) / len(set1 | set2)
                        max_similarity = max(max_similarity, similarity)
                
                # 多样性得分 = 原始得分 * (1 - 最大相似度)
                diversity_score = result.final_score * (1 - max_similarity)
                
                # 如果多样性得分足够高，则选择
                if diversity_score > 0.3 or len(selected_results) < 5:
                    selected_results.append(result)
            
            return selected_results
            
        except Exception as e:
            logger.error(f"多样性优化失败: {e}")
            return results
    
    async def _apply_personalization(self, results: List[FusionResult], 
                                   user_context: Dict[str, Any]) -> List[FusionResult]:
        """应用个性化调整"""
        try:
            # 简化个性化：基于用户偏好调整分数
            user_preferences = user_context.get("preferences", {})
            
            for result in results:
                # 基于用户偏好调整分数
                preference_bonus = 0.0
                
                # 内容长度偏好
                preferred_length = user_preferences.get("content_length", "medium")
                content_length = len(result.content)
                
                if preferred_length == "short" and content_length < 200:
                    preference_bonus += 0.1
                elif preferred_length == "long" and content_length > 500:
                    preference_bonus += 0.1
                elif preferred_length == "medium" and 200 <= content_length <= 500:
                    preference_bonus += 0.1
                
                # 应用个性化调整
                result.final_score = min(1.0, result.final_score + preference_bonus)
            
            # 重新排序
            results.sort(key=lambda x: x.final_score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"个性化调整失败: {e}")
            return results
    
    async def _record_fusion_performance(self, original_results: Dict[str, List[Dict]], 
                                       fused_results: List[FusionResult], 
                                       config: FusionConfig):
        """记录融合性能"""
        try:
            # 计算性能指标
            total_original = sum(len(r) for r in original_results.values())
            total_fused = len(fused_results)
            
            # 简化性能评分
            performance_score = min(1.0, total_fused / max(1, total_original) * 2)
            
            # 记录到历史
            performance_record = {
                "timestamp": datetime.now().isoformat(),
                "fusion_method": config.method,
                "original_count": total_original,
                "fused_count": total_fused,
                "performance_score": performance_score,
                "weights": config.weights
            }
            
            self.performance_history.append(performance_record)
            
            # 保持历史记录在合理范围内
            if len(self.performance_history) > 1000:
                self.performance_history = self.performance_history[-500:]
            
            # 记录监控指标
            enhanced_monitoring.record_multi_vector_metrics(
                len(original_results), performance_score, "fusion", config.method
            )
            
        except Exception as e:
            logger.error(f"性能记录失败: {e}")


# 创建全局实例
result_fusion_optimizer = ResultFusionOptimizer()
