"""
增强检索API接口
提供HyDE、查询重写、高级重排序等功能的API端点
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .enhanced_retrieval import enhanced_retrieval_engine, SearchResult
from .advanced_reranker import advanced_reranker, RerankingResult
from .config import get_retrieval_config


# API路由器
router = APIRouter(prefix="/enhanced", tags=["enhanced-retrieval"])


# 请求模型
class EnhancedSearchRequest(BaseModel):
    """增强检索请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    search_type: str = Field("enhanced", description="检索类型", regex="^(enhanced|hyde|rewrite|baseline)$")
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    collection_name: Optional[str] = Field(None, description="集合名称")
    document_id: Optional[str] = Field(None, description="文档ID")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    user_id: Optional[str] = Field(None, description="用户ID")
    enable_reranking: bool = Field(True, description="是否启用重排序")


class HyDERequest(BaseModel):
    """HyDE请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    generate_only: bool = Field(False, description="仅生成假设性文档，不进行检索")


class QueryRewriteRequest(BaseModel):
    """查询重写请求"""
    query: str = Field(..., description="原始查询", min_length=1, max_length=1000)
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    max_variants: int = Field(5, description="最大变体数量", ge=1, le=10)


class RerankRequest(BaseModel):
    """重排序请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    results: List[Dict[str, Any]] = Field(..., description="原始检索结果")
    user_id: Optional[str] = Field(None, description="用户ID")
    top_k: Optional[int] = Field(None, description="返回结果数量")


# 响应模型
class SearchResponse(BaseModel):
    """检索响应"""
    query: str
    search_type: str
    total_results: int
    processing_time: float
    results: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class HyDEResponse(BaseModel):
    """HyDE响应"""
    query: str
    hypothetical_document: str
    search_results: Optional[List[Dict[str, Any]]] = None
    processing_time: float


class QueryRewriteResponse(BaseModel):
    """查询重写响应"""
    original_query: str
    rewritten_queries: List[str]
    processing_time: float


class RerankResponse(BaseModel):
    """重排序响应"""
    query: str
    original_count: int
    reranked_count: int
    processing_time: float
    results: List[Dict[str, Any]]
    rerank_metadata: Dict[str, Any]


# API端点实现

@router.post("/search", response_model=SearchResponse)
async def enhanced_search(request: EnhancedSearchRequest):
    """
    增强检索主接口
    支持HyDE、查询重写、多路召回等功能
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到增强检索请求: {request.query[:50]}..., 类型: {request.search_type}")
        
        # 执行增强检索
        results = await enhanced_retrieval_engine.enhanced_search(
            query=request.query,
            search_type=request.search_type,
            top_k=request.top_k,
            context=request.context,
            collection_name=request.collection_name,
            document_id=request.document_id,
            filters=request.filters
        )
        
        # 高级重排序（如果启用）
        if request.enable_reranking and request.search_type != "baseline":
            reranked_results = await advanced_reranker.rerank(
                query=request.query,
                results=[{
                    "id": r.id,
                    "content": r.content,
                    "score": r.score,
                    "metadata": r.metadata
                } for r in results],
                user_id=request.user_id,
                top_k=request.top_k
            )
            
            # 转换重排序结果
            final_results = []
            for r in reranked_results:
                result_dict = {
                    "id": r.id,
                    "content": r.content,
                    "score": r.rerank_score,
                    "original_score": r.original_score,
                    "metadata": r.metadata,
                    "rerank_factors": r.factors
                }
                final_results.append(result_dict)
        else:
            # 不使用重排序
            final_results = []
            for r in results:
                result_dict = {
                    "id": r.id,
                    "content": r.content,
                    "score": r.score,
                    "metadata": r.metadata,
                    "source_type": r.source_type
                }
                final_results.append(result_dict)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = SearchResponse(
            query=request.query,
            search_type=request.search_type,
            total_results=len(final_results),
            processing_time=processing_time,
            results=final_results,
            metadata={
                "reranking_enabled": request.enable_reranking,
                "user_id": request.user_id,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        logger.info(f"增强检索完成，返回 {len(final_results)} 个结果，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"增强检索失败: {e}")
        raise HTTPException(status_code=500, detail=f"检索失败: {str(e)}")


@router.post("/hyde", response_model=HyDEResponse)
async def hyde_search(request: HyDERequest):
    """
    HyDE假设性文档生成和检索
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到HyDE请求: {request.query[:50]}...")
        
        # 生成假设性文档
        hypothetical_doc = await enhanced_retrieval_engine.hyde_generator.generate_hypothetical_document(
            request.query
        )
        
        search_results = None
        if not request.generate_only:
            # 执行HyDE检索
            results = await enhanced_retrieval_engine.hyde_generator.hyde_search(
                request.query, 
                request.top_k
            )
            
            search_results = []
            for r in results:
                result_dict = {
                    "id": r.id,
                    "content": r.content,
                    "score": r.score,
                    "metadata": r.metadata,
                    "source_type": r.source_type
                }
                search_results.append(result_dict)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = HyDEResponse(
            query=request.query,
            hypothetical_document=hypothetical_doc,
            search_results=search_results,
            processing_time=processing_time
        )
        
        logger.info(f"HyDE处理完成，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"HyDE处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"HyDE处理失败: {str(e)}")


@router.post("/rewrite", response_model=QueryRewriteResponse)
async def rewrite_query(request: QueryRewriteRequest):
    """
    查询重写和扩展
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到查询重写请求: {request.query[:50]}...")
        
        # 执行查询重写
        rewritten_queries = await enhanced_retrieval_engine.query_rewriter.rewrite_query(
            request.query,
            request.context
        )
        
        # 限制返回数量
        if len(rewritten_queries) > request.max_variants:
            rewritten_queries = rewritten_queries[:request.max_variants]
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = QueryRewriteResponse(
            original_query=request.query,
            rewritten_queries=rewritten_queries,
            processing_time=processing_time
        )
        
        logger.info(f"查询重写完成，生成 {len(rewritten_queries)} 个变体，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"查询重写失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询重写失败: {str(e)}")


@router.post("/rerank", response_model=RerankResponse)
async def rerank_results(request: RerankRequest):
    """
    高级重排序
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到重排序请求: {request.query[:50]}..., 结果数: {len(request.results)}")
        
        # 执行重排序
        reranked_results = await advanced_reranker.rerank(
            query=request.query,
            results=request.results,
            user_id=request.user_id,
            top_k=request.top_k
        )
        
        # 转换结果格式
        final_results = []
        for r in reranked_results:
            result_dict = {
                "id": r.id,
                "content": r.content,
                "score": r.rerank_score,
                "original_score": r.original_score,
                "metadata": r.metadata,
                "rerank_factors": r.factors
            }
            final_results.append(result_dict)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 计算重排序元数据
        rerank_metadata = {
            "factor_weights": advanced_reranker.factor_weights,
            "avg_score_change": 0.0,
            "rank_changes": 0
        }
        
        # 计算平均分数变化
        if final_results:
            score_changes = [
                abs(r["score"] - r["original_score"]) 
                for r in final_results
            ]
            rerank_metadata["avg_score_change"] = sum(score_changes) / len(score_changes)
        
        response = RerankResponse(
            query=request.query,
            original_count=len(request.results),
            reranked_count=len(final_results),
            processing_time=processing_time,
            results=final_results,
            rerank_metadata=rerank_metadata
        )
        
        logger.info(f"重排序完成，处理 {len(final_results)} 个结果，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"重排序失败: {e}")
        raise HTTPException(status_code=500, detail=f"重排序失败: {str(e)}")


@router.get("/config")
async def get_enhanced_config():
    """
    获取增强检索配置
    """
    try:
        config = get_retrieval_config()
        
        enhanced_config = {
            "hyde_enabled": config.get("enable_hyde", True),
            "query_rewrite_enabled": config.get("enable_query_rewrite", True),
            "reranking_enabled": config.get("enable_reranking", True),
            "max_query_variants": config.get("max_query_variants", 5),
            "rerank_factors": config.get("rerank_factors", {}),
            "supported_search_types": ["enhanced", "hyde", "rewrite", "baseline"]
        }
        
        return enhanced_config
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.get("/health")
async def health_check():
    """
    健康检查
    """
    try:
        # 检查各组件状态
        status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "enhanced_retrieval_engine": "ok",
                "query_rewriter": "ok",
                "hyde_generator": "ok",
                "advanced_reranker": "ok"
            }
        }
        
        # 可以添加更详细的健康检查逻辑
        
        return status
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/metrics")
async def get_metrics():
    """
    获取性能指标
    """
    try:
        # 这里应该从监控系统获取实际指标
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "performance": {
                "avg_search_latency": 0.85,
                "avg_rerank_latency": 0.32,
                "cache_hit_rate": 0.67,
                "error_rate": 0.02
            },
            "usage": {
                "total_searches_today": 1250,
                "hyde_usage_rate": 0.45,
                "rerank_usage_rate": 0.78,
                "query_rewrite_rate": 0.62
            },
            "quality": {
                "avg_user_satisfaction": 4.2,
                "avg_click_position": 2.8,
                "bounce_rate": 0.23
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"获取指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


# 将路由器添加到主应用
def include_enhanced_routes(app):
    """将增强检索路由添加到主应用"""
    app.include_router(router)
