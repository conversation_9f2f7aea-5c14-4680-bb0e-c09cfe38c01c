"""
方面向量索引构建器
为多向量检索构建和维护方面特定的向量索引
"""

import asyncio
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

from loguru import logger
from sentence_transformers import SentenceTransformer
from .database_client import database_client
from .enhanced_monitoring import enhanced_monitoring


@dataclass
class AspectVector:
    """方面向量"""
    document_id: str
    chunk_id: str
    aspect_name: str
    vector: List[float]
    content: str
    weight: float
    confidence: float


@dataclass
class IndexStats:
    """索引统计信息"""
    total_vectors: int
    aspect_distribution: Dict[str, int]
    avg_confidence: float
    index_size_mb: float
    build_time_seconds: float


class AspectVectorIndexer:
    """方面向量索引构建器"""
    
    def __init__(self):
        # 方面提取器配置
        self.aspect_extractors = {
            "semantic": self._extract_semantic_aspect,
            "factual": self._extract_factual_aspect,
            "temporal": self._extract_temporal_aspect,
            "entity": self._extract_entity_aspect,
            "procedural": self._extract_procedural_aspect
        }
        
        # 方面权重
        self.aspect_weights = {
            "semantic": 0.3,
            "factual": 0.25,
            "temporal": 0.15,
            "entity": 0.2,
            "procedural": 0.1
        }
        
        # 质量控制参数
        self.min_content_length = 20
        self.max_content_length = 1000
        self.min_confidence = 0.3
        self.batch_size = 100
        
        # 加载模型
        self._load_models()
    
    def _load_models(self):
        """加载所需模型"""
        try:
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # 加载NER模型（如果可用）
            try:
                import spacy
                self.nlp = spacy.load("zh_core_web_sm")
            except:
                logger.warning("中文NLP模型不可用，将使用简化的实体提取")
                self.nlp = None
            
            logger.info("方面向量索引模型加载成功")
            
        except Exception as e:
            logger.error(f"方面向量索引模型加载失败: {e}")
            self.embedding_model = None
            self.nlp = None
    
    async def build_aspect_index(self, rebuild: bool = False) -> IndexStats:
        """
        构建方面向量索引
        
        Args:
            rebuild: 是否重建索引
            
        Returns:
            索引统计信息
        """
        try:
            logger.info("开始构建方面向量索引...")
            start_time = datetime.now()
            
            # 1. 清理旧索引（如果重建）
            if rebuild:
                await self._clear_existing_index()
            
            # 2. 获取文档块
            document_chunks = await self._get_document_chunks()
            
            if not document_chunks:
                logger.warning("未找到文档块，无法构建索引")
                return IndexStats(0, {}, 0.0, 0.0, 0.0)
            
            # 3. 批量处理文档块
            total_vectors = 0
            aspect_distribution = {}
            confidence_scores = []
            
            for i in range(0, len(document_chunks), self.batch_size):
                batch = document_chunks[i:i + self.batch_size]
                
                batch_vectors = await self._process_chunk_batch(batch)
                
                if batch_vectors:
                    await self._save_aspect_vectors(batch_vectors)
                    
                    # 更新统计
                    total_vectors += len(batch_vectors)
                    for vector in batch_vectors:
                        aspect = vector.aspect_name
                        aspect_distribution[aspect] = aspect_distribution.get(aspect, 0) + 1
                        confidence_scores.append(vector.confidence)
                
                logger.info(f"已处理 {min(i + self.batch_size, len(document_chunks))}/{len(document_chunks)} 个文档块")
            
            # 4. 创建向量索引
            await self._create_vector_indexes()
            
            # 5. 计算统计信息
            build_time = (datetime.now() - start_time).total_seconds()
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            index_size = await self._calculate_index_size()
            
            stats = IndexStats(
                total_vectors=total_vectors,
                aspect_distribution=aspect_distribution,
                avg_confidence=avg_confidence,
                index_size_mb=index_size,
                build_time_seconds=build_time
            )
            
            # 6. 保存统计信息
            await self._save_index_stats(stats)
            
            logger.info(f"方面向量索引构建完成，共 {total_vectors} 个向量，耗时 {build_time:.2f} 秒")
            return stats
            
        except Exception as e:
            logger.error(f"方面向量索引构建失败: {e}")
            return IndexStats(0, {}, 0.0, 0.0, 0.0)
    
    async def _clear_existing_index(self):
        """清理现有索引"""
        try:
            await database_client.execute("DELETE FROM aspect_vectors")
            logger.info("已清理现有方面向量索引")
            
        except Exception as e:
            logger.error(f"清理索引失败: {e}")
    
    async def _get_document_chunks(self) -> List[Dict]:
        """获取文档块"""
        try:
            chunks = await database_client.fetch("""
                SELECT 
                    id,
                    document_id,
                    content,
                    chunk_weight,
                    quality_score
                FROM document_chunks
                WHERE LENGTH(content) >= $1 
                AND LENGTH(content) <= $2
                ORDER BY quality_score DESC
            """, self.min_content_length, self.max_content_length)
            
            return [dict(chunk) for chunk in chunks]
            
        except Exception as e:
            logger.error(f"获取文档块失败: {e}")
            return []
    
    async def _process_chunk_batch(self, chunks: List[Dict]) -> List[AspectVector]:
        """处理文档块批次"""
        try:
            aspect_vectors = []
            
            for chunk in chunks:
                chunk_vectors = await self._extract_chunk_aspects(chunk)
                aspect_vectors.extend(chunk_vectors)
            
            return aspect_vectors
            
        except Exception as e:
            logger.error(f"处理文档块批次失败: {e}")
            return []
    
    async def _extract_chunk_aspects(self, chunk: Dict) -> List[AspectVector]:
        """提取文档块的各个方面"""
        try:
            content = chunk["content"]
            document_id = chunk["document_id"]
            chunk_id = chunk["id"]
            
            aspect_vectors = []
            
            for aspect_name, extractor in self.aspect_extractors.items():
                # 提取方面内容
                aspect_content = await extractor(content)
                
                if not aspect_content or len(aspect_content) < 10:
                    continue
                
                # 生成向量
                vector = await self._generate_vector(aspect_content)
                
                if not vector:
                    continue
                
                # 计算置信度
                confidence = await self._calculate_aspect_confidence(
                    aspect_content, aspect_name, content
                )
                
                if confidence < self.min_confidence:
                    continue
                
                # 创建方面向量
                aspect_vector = AspectVector(
                    document_id=document_id,
                    chunk_id=chunk_id,
                    aspect_name=aspect_name,
                    vector=vector,
                    content=aspect_content,
                    weight=self.aspect_weights.get(aspect_name, 0.1),
                    confidence=confidence
                )
                
                aspect_vectors.append(aspect_vector)
            
            return aspect_vectors
            
        except Exception as e:
            logger.error(f"提取文档块方面失败: {e}")
            return []
    
    async def _extract_semantic_aspect(self, content: str) -> str:
        """提取语义方面"""
        # 语义方面就是原始内容
        return content
    
    async def _extract_factual_aspect(self, content: str) -> str:
        """提取事实方面"""
        try:
            # 提取包含数字、统计信息的句子
            import re
            
            factual_patterns = [
                r'[0-9]+(?:\.[0-9]+)?%',  # 百分比
                r'[0-9]+(?:\.[0-9]+)?(?:万|千|百|十)?(?:元|人|个|项|次)',  # 数量
                r'(?:增长|下降|提高|降低|达到)[0-9]+',  # 变化数据
                r'(?:共|总计|合计|累计)[0-9]+',  # 总计数据
            ]
            
            factual_sentences = []
            sentences = content.split('。')
            
            for sentence in sentences:
                for pattern in factual_patterns:
                    if re.search(pattern, sentence):
                        factual_sentences.append(sentence.strip())
                        break
            
            return '。'.join(factual_sentences) if factual_sentences else content[:100]
            
        except Exception as e:
            logger.error(f"事实方面提取失败: {e}")
            return content[:100]
    
    async def _extract_temporal_aspect(self, content: str) -> str:
        """提取时间方面"""
        try:
            import re
            
            temporal_patterns = [
                r'[0-9]{4}年',  # 年份
                r'[0-9]{1,2}月',  # 月份
                r'[0-9]{1,2}日',  # 日期
                r'(?:最近|近期|目前|现在|过去|未来|将来)',  # 时间词
                r'(?:时间|时候|期间|阶段|时期)',  # 时间概念
            ]
            
            temporal_sentences = []
            sentences = content.split('。')
            
            for sentence in sentences:
                for pattern in temporal_patterns:
                    if re.search(pattern, sentence):
                        temporal_sentences.append(sentence.strip())
                        break
            
            return '。'.join(temporal_sentences) if temporal_sentences else content[:100]
            
        except Exception as e:
            logger.error(f"时间方面提取失败: {e}")
            return content[:100]
    
    async def _extract_entity_aspect(self, content: str) -> str:
        """提取实体方面"""
        try:
            if self.nlp:
                doc = self.nlp(content)
                entities = [ent.text for ent in doc.ents]
                
                if entities:
                    # 构建包含实体的句子
                    entity_sentences = []
                    sentences = content.split('。')
                    
                    for sentence in sentences:
                        for entity in entities:
                            if entity in sentence:
                                entity_sentences.append(sentence.strip())
                                break
                    
                    return '。'.join(entity_sentences) if entity_sentences else content[:100]
            
            # 简化实体提取
            import re
            entity_patterns = [
                r'[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*',  # 英文实体
                r'[\u4e00-\u9fff]+(?:公司|集团|企业|机构|组织)',  # 中文机构
                r'[\u4e00-\u9fff]{2,4}(?:市|省|县|区)',  # 地名
            ]
            
            entity_content = []
            for pattern in entity_patterns:
                matches = re.findall(pattern, content)
                entity_content.extend(matches)
            
            return ' '.join(entity_content) if entity_content else content[:100]
            
        except Exception as e:
            logger.error(f"实体方面提取失败: {e}")
            return content[:100]
    
    async def _extract_procedural_aspect(self, content: str) -> str:
        """提取程序方面"""
        try:
            import re
            
            procedural_patterns = [
                r'(?:第[一二三四五六七八九十\d]+步|步骤[一二三四五六七八九十\d]+)',  # 步骤
                r'(?:首先|然后|接着|最后|最终)',  # 顺序词
                r'(?:方法|流程|过程|操作|程序)',  # 程序词
                r'(?:如何|怎么|怎样)',  # 方法询问
            ]
            
            procedural_sentences = []
            sentences = content.split('。')
            
            for sentence in sentences:
                for pattern in procedural_patterns:
                    if re.search(pattern, sentence):
                        procedural_sentences.append(sentence.strip())
                        break
            
            return '。'.join(procedural_sentences) if procedural_sentences else content[:100]
            
        except Exception as e:
            logger.error(f"程序方面提取失败: {e}")
            return content[:100]
    
    async def _generate_vector(self, content: str) -> List[float]:
        """生成向量"""
        try:
            if not self.embedding_model:
                return []
            
            embedding = self.embedding_model.encode([content])
            return embedding[0].tolist()
            
        except Exception as e:
            logger.error(f"向量生成失败: {e}")
            return []
    
    async def _calculate_aspect_confidence(self, aspect_content: str, 
                                         aspect_name: str, original_content: str) -> float:
        """计算方面置信度"""
        try:
            # 基础置信度
            base_confidence = 0.5
            
            # 内容长度因子
            length_factor = min(1.0, len(aspect_content) / 50.0)
            
            # 方面特异性因子
            specificity_factor = self._calculate_aspect_specificity(aspect_content, aspect_name)
            
            # 内容质量因子
            quality_factor = self._calculate_content_quality(aspect_content)
            
            # 相关性因子
            relevance_factor = len(aspect_content) / len(original_content)
            
            confidence = (
                base_confidence * 0.3 +
                length_factor * 0.2 +
                specificity_factor * 0.3 +
                quality_factor * 0.1 +
                relevance_factor * 0.1
            )
            
            return min(1.0, confidence)
            
        except Exception as e:
            logger.error(f"方面置信度计算失败: {e}")
            return 0.5
    
    def _calculate_aspect_specificity(self, content: str, aspect_name: str) -> float:
        """计算方面特异性"""
        try:
            import re
            
            specificity_keywords = {
                "factual": [r'[0-9]+', r'数据', r'统计', r'比例', r'百分比'],
                "temporal": [r'[0-9]{4}年', r'时间', r'期间', r'阶段'],
                "entity": [r'公司', r'机构', r'组织', r'[A-Z][a-z]+'],
                "procedural": [r'步骤', r'方法', r'流程', r'操作'],
                "semantic": []  # 语义方面没有特定关键词
            }
            
            keywords = specificity_keywords.get(aspect_name, [])
            if not keywords:
                return 0.7  # 语义方面默认较高特异性
            
            match_count = 0
            for keyword in keywords:
                if re.search(keyword, content):
                    match_count += 1
            
            specificity = match_count / len(keywords) if keywords else 0.5
            return min(1.0, specificity + 0.3)  # 基础分数0.3
            
        except Exception as e:
            logger.error(f"方面特异性计算失败: {e}")
            return 0.5
    
    def _calculate_content_quality(self, content: str) -> float:
        """计算内容质量"""
        try:
            # 长度合理性
            length_score = 1.0 if 20 <= len(content) <= 500 else 0.5
            
            # 字符多样性
            unique_chars = len(set(content))
            diversity_score = min(1.0, unique_chars / len(content) * 2)
            
            # 是否包含标点符号
            punctuation_score = 1.0 if any(p in content for p in '，。！？；：') else 0.7
            
            quality = (length_score + diversity_score + punctuation_score) / 3
            return quality
            
        except Exception as e:
            logger.error(f"内容质量计算失败: {e}")
            return 0.5
    
    async def _save_aspect_vectors(self, vectors: List[AspectVector]):
        """保存方面向量"""
        try:
            for vector in vectors:
                await database_client.execute("""
                    INSERT INTO aspect_vectors 
                    (document_id, chunk_id, aspect_name, vector, content, weight, confidence)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    ON CONFLICT DO NOTHING
                """,
                vector.document_id,
                vector.chunk_id,
                vector.aspect_name,
                vector.vector,
                vector.content,
                vector.weight,
                vector.confidence
                )
            
        except Exception as e:
            logger.error(f"保存方面向量失败: {e}")
    
    async def _create_vector_indexes(self):
        """创建向量索引"""
        try:
            # 创建向量索引
            await database_client.execute("""
                CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_cosine 
                ON aspect_vectors USING ivfflat (vector vector_cosine_ops) 
                WITH (lists = 100)
            """)
            
            await database_client.execute("""
                CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_l2 
                ON aspect_vectors USING ivfflat (vector vector_l2_ops) 
                WITH (lists = 100)
            """)
            
            # 更新表统计信息
            await database_client.execute("ANALYZE aspect_vectors")
            
            logger.info("方面向量索引创建完成")
            
        except Exception as e:
            logger.error(f"创建向量索引失败: {e}")
    
    async def _calculate_index_size(self) -> float:
        """计算索引大小"""
        try:
            result = await database_client.fetchrow("""
                SELECT pg_total_relation_size('aspect_vectors') as size_bytes
            """)
            
            size_mb = result['size_bytes'] / (1024 * 1024) if result else 0
            return size_mb
            
        except Exception as e:
            logger.error(f"计算索引大小失败: {e}")
            return 0.0
    
    async def _save_index_stats(self, stats: IndexStats):
        """保存索引统计信息"""
        try:
            stats_file = Path("data/aspect_index_stats.json")
            stats_file.parent.mkdir(exist_ok=True)
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "total_vectors": stats.total_vectors,
                    "aspect_distribution": stats.aspect_distribution,
                    "avg_confidence": stats.avg_confidence,
                    "index_size_mb": stats.index_size_mb,
                    "build_time_seconds": stats.build_time_seconds,
                    "build_timestamp": datetime.now().isoformat()
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"索引统计信息保存到: {stats_file}")
            
        except Exception as e:
            logger.error(f"保存索引统计信息失败: {e}")


# 创建全局实例
aspect_vector_indexer = AspectVectorIndexer()
