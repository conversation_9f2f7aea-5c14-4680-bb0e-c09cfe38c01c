"""
用户反馈收集系统
收集和分析用户对检索结果的评价、点击行为、满意度等数据
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

from sqlalchemy import Column, String, Integer, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from loguru import logger

from app.database import get_database_session


Base = declarative_base()


class FeedbackType(Enum):
    """反馈类型"""
    CLICK = "click"
    RATING = "rating"
    BOOKMARK = "bookmark"
    SHARE = "share"
    DWELL_TIME = "dwell_time"
    QUERY_REFINEMENT = "query_refinement"
    SESSION_SATISFACTION = "session_satisfaction"


class SentimentType(Enum):
    """情感类型"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


@dataclass
class ClickEvent:
    """点击事件"""
    user_id: str
    session_id: str
    query: str
    document_id: str
    position: int
    timestamp: datetime
    search_method: str
    result_score: float
    metadata: Dict[str, Any] = None


@dataclass
class RatingEvent:
    """评分事件"""
    user_id: str
    session_id: str
    query: str
    document_id: str
    rating: int  # 1-5分
    timestamp: datetime
    search_method: str
    comment: Optional[str] = None
    metadata: Dict[str, Any] = None


@dataclass
class SessionFeedback:
    """会话反馈"""
    user_id: str
    session_id: str
    satisfaction_score: int  # 1-5分
    query_count: int
    successful_queries: int
    total_dwell_time: float
    timestamp: datetime
    feedback_comment: Optional[str] = None
    metadata: Dict[str, Any] = None


class UserFeedback(Base):
    """用户反馈表"""
    __tablename__ = "user_feedback"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    session_id = Column(String, nullable=False, index=True)
    feedback_type = Column(String, nullable=False)
    query = Column(Text, nullable=False)
    document_id = Column(String, nullable=True)
    rating = Column(Integer, nullable=True)
    position = Column(Integer, nullable=True)
    dwell_time = Column(Float, nullable=True)
    search_method = Column(String, nullable=True)
    result_score = Column(Float, nullable=True)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    comment = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)
    processed = Column(Boolean, default=False)


class UserBehavior(Base):
    """用户行为表"""
    __tablename__ = "user_behavior"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    session_id = Column(String, nullable=False, index=True)
    action_type = Column(String, nullable=False)  # search, click, scroll, exit
    query = Column(Text, nullable=True)
    document_id = Column(String, nullable=True)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    duration = Column(Float, nullable=True)
    metadata = Column(JSON, nullable=True)


class FeedbackAnalytics(Base):
    """反馈分析表"""
    __tablename__ = "feedback_analytics"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    date = Column(DateTime, nullable=False, index=True)
    metric_name = Column(String, nullable=False)
    metric_value = Column(Float, nullable=False)
    search_method = Column(String, nullable=True)
    user_segment = Column(String, nullable=True)
    metadata = Column(JSON, nullable=True)


class UserFeedbackCollector:
    """用户反馈收集器"""
    
    def __init__(self):
        self.session_factory = sessionmaker()
        
    async def record_click_event(self, click_event: ClickEvent) -> bool:
        """记录点击事件"""
        try:
            async with get_database_session() as session:
                feedback = UserFeedback(
                    user_id=click_event.user_id,
                    session_id=click_event.session_id,
                    feedback_type=FeedbackType.CLICK.value,
                    query=click_event.query,
                    document_id=click_event.document_id,
                    position=click_event.position,
                    search_method=click_event.search_method,
                    result_score=click_event.result_score,
                    timestamp=click_event.timestamp,
                    metadata=click_event.metadata
                )
                
                session.add(feedback)
                await session.commit()
                
                logger.debug(f"记录点击事件: 用户{click_event.user_id}, 文档{click_event.document_id}")
                return True
                
        except Exception as e:
            logger.error(f"记录点击事件失败: {e}")
            return False
    
    async def record_rating_event(self, rating_event: RatingEvent) -> bool:
        """记录评分事件"""
        try:
            async with get_database_session() as session:
                feedback = UserFeedback(
                    user_id=rating_event.user_id,
                    session_id=rating_event.session_id,
                    feedback_type=FeedbackType.RATING.value,
                    query=rating_event.query,
                    document_id=rating_event.document_id,
                    rating=rating_event.rating,
                    search_method=rating_event.search_method,
                    timestamp=rating_event.timestamp,
                    comment=rating_event.comment,
                    metadata=rating_event.metadata
                )
                
                session.add(feedback)
                await session.commit()
                
                logger.debug(f"记录评分事件: 用户{rating_event.user_id}, 评分{rating_event.rating}")
                return True
                
        except Exception as e:
            logger.error(f"记录评分事件失败: {e}")
            return False
    
    async def record_session_feedback(self, session_feedback: SessionFeedback) -> bool:
        """记录会话反馈"""
        try:
            async with get_database_session() as session:
                feedback = UserFeedback(
                    user_id=session_feedback.user_id,
                    session_id=session_feedback.session_id,
                    feedback_type=FeedbackType.SESSION_SATISFACTION.value,
                    query="",  # 会话级别反馈没有特定查询
                    rating=session_feedback.satisfaction_score,
                    timestamp=session_feedback.timestamp,
                    comment=session_feedback.feedback_comment,
                    metadata={
                        "query_count": session_feedback.query_count,
                        "successful_queries": session_feedback.successful_queries,
                        "total_dwell_time": session_feedback.total_dwell_time,
                        **(session_feedback.metadata or {})
                    }
                )
                
                session.add(feedback)
                await session.commit()
                
                logger.debug(f"记录会话反馈: 用户{session_feedback.user_id}, 满意度{session_feedback.satisfaction_score}")
                return True
                
        except Exception as e:
            logger.error(f"记录会话反馈失败: {e}")
            return False
    
    async def record_dwell_time(self, user_id: str, session_id: str, 
                               document_id: str, dwell_time: float) -> bool:
        """记录停留时间"""
        try:
            async with get_database_session() as session:
                feedback = UserFeedback(
                    user_id=user_id,
                    session_id=session_id,
                    feedback_type=FeedbackType.DWELL_TIME.value,
                    query="",
                    document_id=document_id,
                    dwell_time=dwell_time,
                    timestamp=datetime.utcnow()
                )
                
                session.add(feedback)
                await session.commit()
                
                logger.debug(f"记录停留时间: 用户{user_id}, 文档{document_id}, 时长{dwell_time}秒")
                return True
                
        except Exception as e:
            logger.error(f"记录停留时间失败: {e}")
            return False
    
    async def record_user_behavior(self, user_id: str, session_id: str,
                                  action_type: str, **kwargs) -> bool:
        """记录用户行为"""
        try:
            async with get_database_session() as session:
                behavior = UserBehavior(
                    user_id=user_id,
                    session_id=session_id,
                    action_type=action_type,
                    query=kwargs.get('query'),
                    document_id=kwargs.get('document_id'),
                    duration=kwargs.get('duration'),
                    metadata=kwargs.get('metadata')
                )
                
                session.add(behavior)
                await session.commit()
                
                logger.debug(f"记录用户行为: 用户{user_id}, 行为{action_type}")
                return True
                
        except Exception as e:
            logger.error(f"记录用户行为失败: {e}")
            return False


class FeedbackAnalyzer:
    """反馈分析器"""
    
    def __init__(self):
        pass
    
    async def calculate_click_through_rate(self, search_method: Optional[str] = None,
                                         start_date: Optional[datetime] = None,
                                         end_date: Optional[datetime] = None) -> float:
        """计算点击率"""
        try:
            async with get_database_session() as session:
                # 构建查询条件
                conditions = [UserFeedback.feedback_type == FeedbackType.CLICK.value]
                
                if search_method:
                    conditions.append(UserFeedback.search_method == search_method)
                if start_date:
                    conditions.append(UserFeedback.timestamp >= start_date)
                if end_date:
                    conditions.append(UserFeedback.timestamp <= end_date)
                
                # 计算点击数
                click_count = await session.query(UserFeedback).filter(*conditions).count()
                
                # 计算总搜索数（假设每个唯一的session_id + query组合代表一次搜索）
                search_conditions = conditions.copy()
                search_conditions[0] = UserFeedback.feedback_type.in_([
                    FeedbackType.CLICK.value, 
                    FeedbackType.RATING.value
                ])
                
                total_searches = await session.query(
                    UserFeedback.session_id, 
                    UserFeedback.query
                ).filter(*search_conditions).distinct().count()
                
                if total_searches > 0:
                    ctr = click_count / total_searches
                    logger.info(f"点击率计算: {click_count}/{total_searches} = {ctr:.3f}")
                    return ctr
                else:
                    return 0.0
                    
        except Exception as e:
            logger.error(f"计算点击率失败: {e}")
            return 0.0
    
    async def calculate_average_rating(self, search_method: Optional[str] = None,
                                     start_date: Optional[datetime] = None,
                                     end_date: Optional[datetime] = None) -> float:
        """计算平均评分"""
        try:
            async with get_database_session() as session:
                conditions = [
                    UserFeedback.feedback_type == FeedbackType.RATING.value,
                    UserFeedback.rating.isnot(None)
                ]
                
                if search_method:
                    conditions.append(UserFeedback.search_method == search_method)
                if start_date:
                    conditions.append(UserFeedback.timestamp >= start_date)
                if end_date:
                    conditions.append(UserFeedback.timestamp <= end_date)
                
                result = await session.query(
                    func.avg(UserFeedback.rating)
                ).filter(*conditions).scalar()
                
                avg_rating = float(result) if result else 0.0
                logger.info(f"平均评分: {avg_rating:.2f}")
                return avg_rating
                
        except Exception as e:
            logger.error(f"计算平均评分失败: {e}")
            return 0.0
    
    async def calculate_session_satisfaction(self, start_date: Optional[datetime] = None,
                                           end_date: Optional[datetime] = None) -> float:
        """计算会话满意度"""
        try:
            async with get_database_session() as session:
                conditions = [
                    UserFeedback.feedback_type == FeedbackType.SESSION_SATISFACTION.value,
                    UserFeedback.rating.isnot(None)
                ]
                
                if start_date:
                    conditions.append(UserFeedback.timestamp >= start_date)
                if end_date:
                    conditions.append(UserFeedback.timestamp <= end_date)
                
                result = await session.query(
                    func.avg(UserFeedback.rating)
                ).filter(*conditions).scalar()
                
                satisfaction = float(result) if result else 0.0
                logger.info(f"会话满意度: {satisfaction:.2f}")
                return satisfaction
                
        except Exception as e:
            logger.error(f"计算会话满意度失败: {e}")
            return 0.0
    
    async def analyze_search_method_performance(self) -> Dict[str, Dict[str, float]]:
        """分析不同搜索方法的性能"""
        try:
            async with get_database_session() as session:
                # 获取所有搜索方法
                search_methods = await session.query(
                    UserFeedback.search_method
                ).filter(
                    UserFeedback.search_method.isnot(None)
                ).distinct().all()
                
                performance = {}
                
                for method_tuple in search_methods:
                    method = method_tuple[0]
                    
                    # 计算各项指标
                    ctr = await self.calculate_click_through_rate(search_method=method)
                    avg_rating = await self.calculate_average_rating(search_method=method)
                    
                    performance[method] = {
                        "click_through_rate": ctr,
                        "average_rating": avg_rating
                    }
                
                logger.info(f"搜索方法性能分析完成: {len(performance)}个方法")
                return performance
                
        except Exception as e:
            logger.error(f"分析搜索方法性能失败: {e}")
            return {}
    
    async def get_user_feedback_trends(self, days: int = 30) -> Dict[str, List[Dict[str, Any]]]:
        """获取用户反馈趋势"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            async with get_database_session() as session:
                # 按天统计各类反馈
                daily_stats = await session.query(
                    func.date(UserFeedback.timestamp).label('date'),
                    UserFeedback.feedback_type,
                    func.count(UserFeedback.id).label('count'),
                    func.avg(UserFeedback.rating).label('avg_rating')
                ).filter(
                    UserFeedback.timestamp >= start_date,
                    UserFeedback.timestamp <= end_date
                ).group_by(
                    func.date(UserFeedback.timestamp),
                    UserFeedback.feedback_type
                ).all()
                
                # 组织数据
                trends = {}
                for stat in daily_stats:
                    feedback_type = stat.feedback_type
                    if feedback_type not in trends:
                        trends[feedback_type] = []
                    
                    trends[feedback_type].append({
                        "date": stat.date.isoformat(),
                        "count": stat.count,
                        "avg_rating": float(stat.avg_rating) if stat.avg_rating else None
                    })
                
                logger.info(f"获取{days}天反馈趋势: {len(trends)}种类型")
                return trends
                
        except Exception as e:
            logger.error(f"获取反馈趋势失败: {e}")
            return {}
    
    async def generate_feedback_report(self) -> Dict[str, Any]:
        """生成反馈报告"""
        try:
            # 计算关键指标
            overall_ctr = await self.calculate_click_through_rate()
            overall_rating = await self.calculate_average_rating()
            session_satisfaction = await self.calculate_session_satisfaction()
            
            # 分析搜索方法性能
            method_performance = await self.analyze_search_method_performance()
            
            # 获取趋势数据
            trends = await self.get_user_feedback_trends()
            
            # 生成报告
            report = {
                "generated_at": datetime.utcnow().isoformat(),
                "overall_metrics": {
                    "click_through_rate": overall_ctr,
                    "average_rating": overall_rating,
                    "session_satisfaction": session_satisfaction
                },
                "search_method_performance": method_performance,
                "trends": trends,
                "recommendations": self._generate_recommendations(
                    overall_ctr, overall_rating, session_satisfaction, method_performance
                )
            }
            
            logger.info("反馈报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成反馈报告失败: {e}")
            return {}
    
    def _generate_recommendations(self, ctr: float, avg_rating: float, 
                                satisfaction: float, method_performance: Dict[str, Dict[str, float]]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于整体指标的建议
        if ctr < 0.2:
            recommendations.append("点击率较低，建议优化搜索结果排序和相关性")
        
        if avg_rating < 3.0:
            recommendations.append("用户评分较低，建议改进搜索质量和结果展示")
        
        if satisfaction < 3.5:
            recommendations.append("会话满意度较低，建议优化用户体验和交互流程")
        
        # 基于搜索方法性能的建议
        if method_performance:
            best_method = max(method_performance.items(), 
                            key=lambda x: x[1].get('average_rating', 0))
            worst_method = min(method_performance.items(), 
                             key=lambda x: x[1].get('average_rating', 0))
            
            if best_method[1]['average_rating'] - worst_method[1]['average_rating'] > 0.5:
                recommendations.append(
                    f"建议优化{worst_method[0]}搜索方法，参考{best_method[0]}的实现"
                )
        
        # 通用建议
        if not recommendations:
            recommendations.append("系统表现良好，建议继续监控用户反馈并持续优化")
        
        return recommendations


# 全局实例
feedback_collector = UserFeedbackCollector()
feedback_analyzer = FeedbackAnalyzer()


async def collect_click_feedback(user_id: str, session_id: str, query: str,
                                document_id: str, position: int, search_method: str,
                                result_score: float, metadata: Dict[str, Any] = None) -> bool:
    """收集点击反馈"""
    click_event = ClickEvent(
        user_id=user_id,
        session_id=session_id,
        query=query,
        document_id=document_id,
        position=position,
        timestamp=datetime.utcnow(),
        search_method=search_method,
        result_score=result_score,
        metadata=metadata
    )
    
    return await feedback_collector.record_click_event(click_event)


async def collect_rating_feedback(user_id: str, session_id: str, query: str,
                                 document_id: str, rating: int, search_method: str,
                                 comment: Optional[str] = None, 
                                 metadata: Dict[str, Any] = None) -> bool:
    """收集评分反馈"""
    rating_event = RatingEvent(
        user_id=user_id,
        session_id=session_id,
        query=query,
        document_id=document_id,
        rating=rating,
        timestamp=datetime.utcnow(),
        search_method=search_method,
        comment=comment,
        metadata=metadata
    )
    
    return await feedback_collector.record_rating_event(rating_event)


async def collect_session_feedback(user_id: str, session_id: str, satisfaction_score: int,
                                  query_count: int, successful_queries: int,
                                  total_dwell_time: float, feedback_comment: Optional[str] = None,
                                  metadata: Dict[str, Any] = None) -> bool:
    """收集会话反馈"""
    session_feedback = SessionFeedback(
        user_id=user_id,
        session_id=session_id,
        satisfaction_score=satisfaction_score,
        query_count=query_count,
        successful_queries=successful_queries,
        total_dwell_time=total_dwell_time,
        timestamp=datetime.utcnow(),
        feedback_comment=feedback_comment,
        metadata=metadata
    )
    
    return await feedback_collector.record_session_feedback(session_feedback)


async def get_feedback_analytics() -> Dict[str, Any]:
    """获取反馈分析"""
    return await feedback_analyzer.generate_feedback_report()
