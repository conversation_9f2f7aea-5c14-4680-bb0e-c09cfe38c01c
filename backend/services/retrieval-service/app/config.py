"""
检索服务配置模块
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = Field(default="RAG检索服务", description="应用名称")
    VERSION: str = Field(default="1.0.0", description="版本号")
    DEBUG: bool = Field(default=False, description="调试模式")
    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=8002, description="服务端口")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/retrieval_service",
        description="数据库连接URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, description="数据库连接池大小")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, description="数据库连接池最大溢出")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379", description="Redis连接URL")
    REDIS_DB: int = Field(default=0, description="Redis数据库编号")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis密码")
    
    # 向量数据库配置
    VECTOR_DB_TYPE: str = Field(default="chroma", description="向量数据库类型")
    CHROMA_URL: str = Field(default="http://localhost:8000", description="ChromaDB URL")
    CHROMA_PATH: str = Field(default="./chroma_db", description="ChromaDB本地路径")
    
    # Pinecone配置
    PINECONE_API_KEY: Optional[str] = Field(default=None, description="Pinecone API密钥")
    PINECONE_ENVIRONMENT: str = Field(default="us-west1-gcp", description="Pinecone环境")
    
    # Elasticsearch配置
    ELASTICSEARCH_URL: str = Field(default="http://localhost:9200", description="Elasticsearch URL")
    ELASTICSEARCH_INDEX: str = Field(default="documents", description="Elasticsearch索引名")
    ELASTICSEARCH_USERNAME: Optional[str] = Field(default=None, description="Elasticsearch用户名")
    ELASTICSEARCH_PASSWORD: Optional[str] = Field(default=None, description="Elasticsearch密码")
    
    # 嵌入服务配置
    EMBEDDING_SERVICE_URL: str = Field(
        default="http://localhost:8001", 
        description="嵌入服务URL"
    )
    EMBEDDING_SERVICE_TIMEOUT: int = Field(default=30, description="嵌入服务超时时间")
    
    # OpenAI配置
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API密钥")
    OPENAI_BASE_URL: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL")
    
    # 检索配置
    DEFAULT_TOP_K: int = Field(default=10, description="默认返回结果数量")
    MAX_TOP_K: int = Field(default=100, description="最大返回结果数量")
    DEFAULT_SIMILARITY_THRESHOLD: float = Field(default=0.7, description="默认相似度阈值")
    
    # 混合检索配置
    SEMANTIC_WEIGHT: float = Field(default=0.7, description="语义检索权重")
    KEYWORD_WEIGHT: float = Field(default=0.3, description="关键词检索权重")
    
    # 重排序配置
    ENABLE_RERANKING: bool = Field(default=True, description="启用重排序")
    RERANK_TOP_K: int = Field(default=50, description="重排序候选数量")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, description="缓存过期时间（秒）")
    ENABLE_QUERY_CACHE: bool = Field(default=True, description="启用查询缓存")
    ENABLE_RESULT_CACHE: bool = Field(default=True, description="启用结果缓存")
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, description="最大并发请求数")
    REQUEST_TIMEOUT: int = Field(default=30, description="请求超时时间")
    BATCH_SIZE: int = Field(default=32, description="批处理大小")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE: str = Field(default="logs/retrieval_service.log", description="日志文件路径")
    LOG_ROTATION: str = Field(default="1 day", description="日志轮转周期")
    LOG_RETENTION: str = Field(default="30 days", description="日志保留时间")
    
    # CORS配置
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:3100"],
        description="允许的CORS来源"
    )
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, description="启用指标收集")
    METRICS_PORT: int = Field(default=8003, description="指标服务端口")
    
    # 安全配置
    SECRET_KEY: str = Field(default="your-secret-key-here", description="密钥")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()


def get_vector_db_config() -> dict:
    """获取向量数据库配置"""
    if settings.VECTOR_DB_TYPE.lower() == "chroma":
        return {
            "type": "chroma",
            "url": settings.CHROMA_URL,
            "path": settings.CHROMA_PATH
        }
    elif settings.VECTOR_DB_TYPE.lower() == "pinecone":
        return {
            "type": "pinecone",
            "api_key": settings.PINECONE_API_KEY,
            "environment": settings.PINECONE_ENVIRONMENT
        }
    else:
        raise ValueError(f"不支持的向量数据库类型: {settings.VECTOR_DB_TYPE}")


def get_elasticsearch_config() -> dict:
    """获取Elasticsearch配置"""
    config = {
        "url": settings.ELASTICSEARCH_URL,
        "index": settings.ELASTICSEARCH_INDEX
    }
    
    if settings.ELASTICSEARCH_USERNAME and settings.ELASTICSEARCH_PASSWORD:
        config["auth"] = (settings.ELASTICSEARCH_USERNAME, settings.ELASTICSEARCH_PASSWORD)
    
    return config


def get_embedding_service_config() -> dict:
    """获取嵌入服务配置"""
    return {
        "url": settings.EMBEDDING_SERVICE_URL,
        "timeout": settings.EMBEDDING_SERVICE_TIMEOUT
    }


def get_cache_config() -> dict:
    """获取缓存配置"""
    return {
        "ttl": settings.CACHE_TTL,
        "enable_query_cache": settings.ENABLE_QUERY_CACHE,
        "enable_result_cache": settings.ENABLE_RESULT_CACHE
    }


def get_retrieval_config() -> dict:
    """获取检索配置"""
    return {
        "default_top_k": settings.DEFAULT_TOP_K,
        "max_top_k": settings.MAX_TOP_K,
        "similarity_threshold": settings.DEFAULT_SIMILARITY_THRESHOLD,
        "semantic_weight": settings.SEMANTIC_WEIGHT,
        "keyword_weight": settings.KEYWORD_WEIGHT,
        "enable_reranking": settings.ENABLE_RERANKING,
        "rerank_top_k": settings.RERANK_TOP_K
    }


def get_performance_config() -> dict:
    """获取性能配置"""
    return {
        "max_concurrent_requests": settings.MAX_CONCURRENT_REQUESTS,
        "request_timeout": settings.REQUEST_TIMEOUT,
        "batch_size": settings.BATCH_SIZE
    }
