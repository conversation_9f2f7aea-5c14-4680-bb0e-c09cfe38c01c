"""
增强监控系统
支持先进检索技术的性能监控和告警
"""

import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from contextlib import asynccontextmanager
import json

from prometheus_client import (
    Counter, Histogram, Gauge, Summary, 
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)
from loguru import logger
import psutil


@dataclass
class MetricConfig:
    """指标配置"""
    name: str
    description: str
    labels: List[str] = None
    buckets: List[float] = None


class EnhancedMonitoring:
    """增强监控系统"""
    
    def __init__(self):
        self.registry = CollectorRegistry()
        self.metrics = {}
        self.alert_rules = {}
        self.setup_metrics()
    
    def setup_metrics(self):
        """设置监控指标"""
        
        # 基础性能指标
        self.metrics.update({
            # 请求计数器
            "search_requests_total": Counter(
                "search_requests_total",
                "总搜索请求数",
                ["search_type", "status"],
                registry=self.registry
            ),
            
            # 响应时间直方图
            "search_duration_seconds": Histogram(
                "search_duration_seconds",
                "搜索响应时间",
                ["search_type"],
                buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
                registry=self.registry
            ),
            
            # 结果质量指标
            "search_result_count": Histogram(
                "search_result_count",
                "搜索结果数量",
                ["search_type"],
                buckets=[1, 5, 10, 20, 50, 100],
                registry=self.registry
            ),
            
            # 用户满意度
            "user_satisfaction_score": Histogram(
                "user_satisfaction_score",
                "用户满意度评分",
                ["search_type"],
                buckets=[1, 2, 3, 4, 5],
                registry=self.registry
            )
        })
        
        # Self-RAG特定指标
        self.metrics.update({
            "self_rag_iterations": Histogram(
                "self_rag_iterations",
                "Self-RAG迭代次数",
                ["query_type"],
                buckets=[1, 2, 3, 4, 5],
                registry=self.registry
            ),
            
            "self_rag_confidence": Histogram(
                "self_rag_confidence",
                "Self-RAG置信度",
                ["iteration"],
                buckets=[0.1, 0.3, 0.5, 0.7, 0.8, 0.9, 0.95],
                registry=self.registry
            ),
            
            "self_rag_refinement_success": Counter(
                "self_rag_refinement_success_total",
                "Self-RAG查询精化成功次数",
                ["refinement_type"],
                registry=self.registry
            )
        })
        
        # 多向量检索指标
        self.metrics.update({
            "multi_vector_aspects": Histogram(
                "multi_vector_aspects",
                "多向量检索方面数量",
                ["query_type"],
                buckets=[1, 2, 3, 4, 5],
                registry=self.registry
            ),
            
            "multi_vector_fusion_score": Histogram(
                "multi_vector_fusion_score",
                "多向量融合分数",
                ["fusion_method"],
                buckets=[0.1, 0.3, 0.5, 0.7, 0.9],
                registry=self.registry
            ),
            
            "aspect_coverage": Gauge(
                "aspect_coverage",
                "方面覆盖率",
                ["aspect_name"],
                registry=self.registry
            )
        })
        
        # 中文优化指标
        self.metrics.update({
            "chinese_segmentation_confidence": Histogram(
                "chinese_segmentation_confidence",
                "中文分词置信度",
                ["segmenter"],
                buckets=[0.1, 0.3, 0.5, 0.7, 0.9],
                registry=self.registry
            ),
            
            "chinese_srl_confidence": Histogram(
                "chinese_srl_confidence",
                "中文语义角色标注置信度",
                ["role_type"],
                buckets=[0.1, 0.3, 0.5, 0.7, 0.9],
                registry=self.registry
            ),
            
            "chinese_optimization_success": Counter(
                "chinese_optimization_success_total",
                "中文优化成功次数",
                ["optimization_type"],
                registry=self.registry
            )
        })
        
        # 困难负样本挖掘指标
        self.metrics.update({
            "hard_negative_mining_duration": Histogram(
                "hard_negative_mining_duration_seconds",
                "困难负样本挖掘耗时",
                ["mining_method"],
                buckets=[0.1, 0.5, 1.0, 2.0, 5.0],
                registry=self.registry
            ),
            
            "hard_negative_quality": Histogram(
                "hard_negative_quality",
                "困难负样本质量分数",
                ["difficulty_level"],
                buckets=[0.1, 0.3, 0.5, 0.7, 0.9],
                registry=self.registry
            ),
            
            "contrastive_training_loss": Gauge(
                "contrastive_training_loss",
                "对比学习训练损失",
                ["epoch"],
                registry=self.registry
            )
        })
        
        # 系统资源指标
        self.metrics.update({
            "system_cpu_usage": Gauge(
                "system_cpu_usage_percent",
                "系统CPU使用率",
                registry=self.registry
            ),
            
            "system_memory_usage": Gauge(
                "system_memory_usage_percent",
                "系统内存使用率",
                registry=self.registry
            ),
            
            "cache_hit_rate": Gauge(
                "cache_hit_rate",
                "缓存命中率",
                ["cache_type"],
                registry=self.registry
            )
        })
        
        # A/B测试指标
        self.metrics.update({
            "ab_test_assignments": Counter(
                "ab_test_assignments_total",
                "A/B测试分组次数",
                ["experiment", "group"],
                registry=self.registry
            ),
            
            "ab_test_conversion": Counter(
                "ab_test_conversion_total",
                "A/B测试转化次数",
                ["experiment", "group"],
                registry=self.registry
            )
        })
    
    @asynccontextmanager
    async def measure_time(self, metric_name: str, labels: Dict[str, str] = None):
        """测量执行时间的上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            if metric_name in self.metrics:
                if labels:
                    self.metrics[metric_name].labels(**labels).observe(duration)
                else:
                    self.metrics[metric_name].observe(duration)
    
    def record_search_request(self, search_type: str, status: str = "success"):
        """记录搜索请求"""
        self.metrics["search_requests_total"].labels(
            search_type=search_type, 
            status=status
        ).inc()
    
    def record_search_duration(self, search_type: str, duration: float):
        """记录搜索耗时"""
        self.metrics["search_duration_seconds"].labels(
            search_type=search_type
        ).observe(duration)
    
    def record_search_results(self, search_type: str, result_count: int):
        """记录搜索结果数量"""
        self.metrics["search_result_count"].labels(
            search_type=search_type
        ).observe(result_count)
    
    def record_user_satisfaction(self, search_type: str, score: float):
        """记录用户满意度"""
        self.metrics["user_satisfaction_score"].labels(
            search_type=search_type
        ).observe(score)
    
    def record_self_rag_metrics(self, iterations: int, confidence: float, 
                               query_type: str = "general"):
        """记录Self-RAG指标"""
        self.metrics["self_rag_iterations"].labels(
            query_type=query_type
        ).observe(iterations)
        
        self.metrics["self_rag_confidence"].labels(
            iteration=str(iterations)
        ).observe(confidence)
    
    def record_multi_vector_metrics(self, aspect_count: int, fusion_score: float,
                                   query_type: str = "general", 
                                   fusion_method: str = "weighted"):
        """记录多向量检索指标"""
        self.metrics["multi_vector_aspects"].labels(
            query_type=query_type
        ).observe(aspect_count)
        
        self.metrics["multi_vector_fusion_score"].labels(
            fusion_method=fusion_method
        ).observe(fusion_score)
    
    def record_chinese_optimization_metrics(self, segmentation_confidence: float,
                                          srl_confidence: float,
                                          segmenter: str = "ensemble",
                                          role_type: str = "general"):
        """记录中文优化指标"""
        self.metrics["chinese_segmentation_confidence"].labels(
            segmenter=segmenter
        ).observe(segmentation_confidence)
        
        self.metrics["chinese_srl_confidence"].labels(
            role_type=role_type
        ).observe(srl_confidence)
    
    def record_hard_negative_metrics(self, mining_duration: float, quality_score: float,
                                   mining_method: str = "semantic",
                                   difficulty_level: str = "hard"):
        """记录困难负样本挖掘指标"""
        self.metrics["hard_negative_mining_duration"].labels(
            mining_method=mining_method
        ).observe(mining_duration)
        
        self.metrics["hard_negative_quality"].labels(
            difficulty_level=difficulty_level
        ).observe(quality_score)
    
    def record_ab_test_assignment(self, experiment: str, group: str):
        """记录A/B测试分组"""
        self.metrics["ab_test_assignments"].labels(
            experiment=experiment,
            group=group
        ).inc()
    
    def record_ab_test_conversion(self, experiment: str, group: str):
        """记录A/B测试转化"""
        self.metrics["ab_test_conversion"].labels(
            experiment=experiment,
            group=group
        ).inc()
    
    async def update_system_metrics(self):
        """更新系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics["system_cpu_usage"].set(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.metrics["system_memory_usage"].set(memory.percent)
            
            logger.debug(f"系统指标更新: CPU {cpu_percent}%, 内存 {memory.percent}%")
            
        except Exception as e:
            logger.error(f"更新系统指标失败: {e}")
    
    def get_metrics(self) -> str:
        """获取Prometheus格式的指标"""
        return generate_latest(self.registry)
    
    def setup_alert_rules(self):
        """设置告警规则"""
        self.alert_rules = {
            "high_latency": {
                "metric": "search_duration_seconds",
                "threshold": 2.0,
                "condition": "greater_than",
                "description": "搜索延迟过高"
            },
            "low_satisfaction": {
                "metric": "user_satisfaction_score",
                "threshold": 3.0,
                "condition": "less_than",
                "description": "用户满意度过低"
            },
            "high_cpu_usage": {
                "metric": "system_cpu_usage_percent",
                "threshold": 80.0,
                "condition": "greater_than",
                "description": "CPU使用率过高"
            },
            "high_memory_usage": {
                "metric": "system_memory_usage_percent",
                "threshold": 85.0,
                "condition": "greater_than",
                "description": "内存使用率过高"
            },
            "self_rag_low_confidence": {
                "metric": "self_rag_confidence",
                "threshold": 0.5,
                "condition": "less_than",
                "description": "Self-RAG置信度过低"
            }
        }
    
    async def check_alerts(self) -> List[Dict[str, Any]]:
        """检查告警条件"""
        alerts = []
        
        try:
            # 这里应该从实际的指标数据中检查告警条件
            # 简化实现，实际应该查询Prometheus或数据库
            
            # 检查系统资源
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            if cpu_percent > self.alert_rules["high_cpu_usage"]["threshold"]:
                alerts.append({
                    "rule": "high_cpu_usage",
                    "value": cpu_percent,
                    "threshold": self.alert_rules["high_cpu_usage"]["threshold"],
                    "description": self.alert_rules["high_cpu_usage"]["description"],
                    "timestamp": datetime.now().isoformat()
                })
            
            if memory_percent > self.alert_rules["high_memory_usage"]["threshold"]:
                alerts.append({
                    "rule": "high_memory_usage",
                    "value": memory_percent,
                    "threshold": self.alert_rules["high_memory_usage"]["threshold"],
                    "description": self.alert_rules["high_memory_usage"]["description"],
                    "timestamp": datetime.now().isoformat()
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"检查告警失败: {e}")
            return []
    
    async def start_monitoring_loop(self):
        """启动监控循环"""
        logger.info("启动增强监控系统...")
        
        while True:
            try:
                # 更新系统指标
                await self.update_system_metrics()
                
                # 检查告警
                alerts = await self.check_alerts()
                if alerts:
                    for alert in alerts:
                        logger.warning(f"告警触发: {alert}")
                
                # 等待下一次检查
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(60)


# 创建全局监控实例
enhanced_monitoring = EnhancedMonitoring()
enhanced_monitoring.setup_alert_rules()
