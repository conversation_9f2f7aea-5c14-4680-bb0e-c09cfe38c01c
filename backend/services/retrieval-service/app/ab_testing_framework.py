"""
A/B测试框架
支持先进检索技术的效果对比和验证
"""

import asyncio
import hashlib
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import random

from loguru import logger
from .database_client import database_client
from .redis_client import retrieval_cache


class ExperimentStatus(Enum):
    """实验状态"""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"


class GroupType(Enum):
    """实验组类型"""
    CONTROL = "control"
    TREATMENT = "treatment"


@dataclass
class ExperimentConfig:
    """实验配置"""
    experiment_name: str
    description: str
    control_group: str
    treatment_group: str
    traffic_split: float
    start_date: datetime
    end_date: Optional[datetime] = None
    status: ExperimentStatus = ExperimentStatus.ACTIVE
    metrics: List[str] = None
    
    def __post_init__(self):
        if self.metrics is None:
            self.metrics = ["accuracy", "latency", "user_satisfaction"]


@dataclass
class ExperimentResult:
    """实验结果"""
    experiment_id: str
    user_id: str
    group_name: str
    query: str
    search_type: str
    response_time_ms: int
    result_count: int
    user_feedback: Optional[int] = None
    click_positions: List[int] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.click_positions is None:
            self.click_positions = []


class ABTestingFramework:
    """A/B测试框架"""
    
    def __init__(self):
        self.cache_ttl = 3600  # 1小时缓存
        self.default_experiments = self._get_default_experiments()
    
    def _get_default_experiments(self) -> List[ExperimentConfig]:
        """获取默认实验配置"""
        return [
            ExperimentConfig(
                experiment_name="self_rag_test",
                description="Self-RAG自我反思检索效果测试",
                control_group="enhanced_search",
                treatment_group="self_rag_search",
                traffic_split=0.3,
                start_date=datetime.now(),
                metrics=["accuracy", "latency", "user_satisfaction", "iteration_count"]
            ),
            ExperimentConfig(
                experiment_name="multi_vector_test",
                description="多向量检索效果测试",
                control_group="single_vector_search",
                treatment_group="multi_vector_search",
                traffic_split=0.2,
                start_date=datetime.now(),
                metrics=["recall", "precision", "diversity", "aspect_coverage"]
            ),
            ExperimentConfig(
                experiment_name="chinese_optimization_test",
                description="中文优化检索效果测试",
                control_group="basic_chinese_search",
                treatment_group="optimized_chinese_search",
                traffic_split=0.4,
                start_date=datetime.now(),
                metrics=["chinese_accuracy", "segmentation_quality", "srl_confidence"]
            ),
            ExperimentConfig(
                experiment_name="hard_negative_test",
                description="困难负样本挖掘效果测试",
                control_group="baseline_embedding",
                treatment_group="contrastive_embedding",
                traffic_split=0.25,
                start_date=datetime.now(),
                metrics=["embedding_quality", "negative_sample_accuracy", "model_performance"]
            ),
            ExperimentConfig(
                experiment_name="temporal_aware_test",
                description="时效性感知检索效果测试",
                control_group="static_ranking",
                treatment_group="temporal_ranking",
                traffic_split=0.15,
                start_date=datetime.now(),
                metrics=["freshness_score", "temporal_relevance", "user_preference"]
            )
        ]
    
    async def initialize_experiments(self) -> bool:
        """初始化默认实验"""
        try:
            logger.info("初始化A/B测试实验...")
            
            for experiment in self.default_experiments:
                await self.create_experiment(experiment)
            
            logger.info(f"成功初始化 {len(self.default_experiments)} 个实验")
            return True
            
        except Exception as e:
            logger.error(f"实验初始化失败: {e}")
            return False
    
    async def create_experiment(self, config: ExperimentConfig) -> str:
        """创建实验"""
        try:
            # 检查实验是否已存在
            existing = await database_client.fetchrow(
                "SELECT id FROM ab_experiments WHERE experiment_name = $1",
                config.experiment_name
            )
            
            if existing:
                logger.info(f"实验 {config.experiment_name} 已存在")
                return str(existing['id'])
            
            # 创建新实验
            experiment_id = await database_client.fetchval("""
                INSERT INTO ab_experiments 
                (experiment_name, description, control_group, treatment_group, 
                 traffic_split, start_date, end_date, status, metrics)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """,
            config.experiment_name,
            config.description,
            config.control_group,
            config.treatment_group,
            config.traffic_split,
            config.start_date,
            config.end_date,
            config.status.value,
            json.dumps(config.metrics)
            )
            
            logger.info(f"创建实验: {config.experiment_name} (ID: {experiment_id})")
            return str(experiment_id)
            
        except Exception as e:
            logger.error(f"创建实验失败: {e}")
            raise
    
    async def assign_user_to_experiment(self, user_id: str, experiment_name: str) -> str:
        """为用户分配实验组"""
        try:
            # 检查缓存
            cache_key = f"ab_assignment:{user_id}:{experiment_name}"
            cached_group = await retrieval_cache.get(cache_key)
            
            if cached_group:
                return cached_group
            
            # 检查数据库中的现有分配
            existing_assignment = await database_client.fetchrow("""
                SELECT ua.group_name 
                FROM ab_user_assignments ua
                JOIN ab_experiments e ON ua.experiment_id = e.id
                WHERE ua.user_id = $1 AND e.experiment_name = $2
            """, user_id, experiment_name)
            
            if existing_assignment:
                group_name = existing_assignment['group_name']
                await retrieval_cache.set(cache_key, group_name, ttl=self.cache_ttl)
                return group_name
            
            # 获取实验配置
            experiment = await database_client.fetchrow(
                "SELECT * FROM ab_experiments WHERE experiment_name = $1 AND status = 'active'",
                experiment_name
            )
            
            if not experiment:
                logger.warning(f"实验 {experiment_name} 不存在或未激活")
                return "control"  # 默认返回对照组
            
            # 基于用户ID的确定性分组
            group_name = self._deterministic_assignment(
                user_id, 
                experiment['traffic_split'],
                experiment['control_group'],
                experiment['treatment_group']
            )
            
            # 保存分配结果
            await database_client.execute("""
                INSERT INTO ab_user_assignments (experiment_id, user_id, group_name)
                VALUES ($1, $2, $3)
                ON CONFLICT (experiment_id, user_id) DO NOTHING
            """, experiment['id'], user_id, group_name)
            
            # 缓存结果
            await retrieval_cache.set(cache_key, group_name, ttl=self.cache_ttl)
            
            logger.debug(f"用户 {user_id} 分配到实验 {experiment_name} 的 {group_name} 组")
            return group_name
            
        except Exception as e:
            logger.error(f"用户分组失败: {e}")
            return "control"  # 出错时返回对照组
    
    def _deterministic_assignment(self, user_id: str, traffic_split: float, 
                                control_group: str, treatment_group: str) -> str:
        """确定性用户分组"""
        # 使用用户ID的哈希值确保一致性分组
        hash_value = int(hashlib.md5(user_id.encode()).hexdigest(), 16)
        normalized_hash = (hash_value % 10000) / 10000.0
        
        if normalized_hash < traffic_split:
            return treatment_group
        else:
            return control_group
    
    async def record_experiment_result(self, result: ExperimentResult) -> bool:
        """记录实验结果"""
        try:
            # 获取实验ID
            experiment = await database_client.fetchrow(
                "SELECT id FROM ab_experiments WHERE experiment_name = $1",
                self._extract_experiment_name(result.search_type)
            )
            
            if not experiment:
                logger.warning(f"未找到对应的实验: {result.search_type}")
                return False
            
            # 记录到查询历史表
            await database_client.execute("""
                INSERT INTO query_history 
                (user_id, original_query, search_type, response_time_ms, 
                 result_count, user_feedback, click_positions)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
            result.user_id,
            result.query,
            result.search_type,
            result.response_time_ms,
            result.result_count,
            result.user_feedback,
            result.click_positions
            )
            
            # 记录到性能指标表
            await self._record_performance_metrics(result, experiment['id'])
            
            logger.debug(f"记录实验结果: {result.experiment_id}")
            return True
            
        except Exception as e:
            logger.error(f"记录实验结果失败: {e}")
            return False
    
    async def _record_performance_metrics(self, result: ExperimentResult, experiment_id: str):
        """记录性能指标"""
        metrics = [
            ("latency", result.response_time_ms),
            ("result_count", result.result_count)
        ]
        
        if result.user_feedback:
            metrics.append(("user_satisfaction", result.user_feedback))
        
        if result.click_positions:
            # 计算点击率
            ctr = len(result.click_positions) / max(result.result_count, 1)
            metrics.append(("click_through_rate", ctr))
        
        for metric_name, metric_value in metrics:
            await database_client.execute("""
                INSERT INTO performance_metrics 
                (metric_name, metric_value, metric_type, tags)
                VALUES ($1, $2, $3, $4)
            """,
            metric_name,
            float(metric_value),
            self._get_metric_type(metric_name),
            json.dumps({
                "experiment_id": experiment_id,
                "search_type": result.search_type,
                "group_name": result.group_name
            })
            )
    
    def _extract_experiment_name(self, search_type: str) -> str:
        """从搜索类型提取实验名称"""
        type_to_experiment = {
            "self_rag_search": "self_rag_test",
            "multi_vector_search": "multi_vector_test",
            "optimized_chinese_search": "chinese_optimization_test",
            "contrastive_embedding": "hard_negative_test",
            "temporal_ranking": "temporal_aware_test"
        }
        
        return type_to_experiment.get(search_type, "default_test")
    
    def _get_metric_type(self, metric_name: str) -> str:
        """获取指标类型"""
        type_mapping = {
            "latency": "latency",
            "response_time_ms": "latency",
            "accuracy": "accuracy",
            "recall": "recall",
            "precision": "precision",
            "user_satisfaction": "accuracy",
            "click_through_rate": "accuracy",
            "result_count": "qps"
        }
        
        return type_mapping.get(metric_name, "accuracy")
    
    async def get_experiment_results(self, experiment_name: str, 
                                   days: int = 7) -> Dict[str, Any]:
        """获取实验结果分析"""
        try:
            # 获取实验配置
            experiment = await database_client.fetchrow(
                "SELECT * FROM ab_experiments WHERE experiment_name = $1",
                experiment_name
            )
            
            if not experiment:
                return {"error": "实验不存在"}
            
            # 获取时间范围内的数据
            start_date = datetime.now() - timedelta(days=days)
            
            # 查询各组的性能数据
            results = await database_client.fetch("""
                SELECT 
                    ua.group_name,
                    COUNT(*) as query_count,
                    AVG(qh.response_time_ms) as avg_latency,
                    AVG(qh.user_feedback) as avg_satisfaction,
                    AVG(qh.result_count) as avg_result_count,
                    COUNT(CASE WHEN array_length(qh.click_positions, 1) > 0 THEN 1 END)::FLOAT / COUNT(*) as ctr
                FROM query_history qh
                JOIN ab_user_assignments ua ON qh.user_id = ua.user_id
                WHERE ua.experiment_id = $1 
                AND qh.created_at >= $2
                GROUP BY ua.group_name
            """, experiment['id'], start_date)
            
            # 组织结果
            analysis = {
                "experiment_name": experiment_name,
                "period_days": days,
                "groups": {},
                "summary": {}
            }
            
            for row in results:
                group_data = {
                    "query_count": row['query_count'],
                    "avg_latency_ms": round(row['avg_latency'] or 0, 2),
                    "avg_satisfaction": round(row['avg_satisfaction'] or 0, 2),
                    "avg_result_count": round(row['avg_result_count'] or 0, 2),
                    "click_through_rate": round(row['ctr'] or 0, 4)
                }
                analysis["groups"][row['group_name']] = group_data
            
            # 计算对比分析
            if len(analysis["groups"]) >= 2:
                analysis["summary"] = self._calculate_comparison(analysis["groups"])
            
            return analysis
            
        except Exception as e:
            logger.error(f"获取实验结果失败: {e}")
            return {"error": str(e)}
    
    def _calculate_comparison(self, groups: Dict[str, Dict]) -> Dict[str, Any]:
        """计算对比分析"""
        group_names = list(groups.keys())
        if len(group_names) < 2:
            return {}
        
        control_group = group_names[0]
        treatment_group = group_names[1]
        
        control_data = groups[control_group]
        treatment_data = groups[treatment_group]
        
        comparison = {}
        
        # 计算各指标的提升比例
        for metric in ["avg_latency_ms", "avg_satisfaction", "click_through_rate"]:
            control_value = control_data.get(metric, 0)
            treatment_value = treatment_data.get(metric, 0)
            
            if control_value > 0:
                if metric == "avg_latency_ms":
                    # 延迟越低越好
                    improvement = (control_value - treatment_value) / control_value * 100
                else:
                    # 其他指标越高越好
                    improvement = (treatment_value - control_value) / control_value * 100
                
                comparison[f"{metric}_improvement_percent"] = round(improvement, 2)
        
        # 统计显著性检验（简化版）
        comparison["sample_size_control"] = control_data.get("query_count", 0)
        comparison["sample_size_treatment"] = treatment_data.get("query_count", 0)
        comparison["sufficient_sample"] = (
            comparison["sample_size_control"] >= 100 and 
            comparison["sample_size_treatment"] >= 100
        )
        
        return comparison
    
    async def get_active_experiments(self) -> List[Dict[str, Any]]:
        """获取活跃的实验列表"""
        try:
            experiments = await database_client.fetch("""
                SELECT 
                    experiment_name,
                    description,
                    control_group,
                    treatment_group,
                    traffic_split,
                    start_date,
                    status
                FROM ab_experiments 
                WHERE status = 'active'
                ORDER BY start_date DESC
            """)
            
            return [dict(exp) for exp in experiments]
            
        except Exception as e:
            logger.error(f"获取活跃实验失败: {e}")
            return []
    
    async def update_experiment_status(self, experiment_name: str, 
                                     status: ExperimentStatus) -> bool:
        """更新实验状态"""
        try:
            await database_client.execute("""
                UPDATE ab_experiments 
                SET status = $1, updated_at = NOW()
                WHERE experiment_name = $2
            """, status.value, experiment_name)
            
            logger.info(f"实验 {experiment_name} 状态更新为 {status.value}")
            return True
            
        except Exception as e:
            logger.error(f"更新实验状态失败: {e}")
            return False


# 创建全局实例
ab_testing_framework = ABTestingFramework()
