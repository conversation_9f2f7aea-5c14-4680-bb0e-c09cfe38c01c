"""
先进检索技术实现
包含Self-RAG、CoT检索、困难负样本挖掘等最新技术
"""

import asyncio
import numpy as np
import torch
import torch.nn.functional as F
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from loguru import logger

from .config import settings
from .enhanced_retrieval import SearchResult
from .embedding_client import embedding_client
from .redis_client import retrieval_cache


@dataclass
class ReasoningStep:
    """推理步骤"""
    step_id: int
    query: str
    reasoning: str
    confidence: float


@dataclass
class Critique:
    """自我评估结果"""
    confidence: float
    relevance_score: float
    completeness_score: float
    needs_refinement: bool
    suggestions: List[str]


@dataclass
class TrainingSample:
    """训练样本"""
    query: str
    document: str
    label: float
    weight: float = 1.0


class SelfRAGRetriever:
    """Self-RAG自我反思检索器"""

    def __init__(self):
        self.max_iterations = 3
        self.confidence_threshold = 0.8
        self.min_confidence_threshold = 0.3
        self.iteration_timeout = 10  # 每次迭代超时时间（秒）

        # 评估权重
        self.relevance_weight = 0.4
        self.completeness_weight = 0.3
        self.confidence_weight = 0.3
        self.min_relevance_score = 0.6
        self.min_completeness_score = 0.5

        # 查询精化参数
        self.max_refinement_length = 200
        self.synonym_expansion = True
        self.context_enhancement = True
        self.concept_addition = True

        # 状态跟踪
        self.last_iterations = 0
        self.last_confidence = 0.0
        self.critique_history = []

        # 加载模型
        self._load_models()

    def _load_models(self):
        """加载所需模型"""
        try:
            from sentence_transformers import SentenceTransformer, CrossEncoder

            # 加载嵌入模型
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

            # 加载交叉编码器用于相关性评估
            self.cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')

            # 加载LLM客户端（如果可用）
            self.llm_client = None
            try:
                from .llm_client import llm_client
                self.llm_client = llm_client
            except ImportError:
                logger.warning("LLM客户端不可用，将使用简化的自我评估")

            logger.info("Self-RAG模型加载成功")

        except Exception as e:
            logger.error(f"Self-RAG模型加载失败: {e}")
            self.embedding_model = None
            self.cross_encoder = None
            self.llm_client = None

    async def self_rag_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        Self-RAG自我反思检索

        Args:
            query: 查询文本
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        try:
            logger.info(f"开始Self-RAG检索: {query[:50]}...")

            if not self.embedding_model:
                logger.error("Self-RAG模型未正确加载")
                return []

            # 初始化状态
            self.last_iterations = 0
            self.last_confidence = 0.0
            self.critique_history = []

            current_query = query
            best_results = []
            best_confidence = 0.0

            # 迭代检索和评估
            for iteration in range(self.max_iterations):
                self.last_iterations = iteration + 1

                logger.debug(f"Self-RAG迭代 {iteration + 1}: {current_query[:50]}...")

                # 1. 执行检索
                results = await self._retrieve_documents(current_query, top_k * 2)

                if not results:
                    logger.warning(f"迭代 {iteration + 1} 未找到结果")
                    break

                # 2. 自我评估
                critique = await self._self_critique(current_query, results)
                self.critique_history.append({
                    "iteration": iteration + 1,
                    "query": current_query,
                    "confidence": critique.confidence,
                    "relevance": critique.relevance_score,
                    "completeness": critique.completeness_score,
                    "suggestions": critique.suggestions
                })

                # 3. 更新最佳结果
                if critique.confidence > best_confidence:
                    best_confidence = critique.confidence
                    best_results = results[:top_k]

                self.last_confidence = critique.confidence

                # 4. 判断是否需要继续
                if critique.confidence >= self.confidence_threshold:
                    logger.info(f"Self-RAG在迭代 {iteration + 1} 达到置信度阈值")
                    break

                if not critique.needs_refinement:
                    logger.info(f"Self-RAG在迭代 {iteration + 1} 无需进一步精化")
                    break

                # 5. 精化查询
                refined_query = await self._refine_query(current_query, results, critique)

                if refined_query == current_query:
                    logger.info("查询精化无变化，停止迭代")
                    break

                current_query = refined_query

            # 记录监控指标
            from .enhanced_monitoring import enhanced_monitoring
            enhanced_monitoring.record_self_rag_metrics(
                self.last_iterations,
                self.last_confidence,
                self._classify_query_type(query)
            )

            logger.info(f"Self-RAG检索完成，{self.last_iterations}次迭代，最终置信度: {self.last_confidence:.3f}")
            return best_results if best_results else results[:top_k] if results else []

        except Exception as e:
            logger.error(f"Self-RAG检索失败: {e}")
            return []

    async def _retrieve_documents(self, query: str, top_k: int) -> List[SearchResult]:
        """执行文档检索"""
        try:
            from .enhanced_retrieval import enhanced_retrieval_engine

            # 使用增强检索引擎
            results = await enhanced_retrieval_engine.enhanced_search(query, top_k)

            return results

        except Exception as e:
            logger.error(f"文档检索失败: {e}")
            return []

    async def _self_critique(self, query: str, results: List[SearchResult]) -> Critique:
        """自我评估检索结果"""
        try:
            if not results:
                return Critique(
                    confidence=0.0,
                    relevance_score=0.0,
                    completeness_score=0.0,
                    needs_refinement=True,
                    suggestions=["未找到相关结果，建议扩展查询"]
                )

            # 1. 评估相关性
            relevance_score = await self._evaluate_relevance(query, results)

            # 2. 评估完整性
            completeness_score = await self._evaluate_completeness(query, results)

            # 3. 计算综合置信度
            confidence = (
                relevance_score * self.relevance_weight +
                completeness_score * self.completeness_weight
            )

            # 4. 判断是否需要精化
            needs_refinement = (
                confidence < self.confidence_threshold and
                relevance_score < self.min_relevance_score or
                completeness_score < self.min_completeness_score
            )

            # 5. 生成改进建议
            suggestions = await self._generate_suggestions(query, results, relevance_score, completeness_score)

            return Critique(
                confidence=confidence,
                relevance_score=relevance_score,
                completeness_score=completeness_score,
                needs_refinement=needs_refinement,
                suggestions=suggestions
            )

        except Exception as e:
            logger.error(f"自我评估失败: {e}")
            return Critique(0.0, 0.0, 0.0, True, ["评估失败"])

    async def _evaluate_relevance(self, query: str, results: List[SearchResult]) -> float:
        """评估结果相关性"""
        try:
            if not self.cross_encoder:
                # 简化评估：基于分数
                if not results:
                    return 0.0
                avg_score = sum(r.score for r in results) / len(results)
                return min(1.0, avg_score)

            # 使用交叉编码器评估
            pairs = [(query, result.content) for result in results[:5]]  # 只评估前5个
            relevance_scores = self.cross_encoder.predict(pairs)

            # 归一化到0-1范围
            import numpy as np
            scores = np.array(relevance_scores)
            normalized_scores = (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)

            return float(np.mean(normalized_scores))

        except Exception as e:
            logger.error(f"相关性评估失败: {e}")
            return 0.5

    async def _evaluate_completeness(self, query: str, results: List[SearchResult]) -> float:
        """评估结果完整性"""
        try:
            if not results:
                return 0.0

            # 简化完整性评估
            # 1. 结果数量评估
            count_score = min(1.0, len(results) / 10.0)  # 10个结果为满分

            # 2. 内容多样性评估
            diversity_score = await self._calculate_diversity(results)

            # 3. 覆盖度评估
            coverage_score = await self._calculate_coverage(query, results)

            # 综合评分
            completeness = (count_score * 0.3 + diversity_score * 0.4 + coverage_score * 0.3)

            return completeness

        except Exception as e:
            logger.error(f"完整性评估失败: {e}")
            return 0.5

    async def _calculate_diversity(self, results: List[SearchResult]) -> float:
        """计算结果多样性"""
        try:
            if len(results) <= 1:
                return 0.0

            # 计算结果之间的相似度
            contents = [result.content for result in results[:10]]  # 最多10个
            embeddings = self.embedding_model.encode(contents)

            # 计算平均相似度
            from sklearn.metrics.pairwise import cosine_similarity
            similarity_matrix = cosine_similarity(embeddings)

            # 排除对角线（自相似度）
            import numpy as np
            mask = np.ones_like(similarity_matrix, dtype=bool)
            np.fill_diagonal(mask, False)

            avg_similarity = similarity_matrix[mask].mean()

            # 多样性 = 1 - 平均相似度
            diversity = 1.0 - avg_similarity

            return max(0.0, min(1.0, diversity))

        except Exception as e:
            logger.error(f"多样性计算失败: {e}")
            return 0.5

    async def _calculate_coverage(self, query: str, results: List[SearchResult]) -> float:
        """计算查询覆盖度"""
        try:
            # 简化覆盖度计算：查询词在结果中的覆盖情况
            query_words = set(query.lower().split())

            if not query_words:
                return 0.0

            covered_words = set()
            for result in results[:5]:  # 检查前5个结果
                result_words = set(result.content.lower().split())
                covered_words.update(query_words & result_words)

            coverage = len(covered_words) / len(query_words)
            return coverage

        except Exception as e:
            logger.error(f"覆盖度计算失败: {e}")
            return 0.5

    async def _generate_suggestions(self, query: str, results: List[SearchResult],
                                  relevance_score: float, completeness_score: float) -> List[str]:
        """生成改进建议"""
        suggestions = []

        try:
            if relevance_score < self.min_relevance_score:
                suggestions.append("相关性不足，建议添加更具体的关键词")
                suggestions.append("考虑使用同义词或相关概念")

            if completeness_score < self.min_completeness_score:
                suggestions.append("结果不够完整，建议扩展查询范围")
                suggestions.append("考虑添加上下文信息")

            if len(results) < 5:
                suggestions.append("结果数量较少，建议放宽查询条件")

            # 基于LLM的建议（如果可用）
            if self.llm_client:
                llm_suggestions = await self._get_llm_suggestions(query, results)
                suggestions.extend(llm_suggestions)

            return suggestions[:3]  # 最多返回3个建议

        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            return ["建议重新组织查询"]

    async def _get_llm_suggestions(self, query: str, results: List[SearchResult]) -> List[str]:
        """使用LLM生成改进建议"""
        try:
            if not self.llm_client:
                return []

            # 构建提示
            results_summary = "\n".join([f"- {r.content[:100]}..." for r in results[:3]])

            prompt = f"""
            查询: {query}

            当前检索结果:
            {results_summary}

            请分析这些结果的质量，并提供2-3个具体的查询改进建议：
            """

            response = await self.llm_client.generate(prompt, max_tokens=200)

            # 解析建议
            suggestions = []
            for line in response.split('\n'):
                line = line.strip()
                if line and (line.startswith('-') or line.startswith('•')):
                    suggestions.append(line[1:].strip())

            return suggestions[:3]

        except Exception as e:
            logger.error(f"LLM建议生成失败: {e}")
            return []

    async def _refine_query(self, query: str, results: List[SearchResult],
                          critique: Critique) -> str:
        """精化查询"""
        try:
            refined_query = query

            # 1. 基于建议精化
            for suggestion in critique.suggestions:
                if "关键词" in suggestion:
                    refined_query = await self._add_keywords(refined_query, results)
                elif "同义词" in suggestion:
                    refined_query = await self._expand_synonyms(refined_query)
                elif "上下文" in suggestion:
                    refined_query = await self._add_context(refined_query, results)
                elif "扩展" in suggestion:
                    refined_query = await self._expand_query(refined_query)

            # 2. 长度限制
            if len(refined_query) > self.max_refinement_length:
                refined_query = refined_query[:self.max_refinement_length]

            logger.debug(f"查询精化: '{query}' -> '{refined_query}'")
            return refined_query

        except Exception as e:
            logger.error(f"查询精化失败: {e}")
            return query

    async def _add_keywords(self, query: str, results: List[SearchResult]) -> str:
        """添加关键词"""
        try:
            if not results:
                return query

            # 从结果中提取高频词作为关键词
            all_words = []
            for result in results[:3]:
                words = result.content.lower().split()
                all_words.extend(words)

            # 简化关键词提取
            from collections import Counter
            word_counts = Counter(all_words)

            # 排除停用词和查询中已有的词
            query_words = set(query.lower().split())
            stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过'}

            keywords = []
            for word, count in word_counts.most_common(10):
                if (word not in query_words and
                    word not in stop_words and
                    len(word) > 1):
                    keywords.append(word)
                    if len(keywords) >= 2:
                        break

            if keywords:
                return f"{query} {' '.join(keywords)}"

            return query

        except Exception as e:
            logger.error(f"添加关键词失败: {e}")
            return query

    async def _expand_synonyms(self, query: str) -> str:
        """扩展同义词"""
        try:
            # 简化同义词扩展
            synonym_map = {
                "搜索": ["检索", "查找", "寻找"],
                "方法": ["方式", "途径", "手段"],
                "技术": ["技巧", "方法", "工艺"],
                "系统": ["体系", "框架", "平台"],
                "问题": ["疑问", "难题", "困难"],
                "解决": ["处理", "解答", "应对"],
                "分析": ["研究", "探讨", "剖析"],
                "优化": ["改进", "提升", "完善"]
            }

            words = query.split()
            expanded_words = []

            for word in words:
                expanded_words.append(word)
                if word in synonym_map:
                    # 随机选择一个同义词
                    import random
                    synonym = random.choice(synonym_map[word])
                    expanded_words.append(synonym)

            return " ".join(expanded_words)

        except Exception as e:
            logger.error(f"同义词扩展失败: {e}")
            return query

    async def _add_context(self, query: str, results: List[SearchResult]) -> str:
        """添加上下文"""
        try:
            if not results:
                return query

            # 从结果中提取上下文信息
            context_words = []
            for result in results[:2]:
                # 简化上下文提取：取结果的前几个词
                words = result.content.split()[:5]
                context_words.extend(words)

            if context_words:
                context = " ".join(context_words[:3])
                return f"{query} {context}"

            return query

        except Exception as e:
            logger.error(f"添加上下文失败: {e}")
            return query

    async def _expand_query(self, query: str) -> str:
        """扩展查询"""
        try:
            # 简化查询扩展
            expansion_templates = [
                f"{query} 详细信息",
                f"{query} 相关内容",
                f"{query} 具体方法",
                f"关于 {query}",
                f"{query} 解决方案"
            ]

            import random
            return random.choice(expansion_templates)

        except Exception as e:
            logger.error(f"查询扩展失败: {e}")
            return query

    def _classify_query_type(self, query: str) -> str:
        """分类查询类型"""
        try:
            query_lower = query.lower()

            if any(word in query_lower for word in ["如何", "怎么", "怎样", "方法"]):
                return "how_to"
            elif any(word in query_lower for word in ["什么", "是什么", "定义"]):
                return "definition"
            elif any(word in query_lower for word in ["为什么", "原因", "why"]):
                return "explanation"
            elif any(word in query_lower for word in ["哪里", "在哪", "位置"]):
                return "location"
            elif any(word in query_lower for word in ["时间", "什么时候", "when"]):
                return "temporal"
            else:
                return "general"

        except Exception as e:
            logger.error(f"查询类型分类失败: {e}")
            return "general"

    async def optimize_evaluation_mechanism(self) -> Dict[str, Any]:
        """优化评估机制"""
        try:
            logger.info("开始优化Self-RAG评估机制...")

            # 1. 分析历史评估数据
            evaluation_stats = await self._analyze_evaluation_history()

            # 2. 调整评估权重
            optimized_weights = await self._optimize_evaluation_weights(evaluation_stats)

            # 3. 更新置信度阈值
            optimized_thresholds = await self._optimize_confidence_thresholds(evaluation_stats)

            # 4. 应用优化参数
            self._apply_optimization_results(optimized_weights, optimized_thresholds)

            # 5. 生成优化报告
            optimization_report = {
                "evaluation_stats": evaluation_stats,
                "optimized_weights": optimized_weights,
                "optimized_thresholds": optimized_thresholds,
                "optimization_timestamp": datetime.now().isoformat()
            }

            logger.info("Self-RAG评估机制优化完成")
            return optimization_report

        except Exception as e:
            logger.error(f"评估机制优化失败: {e}")
            return {}

    async def _analyze_evaluation_history(self) -> Dict[str, Any]:
        """分析历史评估数据"""
        try:
            # 分析评估历史
            evaluation_history = self.critique_history[-100:]  # 最近100次评估

            if evaluation_history:
                avg_iterations = sum(c['iteration'] for c in evaluation_history) / len(evaluation_history)
                avg_confidence = sum(c['confidence'] for c in evaluation_history) / len(evaluation_history)
                avg_relevance = sum(c['relevance'] for c in evaluation_history) / len(evaluation_history)
                avg_completeness = sum(c['completeness'] for c in evaluation_history) / len(evaluation_history)

                # 成功率统计
                success_count = sum(1 for c in evaluation_history if c['confidence'] >= self.confidence_threshold)
                success_rate = success_count / len(evaluation_history)

                # 迭代分布
                iteration_dist = {}
                for c in evaluation_history:
                    iter_count = c['iteration']
                    iteration_dist[iter_count] = iteration_dist.get(iter_count, 0) + 1

            else:
                avg_iterations = self.last_iterations
                avg_confidence = self.last_confidence
                avg_relevance = 0.5
                avg_completeness = 0.5
                success_rate = 0.5
                iteration_dist = {}

            return {
                "avg_iterations": avg_iterations,
                "avg_confidence": avg_confidence,
                "avg_relevance": avg_relevance,
                "avg_completeness": avg_completeness,
                "success_rate": success_rate,
                "iteration_distribution": iteration_dist,
                "evaluation_count": len(evaluation_history)
            }

        except Exception as e:
            logger.error(f"评估历史分析失败: {e}")
            return {}

    async def _optimize_evaluation_weights(self, stats: Dict[str, Any]) -> Dict[str, float]:
        """优化评估权重"""
        try:
            if not stats:
                return {
                    "relevance_weight": self.relevance_weight,
                    "completeness_weight": self.completeness_weight,
                    "confidence_weight": self.confidence_weight
                }

            avg_relevance = stats.get("avg_relevance", 0.5)
            avg_completeness = stats.get("avg_completeness", 0.5)
            success_rate = stats.get("success_rate", 0.5)

            # 基于历史表现调整权重
            relevance_weight = self.relevance_weight
            completeness_weight = self.completeness_weight

            # 如果相关性普遍较低，增加相关性权重
            if avg_relevance < 0.6:
                relevance_weight = min(0.6, self.relevance_weight + 0.1)
                completeness_weight = max(0.2, self.completeness_weight - 0.05)

            # 如果完整性普遍较低，增加完整性权重
            if avg_completeness < 0.5:
                completeness_weight = min(0.5, completeness_weight + 0.1)
                relevance_weight = max(0.3, relevance_weight - 0.05)

            # 如果成功率低，平衡权重
            if success_rate < 0.4:
                relevance_weight = 0.4
                completeness_weight = 0.3

            # 确保权重和为1
            confidence_weight = 1.0 - relevance_weight - completeness_weight
            confidence_weight = max(0.1, min(0.4, confidence_weight))

            # 重新归一化
            total_weight = relevance_weight + completeness_weight + confidence_weight
            relevance_weight /= total_weight
            completeness_weight /= total_weight
            confidence_weight /= total_weight

            return {
                "relevance_weight": relevance_weight,
                "completeness_weight": completeness_weight,
                "confidence_weight": confidence_weight
            }

        except Exception as e:
            logger.error(f"评估权重优化失败: {e}")
            return {
                "relevance_weight": self.relevance_weight,
                "completeness_weight": self.completeness_weight,
                "confidence_weight": self.confidence_weight
            }

    async def _optimize_confidence_thresholds(self, stats: Dict[str, Any]) -> Dict[str, float]:
        """优化置信度阈值"""
        try:
            if not stats:
                return {
                    "confidence_threshold": self.confidence_threshold,
                    "min_confidence_threshold": self.min_confidence_threshold,
                    "min_relevance_score": self.min_relevance_score,
                    "min_completeness_score": self.min_completeness_score
                }

            avg_iterations = stats.get("avg_iterations", 2.0)
            avg_confidence = stats.get("avg_confidence", 0.7)
            success_rate = stats.get("success_rate", 0.5)

            # 如果平均迭代次数过多，降低置信度阈值
            confidence_threshold = self.confidence_threshold
            if avg_iterations > 2.5:
                confidence_threshold = max(0.6, self.confidence_threshold - 0.1)
            elif avg_iterations < 1.5 and success_rate > 0.7:
                confidence_threshold = min(0.9, self.confidence_threshold + 0.05)

            # 如果成功率低，调整最小阈值
            min_relevance_score = self.min_relevance_score
            min_completeness_score = self.min_completeness_score

            if success_rate < 0.4:
                min_relevance_score = max(0.4, self.min_relevance_score - 0.1)
                min_completeness_score = max(0.3, self.min_completeness_score - 0.1)
            elif success_rate > 0.8:
                min_relevance_score = min(0.8, self.min_relevance_score + 0.05)
                min_completeness_score = min(0.7, self.min_completeness_score + 0.05)

            # 最小置信度阈值调整
            min_confidence_threshold = self.min_confidence_threshold
            if avg_confidence < 0.5:
                min_confidence_threshold = max(0.2, self.min_confidence_threshold - 0.05)
            elif avg_confidence > 0.8:
                min_confidence_threshold = min(0.5, self.min_confidence_threshold + 0.05)

            return {
                "confidence_threshold": confidence_threshold,
                "min_confidence_threshold": min_confidence_threshold,
                "min_relevance_score": min_relevance_score,
                "min_completeness_score": min_completeness_score
            }

        except Exception as e:
            logger.error(f"置信度阈值优化失败: {e}")
            return {
                "confidence_threshold": self.confidence_threshold,
                "min_confidence_threshold": self.min_confidence_threshold,
                "min_relevance_score": self.min_relevance_score,
                "min_completeness_score": self.min_completeness_score
            }

    def _apply_optimization_results(self, weights: Dict[str, float],
                                  thresholds: Dict[str, float]):
        """应用优化结果"""
        try:
            # 更新评估权重
            self.relevance_weight = weights.get("relevance_weight", self.relevance_weight)
            self.completeness_weight = weights.get("completeness_weight", self.completeness_weight)
            self.confidence_weight = weights.get("confidence_weight", self.confidence_weight)

            # 更新置信度阈值
            self.confidence_threshold = thresholds.get("confidence_threshold", self.confidence_threshold)
            self.min_confidence_threshold = thresholds.get("min_confidence_threshold", self.min_confidence_threshold)
            self.min_relevance_score = thresholds.get("min_relevance_score", self.min_relevance_score)
            self.min_completeness_score = thresholds.get("min_completeness_score", self.min_completeness_score)

            logger.info(f"Self-RAG参数已更新: 置信度阈值={self.confidence_threshold:.3f}, "
                       f"相关性权重={self.relevance_weight:.3f}")

        except Exception as e:
            logger.error(f"优化结果应用失败: {e}")

    async def get_evaluation_metrics(self) -> Dict[str, Any]:
        """获取评估指标"""
        try:
            return {
                "current_parameters": {
                    "max_iterations": self.max_iterations,
                    "confidence_threshold": self.confidence_threshold,
                    "min_confidence_threshold": self.min_confidence_threshold,
                    "relevance_weight": self.relevance_weight,
                    "completeness_weight": self.completeness_weight,
                    "confidence_weight": self.confidence_weight,
                    "min_relevance_score": self.min_relevance_score,
                    "min_completeness_score": self.min_completeness_score
                },
                "recent_performance": {
                    "last_iterations": self.last_iterations,
                    "last_confidence": self.last_confidence,
                    "critique_history_count": len(self.critique_history)
                },
                "evaluation_history": self.critique_history[-10:] if self.critique_history else []
            }

        except Exception as e:
            logger.error(f"获取评估指标失败: {e}")
            return {}
    
    def __init__(self):
        self.max_iterations = 3
        self.confidence_threshold = 0.8
        self.llm_client = None  # 需要集成LLM客户端
    
    async def self_rag_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        Self-RAG检索主流程
        
        Args:
            query: 用户查询
            top_k: 返回结果数量
            
        Returns:
            优化后的检索结果
        """
        try:
            logger.info(f"开始Self-RAG检索: {query[:50]}...")
            
            current_results = []
            refined_query = query
            
            for iteration in range(self.max_iterations):
                logger.info(f"Self-RAG迭代 {iteration + 1}/{self.max_iterations}")
                
                # 1. 执行检索
                if iteration == 0:
                    # 初始检索
                    from .enhanced_retrieval import enhanced_retrieval_engine
                    results = await enhanced_retrieval_engine.enhanced_search(
                        refined_query, top_k=top_k * 2
                    )
                else:
                    # 基于反思结果的精化检索
                    results = await enhanced_retrieval_engine.enhanced_search(
                        refined_query, top_k=top_k
                    )
                
                # 2. 自我评估
                critique = await self.self_critique(query, results)
                
                # 3. 判断是否需要继续
                if (critique.confidence > self.confidence_threshold or 
                    iteration == self.max_iterations - 1):
                    current_results = results
                    break
                
                # 4. 基于评估结果精化查询
                if critique.needs_refinement:
                    refined_query = await self.refine_query(query, results, critique)
                    logger.info(f"精化查询: {refined_query}")
                
                current_results = results
            
            logger.info(f"Self-RAG检索完成，最终置信度: {critique.confidence:.3f}")
            return current_results[:top_k]
            
        except Exception as e:
            logger.error(f"Self-RAG检索失败: {e}")
            # 回退到基础检索
            from .enhanced_retrieval import enhanced_retrieval_engine
            return await enhanced_retrieval_engine.enhanced_search(query, top_k=top_k)
    
    async def self_critique(self, query: str, results: List[SearchResult]) -> Critique:
        """
        自我评估检索结果质量
        
        Args:
            query: 原始查询
            results: 检索结果
            
        Returns:
            评估结果
        """
        try:
            # 构建评估提示词
            critique_prompt = self._build_critique_prompt(query, results)
            
            # 调用LLM进行评估（简化实现）
            if self.llm_client:
                response = await self.llm_client.generate(critique_prompt)
                return self._parse_critique_response(response)
            else:
                # 简化的启发式评估
                return await self._heuristic_critique(query, results)
                
        except Exception as e:
            logger.error(f"自我评估失败: {e}")
            return Critique(
                confidence=0.5,
                relevance_score=0.5,
                completeness_score=0.5,
                needs_refinement=True,
                suggestions=["增加更多相关信息"]
            )
    
    def _build_critique_prompt(self, query: str, results: List[SearchResult]) -> str:
        """构建评估提示词"""
        results_text = "\n".join([
            f"{i+1}. {result.content[:200]}..." 
            for i, result in enumerate(results[:5])
        ])
        
        prompt = f"""
        请评估以下检索结果对查询的相关性和完整性：
        
        查询：{query}
        
        检索结果：
        {results_text}
        
        请从以下维度评估（0-1分）：
        1. 相关性得分：结果与查询的相关程度
        2. 完整性得分：结果是否完整回答了查询
        3. 整体置信度：对结果质量的整体信心
        4. 是否需要改进：true/false
        5. 改进建议：具体的改进方向
        
        请以JSON格式返回评估结果。
        """
        
        return prompt
    
    async def _heuristic_critique(self, query: str, results: List[SearchResult]) -> Critique:
        """启发式评估（当没有LLM时使用）"""
        if not results:
            return Critique(
                confidence=0.0,
                relevance_score=0.0,
                completeness_score=0.0,
                needs_refinement=True,
                suggestions=["没有找到相关结果，需要扩展查询"]
            )
        
        # 计算平均相关性分数
        avg_score = sum(r.score for r in results) / len(results)
        
        # 计算查询覆盖度
        query_terms = set(query.lower().split())
        covered_terms = set()
        
        for result in results:
            content_terms = set(result.content.lower().split())
            covered_terms.update(query_terms.intersection(content_terms))
        
        coverage = len(covered_terms) / len(query_terms) if query_terms else 0
        
        # 综合评估
        relevance_score = min(avg_score, 1.0)
        completeness_score = coverage
        confidence = (relevance_score + completeness_score) / 2
        
        needs_refinement = confidence < self.confidence_threshold
        
        suggestions = []
        if relevance_score < 0.6:
            suggestions.append("提高结果相关性")
        if completeness_score < 0.6:
            suggestions.append("增加查询覆盖度")
        if not suggestions:
            suggestions.append("结果质量良好")
        
        return Critique(
            confidence=confidence,
            relevance_score=relevance_score,
            completeness_score=completeness_score,
            needs_refinement=needs_refinement,
            suggestions=suggestions
        )
    
    async def refine_query(self, original_query: str, results: List[SearchResult], 
                          critique: Critique) -> str:
        """基于评估结果精化查询"""
        try:
            # 分析缺失的信息
            missing_aspects = self._identify_missing_aspects(original_query, results, critique)
            
            # 构建精化查询
            refined_parts = [original_query]
            
            # 添加缺失的方面
            for aspect in missing_aspects:
                refined_parts.append(aspect)
            
            # 基于建议调整查询
            for suggestion in critique.suggestions:
                if "相关性" in suggestion:
                    # 添加同义词
                    synonyms = await self._get_query_synonyms(original_query)
                    refined_parts.extend(synonyms[:2])
                elif "覆盖度" in suggestion:
                    # 添加相关概念
                    related_concepts = await self._get_related_concepts(original_query)
                    refined_parts.extend(related_concepts[:2])
            
            refined_query = " ".join(refined_parts)
            return refined_query
            
        except Exception as e:
            logger.error(f"查询精化失败: {e}")
            return original_query
    
    def _identify_missing_aspects(self, query: str, results: List[SearchResult], 
                                 critique: Critique) -> List[str]:
        """识别缺失的查询方面"""
        missing_aspects = []
        
        # 简化的缺失方面识别
        query_lower = query.lower()
        
        # 检查是否缺少时间信息
        if "什么时候" in query_lower or "时间" in query_lower:
            has_time_info = any("时间" in r.content or "日期" in r.content for r in results)
            if not has_time_info:
                missing_aspects.append("时间信息")
        
        # 检查是否缺少地点信息
        if "哪里" in query_lower or "地点" in query_lower:
            has_location_info = any("地点" in r.content or "位置" in r.content for r in results)
            if not has_location_info:
                missing_aspects.append("地点信息")
        
        # 检查是否缺少方法信息
        if "如何" in query_lower or "怎么" in query_lower:
            has_method_info = any("方法" in r.content or "步骤" in r.content for r in results)
            if not has_method_info:
                missing_aspects.append("方法步骤")
        
        return missing_aspects
    
    async def _get_query_synonyms(self, query: str) -> List[str]:
        """获取查询同义词"""
        # 简化的同义词映射
        synonym_map = {
            "方法": ["方式", "途径", "手段"],
            "问题": ["疑问", "难题", "困难"],
            "解决": ["处理", "解答", "应对"],
            "优化": ["改进", "提升", "完善"],
            "实现": ["完成", "达成", "执行"]
        }
        
        synonyms = []
        for word in query.split():
            if word in synonym_map:
                synonyms.extend(synonym_map[word][:1])  # 每个词取1个同义词
        
        return synonyms
    
    async def _get_related_concepts(self, query: str) -> List[str]:
        """获取相关概念"""
        # 简化的相关概念映射
        concept_map = {
            "机器学习": ["深度学习", "神经网络", "算法"],
            "数据库": ["SQL", "索引", "查询优化"],
            "编程": ["代码", "开发", "软件工程"],
            "系统": ["架构", "设计", "性能"]
        }
        
        related_concepts = []
        for word in query.split():
            if word in concept_map:
                related_concepts.extend(concept_map[word][:1])
        
        return related_concepts
    
    def _parse_critique_response(self, response: str) -> Critique:
        """解析LLM评估响应"""
        try:
            # 这里应该解析LLM返回的JSON格式评估结果
            # 简化实现，返回默认值
            return Critique(
                confidence=0.7,
                relevance_score=0.7,
                completeness_score=0.6,
                needs_refinement=True,
                suggestions=["提高结果相关性"]
            )
        except Exception as e:
            logger.error(f"解析评估响应失败: {e}")
            return Critique(
                confidence=0.5,
                relevance_score=0.5,
                completeness_score=0.5,
                needs_refinement=True,
                suggestions=["评估解析失败"]
            )


class ChainOfThoughtRetriever:
    """思维链检索器"""
    
    def __init__(self):
        self.max_steps = 3
        self.llm_client = None
    
    async def cot_retrieval(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        思维链检索主流程
        
        Args:
            query: 用户查询
            top_k: 返回结果数量
            
        Returns:
            基于推理步骤的检索结果
        """
        try:
            logger.info(f"开始CoT检索: {query[:50]}...")
            
            # 1. 生成推理步骤
            reasoning_steps = await self.generate_reasoning_steps(query)
            
            # 2. 分步检索
            all_results = []
            step_weights = [1.0, 0.8, 0.6]  # 步骤权重递减
            
            for i, step in enumerate(reasoning_steps):
                logger.info(f"执行推理步骤 {i+1}: {step.query}")
                
                # 执行该步骤的检索
                from .enhanced_retrieval import enhanced_retrieval_engine
                step_results = await enhanced_retrieval_engine.enhanced_search(
                    step.query, top_k=top_k
                )
                
                # 添加步骤上下文和权重
                for result in step_results:
                    result.metadata["reasoning_step"] = i + 1
                    result.metadata["step_query"] = step.query
                    result.metadata["step_reasoning"] = step.reasoning
                    result.score *= step_weights[min(i, len(step_weights) - 1)]
                
                all_results.extend(step_results)
            
            # 3. 融合和去重
            final_results = self._merge_step_results(all_results, query)
            
            logger.info(f"CoT检索完成，共{len(reasoning_steps)}个推理步骤")
            return final_results[:top_k]
            
        except Exception as e:
            logger.error(f"CoT检索失败: {e}")
            # 回退到基础检索
            from .enhanced_retrieval import enhanced_retrieval_engine
            return await enhanced_retrieval_engine.enhanced_search(query, top_k=top_k)
    
    async def generate_reasoning_steps(self, query: str) -> List[ReasoningStep]:
        """生成推理步骤"""
        try:
            if self.llm_client:
                return await self._llm_generate_steps(query)
            else:
                return await self._heuristic_generate_steps(query)
                
        except Exception as e:
            logger.error(f"生成推理步骤失败: {e}")
            # 返回原始查询作为单一步骤
            return [ReasoningStep(
                step_id=1,
                query=query,
                reasoning="直接查询",
                confidence=0.8
            )]
    
    async def _heuristic_generate_steps(self, query: str) -> List[ReasoningStep]:
        """启发式生成推理步骤"""
        steps = []
        
        # 步骤1：直接查询
        steps.append(ReasoningStep(
            step_id=1,
            query=query,
            reasoning="直接查询原始问题",
            confidence=0.9
        ))
        
        # 步骤2：分解查询
        if "如何" in query or "怎么" in query:
            # 方法类查询：先查概念，再查具体方法
            concept = self._extract_main_concept(query)
            if concept:
                steps.append(ReasoningStep(
                    step_id=2,
                    query=f"{concept} 基本概念 定义",
                    reasoning=f"首先了解{concept}的基本概念",
                    confidence=0.8
                ))
                
                steps.append(ReasoningStep(
                    step_id=3,
                    query=f"{concept} 实现方法 步骤",
                    reasoning=f"然后查找{concept}的具体实现方法",
                    confidence=0.8
                ))
        
        elif "什么" in query:
            # 定义类查询：先查定义，再查应用
            concept = self._extract_main_concept(query)
            if concept:
                steps.append(ReasoningStep(
                    step_id=2,
                    query=f"{concept} 应用场景 实例",
                    reasoning=f"查找{concept}的应用场景和实例",
                    confidence=0.7
                ))
        
        return steps[:self.max_steps]
    
    def _extract_main_concept(self, query: str) -> str:
        """提取查询中的主要概念"""
        # 简化的概念提取
        stop_words = {"如何", "怎么", "什么", "是", "的", "了", "在", "和", "与"}
        words = [w for w in query.split() if w not in stop_words and len(w) > 1]
        
        # 返回最长的词作为主要概念
        if words:
            return max(words, key=len)
        return ""
    
    def _merge_step_results(self, all_results: List[SearchResult], 
                           original_query: str) -> List[SearchResult]:
        """融合多步骤检索结果"""
        # 按文档ID去重，保留最高分数
        doc_dict = {}
        
        for result in all_results:
            doc_id = result.id
            if doc_id not in doc_dict or result.score > doc_dict[doc_id].score:
                doc_dict[doc_id] = result
        
        # 按分数排序
        merged_results = list(doc_dict.values())
        merged_results.sort(key=lambda x: x.score, reverse=True)
        
        return merged_results


# 创建全局实例
self_rag_retriever = SelfRAGRetriever()
cot_retriever = ChainOfThoughtRetriever()
