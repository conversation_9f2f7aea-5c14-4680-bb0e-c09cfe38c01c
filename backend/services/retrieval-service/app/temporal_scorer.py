"""
时效性评分器
为检索结果提供基于时间的相关性评分
"""

import re
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from dateutil import parser

from loguru import logger
from .database_client import database_client
from .enhanced_monitoring import enhanced_monitoring


@dataclass
class TemporalFeatures:
    """时间特征"""
    creation_time: Optional[datetime]
    update_time: Optional[datetime]
    mentioned_dates: List[datetime]
    temporal_keywords: List[str]
    freshness_score: float
    relevance_score: float


@dataclass
class TemporalScore:
    """时效性评分"""
    freshness_score: float
    relevance_score: float
    decay_score: float
    final_score: float
    features: TemporalFeatures


class TemporalScorer:
    """时效性评分器"""
    
    def __init__(self):
        # 时效性参数
        self.freshness_decay_factor = 0.1  # 新鲜度衰减因子
        self.max_age_days = 365  # 最大有效天数
        self.relevance_weight = 0.6  # 相关性权重
        self.freshness_weight = 0.4  # 新鲜度权重
        
        # 时间关键词
        self.temporal_keywords = {
            "recent": ["最近", "近期", "目前", "现在", "当前", "新", "最新"],
            "past": ["过去", "以前", "之前", "历史", "曾经", "原来"],
            "future": ["未来", "将来", "即将", "计划", "预计", "预期"],
            "urgent": ["紧急", "立即", "马上", "急需", "迫切", "及时"],
            "periodic": ["定期", "每天", "每周", "每月", "每年", "周期性"]
        }
        
        # 时间模式
        self.date_patterns = [
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',  # 2023年12月25日
            r'(\d{4})-(\d{1,2})-(\d{1,2})',     # 2023-12-25
            r'(\d{4})/(\d{1,2})/(\d{1,2})',     # 2023/12/25
            r'(\d{1,2})月(\d{1,2})日',           # 12月25日
            r'(\d{4})年(\d{1,2})月',             # 2023年12月
            r'(\d{4})年',                        # 2023年
        ]
    
    async def calculate_temporal_score(self, content: str, query: str, 
                                     metadata: Dict[str, Any] = None) -> TemporalScore:
        """
        计算时效性评分
        
        Args:
            content: 文档内容
            query: 查询文本
            metadata: 文档元数据
            
        Returns:
            时效性评分
        """
        try:
            logger.debug(f"计算时效性评分: {content[:50]}...")
            
            # 1. 提取时间特征
            features = await self._extract_temporal_features(content, metadata)
            
            # 2. 分析查询时间意图
            query_temporal_intent = await self._analyze_query_temporal_intent(query)
            
            # 3. 计算新鲜度评分
            freshness_score = await self._calculate_freshness_score(features)
            
            # 4. 计算时间相关性评分
            relevance_score = await self._calculate_temporal_relevance(
                features, query_temporal_intent
            )
            
            # 5. 计算时间衰减评分
            decay_score = await self._calculate_decay_score(features)
            
            # 6. 计算最终评分
            final_score = (
                relevance_score * self.relevance_weight +
                freshness_score * self.freshness_weight
            ) * decay_score
            
            temporal_score = TemporalScore(
                freshness_score=freshness_score,
                relevance_score=relevance_score,
                decay_score=decay_score,
                final_score=final_score,
                features=features
            )
            
            # 记录监控指标
            enhanced_monitoring.record_hard_negative_metrics(
                mining_duration=0,  # 这里是评分时间，简化处理
                quality_score=final_score,
                mining_method="temporal_scoring"
            )
            
            logger.debug(f"时效性评分完成: {final_score:.3f}")
            return temporal_score
            
        except Exception as e:
            logger.error(f"时效性评分计算失败: {e}")
            return TemporalScore(0.5, 0.5, 1.0, 0.5, TemporalFeatures(None, None, [], [], 0.5, 0.5))
    
    async def _extract_temporal_features(self, content: str, 
                                       metadata: Dict[str, Any] = None) -> TemporalFeatures:
        """提取时间特征"""
        try:
            # 从元数据获取时间信息
            creation_time = None
            update_time = None
            
            if metadata:
                if 'created_at' in metadata:
                    creation_time = self._parse_datetime(metadata['created_at'])
                if 'updated_at' in metadata:
                    update_time = self._parse_datetime(metadata['updated_at'])
            
            # 从内容中提取提及的日期
            mentioned_dates = self._extract_mentioned_dates(content)
            
            # 提取时间关键词
            temporal_keywords = self._extract_temporal_keywords(content)
            
            # 计算基础新鲜度和相关性
            freshness_score = self._calculate_base_freshness(creation_time, update_time)
            relevance_score = self._calculate_base_relevance(mentioned_dates, temporal_keywords)
            
            return TemporalFeatures(
                creation_time=creation_time,
                update_time=update_time,
                mentioned_dates=mentioned_dates,
                temporal_keywords=temporal_keywords,
                freshness_score=freshness_score,
                relevance_score=relevance_score
            )
            
        except Exception as e:
            logger.error(f"时间特征提取失败: {e}")
            return TemporalFeatures(None, None, [], [], 0.5, 0.5)
    
    def _parse_datetime(self, dt_str: Any) -> Optional[datetime]:
        """解析日期时间"""
        try:
            if isinstance(dt_str, datetime):
                return dt_str
            elif isinstance(dt_str, str):
                return parser.parse(dt_str)
            else:
                return None
        except:
            return None
    
    def _extract_mentioned_dates(self, content: str) -> List[datetime]:
        """提取内容中提及的日期"""
        try:
            dates = []
            current_year = datetime.now().year
            
            for pattern in self.date_patterns:
                matches = re.findall(pattern, content)
                
                for match in matches:
                    try:
                        if len(match) == 3:  # 年月日
                            year, month, day = match
                            date = datetime(int(year), int(month), int(day))
                        elif len(match) == 2:  # 月日或年月
                            if '年' in pattern:  # 年月
                                year, month = match
                                date = datetime(int(year), int(month), 1)
                            else:  # 月日
                                month, day = match
                                date = datetime(current_year, int(month), int(day))
                        elif len(match) == 1:  # 年
                            year = match[0]
                            date = datetime(int(year), 1, 1)
                        else:
                            continue
                        
                        dates.append(date)
                        
                    except ValueError:
                        continue
            
            return dates
            
        except Exception as e:
            logger.error(f"日期提取失败: {e}")
            return []
    
    def _extract_temporal_keywords(self, content: str) -> List[str]:
        """提取时间关键词"""
        try:
            found_keywords = []
            content_lower = content.lower()
            
            for category, keywords in self.temporal_keywords.items():
                for keyword in keywords:
                    if keyword in content_lower:
                        found_keywords.append(keyword)
            
            return found_keywords
            
        except Exception as e:
            logger.error(f"时间关键词提取失败: {e}")
            return []
    
    def _calculate_base_freshness(self, creation_time: Optional[datetime], 
                                update_time: Optional[datetime]) -> float:
        """计算基础新鲜度"""
        try:
            now = datetime.now()
            
            # 使用更新时间或创建时间
            reference_time = update_time or creation_time
            
            if not reference_time:
                return 0.5  # 无时间信息，中等新鲜度
            
            # 计算天数差
            days_diff = (now - reference_time).days
            
            if days_diff < 0:
                return 1.0  # 未来时间，最高新鲜度
            
            # 指数衰减
            freshness = math.exp(-days_diff * self.freshness_decay_factor / 365)
            
            return max(0.1, min(1.0, freshness))
            
        except Exception as e:
            logger.error(f"基础新鲜度计算失败: {e}")
            return 0.5
    
    def _calculate_base_relevance(self, mentioned_dates: List[datetime], 
                                temporal_keywords: List[str]) -> float:
        """计算基础时间相关性"""
        try:
            relevance = 0.5  # 基础相关性
            
            # 日期提及奖励
            if mentioned_dates:
                now = datetime.now()
                recent_dates = [d for d in mentioned_dates if (now - d).days <= 30]
                date_score = len(recent_dates) / len(mentioned_dates)
                relevance += date_score * 0.3
            
            # 时间关键词奖励
            if temporal_keywords:
                recent_keywords = [k for k in temporal_keywords if k in self.temporal_keywords["recent"]]
                keyword_score = len(recent_keywords) / len(temporal_keywords)
                relevance += keyword_score * 0.2
            
            return min(1.0, relevance)
            
        except Exception as e:
            logger.error(f"基础时间相关性计算失败: {e}")
            return 0.5
    
    async def _analyze_query_temporal_intent(self, query: str) -> Dict[str, float]:
        """分析查询的时间意图"""
        try:
            intent_scores = {
                "recent": 0.0,
                "past": 0.0,
                "future": 0.0,
                "urgent": 0.0,
                "periodic": 0.0
            }
            
            query_lower = query.lower()
            
            for intent, keywords in self.temporal_keywords.items():
                for keyword in keywords:
                    if keyword in query_lower:
                        intent_scores[intent] += 1.0
            
            # 归一化
            total_score = sum(intent_scores.values())
            if total_score > 0:
                for intent in intent_scores:
                    intent_scores[intent] /= total_score
            else:
                # 默认偏向最近
                intent_scores["recent"] = 0.6
                intent_scores["past"] = 0.2
                intent_scores["future"] = 0.1
                intent_scores["urgent"] = 0.05
                intent_scores["periodic"] = 0.05
            
            return intent_scores
            
        except Exception as e:
            logger.error(f"查询时间意图分析失败: {e}")
            return {"recent": 0.6, "past": 0.2, "future": 0.1, "urgent": 0.05, "periodic": 0.05}
    
    async def _calculate_freshness_score(self, features: TemporalFeatures) -> float:
        """计算新鲜度评分"""
        try:
            base_freshness = features.freshness_score
            
            # 时间关键词调整
            keyword_bonus = 0.0
            for keyword in features.temporal_keywords:
                if keyword in self.temporal_keywords["recent"]:
                    keyword_bonus += 0.1
                elif keyword in self.temporal_keywords["urgent"]:
                    keyword_bonus += 0.15
            
            freshness_score = min(1.0, base_freshness + keyword_bonus)
            return freshness_score
            
        except Exception as e:
            logger.error(f"新鲜度评分计算失败: {e}")
            return 0.5
    
    async def _calculate_temporal_relevance(self, features: TemporalFeatures, 
                                          query_intent: Dict[str, float]) -> float:
        """计算时间相关性评分"""
        try:
            relevance_score = features.relevance_score
            
            # 根据查询意图调整
            intent_adjustment = 0.0
            
            # 最近意图
            if query_intent["recent"] > 0.3:
                recent_keywords = [k for k in features.temporal_keywords 
                                 if k in self.temporal_keywords["recent"]]
                if recent_keywords:
                    intent_adjustment += query_intent["recent"] * 0.3
            
            # 紧急意图
            if query_intent["urgent"] > 0.2:
                urgent_keywords = [k for k in features.temporal_keywords 
                                 if k in self.temporal_keywords["urgent"]]
                if urgent_keywords:
                    intent_adjustment += query_intent["urgent"] * 0.4
            
            # 历史意图
            if query_intent["past"] > 0.3:
                past_keywords = [k for k in features.temporal_keywords 
                               if k in self.temporal_keywords["past"]]
                if past_keywords:
                    intent_adjustment += query_intent["past"] * 0.2
            
            final_relevance = min(1.0, relevance_score + intent_adjustment)
            return final_relevance
            
        except Exception as e:
            logger.error(f"时间相关性评分计算失败: {e}")
            return 0.5
    
    async def _calculate_decay_score(self, features: TemporalFeatures) -> float:
        """计算时间衰减评分"""
        try:
            if not features.creation_time:
                return 1.0  # 无时间信息，不衰减
            
            now = datetime.now()
            days_diff = (now - features.creation_time).days
            
            if days_diff <= 0:
                return 1.0  # 当天或未来，无衰减
            
            if days_diff > self.max_age_days:
                return 0.1  # 超过最大年龄，最小分数
            
            # 线性衰减
            decay_score = 1.0 - (days_diff / self.max_age_days) * 0.9
            
            return max(0.1, decay_score)
            
        except Exception as e:
            logger.error(f"时间衰减评分计算失败: {e}")
            return 1.0
    
    async def batch_score_documents(self, documents: List[Dict[str, Any]], 
                                  query: str) -> List[TemporalScore]:
        """批量评分文档"""
        try:
            scores = []
            
            for doc in documents:
                content = doc.get("content", "")
                metadata = doc.get("metadata", {})
                
                score = await self.calculate_temporal_score(content, query, metadata)
                scores.append(score)
            
            return scores
            
        except Exception as e:
            logger.error(f"批量文档评分失败: {e}")
            return []
    
    def update_parameters(self, freshness_decay_factor: float = None,
                         max_age_days: int = None,
                         relevance_weight: float = None,
                         freshness_weight: float = None):
        """更新时效性参数"""
        try:
            if freshness_decay_factor is not None:
                self.freshness_decay_factor = freshness_decay_factor
            
            if max_age_days is not None:
                self.max_age_days = max_age_days
            
            if relevance_weight is not None:
                self.relevance_weight = relevance_weight
                self.freshness_weight = 1.0 - relevance_weight
            
            if freshness_weight is not None:
                self.freshness_weight = freshness_weight
                self.relevance_weight = 1.0 - freshness_weight
            
            logger.info(f"时效性参数已更新: 衰减因子={self.freshness_decay_factor}, "
                       f"最大年龄={self.max_age_days}天")
            
        except Exception as e:
            logger.error(f"参数更新失败: {e}")
    
    async def get_temporal_statistics(self) -> Dict[str, Any]:
        """获取时效性统计信息"""
        try:
            # 从数据库获取文档时间分布
            stats = await database_client.fetch("""
                SELECT 
                    DATE_TRUNC('month', created_at) as month,
                    COUNT(*) as doc_count
                FROM documents 
                WHERE created_at >= NOW() - INTERVAL '12 months'
                GROUP BY DATE_TRUNC('month', created_at)
                ORDER BY month DESC
            """)
            
            monthly_distribution = {str(row['month']): row['doc_count'] for row in stats}
            
            # 计算平均文档年龄
            avg_age_result = await database_client.fetchrow("""
                SELECT AVG(EXTRACT(DAYS FROM NOW() - created_at)) as avg_age_days
                FROM documents
                WHERE created_at IS NOT NULL
            """)
            
            avg_age_days = avg_age_result['avg_age_days'] if avg_age_result else 0
            
            return {
                "monthly_distribution": monthly_distribution,
                "avg_document_age_days": float(avg_age_days) if avg_age_days else 0,
                "current_parameters": {
                    "freshness_decay_factor": self.freshness_decay_factor,
                    "max_age_days": self.max_age_days,
                    "relevance_weight": self.relevance_weight,
                    "freshness_weight": self.freshness_weight
                }
            }
            
        except Exception as e:
            logger.error(f"获取时效性统计失败: {e}")
            return {}


# 创建全局实例
temporal_scorer = TemporalScorer()
