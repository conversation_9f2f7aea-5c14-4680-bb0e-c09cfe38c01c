"""
对比学习模型训练器
使用InfoNCE损失训练嵌入模型，提升语义匹配精度
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import math
from datetime import datetime

from loguru import logger
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel, AdamW, get_linear_schedule_with_warmup

from .contrastive_data_builder import ContrastiveExample
from .enhanced_monitoring import enhanced_monitoring


@dataclass
class TrainingConfig:
    """训练配置"""
    model_name: str = "sentence-transformers/all-MiniLM-L6-v2"
    batch_size: int = 32
    learning_rate: float = 2e-5
    epochs: int = 3
    warmup_steps: int = 500
    max_seq_length: int = 512
    temperature: float = 0.07
    margin: float = 0.2
    weight_decay: float = 0.01
    gradient_clip_norm: float = 1.0
    save_steps: int = 1000
    eval_steps: int = 500
    logging_steps: int = 100


class ContrastiveDataset(Dataset):
    """对比学习数据集"""
    
    def __init__(self, examples: List[ContrastiveExample], tokenizer, max_length: int = 512):
        self.examples = examples
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        example = self.examples[idx]
        
        # 编码查询
        query_encoding = self.tokenizer(
            example.query,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # 编码正样本
        positive_encoding = self.tokenizer(
            example.positive,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # 编码负样本
        negative_encoding = self.tokenizer(
            example.negative,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'query_input_ids': query_encoding['input_ids'].squeeze(),
            'query_attention_mask': query_encoding['attention_mask'].squeeze(),
            'positive_input_ids': positive_encoding['input_ids'].squeeze(),
            'positive_attention_mask': positive_encoding['attention_mask'].squeeze(),
            'negative_input_ids': negative_encoding['input_ids'].squeeze(),
            'negative_attention_mask': negative_encoding['attention_mask'].squeeze(),
            'weight': torch.tensor(example.weight, dtype=torch.float),
            'difficulty': example.difficulty
        }


class ContrastiveModel(nn.Module):
    """对比学习模型"""
    
    def __init__(self, model_name: str, temperature: float = 0.07):
        super().__init__()
        self.encoder = AutoModel.from_pretrained(model_name)
        self.temperature = temperature
        
        # 添加投影层
        hidden_size = self.encoder.config.hidden_size
        self.projection = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size)
        )
    
    def forward(self, input_ids, attention_mask):
        """前向传播"""
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        
        # 使用CLS token的表示
        pooled_output = outputs.last_hidden_state[:, 0]
        
        # 投影
        projected = self.projection(pooled_output)
        
        # L2归一化
        normalized = F.normalize(projected, p=2, dim=1)
        
        return normalized
    
    def compute_similarity(self, query_emb, doc_emb):
        """计算相似度"""
        return torch.matmul(query_emb, doc_emb.T) / self.temperature


class InfoNCELoss(nn.Module):
    """InfoNCE损失函数"""
    
    def __init__(self, temperature: float = 0.07, margin: float = 0.2):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
    
    def forward(self, query_emb, positive_emb, negative_emb, weights=None):
        """
        计算InfoNCE损失
        
        Args:
            query_emb: 查询嵌入 [batch_size, hidden_size]
            positive_emb: 正样本嵌入 [batch_size, hidden_size]
            negative_emb: 负样本嵌入 [batch_size, hidden_size]
            weights: 样本权重 [batch_size]
        """
        batch_size = query_emb.size(0)
        
        # 计算正样本相似度
        pos_sim = torch.sum(query_emb * positive_emb, dim=1) / self.temperature
        
        # 计算负样本相似度
        neg_sim = torch.sum(query_emb * negative_emb, dim=1) / self.temperature
        
        # InfoNCE损失
        logits = torch.cat([pos_sim.unsqueeze(1), neg_sim.unsqueeze(1)], dim=1)
        labels = torch.zeros(batch_size, dtype=torch.long, device=query_emb.device)
        
        loss = F.cross_entropy(logits, labels, reduction='none')
        
        # 应用权重
        if weights is not None:
            loss = loss * weights
        
        return loss.mean()


class ContrastiveTrainer:
    """对比学习训练器"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)
        self.model = ContrastiveModel(config.model_name, config.temperature).to(self.device)
        
        # 损失函数
        self.criterion = InfoNCELoss(config.temperature, config.margin)
        
        # 训练状态
        self.global_step = 0
        self.best_eval_loss = float('inf')
        
        logger.info(f"对比学习训练器初始化完成，使用设备: {self.device}")
    
    def load_dataset(self, data_path: str) -> Tuple[ContrastiveDataset, ContrastiveDataset]:
        """加载数据集"""
        try:
            data_dir = Path(data_path)
            
            # 加载训练集
            train_examples = []
            train_file = data_dir / "train.jsonl"
            if train_file.exists():
                with open(train_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line)
                        example = ContrastiveExample(**data)
                        train_examples.append(example)
            
            # 加载测试集
            test_examples = []
            test_file = data_dir / "test.jsonl"
            if test_file.exists():
                with open(test_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        data = json.loads(line)
                        example = ContrastiveExample(**data)
                        test_examples.append(example)
            
            # 创建数据集
            train_dataset = ContrastiveDataset(train_examples, self.tokenizer, self.config.max_seq_length)
            test_dataset = ContrastiveDataset(test_examples, self.tokenizer, self.config.max_seq_length)
            
            logger.info(f"数据集加载完成: 训练集 {len(train_dataset)} 样本，测试集 {len(test_dataset)} 样本")
            return train_dataset, test_dataset
            
        except Exception as e:
            logger.error(f"数据集加载失败: {e}")
            raise
    
    def train(self, train_dataset: ContrastiveDataset, 
              eval_dataset: ContrastiveDataset = None,
              output_dir: str = "models/contrastive") -> Dict[str, Any]:
        """训练模型"""
        try:
            logger.info("开始对比学习模型训练...")
            
            # 创建数据加载器
            train_loader = DataLoader(
                train_dataset, 
                batch_size=self.config.batch_size, 
                shuffle=True,
                num_workers=4
            )
            
            eval_loader = None
            if eval_dataset:
                eval_loader = DataLoader(
                    eval_dataset,
                    batch_size=self.config.batch_size,
                    shuffle=False,
                    num_workers=4
                )
            
            # 设置优化器和调度器
            optimizer = AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
            
            total_steps = len(train_loader) * self.config.epochs
            scheduler = get_linear_schedule_with_warmup(
                optimizer,
                num_warmup_steps=self.config.warmup_steps,
                num_training_steps=total_steps
            )
            
            # 训练循环
            training_stats = {
                'train_losses': [],
                'eval_losses': [],
                'learning_rates': [],
                'best_eval_loss': float('inf')
            }
            
            for epoch in range(self.config.epochs):
                logger.info(f"开始第 {epoch + 1}/{self.config.epochs} 轮训练")
                
                # 训练一个epoch
                train_loss = self._train_epoch(train_loader, optimizer, scheduler)
                training_stats['train_losses'].append(train_loss)
                
                # 评估
                if eval_loader:
                    eval_loss = self._evaluate(eval_loader)
                    training_stats['eval_losses'].append(eval_loss)
                    
                    # 保存最佳模型
                    if eval_loss < training_stats['best_eval_loss']:
                        training_stats['best_eval_loss'] = eval_loss
                        self._save_model(output_dir, f"best_model_epoch_{epoch}")
                
                logger.info(f"Epoch {epoch + 1} 完成 - 训练损失: {train_loss:.4f}")
                
                # 记录监控指标
                enhanced_monitoring.record_hard_negative_metrics(
                    mining_duration=0,  # 这里是训练时间，简化处理
                    quality_score=1.0 - train_loss,
                    mining_method="contrastive_training"
                )
            
            # 保存最终模型
            self._save_model(output_dir, "final_model")
            
            # 保存训练统计
            self._save_training_stats(training_stats, output_dir)
            
            logger.info("对比学习模型训练完成")
            return training_stats
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            raise
    
    def _train_epoch(self, train_loader: DataLoader, optimizer, scheduler) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, batch in enumerate(train_loader):
            # 移动数据到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            query_emb = self.model(batch['query_input_ids'], batch['query_attention_mask'])
            positive_emb = self.model(batch['positive_input_ids'], batch['positive_attention_mask'])
            negative_emb = self.model(batch['negative_input_ids'], batch['negative_attention_mask'])
            
            # 计算损失
            loss = self.criterion(query_emb, positive_emb, negative_emb, batch['weight'])
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip_norm)
            
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
            self.global_step += 1
            
            # 记录日志
            if self.global_step % self.config.logging_steps == 0:
                logger.debug(f"Step {self.global_step}, Loss: {loss.item():.4f}")
        
        return total_loss / num_batches
    
    def _evaluate(self, eval_loader: DataLoader) -> float:
        """评估模型"""
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for batch in eval_loader:
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # 前向传播
                query_emb = self.model(batch['query_input_ids'], batch['query_attention_mask'])
                positive_emb = self.model(batch['positive_input_ids'], batch['positive_attention_mask'])
                negative_emb = self.model(batch['negative_input_ids'], batch['negative_attention_mask'])
                
                # 计算损失
                loss = self.criterion(query_emb, positive_emb, negative_emb, batch['weight'])
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def _save_model(self, output_dir: str, model_name: str):
        """保存模型"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            model_path = output_path / model_name
            
            # 保存模型状态
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'config': self.config,
                'global_step': self.global_step
            }, model_path / "pytorch_model.bin")
            
            # 保存分词器
            self.tokenizer.save_pretrained(model_path)
            
            logger.info(f"模型保存到: {model_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
    
    def _save_training_stats(self, stats: Dict[str, Any], output_dir: str):
        """保存训练统计"""
        try:
            output_path = Path(output_dir)
            stats_file = output_path / "training_stats.json"
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            logger.info(f"训练统计保存到: {stats_file}")
            
        except Exception as e:
            logger.error(f"保存训练统计失败: {e}")


# 创建全局实例
def create_trainer(config: TrainingConfig = None) -> ContrastiveTrainer:
    """创建训练器实例"""
    if config is None:
        config = TrainingConfig()
    return ContrastiveTrainer(config)
