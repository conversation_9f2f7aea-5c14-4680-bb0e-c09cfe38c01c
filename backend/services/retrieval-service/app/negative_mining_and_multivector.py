"""
困难负样本挖掘和多向量检索实现
提升检索模型的判别能力和语义覆盖度
"""

import asyncio
import numpy as np
import torch
import torch.nn.functional as F
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
import math
import random

from .config import settings
from .enhanced_retrieval import SearchResult
from .embedding_client import embedding_client
from .redis_client import retrieval_cache


@dataclass
class ContrastiveExample:
    """对比学习样本"""
    query: str
    positive: str
    negative: str
    difficulty: str  # "easy", "medium", "hard"
    weight: float = 1.0


@dataclass
class AspectVector:
    """方面向量"""
    aspect_name: str
    vector: List[float]
    content: str
    weight: float = 1.0


class HardNegativeMiner:
    """困难负样本挖掘器"""
    
    def __init__(self):
        self.similarity_threshold_low = 0.6   # 最低相似度阈值
        self.similarity_threshold_high = 0.85 # 最高相似度阈值
        self.relevance_threshold = 0.3        # 真实相关性阈值
        self.max_candidates = 100             # 最大候选数量
        self.max_hard_negatives = 10          # 最大困难负样本数

        # 质量控制参数
        self.min_content_length = 50
        self.max_content_length = 2000
        self.diversity_threshold = 0.8

        # 加载模型
        try:
            from sentence_transformers import SentenceTransformer, CrossEncoder
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')
            logger.info("困难负样本挖掘模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            self.embedding_model = None
            self.cross_encoder = None

        # 缓存
        self.embedding_cache = {}
        self.similarity_cache = {}
    
    async def mine_hard_negatives(self, query: str, positive_docs: List[str], 
                                 collection_name: str = None) -> List[str]:
        """
        挖掘困难负样本
        
        Args:
            query: 查询文本
            positive_docs: 正样本文档列表
            collection_name: 集合名称
            
        Returns:
            困难负样本列表
        """
        try:
            logger.info(f"开始挖掘困难负样本: {query[:50]}...")

            if not self.embedding_model or not self.cross_encoder:
                logger.error("模型未正确加载")
                return []

            # 1. 获取语义相似的候选文档
            candidates = await self._get_similar_candidates(query, positive_docs, collection_name)

            if not candidates:
                logger.warning("未找到候选文档")
                return []

            # 2. 计算语义相似度
            similarities = await self._calculate_similarities(query, candidates)

            # 3. 评估真实相关性
            relevance_scores = await self._evaluate_relevance(query, candidates)

            # 4. 筛选困难负样本
            hard_negatives = await self._filter_hard_negatives(
                query, candidates, similarities, relevance_scores
            )

            # 5. 质量控制和多样性保证
            filtered_negatives = await self._quality_control(hard_negatives, query)

            # 6. 保存到数据库
            await self._save_hard_negatives(query, positive_docs, filtered_negatives)

            logger.info(f"挖掘到 {len(filtered_negatives)} 个高质量困难负样本")
            return filtered_negatives
                query_embedding, similar_docs
            )
            
            # 4. 筛选困难负样本
            hard_negatives = await self._filter_hard_negatives(
                query, similar_docs, similarity_scores
            )
            
            logger.info(f"挖掘到 {len(hard_negatives)} 个困难负样本")
            return hard_negatives
            
        except Exception as e:
            logger.error(f"困难负样本挖掘失败: {e}")
            return []

    async def _get_similar_candidates(self, query: str, positive_docs: List[str],
                                    collection_name: str = None) -> List[str]:
        """获取语义相似的候选文档"""
        try:
            # 使用向量检索获取相似文档
            from .enhanced_retrieval import enhanced_retrieval_engine

            # 执行向量检索，获取更多候选
            search_results = await enhanced_retrieval_engine.vector_search(
                query, top_k=self.max_candidates * 2
            )

            candidates = []
            positive_set = set(positive_docs)

            for result in search_results:
                # 排除正样本
                if result.content not in positive_set:
                    # 基本质量过滤
                    if (self.min_content_length <= len(result.content) <= self.max_content_length):
                        candidates.append(result.content)

                if len(candidates) >= self.max_candidates:
                    break

            logger.info(f"获取到 {len(candidates)} 个候选文档")
            return candidates

        except Exception as e:
            logger.error(f"获取候选文档失败: {e}")
            return []

    async def _calculate_similarities(self, query: str, candidates: List[str]) -> List[float]:
        """计算语义相似度"""
        try:
            # 使用缓存
            cache_key = f"sim_{hash(query)}_{hash(tuple(candidates))}"
            if cache_key in self.similarity_cache:
                return self.similarity_cache[cache_key]

            # 计算查询和候选文档的嵌入
            query_embedding = self.embedding_model.encode([query])
            candidate_embeddings = self.embedding_model.encode(candidates)

            # 计算余弦相似度
            from sklearn.metrics.pairwise import cosine_similarity
            similarities = cosine_similarity(query_embedding, candidate_embeddings)[0]

            # 缓存结果
            self.similarity_cache[cache_key] = similarities.tolist()

            return similarities.tolist()

        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return [0.0] * len(candidates)

    async def _evaluate_relevance(self, query: str, candidates: List[str]) -> List[float]:
        """评估真实相关性"""
        try:
            # 使用交叉编码器评估相关性
            pairs = [(query, candidate) for candidate in candidates]
            relevance_scores = self.cross_encoder.predict(pairs)

            # 归一化到0-1范围
            import numpy as np
            scores = np.array(relevance_scores)
            normalized_scores = (scores - scores.min()) / (scores.max() - scores.min() + 1e-8)

            return normalized_scores.tolist()

        except Exception as e:
            logger.error(f"评估相关性失败: {e}")
            return [0.0] * len(candidates)

    async def _filter_hard_negatives(self, query: str, candidates: List[str],
                                   similarities: List[float],
                                   relevance_scores: List[float]) -> List[str]:
        """筛选困难负样本"""
        try:
            hard_negatives = []

            for i, (candidate, sim, rel) in enumerate(zip(candidates, similarities, relevance_scores)):
                # 困难负样本条件：
                # 1. 语义相似度在指定范围内（不太相似也不太不相似）
                # 2. 真实相关性低（确实不相关）
                if (self.similarity_threshold_low <= sim <= self.similarity_threshold_high and
                    rel <= self.relevance_threshold):

                    hard_negatives.append({
                        "content": candidate,
                        "similarity": sim,
                        "relevance": rel,
                        "difficulty": self._calculate_difficulty(sim, rel)
                    })

            # 按困难度排序，选择最困难的样本
            hard_negatives.sort(key=lambda x: x["difficulty"], reverse=True)

            # 返回内容列表
            result = [item["content"] for item in hard_negatives[:self.max_hard_negatives]]

            logger.info(f"筛选出 {len(result)} 个困难负样本")
            return result

        except Exception as e:
            logger.error(f"筛选困难负样本失败: {e}")
            return []

    def _calculate_difficulty(self, similarity: float, relevance: float) -> float:
        """计算困难度分数"""
        # 困难度 = 语义相似度 * (1 - 真实相关性)
        # 语义相似度越高，真实相关性越低，困难度越高
        return similarity * (1 - relevance)

    async def _quality_control(self, hard_negatives: List[str], query: str) -> List[str]:
        """质量控制和多样性保证"""
        try:
            if not hard_negatives:
                return []

            # 1. 长度过滤
            filtered = [
                neg for neg in hard_negatives
                if self.min_content_length <= len(neg) <= self.max_content_length
            ]

            # 2. 多样性过滤
            diverse_negatives = await self._ensure_diversity(filtered)

            # 3. 内容质量检查
            quality_negatives = await self._check_content_quality(diverse_negatives, query)

            logger.info(f"质量控制后保留 {len(quality_negatives)} 个负样本")
            return quality_negatives

        except Exception as e:
            logger.error(f"质量控制失败: {e}")
            return hard_negatives

    async def _ensure_diversity(self, negatives: List[str]) -> List[str]:
        """确保负样本多样性"""
        try:
            if len(negatives) <= 1:
                return negatives

            # 计算负样本之间的相似度
            embeddings = self.embedding_model.encode(negatives)

            diverse_negatives = [negatives[0]]  # 保留第一个
            diverse_embeddings = [embeddings[0]]

            for i, (neg, emb) in enumerate(zip(negatives[1:], embeddings[1:]), 1):
                # 检查与已选择样本的相似度
                from sklearn.metrics.pairwise import cosine_similarity
                similarities = cosine_similarity([emb], diverse_embeddings)[0]

                # 如果与所有已选择样本的相似度都低于阈值，则保留
                if all(sim < self.diversity_threshold for sim in similarities):
                    diverse_negatives.append(neg)
                    diverse_embeddings.append(emb)

            return diverse_negatives

        except Exception as e:
            logger.error(f"多样性过滤失败: {e}")
            return negatives

    async def _check_content_quality(self, negatives: List[str], query: str) -> List[str]:
        """检查内容质量"""
        try:
            quality_negatives = []

            for negative in negatives:
                # 基本质量检查
                if self._is_quality_content(negative, query):
                    quality_negatives.append(negative)

            return quality_negatives

        except Exception as e:
            logger.error(f"内容质量检查失败: {e}")
            return negatives

    def _is_quality_content(self, content: str, query: str) -> bool:
        """判断内容质量"""
        try:
            # 1. 长度检查
            if not (self.min_content_length <= len(content) <= self.max_content_length):
                return False

            # 2. 重复内容检查
            words = content.lower().split()
            if len(set(words)) / len(words) < 0.5:  # 词汇多样性太低
                return False

            # 3. 与查询的词汇重叠检查
            query_words = set(query.lower().split())
            content_words = set(words)
            overlap = len(query_words & content_words) / len(query_words)

            # 重叠度不能太高（避免过于相似）也不能太低（避免完全无关）
            if overlap > 0.8 or overlap < 0.1:
                return False

            return True

        except Exception as e:
            logger.error(f"质量检查失败: {e}")
            return True

    async def _save_hard_negatives(self, query: str, positive_docs: List[str],
                                 hard_negatives: List[str]):
        """保存困难负样本到数据库"""
        try:
            from .database_client import database_client

            for positive_doc in positive_docs:
                for negative_doc in hard_negatives:
                    # 计算相似度和相关性分数
                    similarity = await self._calculate_single_similarity(query, negative_doc)
                    relevance = await self._calculate_single_relevance(query, negative_doc)

                    # 保存到数据库
                    await database_client.execute("""
                        INSERT INTO hard_negative_samples
                        (query, positive_doc_id, negative_doc_id, similarity_score,
                         relevance_score, difficulty_level)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT DO NOTHING
                    """,
                    query,
                    hash(positive_doc),  # 简化处理，实际应该是文档ID
                    hash(negative_doc),
                    similarity,
                    relevance,
                    "hard"
                    )

            logger.info(f"保存了 {len(hard_negatives)} 个困难负样本到数据库")

        except Exception as e:
            logger.error(f"保存困难负样本失败: {e}")

    async def _calculate_single_similarity(self, query: str, document: str) -> float:
        """计算单个文档的相似度"""
        try:
            embeddings = self.embedding_model.encode([query, document])
            from sklearn.metrics.pairwise import cosine_similarity
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
        except:
            return 0.0

    async def _calculate_single_relevance(self, query: str, document: str) -> float:
        """计算单个文档的相关性"""
        try:
            relevance = self.cross_encoder.predict([(query, document)])[0]
            return float(relevance)
        except:
            return 0.0

    async def create_contrastive_training_data(self, training_queries: List[str],
                                             positive_docs: Dict[str, List[str]]) -> List[ContrastiveExample]:
        """创建对比学习训练数据"""
        try:
            logger.info("开始创建对比学习训练数据...")

            contrastive_examples = []

            for query in training_queries:
                if query not in positive_docs:
                    continue

                query_positives = positive_docs[query]

                # 为每个查询挖掘困难负样本
                hard_negatives = await self.mine_hard_negatives(query, query_positives)

                # 创建对比学习样本
                for positive in query_positives:
                    for negative in hard_negatives:
                        # 计算困难度
                        similarity = await self._calculate_single_similarity(query, negative)
                        relevance = await self._calculate_single_relevance(query, negative)
                        difficulty_score = self._calculate_difficulty(similarity, relevance)

                        # 确定困难度等级
                        if difficulty_score > 0.7:
                            difficulty = "hard"
                            weight = 1.5
                        elif difficulty_score > 0.4:
                            difficulty = "medium"
                            weight = 1.0
                        else:
                            difficulty = "easy"
                            weight = 0.5

                        example = ContrastiveExample(
                            query=query,
                            positive=positive,
                            negative=negative,
                            difficulty=difficulty,
                            weight=weight
                        )

                        contrastive_examples.append(example)

            logger.info(f"创建了 {len(contrastive_examples)} 个对比学习样本")
            return contrastive_examples

        except Exception as e:
            logger.error(f"创建对比学习数据失败: {e}")
            return []
    
    async def _get_similar_candidates(self, query_embedding: List[float], 
                                    positive_docs: List[str],
                                    collection_name: str = None) -> List[Dict]:
        """获取语义相似的候选文档"""
        try:
            # 从向量数据库检索相似文档
            from .retrieval_engine import retrieval_engine
            
            # 使用向量检索获取候选文档
            candidates = await retrieval_engine.vector_search(
                query_embedding, 
                top_k=self.max_candidates,
                collection_name=collection_name
            )
            
            # 排除正样本
            positive_ids = set()
            for pos_doc in positive_docs:
                # 简化的ID提取，实际应该有更好的方法
                doc_hash = hash(pos_doc) % 1000000
                positive_ids.add(str(doc_hash))
            
            filtered_candidates = [
                doc for doc in candidates 
                if doc.get("id") not in positive_ids
            ]
            
            return filtered_candidates[:self.max_candidates]
            
        except Exception as e:
            logger.error(f"获取候选文档失败: {e}")
            return []
    
    async def _calculate_similarities(self, query_embedding: List[float], 
                                    candidates: List[Dict]) -> List[Tuple[Dict, float]]:
        """计算语义相似度"""
        similarities = []
        
        for candidate in candidates:
            try:
                # 获取候选文档的向量
                doc_content = candidate.get("content", "")
                doc_embedding = await embedding_client.embed_text(doc_content)
                
                if doc_embedding:
                    # 计算余弦相似度
                    similarity = self._cosine_similarity(query_embedding, doc_embedding)
                    similarities.append((candidate, similarity))
                
            except Exception as e:
                logger.error(f"计算相似度失败: {e}")
                continue
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1_np = np.array(vec1)
            vec2_np = np.array(vec2)
            
            dot_product = np.dot(vec1_np, vec2_np)
            norm1 = np.linalg.norm(vec1_np)
            norm2 = np.linalg.norm(vec2_np)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"余弦相似度计算失败: {e}")
            return 0.0
    
    async def _filter_hard_negatives(self, query: str, candidates: List[Dict], 
                                   similarities: List[Tuple[Dict, float]]) -> List[str]:
        """筛选困难负样本"""
        hard_negatives = []
        
        for candidate, similarity in similarities:
            # 1. 检查相似度范围（语义相似但不完全匹配）
            if not (self.similarity_threshold_low < similarity < self.similarity_threshold_high):
                continue
            
            # 2. 评估真实相关性
            doc_content = candidate.get("content", "")
            relevance = await self._assess_true_relevance(query, doc_content)
            
            # 3. 如果语义相似但实际不相关，则为困难负样本
            if relevance < self.relevance_threshold:
                hard_negatives.append(doc_content)
                
                if len(hard_negatives) >= self.max_hard_negatives:
                    break
        
        return hard_negatives
    
    async def _assess_true_relevance(self, query: str, document: str) -> float:
        """评估真实相关性"""
        try:
            # 简化的相关性评估
            # 实际应该使用更复杂的方法，如交叉编码器
            
            # 1. 关键词重叠
            query_words = set(query.lower().split())
            doc_words = set(document.lower().split())
            
            if not query_words:
                return 0.0
            
            overlap = len(query_words.intersection(doc_words))
            keyword_relevance = overlap / len(query_words)
            
            # 2. 长度惩罚（过短或过长的文档相关性较低）
            doc_length = len(document.split())
            length_penalty = 1.0
            
            if doc_length < 10:
                length_penalty = 0.5
            elif doc_length > 1000:
                length_penalty = 0.8
            
            # 3. 综合相关性
            relevance = keyword_relevance * length_penalty
            
            return min(relevance, 1.0)
            
        except Exception as e:
            logger.error(f"相关性评估失败: {e}")
            return 0.5
    
    async def create_contrastive_training_data(self, 
                                             training_queries: List[str],
                                             positive_docs: Dict[str, List[str]]) -> List[ContrastiveExample]:
        """创建对比学习训练数据"""
        try:
            logger.info(f"创建对比学习数据，查询数: {len(training_queries)}")
            
            contrastive_examples = []
            
            for query in training_queries:
                pos_docs = positive_docs.get(query, [])
                if not pos_docs:
                    continue
                
                # 为每个正样本挖掘困难负样本
                hard_negatives = await self.mine_hard_negatives(query, pos_docs)
                
                # 创建对比样本
                for pos_doc in pos_docs:
                    for neg_doc in hard_negatives:
                        example = ContrastiveExample(
                            query=query,
                            positive=pos_doc,
                            negative=neg_doc,
                            difficulty="hard",
                            weight=1.0
                        )
                        contrastive_examples.append(example)
            
            logger.info(f"创建了 {len(contrastive_examples)} 个对比学习样本")
            return contrastive_examples
            
        except Exception as e:
            logger.error(f"创建对比学习数据失败: {e}")
            return []


class MultiVectorRetriever:
    """多向量检索器"""
    
    def __init__(self):
        self.aspect_extractors = {
            "semantic": self._extract_semantic_aspect,
            "factual": self._extract_factual_aspect,
            "temporal": self._extract_temporal_aspect,
            "entity": self._extract_entity_aspect,
            "procedural": self._extract_procedural_aspect
        }

        self.aspect_weights = {
            "semantic": 0.3,
            "factual": 0.25,
            "temporal": 0.15,
            "entity": 0.2,
            "procedural": 0.1
        }

        # 融合参数
        self.fusion_method = "weighted_sum"  # weighted_sum, max_pooling, attention
        self.diversity_weight = 0.1
        self.min_aspect_confidence = 0.3

        # 加载模型
        self._load_models()

    def _load_models(self):
        """加载所需模型"""
        try:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

            # 加载NER模型（如果可用）
            try:
                import spacy
                self.nlp = spacy.load("zh_core_web_sm")
            except:
                logger.warning("中文NLP模型不可用，将使用简化的实体提取")
                self.nlp = None

            logger.info("多向量检索模型加载成功")

        except Exception as e:
            logger.error(f"多向量检索模型加载失败: {e}")
            self.embedding_model = None
            self.nlp = None
    
    async def create_multi_vectors(self, document: str) -> Dict[str, AspectVector]:
        """
        为文档创建多个向量表示
        
        Args:
            document: 文档内容
            
        Returns:
            多方面向量字典
        """
        try:
            logger.debug(f"创建多向量表示: {document[:50]}...")
            
            vectors = {}
            
            for aspect_name, extractor in self.aspect_extractors.items():
                try:
                    # 提取特定方面的内容
                    aspect_content = await extractor(document)
                    
                    if aspect_content:
                        # 生成该方面的向量
                        vector = await embedding_client.embed_text(aspect_content)
                        
                        if vector:
                            vectors[aspect_name] = AspectVector(
                                aspect_name=aspect_name,
                                vector=vector,
                                content=aspect_content,
                                weight=self.aspect_weights.get(aspect_name, 1.0)
                            )
                
                except Exception as e:
                    logger.error(f"提取{aspect_name}方面失败: {e}")
                    continue
            
            return vectors
            
        except Exception as e:
            logger.error(f"创建多向量失败: {e}")
            return {}
    
    async def multi_vector_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        多向量检索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            检索结果列表
        """
        try:
            logger.info(f"开始多向量检索: {query[:50]}...")
            
            # 1. 分析查询的主要方面
            query_aspects = await self._analyze_query_aspects(query)
            
            # 2. 为每个方面生成查询向量
            query_vectors = {}
            for aspect in query_aspects:
                aspect_query = await self._extract_aspect_query(query, aspect)
                if aspect_query:
                    vector = await self._embed_text(aspect_query)
                    if vector:
                        query_vectors[aspect] = {
                            "vector": vector,
                            "content": aspect_query,
                            "weight": self.aspect_weights.get(aspect, 0.1)
                        }

            if not query_vectors:
                logger.warning("未能生成任何方面的查询向量")
                return []

            # 3. 执行多方面检索
            aspect_results = {}
            for aspect, query_info in query_vectors.items():
                results = await self._search_aspect_vectors(
                    aspect, query_info["vector"], top_k * 2
                )
                if results:
                    aspect_results[aspect] = results

            # 4. 融合多方面结果
            fused_results = await self._fuse_aspect_results(aspect_results, query_vectors)

            # 5. 重排序和多样性优化
            final_results = await self._rerank_and_diversify(fused_results, query)

            # 记录监控指标
            from .enhanced_monitoring import enhanced_monitoring
            enhanced_monitoring.record_multi_vector_metrics(
                len(query_vectors),
                self._calculate_fusion_score(fused_results),
                self._classify_query_type(query),
                self.fusion_method
            )

            logger.info(f"多向量检索完成，{len(query_vectors)}个方面，返回{len(final_results)}个结果")
            return final_results[:top_k]

        except Exception as e:
            logger.error(f"多向量检索失败: {e}")
            # 回退到基础检索
            from .enhanced_retrieval import enhanced_retrieval_engine
            return await enhanced_retrieval_engine.enhanced_search(query, top_k=top_k)

    async def _embed_text(self, text: str) -> List[float]:
        """嵌入文本"""
        try:
            if not self.embedding_model:
                return []

            embedding = self.embedding_model.encode([text])
            return embedding[0].tolist()

        except Exception as e:
            logger.error(f"文本嵌入失败: {e}")
            return []

    async def _search_aspect_vectors(self, aspect: str, query_vector: List[float],
                                   top_k: int) -> List[Dict]:
        """搜索特定方面的向量"""
        try:
            from .database_client import database_client

            # 从方面向量表中检索
            results = await database_client.fetch("""
                SELECT
                    av.document_id,
                    av.content,
                    av.weight,
                    av.confidence,
                    av.vector <-> $1::vector as distance
                FROM aspect_vectors av
                WHERE av.aspect_name = $2
                ORDER BY av.vector <-> $1::vector
                LIMIT $3
            """, query_vector, aspect, top_k)

            # 转换为标准格式
            search_results = []
            for row in results:
                search_results.append({
                    "document_id": row["document_id"],
                    "content": row["content"],
                    "score": 1.0 - row["distance"],  # 距离转换为相似度
                    "aspect": aspect,
                    "weight": row["weight"],
                    "confidence": row["confidence"]
                })

            return search_results

        except Exception as e:
            logger.error(f"方面向量搜索失败: {e}")
            return []

    async def _fuse_aspect_results(self, aspect_results: Dict[str, List[Dict]],
                                 query_vectors: Dict[str, Dict]) -> List[Dict]:
        """融合多方面检索结果"""
        try:
            if not aspect_results:
                return []

            # 收集所有文档
            all_documents = {}

            for aspect, results in aspect_results.items():
                aspect_weight = query_vectors[aspect]["weight"]

                for result in results:
                    doc_id = result["document_id"]

                    if doc_id not in all_documents:
                        all_documents[doc_id] = {
                            "document_id": doc_id,
                            "content": result["content"],
                            "aspect_scores": {},
                            "total_score": 0.0,
                            "aspect_count": 0
                        }

                    # 计算方面得分
                    aspect_score = result["score"] * aspect_weight * result.get("confidence", 1.0)
                    all_documents[doc_id]["aspect_scores"][aspect] = aspect_score
                    all_documents[doc_id]["aspect_count"] += 1

            # 融合得分
            fused_results = []
            for doc_info in all_documents.values():
                if self.fusion_method == "weighted_sum":
                    total_score = sum(doc_info["aspect_scores"].values())
                elif self.fusion_method == "max_pooling":
                    total_score = max(doc_info["aspect_scores"].values()) if doc_info["aspect_scores"] else 0
                else:  # 默认加权平均
                    total_score = sum(doc_info["aspect_scores"].values()) / len(doc_info["aspect_scores"])

                # 多样性奖励
                diversity_bonus = (doc_info["aspect_count"] - 1) * self.diversity_weight
                final_score = total_score + diversity_bonus

                fused_results.append({
                    "document_id": doc_info["document_id"],
                    "content": doc_info["content"],
                    "score": final_score,
                    "aspect_scores": doc_info["aspect_scores"],
                    "aspect_count": doc_info["aspect_count"]
                })

            # 按得分排序
            fused_results.sort(key=lambda x: x["score"], reverse=True)
            return fused_results

        except Exception as e:
            logger.error(f"方面结果融合失败: {e}")
            return []

    async def _rerank_and_diversify(self, results: List[Dict], query: str) -> List[SearchResult]:
        """重排序和多样性优化"""
        try:
            if not results:
                return []

            # 转换为SearchResult格式
            search_results = []
            for result in results:
                search_result = SearchResult(
                    content=result["content"],
                    score=result["score"],
                    metadata={
                        "document_id": result["document_id"],
                        "aspect_scores": result.get("aspect_scores", {}),
                        "aspect_count": result.get("aspect_count", 1)
                    }
                )
                search_results.append(search_result)

            # 多样性优化
            diversified_results = await self._apply_diversity_optimization(search_results)

            return diversified_results

        except Exception as e:
            logger.error(f"重排序和多样性优化失败: {e}")
            return []

    async def _apply_diversity_optimization(self, results: List[SearchResult]) -> List[SearchResult]:
        """应用多样性优化"""
        try:
            if len(results) <= 1:
                return results

            # 计算结果之间的相似度
            contents = [result.content for result in results]
            embeddings = self.embedding_model.encode(contents)

            # 贪心多样性选择
            selected_results = [results[0]]  # 选择得分最高的
            selected_embeddings = [embeddings[0]]

            for i, result in enumerate(results[1:], 1):
                # 计算与已选择结果的最大相似度
                from sklearn.metrics.pairwise import cosine_similarity
                similarities = cosine_similarity([embeddings[i]], selected_embeddings)[0]
                max_similarity = max(similarities)

                # 多样性得分 = 原始得分 * (1 - 最大相似度)
                diversity_score = result.score * (1 - max_similarity)

                # 如果多样性得分足够高，则选择
                if diversity_score > 0.3:  # 阈值可调
                    selected_results.append(result)
                    selected_embeddings.append(embeddings[i])

            return selected_results

        except Exception as e:
            logger.error(f"多样性优化失败: {e}")
            return results

    def _calculate_fusion_score(self, results: List[Dict]) -> float:
        """计算融合分数"""
        try:
            if not results:
                return 0.0

            # 计算平均得分
            avg_score = sum(r.get("score", 0) for r in results) / len(results)

            # 计算方面覆盖度
            all_aspects = set()
            for result in results:
                aspect_scores = result.get("aspect_scores", {})
                all_aspects.update(aspect_scores.keys())

            aspect_coverage = len(all_aspects) / len(self.aspect_weights)

            # 融合分数 = 平均得分 * 方面覆盖度
            fusion_score = avg_score * aspect_coverage

            return min(1.0, fusion_score)

        except Exception as e:
            logger.error(f"融合分数计算失败: {e}")
            return 0.0

    def _classify_query_type(self, query: str) -> str:
        """分类查询类型"""
        try:
            query_lower = query.lower()

            if any(word in query_lower for word in ["如何", "怎么", "怎样", "方法"]):
                return "how_to"
            elif any(word in query_lower for word in ["什么", "是什么", "定义"]):
                return "definition"
            elif any(word in query_lower for word in ["为什么", "原因", "why"]):
                return "explanation"
            elif any(word in query_lower for word in ["哪里", "在哪", "位置"]):
                return "location"
            elif any(word in query_lower for word in ["时间", "什么时候", "when"]):
                return "temporal"
            else:
                return "general"

        except Exception as e:
            logger.error(f"查询类型分类失败: {e}")
            return "general"
    
    async def _analyze_query_aspects(self, query: str) -> List[str]:
        """分析查询的主要方面"""
        aspects = []
        query_lower = query.lower()
        
        # 语义方面（默认包含）
        aspects.append("semantic")
        
        # 事实性方面
        if any(word in query_lower for word in ["什么", "定义", "是", "概念"]):
            aspects.append("factual")
        
        # 时间方面
        if any(word in query_lower for word in ["时间", "什么时候", "何时", "日期", "年"]):
            aspects.append("temporal")
        
        # 实体方面
        if any(word in query_lower for word in ["谁", "哪个", "哪些", "人物", "公司"]):
            aspects.append("entity")
        
        # 程序性方面
        if any(word in query_lower for word in ["如何", "怎么", "步骤", "方法", "流程"]):
            aspects.append("procedural")
        
        return aspects
    
    async def _extract_aspect_query(self, query: str, aspect: str) -> str:
        """提取特定方面的查询"""
        if aspect == "semantic":
            return query  # 语义方面使用原始查询
        
        elif aspect == "factual":
            # 事实性查询：关注定义和概念
            return f"{query} 定义 概念 解释"
        
        elif aspect == "temporal":
            # 时间性查询：关注时间信息
            return f"{query} 时间 日期 历史"
        
        elif aspect == "entity":
            # 实体性查询：关注人物、组织等
            return f"{query} 人物 组织 实体"
        
        elif aspect == "procedural":
            # 程序性查询：关注方法和步骤
            return f"{query} 方法 步骤 流程 实现"
        
        return query
    
    async def _search_by_aspect(self, aspect: str, query_vector: List[float], 
                              top_k: int) -> List[SearchResult]:
        """按方面检索"""
        try:
            # 这里应该从专门的方面向量索引中检索
            # 简化实现：使用通用向量检索
            from .retrieval_engine import retrieval_engine
            
            results = await retrieval_engine.vector_search(
                query_vector, 
                top_k=top_k,
                collection_name=f"aspect_{aspect}"  # 方面特定的集合
            )
            
            # 转换为SearchResult格式
            search_results = []
            for result in results:
                search_result = SearchResult(
                    id=result.get("id", ""),
                    content=result.get("content", ""),
                    score=result.get("score", 0.0),
                    metadata={
                        **result.get("metadata", {}),
                        "aspect": aspect
                    },
                    source_type="multi_vector"
                )
                search_results.append(search_result)
            
            return search_results
            
        except Exception as e:
            logger.error(f"按方面检索失败: {e}")
            return []
    
    def _fuse_aspect_results(self, aspect_results: Dict[str, List[SearchResult]], 
                           query_aspects: List[str]) -> List[SearchResult]:
        """融合多方面检索结果"""
        doc_scores = {}
        
        for aspect, results in aspect_results.items():
            aspect_weight = self._get_aspect_weight(aspect, query_aspects)
            
            for result in results:
                doc_id = result.id
                if doc_id not in doc_scores:
                    doc_scores[doc_id] = {
                        "total_score": 0.0,
                        "result": result,
                        "aspect_scores": {},
                        "aspect_count": 0
                    }
                
                weighted_score = result.score * aspect_weight
                doc_scores[doc_id]["total_score"] += weighted_score
                doc_scores[doc_id]["aspect_scores"][aspect] = result.score
                doc_scores[doc_id]["aspect_count"] += 1
        
        # 多样性奖励：出现在多个方面的文档获得额外分数
        for doc_id, doc_info in doc_scores.items():
            diversity_bonus = (doc_info["aspect_count"] - 1) * 0.1
            doc_info["total_score"] += diversity_bonus
        
        # 按总分排序
        sorted_docs = sorted(
            doc_scores.values(), 
            key=lambda x: x["total_score"], 
            reverse=True
        )
        
        # 更新结果的元数据
        final_results = []
        for doc_info in sorted_docs:
            result = doc_info["result"]
            result.score = doc_info["total_score"]
            result.metadata["aspect_scores"] = doc_info["aspect_scores"]
            result.metadata["aspect_count"] = doc_info["aspect_count"]
            final_results.append(result)
        
        return final_results
    
    def _get_aspect_weight(self, aspect: str, query_aspects: List[str]) -> float:
        """获取方面权重"""
        base_weight = self.aspect_weights.get(aspect, 0.2)
        
        # 如果该方面在查询中被识别为重要，增加权重
        if aspect in query_aspects:
            return base_weight * 1.5
        
        return base_weight
    
    # 方面提取器实现
    async def _extract_semantic_aspect(self, document: str) -> str:
        """提取语义方面"""
        # 语义方面使用完整文档
        return document
    
    async def _extract_factual_aspect(self, document: str) -> str:
        """提取事实性方面"""
        # 简化实现：提取包含定义性词汇的句子
        sentences = document.split('。')
        factual_sentences = []
        
        factual_keywords = ["是", "定义为", "指的是", "概念", "含义", "表示"]
        
        for sentence in sentences:
            if any(keyword in sentence for keyword in factual_keywords):
                factual_sentences.append(sentence)
        
        return '。'.join(factual_sentences) if factual_sentences else document[:200]
    
    async def _extract_temporal_aspect(self, document: str) -> str:
        """提取时间方面"""
        # 简化实现：提取包含时间词汇的句子
        sentences = document.split('。')
        temporal_sentences = []
        
        temporal_keywords = ["年", "月", "日", "时间", "历史", "发展", "演变", "过程"]
        
        for sentence in sentences:
            if any(keyword in sentence for keyword in temporal_keywords):
                temporal_sentences.append(sentence)
        
        return '。'.join(temporal_sentences) if temporal_sentences else ""
    
    async def _extract_entity_aspect(self, document: str) -> str:
        """提取实体方面"""
        # 简化实现：提取可能包含实体的句子
        sentences = document.split('。')
        entity_sentences = []
        
        # 简单的实体识别（实际应该使用NER）
        for sentence in sentences:
            # 检查是否包含大写字母开头的词（可能是实体）
            words = sentence.split()
            has_entity = any(word[0].isupper() for word in words if len(word) > 1)
            
            if has_entity:
                entity_sentences.append(sentence)
        
        return '。'.join(entity_sentences) if entity_sentences else ""
    
    async def _extract_procedural_aspect(self, document: str) -> str:
        """提取程序性方面"""
        # 简化实现：提取包含步骤和方法的句子
        sentences = document.split('。')
        procedural_sentences = []
        
        procedural_keywords = ["步骤", "方法", "流程", "过程", "实现", "操作", "执行"]
        
        for sentence in sentences:
            if any(keyword in sentence for keyword in procedural_keywords):
                procedural_sentences.append(sentence)
        
        return '。'.join(procedural_sentences) if procedural_sentences else ""


# 创建全局实例
hard_negative_miner = HardNegativeMiner()
multi_vector_retriever = MultiVectorRetriever()
