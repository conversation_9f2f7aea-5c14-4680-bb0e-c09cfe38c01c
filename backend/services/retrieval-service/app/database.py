"""
数据库连接和操作模块
"""

import asyncio
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, Integer, DateTime, Text, JSON, Float, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from loguru import logger

from .config import settings

# 创建基础模型类
Base = declarative_base()

# 全局变量
engine = None
async_session = None


class SearchQuery(Base):
    """搜索查询记录表"""
    __tablename__ = "search_queries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_text = Column(Text, nullable=False)
    query_hash = Column(String(64), nullable=False, index=True)
    query_type = Column(String(20), nullable=False)  # semantic, keyword, hybrid
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    session_id = Column(String(100), nullable=True, index=True)
    
    # 查询参数
    top_k = Column(Integer, default=10)
    similarity_threshold = Column(Float, default=0.7)
    filters = Column(JSON, default={})
    
    # 结果统计
    results_count = Column(Integer, default=0)
    response_time_ms = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SearchQuery(id={self.id}, query_text='{self.query_text[:50]}...')>"


class SearchResult(Base):
    """搜索结果记录表"""
    __tablename__ = "search_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 结果信息
    document_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    chunk_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    rank = Column(Integer, nullable=False)
    score = Column(Float, nullable=False)
    
    # 内容信息
    content = Column(Text, nullable=False)
    metadata = Column(JSON, default={})
    
    # 检索来源
    source_type = Column(String(20), nullable=False)  # semantic, keyword, hybrid
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<SearchResult(id={self.id}, rank={self.rank}, score={self.score})>"


class QueryAnalytics(Base):
    """查询分析统计表"""
    __tablename__ = "query_analytics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 时间维度
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    hour = Column(Integer, nullable=False, index=True)
    
    # 统计指标
    total_queries = Column(Integer, default=0)
    semantic_queries = Column(Integer, default=0)
    keyword_queries = Column(Integer, default=0)
    hybrid_queries = Column(Integer, default=0)
    
    # 性能指标
    avg_response_time = Column(Float, default=0.0)
    avg_results_count = Column(Float, default=0.0)
    
    # 质量指标
    zero_results_count = Column(Integer, default=0)
    high_score_results_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<QueryAnalytics(date={self.date}, total_queries={self.total_queries})>"


class DocumentIndex(Base):
    """文档索引表"""
    __tablename__ = "document_index"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)
    
    # 文档信息
    title = Column(String(500), nullable=True)
    content_hash = Column(String(64), nullable=False, index=True)
    
    # 索引状态
    is_indexed = Column(Boolean, default=False, index=True)
    index_version = Column(String(20), default="1.0")
    
    # 统计信息
    chunks_count = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # 元数据
    metadata = Column(JSON, default={})
    
    # 时间戳
    indexed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<DocumentIndex(document_id={self.document_id}, is_indexed={self.is_indexed})>"


async def init_db():
    """初始化数据库连接"""
    global engine, async_session
    
    try:
        # 创建异步引擎
        engine = create_async_engine(
            settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
            echo=settings.DEBUG,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # 创建会话工厂
        async_session = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化成功")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_db():
    """关闭数据库连接"""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("数据库连接已关闭")


async def get_db_session() -> AsyncSession:
    """获取数据库会话"""
    if not async_session:
        raise RuntimeError("数据库未初始化")
    
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()


# 数据访问对象
class SearchQueryDAO:
    """搜索查询数据访问对象"""
    
    @staticmethod
    async def create(session: AsyncSession, **kwargs) -> SearchQuery:
        """创建搜索查询记录"""
        query = SearchQuery(**kwargs)
        session.add(query)
        await session.commit()
        await session.refresh(query)
        return query
    
    @staticmethod
    async def get_by_id(session: AsyncSession, query_id: uuid.UUID) -> Optional[SearchQuery]:
        """根据ID获取查询记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(SearchQuery).where(SearchQuery.id == query_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_recent_queries(
        session: AsyncSession, 
        user_id: Optional[uuid.UUID] = None,
        limit: int = 50
    ) -> List[SearchQuery]:
        """获取最近的查询记录"""
        from sqlalchemy import select
        query = select(SearchQuery).order_by(SearchQuery.created_at.desc()).limit(limit)
        
        if user_id:
            query = query.where(SearchQuery.user_id == user_id)
        
        result = await session.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_popular_queries(
        session: AsyncSession,
        days: int = 7,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取热门查询"""
        from sqlalchemy import select, func
        from datetime import datetime, timedelta
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        result = await session.execute(
            select(
                SearchQuery.query_text,
                func.count(SearchQuery.id).label('count'),
                func.avg(SearchQuery.response_time_ms).label('avg_response_time'),
                func.avg(SearchQuery.results_count).label('avg_results_count')
            )
            .where(SearchQuery.created_at >= start_date)
            .group_by(SearchQuery.query_text)
            .order_by(func.count(SearchQuery.id).desc())
            .limit(limit)
        )
        
        return [
            {
                "query_text": row.query_text,
                "count": row.count,
                "avg_response_time": float(row.avg_response_time or 0),
                "avg_results_count": float(row.avg_results_count or 0)
            }
            for row in result
        ]


class SearchResultDAO:
    """搜索结果数据访问对象"""
    
    @staticmethod
    async def create_batch(session: AsyncSession, results: List[Dict[str, Any]]) -> List[SearchResult]:
        """批量创建搜索结果记录"""
        result_objects = [SearchResult(**result_data) for result_data in results]
        session.add_all(result_objects)
        await session.commit()
        
        for obj in result_objects:
            await session.refresh(obj)
        
        return result_objects
    
    @staticmethod
    async def get_by_query_id(session: AsyncSession, query_id: uuid.UUID) -> List[SearchResult]:
        """根据查询ID获取结果"""
        from sqlalchemy import select
        result = await session.execute(
            select(SearchResult)
            .where(SearchResult.query_id == query_id)
            .order_by(SearchResult.rank)
        )
        return result.scalars().all()


class DocumentIndexDAO:
    """文档索引数据访问对象"""
    
    @staticmethod
    async def create_or_update(session: AsyncSession, **kwargs) -> DocumentIndex:
        """创建或更新文档索引"""
        from sqlalchemy import select
        
        document_id = kwargs.get('document_id')
        result = await session.execute(
            select(DocumentIndex).where(DocumentIndex.document_id == document_id)
        )
        doc_index = result.scalar_one_or_none()
        
        if doc_index:
            # 更新现有记录
            for key, value in kwargs.items():
                setattr(doc_index, key, value)
        else:
            # 创建新记录
            doc_index = DocumentIndex(**kwargs)
            session.add(doc_index)
        
        await session.commit()
        await session.refresh(doc_index)
        return doc_index
    
    @staticmethod
    async def get_by_document_id(session: AsyncSession, document_id: uuid.UUID) -> Optional[DocumentIndex]:
        """根据文档ID获取索引记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(DocumentIndex).where(DocumentIndex.document_id == document_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_unindexed_documents(session: AsyncSession, limit: int = 100) -> List[DocumentIndex]:
        """获取未索引的文档"""
        from sqlalchemy import select
        result = await session.execute(
            select(DocumentIndex)
            .where(DocumentIndex.is_indexed == False)
            .limit(limit)
        )
        return result.scalars().all()
    
    @staticmethod
    async def mark_as_indexed(session: AsyncSession, document_id: uuid.UUID) -> bool:
        """标记文档为已索引"""
        from sqlalchemy import select
        result = await session.execute(
            select(DocumentIndex).where(DocumentIndex.document_id == document_id)
        )
        doc_index = result.scalar_one_or_none()
        
        if doc_index:
            doc_index.is_indexed = True
            doc_index.indexed_at = datetime.utcnow()
            await session.commit()
            return True
        
        return False


class QueryAnalyticsDAO:
    """查询分析数据访问对象"""
    
    @staticmethod
    async def update_analytics(
        session: AsyncSession,
        date: datetime,
        hour: int,
        query_type: str,
        response_time: int,
        results_count: int
    ):
        """更新查询分析统计"""
        from sqlalchemy import select
        
        # 查找现有记录
        result = await session.execute(
            select(QueryAnalytics)
            .where(QueryAnalytics.date == date.date())
            .where(QueryAnalytics.hour == hour)
        )
        analytics = result.scalar_one_or_none()
        
        if not analytics:
            # 创建新记录
            analytics = QueryAnalytics(
                date=date.date(),
                hour=hour,
                total_queries=0,
                semantic_queries=0,
                keyword_queries=0,
                hybrid_queries=0,
                avg_response_time=0.0,
                avg_results_count=0.0,
                zero_results_count=0,
                high_score_results_count=0
            )
            session.add(analytics)
        
        # 更新统计
        analytics.total_queries += 1
        
        if query_type == "semantic":
            analytics.semantic_queries += 1
        elif query_type == "keyword":
            analytics.keyword_queries += 1
        elif query_type == "hybrid":
            analytics.hybrid_queries += 1
        
        # 更新平均值
        total = analytics.total_queries
        analytics.avg_response_time = (
            (analytics.avg_response_time * (total - 1) + response_time) / total
        )
        analytics.avg_results_count = (
            (analytics.avg_results_count * (total - 1) + results_count) / total
        )
        
        # 更新质量指标
        if results_count == 0:
            analytics.zero_results_count += 1
        
        await session.commit()
        return analytics
