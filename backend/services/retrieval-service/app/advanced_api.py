"""
先进检索技术API接口
提供Self-RAG、CoT检索、多向量检索等先进技术的API端点
"""

from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .advanced_retrieval_techniques import self_rag_retriever, cot_retriever
from .negative_mining_and_multivector import hard_negative_miner, multi_vector_retriever
from .chinese_optimization import chinese_segmentation_optimizer, chinese_semantic_role_labeler
from .temporal_scorer import temporal_scorer
from .result_fusion_optimizer import result_fusion_optimizer
from .aspect_vector_indexer import aspect_vector_indexer
from .ab_testing_framework import ab_testing_framework
from .enhanced_monitoring import enhanced_monitoring
from .database_manager import database_manager
from .enhanced_retrieval import SearchResult


# API路由器
router = APIRouter(prefix="/advanced", tags=["advanced-retrieval"])


# 请求模型
class SelfRAGRequest(BaseModel):
    """Self-RAG请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    max_iterations: int = Field(3, description="最大迭代次数", ge=1, le=5)
    confidence_threshold: float = Field(0.8, description="置信度阈值", ge=0.1, le=1.0)


class CoTRequest(BaseModel):
    """Chain-of-Thought请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    max_steps: int = Field(3, description="最大推理步骤", ge=1, le=5)
    domain: Optional[str] = Field(None, description="领域信息")


class MultiVectorRequest(BaseModel):
    """多向量检索请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    aspects: Optional[List[str]] = Field(None, description="指定检索方面")
    aspect_weights: Optional[Dict[str, float]] = Field(None, description="方面权重")


class ChineseOptimizationRequest(BaseModel):
    """中文优化请求"""
    text: str = Field(..., description="待处理文本", min_length=1, max_length=5000)
    context: Optional[str] = Field(None, description="上下文信息")
    optimization_type: str = Field(default="segmentation", description="优化类型")


class AdvancedSearchRequest(BaseModel):
    """高级搜索请求"""
    query: str = Field(..., description="搜索查询")
    search_methods: List[str] = Field(
        default=["self_rag", "multi_vector", "semantic", "temporal"],
        description="使用的搜索方法"
    )
    fusion_method: str = Field(default="adaptive", description="结果融合方法")
    top_k: int = Field(default=10, description="返回结果数量")
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    enable_chinese_optimization: bool = Field(True, description="启用中文优化")
    enable_temporal_scoring: bool = Field(True, description="启用时效性评分")


class TemporalScoringRequest(BaseModel):
    """时效性评分请求"""
    content: str = Field(..., description="文档内容")
    query: str = Field(..., description="查询文本")
    metadata: Optional[Dict[str, Any]] = Field(None, description="文档元数据")


class IndexBuildRequest(BaseModel):
    """索引构建请求"""
    rebuild: bool = Field(False, description="是否重建索引")
    batch_size: int = Field(100, description="批处理大小")
    optimization_type: str = Field("segmentation", description="优化类型", regex="^(segmentation|srl|both)$")


class HardNegativeMiningRequest(BaseModel):
    """困难负样本挖掘请求"""
    query: str = Field(..., description="查询文本", min_length=1, max_length=1000)
    positive_docs: List[str] = Field(..., description="正样本文档列表")
    max_negatives: int = Field(10, description="最大负样本数", ge=1, le=50)
    collection_name: Optional[str] = Field(None, description="集合名称")


# 响应模型
class SelfRAGResponse(BaseModel):
    """Self-RAG响应"""
    query: str
    iterations: int
    final_confidence: float
    processing_time: float
    results: List[Dict[str, Any]]
    critique_history: List[Dict[str, Any]]


class CoTResponse(BaseModel):
    """CoT响应"""
    query: str
    reasoning_steps: List[Dict[str, Any]]
    processing_time: float
    results: List[Dict[str, Any]]


class MultiVectorResponse(BaseModel):
    """多向量检索响应"""
    query: str
    detected_aspects: List[str]
    aspect_results: Dict[str, List[Dict[str, Any]]]
    fused_results: List[Dict[str, Any]]
    processing_time: float


class ChineseOptimizationResponse(BaseModel):
    """中文优化响应"""
    original_text: str
    optimization_type: str
    segmentation_result: Optional[Dict[str, Any]] = None
    srl_result: Optional[Dict[str, Any]] = None
    enhanced_query: Optional[str] = None
    processing_time: float


# API端点实现

@router.post("/self-rag", response_model=SelfRAGResponse)
async def self_rag_search(request: SelfRAGRequest):
    """
    Self-RAG自我反思检索
    通过迭代自我评估和查询精化提升检索质量
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到Self-RAG请求: {request.query[:50]}...")
        
        # 设置参数
        self_rag_retriever.max_iterations = request.max_iterations
        self_rag_retriever.confidence_threshold = request.confidence_threshold
        
        # 执行Self-RAG检索
        results = await self_rag_retriever.self_rag_search(
            request.query, 
            request.top_k
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 转换结果格式
        result_dicts = []
        for r in results:
            result_dict = {
                "id": r.id,
                "content": r.content,
                "score": r.score,
                "metadata": r.metadata,
                "source_type": r.source_type
            }
            result_dicts.append(result_dict)
        
        response = SelfRAGResponse(
            query=request.query,
            iterations=getattr(self_rag_retriever, 'last_iterations', 1),
            final_confidence=getattr(self_rag_retriever, 'last_confidence', 0.8),
            processing_time=processing_time,
            results=result_dicts,
            critique_history=getattr(self_rag_retriever, 'critique_history', [])
        )
        
        logger.info(f"Self-RAG检索完成，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"Self-RAG检索失败: {e}")
        raise HTTPException(status_code=500, detail=f"Self-RAG检索失败: {str(e)}")


@router.post("/chain-of-thought", response_model=CoTResponse)
async def cot_search(request: CoTRequest):
    """
    Chain-of-Thought思维链检索
    通过分步推理提升复杂查询的处理能力
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到CoT请求: {request.query[:50]}...")
        
        # 设置参数
        cot_retriever.max_steps = request.max_steps
        
        # 执行CoT检索
        results = await cot_retriever.cot_retrieval(
            request.query, 
            request.top_k
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 获取推理步骤
        reasoning_steps = getattr(cot_retriever, 'last_reasoning_steps', [])
        steps_data = []
        for step in reasoning_steps:
            steps_data.append({
                "step_id": step.step_id,
                "query": step.query,
                "reasoning": step.reasoning,
                "confidence": step.confidence
            })
        
        # 转换结果格式
        result_dicts = []
        for r in results:
            result_dict = {
                "id": r.id,
                "content": r.content,
                "score": r.score,
                "metadata": r.metadata,
                "source_type": r.source_type
            }
            result_dicts.append(result_dict)
        
        response = CoTResponse(
            query=request.query,
            reasoning_steps=steps_data,
            processing_time=processing_time,
            results=result_dicts
        )
        
        logger.info(f"CoT检索完成，{len(steps_data)}个推理步骤，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"CoT检索失败: {e}")
        raise HTTPException(status_code=500, detail=f"CoT检索失败: {str(e)}")


@router.post("/multi-vector", response_model=MultiVectorResponse)
async def multi_vector_search(request: MultiVectorRequest):
    """
    多向量检索
    从多个语义方面进行检索并融合结果
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到多向量检索请求: {request.query[:50]}...")
        
        # 设置方面权重
        if request.aspect_weights:
            multi_vector_retriever.aspect_weights.update(request.aspect_weights)
        
        # 执行多向量检索
        results = await multi_vector_retriever.multi_vector_search(
            request.query, 
            request.top_k
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 获取检测到的方面
        detected_aspects = await multi_vector_retriever._analyze_query_aspects(request.query)
        
        # 转换结果格式
        result_dicts = []
        aspect_results = {}
        
        for r in results:
            result_dict = {
                "id": r.id,
                "content": r.content,
                "score": r.score,
                "metadata": r.metadata,
                "source_type": r.source_type
            }
            result_dicts.append(result_dict)
            
            # 按方面组织结果
            aspect = r.metadata.get("aspect", "unknown")
            if aspect not in aspect_results:
                aspect_results[aspect] = []
            aspect_results[aspect].append(result_dict)
        
        response = MultiVectorResponse(
            query=request.query,
            detected_aspects=detected_aspects,
            aspect_results=aspect_results,
            fused_results=result_dicts,
            processing_time=processing_time
        )
        
        logger.info(f"多向量检索完成，{len(detected_aspects)}个方面，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"多向量检索失败: {e}")
        raise HTTPException(status_code=500, detail=f"多向量检索失败: {str(e)}")


@router.post("/chinese-optimization", response_model=ChineseOptimizationResponse)
async def chinese_optimization(request: ChineseOptimizationRequest):
    """
    中文文本优化
    包含分词优化和语义角色标注
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到中文优化请求: {request.text[:50]}...")
        
        segmentation_result = None
        srl_result = None
        enhanced_query = None
        
        # 分词优化
        if request.optimization_type in ["segmentation", "both"]:
            seg_result = await chinese_segmenter.optimized_segmentation(
                request.text, 
                request.context or ""
            )
            
            segmentation_result = {
                "words": seg_result.words,
                "pos_tags": seg_result.pos_tags,
                "confidence": seg_result.confidence,
                "method": seg_result.method
            }
        
        # 语义角色标注
        if request.optimization_type in ["srl", "both"]:
            srl_info = await chinese_srl.analyze_semantic_roles(request.text)
            
            srl_result = {
                "predicate": srl_info["predicate"],
                "roles": {
                    role: {
                        "content": role_obj.content,
                        "weight": role_obj.weight
                    } for role, role_obj in srl_info["roles"].items()
                },
                "semantic_focus": srl_info["semantic_focus"],
                "query_intent": srl_info["query_intent"],
                "confidence": srl_info["confidence"]
            }
            
            # 生成增强查询
            enhanced_query = await chinese_srl.enhance_query_with_srl(request.text)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = ChineseOptimizationResponse(
            original_text=request.text,
            optimization_type=request.optimization_type,
            segmentation_result=segmentation_result,
            srl_result=srl_result,
            enhanced_query=enhanced_query,
            processing_time=processing_time
        )
        
        logger.info(f"中文优化完成，类型: {request.optimization_type}，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"中文优化失败: {e}")
        raise HTTPException(status_code=500, detail=f"中文优化失败: {str(e)}")


@router.post("/hard-negative-mining")
async def mine_hard_negatives(request: HardNegativeMiningRequest, 
                             background_tasks: BackgroundTasks):
    """
    困难负样本挖掘
    为对比学习挖掘高质量的负样本
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"收到困难负样本挖掘请求: {request.query[:50]}...")
        
        # 设置参数
        hard_negative_miner.max_hard_negatives = request.max_negatives
        
        # 执行困难负样本挖掘
        hard_negatives = await hard_negative_miner.mine_hard_negatives(
            request.query,
            request.positive_docs,
            request.collection_name
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 后台任务：创建对比学习数据
        if hard_negatives:
            background_tasks.add_task(
                create_contrastive_training_data,
                request.query,
                request.positive_docs,
                hard_negatives
            )
        
        response = {
            "query": request.query,
            "positive_docs_count": len(request.positive_docs),
            "hard_negatives": hard_negatives,
            "hard_negatives_count": len(hard_negatives),
            "processing_time": processing_time,
            "status": "completed"
        }
        
        logger.info(f"困难负样本挖掘完成，挖掘到 {len(hard_negatives)} 个负样本，耗时 {processing_time:.3f}s")
        return response
        
    except Exception as e:
        logger.error(f"困难负样本挖掘失败: {e}")
        raise HTTPException(status_code=500, detail=f"困难负样本挖掘失败: {str(e)}")


@router.get("/techniques/status")
async def get_techniques_status():
    """
    获取先进技术状态
    """
    try:
        status = {
            "timestamp": datetime.now().isoformat(),
            "techniques": {
                "self_rag": {
                    "enabled": True,
                    "max_iterations": getattr(self_rag_retriever, 'max_iterations', 3),
                    "confidence_threshold": getattr(self_rag_retriever, 'confidence_threshold', 0.8)
                },
                "chain_of_thought": {
                    "enabled": True,
                    "max_steps": getattr(cot_retriever, 'max_steps', 3),
                    "llm_available": cot_retriever.llm_client is not None
                },
                "multi_vector": {
                    "enabled": True,
                    "aspects": list(multi_vector_retriever.aspect_extractors.keys()),
                    "aspect_weights": multi_vector_retriever.aspect_weights
                },
                "chinese_optimization": {
                    "enabled": True,
                    "segmenters": list(chinese_segmenter.segmenters.keys()),
                    "ensemble_weights": chinese_segmenter.ensemble_weights
                },
                "hard_negative_mining": {
                    "enabled": True,
                    "similarity_thresholds": {
                        "low": hard_negative_miner.similarity_threshold_low,
                        "high": hard_negative_miner.similarity_threshold_high
                    }
                }
            }
        }
        
        return status
        
    except Exception as e:
        logger.error(f"获取技术状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取技术状态失败: {str(e)}")


# 后台任务
async def create_contrastive_training_data(query: str, positive_docs: List[str], 
                                         hard_negatives: List[str]):
    """创建对比学习训练数据的后台任务"""
    try:
        logger.info(f"开始创建对比学习数据: {query[:50]}...")
        
        # 这里可以将对比学习数据保存到数据库或文件
        # 用于后续的模型训练
        
        training_data = []
        for pos_doc in positive_docs:
            for neg_doc in hard_negatives:
                training_data.append({
                    "query": query,
                    "positive": pos_doc,
                    "negative": neg_doc,
                    "timestamp": datetime.now().isoformat()
                })
        
        # 保存训练数据（这里是示例，实际应该保存到数据库）
        logger.info(f"创建了 {len(training_data)} 个对比学习样本")
        
    except Exception as e:
        logger.error(f"创建对比学习数据失败: {e}")


@router.get("/ab-testing/experiments")
async def get_active_experiments():
    """
    获取活跃的A/B测试实验列表
    """
    try:
        from .ab_testing_framework import ab_testing_framework

        experiments = await ab_testing_framework.get_active_experiments()

        return {
            "experiments": experiments,
            "count": len(experiments),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取实验列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实验列表失败: {str(e)}")


@router.get("/ab-testing/experiments/{experiment_name}/results")
async def get_experiment_results(
    experiment_name: str,
    days: int = Query(7, description="分析天数", ge=1, le=30)
):
    """
    获取A/B测试实验结果分析
    """
    try:
        from .ab_testing_framework import ab_testing_framework

        logger.info(f"获取实验 {experiment_name} 的结果分析，时间范围: {days}天")

        results = await ab_testing_framework.get_experiment_results(experiment_name, days)

        if "error" in results:
            raise HTTPException(status_code=404, detail=results["error"])

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实验结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取实验结果失败: {str(e)}")


@router.post("/ab-testing/assign/{experiment_name}")
async def assign_user_to_experiment(
    experiment_name: str,
    user_id: str = Query(..., description="用户ID")
):
    """
    为用户分配A/B测试实验组
    """
    try:
        from .ab_testing_framework import ab_testing_framework

        group_name = await ab_testing_framework.assign_user_to_experiment(user_id, experiment_name)

        return {
            "experiment_name": experiment_name,
            "user_id": user_id,
            "assigned_group": group_name,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"用户分组失败: {e}")
        raise HTTPException(status_code=500, detail=f"用户分组失败: {str(e)}")


@router.post("/ab-testing/record-result")
async def record_experiment_result(
    experiment_name: str,
    user_id: str,
    query: str,
    search_type: str,
    response_time_ms: int,
    result_count: int,
    user_feedback: Optional[int] = None,
    click_positions: Optional[List[int]] = None
):
    """
    记录A/B测试实验结果
    """
    try:
        from .ab_testing_framework import ab_testing_framework, ExperimentResult

        # 获取用户分组
        group_name = await ab_testing_framework.assign_user_to_experiment(user_id, experiment_name)

        # 创建实验结果
        result = ExperimentResult(
            experiment_id=experiment_name,
            user_id=user_id,
            group_name=group_name,
            query=query,
            search_type=search_type,
            response_time_ms=response_time_ms,
            result_count=result_count,
            user_feedback=user_feedback,
            click_positions=click_positions or []
        )

        # 记录结果
        success = await ab_testing_framework.record_experiment_result(result)

        if not success:
            raise HTTPException(status_code=500, detail="记录实验结果失败")

        return {
            "status": "success",
            "experiment_name": experiment_name,
            "user_group": group_name,
            "timestamp": datetime.now().isoformat()
        }


@router.post("/search/advanced")
async def advanced_search(request: AdvancedSearchRequest):
    """
    高级搜索接口
    整合多种先进检索技术
    """
    try:
        start_time = datetime.now()
        logger.info(f"开始高级搜索: {request.query[:50]}...")

        # 1. 中文优化预处理
        optimized_query = request.query
        if request.enable_chinese_optimization:
            segmentation_result = await chinese_segmentation_optimizer.optimize_segmentation(
                request.query
            )
            if segmentation_result.confidence > 0.7:
                optimized_query = " ".join(segmentation_result.words)

        # 2. 执行多种检索方法
        search_results = {}

        if "self_rag" in request.search_methods:
            self_rag_results = await self_rag_retriever.self_rag_search(
                optimized_query, top_k=request.top_k
            )
            search_results["self_rag_search"] = [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in self_rag_results
            ]

        if "multi_vector" in request.search_methods:
            multi_vector_results = await multi_vector_retriever.multi_vector_search(
                optimized_query, top_k=request.top_k
            )
            search_results["multi_vector_search"] = [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in multi_vector_results
            ]

        if "semantic" in request.search_methods:
            # 基础语义搜索
            from .enhanced_retrieval import enhanced_retrieval_engine
            semantic_results = await enhanced_retrieval_engine.enhanced_search(
                optimized_query, top_k=request.top_k
            )
            search_results["semantic_search"] = [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in semantic_results
            ]

        if "temporal" in request.search_methods and request.enable_temporal_scoring:
            # 时效性感知搜索
            from .enhanced_retrieval import enhanced_retrieval_engine
            temporal_results = await enhanced_retrieval_engine.enhanced_search(
                optimized_query, top_k=request.top_k
            )
            # 为结果添加时效性评分
            for result in temporal_results:
                temporal_score = await temporal_scorer.calculate_temporal_score(
                    result.content, optimized_query, result.metadata
                )
                result.score = result.score * temporal_score.final_score

            search_results["temporal_aware_search"] = [
                {"content": r.content, "score": r.score, "metadata": r.metadata}
                for r in temporal_results
            ]

        # 3. 结果融合
        if len(search_results) > 1:
            fused_results = await result_fusion_optimizer.optimize_fusion(
                search_results, optimized_query, {"user_id": request.user_id}
            )
            final_results = [
                {
                    "content": r.content,
                    "score": r.final_score,
                    "fusion_method": r.fusion_method,
                    "component_scores": r.component_scores,
                    "confidence": r.confidence,
                    "metadata": r.metadata
                }
                for r in fused_results[:request.top_k]
            ]
        else:
            # 单一搜索方法，直接返回结果
            method_name = list(search_results.keys())[0]
            method_results = search_results[method_name]
            final_results = method_results[:request.top_k]

        # 4. 计算响应时间和置信度
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        avg_confidence = sum(r.get("confidence", 0.8) for r in final_results) / len(final_results) if final_results else 0

        # 5. 记录查询历史
        if request.user_id:
            from .database_client import database_client
            await database_client.execute("""
                INSERT INTO query_history
                (user_id, original_query, refined_query, search_type, response_time_ms, result_count, session_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
            request.user_id, request.query, optimized_query,
            "advanced_search", response_time, len(final_results), request.session_id
            )

        # 6. 记录监控指标
        enhanced_monitoring.record_search_metrics(
            response_time, len(final_results), "advanced_search"
        )

        return {
            "results": final_results,
            "fusion_method": request.fusion_method,
            "search_methods_used": list(search_results.keys()),
            "total_results": len(final_results),
            "response_time_ms": response_time,
            "confidence": avg_confidence,
            "metadata": {
                "optimized_query": optimized_query,
                "original_query": request.query,
                "search_methods_count": len(search_results)
            }
        }

    except Exception as e:
        logger.error(f"高级搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.post("/temporal/score")
async def temporal_scoring(request: TemporalScoringRequest):
    """
    时效性评分接口
    计算文档的时效性相关性分数
    """
    try:
        logger.info(f"开始时效性评分: {request.content[:50]}...")

        temporal_score = await temporal_scorer.calculate_temporal_score(
            request.content, request.query, request.metadata
        )

        return {
            "freshness_score": temporal_score.freshness_score,
            "relevance_score": temporal_score.relevance_score,
            "decay_score": temporal_score.decay_score,
            "final_score": temporal_score.final_score,
            "temporal_features": {
                "creation_time": temporal_score.features.creation_time.isoformat() if temporal_score.features.creation_time else None,
                "update_time": temporal_score.features.update_time.isoformat() if temporal_score.features.update_time else None,
                "mentioned_dates": [d.isoformat() for d in temporal_score.features.mentioned_dates],
                "temporal_keywords": temporal_score.features.temporal_keywords
            }
        }

    except Exception as e:
        logger.error(f"时效性评分失败: {e}")
        raise HTTPException(status_code=500, detail=f"评分失败: {str(e)}")


@router.post("/index/build")
async def build_aspect_index(request: IndexBuildRequest, background_tasks: BackgroundTasks):
    """
    构建方面向量索引接口
    在后台异步构建索引
    """
    try:
        logger.info("开始构建方面向量索引...")

        # 在后台任务中执行索引构建
        def build_index_task():
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                stats = loop.run_until_complete(
                    aspect_vector_indexer.build_aspect_index(rebuild=request.rebuild)
                )
                logger.info(f"索引构建完成: {stats}")
            except Exception as e:
                logger.error(f"后台索引构建失败: {e}")
            finally:
                loop.close()

        background_tasks.add_task(build_index_task)

        return {
            "success": True,
            "message": "索引构建任务已启动",
            "rebuild": request.rebuild,
            "batch_size": request.batch_size
        }

    except Exception as e:
        logger.error(f"索引构建请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"索引构建失败: {str(e)}")


@router.get("/monitoring/metrics")
async def get_monitoring_metrics():
    """
    获取监控指标接口
    """
    try:
        metrics = await enhanced_monitoring.get_metrics_summary()
        return metrics

    except Exception as e:
        logger.error(f"获取监控指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/database/health")
async def get_database_health():
    """
    获取数据库健康状态接口
    """
    try:
        health = await database_manager.get_database_health()
        return health

    except Exception as e:
        logger.error(f"获取数据库健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@router.post("/database/migrate")
async def run_database_migrations():
    """
    运行数据库迁移接口
    """
    try:
        success = await database_manager.run_migrations()

        if success:
            return {"success": True, "message": "数据库迁移完成"}
        else:
            raise HTTPException(status_code=500, detail="数据库迁移失败")

    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        raise HTTPException(status_code=500, detail=f"迁移失败: {str(e)}")


@router.post("/self-rag/optimize")
async def optimize_self_rag():
    """
    优化Self-RAG评估机制接口
    """
    try:
        optimization_result = await self_rag_retriever.optimize_evaluation_mechanism()
        return optimization_result

    except Exception as e:
        logger.error(f"Self-RAG优化失败: {e}")
        raise HTTPException(status_code=500, detail=f"优化失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "self_rag": "active",
            "multi_vector": "active",
            "chinese_optimization": "active",
            "temporal_scoring": "active",
            "result_fusion": "active",
            "ab_testing": "active",
            "monitoring": "active"
        }
    }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"记录实验结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"记录实验结果失败: {str(e)}")


# 将路由器添加到主应用
def include_advanced_routes(app):
    """将先进检索路由添加到主应用"""
    app.include_router(router)
