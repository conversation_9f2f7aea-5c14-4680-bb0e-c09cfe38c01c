"""
高级重排序模块
实现基于交叉编码器、用户行为、多因子评分的重排序机制
"""

import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from loguru import logger

from .config import settings, get_retrieval_config
from .redis_client import retrieval_cache


@dataclass
class RerankingResult:
    """重排序结果"""
    id: str
    content: str
    original_score: float
    rerank_score: float
    factors: Dict[str, float]  # 各因子得分
    metadata: Dict[str, Any]


class CrossEncoderReranker:
    """交叉编码器重排序器"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"
        self.max_length = 512
        self.is_loaded = False
    
    async def load_model(self):
        """加载交叉编码器模型"""
        try:
            if self.is_loaded:
                return True
            
            # 在线程池中加载模型，避免阻塞
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._load_model_sync)
            
            self.is_loaded = True
            logger.info(f"交叉编码器模型 {self.model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"交叉编码器模型加载失败: {e}")
            return False
    
    def _load_model_sync(self):
        """同步加载模型"""
        try:
            from transformers import AutoTokenizer, AutoModelForSequenceClassification
            import torch
            
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            # 设置为评估模式
            self.model.eval()
            
        except ImportError:
            logger.error("transformers库未安装，无法使用交叉编码器")
            raise
    
    async def score_pairs(self, query: str, documents: List[str]) -> List[float]:
        """
        计算查询-文档对的相关性分数
        
        Args:
            query: 查询文本
            documents: 文档列表
            
        Returns:
            相关性分数列表
        """
        try:
            if not self.is_loaded:
                await self.load_model()
            
            if not self.is_loaded:
                # 模型加载失败，返回默认分数
                return [0.5] * len(documents)
            
            # 在线程池中执行推理
            loop = asyncio.get_event_loop()
            scores = await loop.run_in_executor(
                None, 
                self._score_pairs_sync, 
                query, 
                documents
            )
            
            return scores
            
        except Exception as e:
            logger.error(f"交叉编码器评分失败: {e}")
            return [0.5] * len(documents)
    
    def _score_pairs_sync(self, query: str, documents: List[str]) -> List[float]:
        """同步计算相关性分数"""
        try:
            import torch
            
            scores = []
            
            for doc in documents:
                # 构建输入对
                inputs = self.tokenizer(
                    query, 
                    doc, 
                    truncation=True, 
                    max_length=self.max_length,
                    return_tensors="pt"
                )
                
                # 推理
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs.logits
                    
                    # 使用sigmoid将logits转换为0-1分数
                    score = torch.sigmoid(logits).item()
                    scores.append(score)
            
            return scores
            
        except Exception as e:
            logger.error(f"同步评分失败: {e}")
            return [0.5] * len(documents)


class UserBehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self):
        self.behavior_cache_ttl = 86400  # 24小时
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """获取用户偏好"""
        try:
            cache_key = f"user_behavior:{user_id}"
            cached_prefs = await retrieval_cache.get(cache_key)
            
            if cached_prefs:
                return cached_prefs
            
            # 从数据库获取用户行为数据
            prefs = await self._analyze_user_behavior(user_id)
            
            # 缓存结果
            await retrieval_cache.set(cache_key, prefs, ttl=self.behavior_cache_ttl)
            
            return prefs
            
        except Exception as e:
            logger.error(f"获取用户偏好失败: {e}")
            return self._get_default_preferences()
    
    async def _analyze_user_behavior(self, user_id: str) -> Dict[str, Any]:
        """分析用户行为模式"""
        # 这里应该从数据库查询用户的历史行为
        # 简化实现，返回默认偏好
        return {
            "preferred_content_types": ["technical", "tutorial"],
            "avg_click_position": 3.2,
            "preferred_content_length": "medium",
            "topic_interests": ["programming", "system_design"],
            "interaction_patterns": {
                "click_through_rate": 0.65,
                "avg_session_length": 15.5,
                "bounce_rate": 0.25
            }
        }
    
    def _get_default_preferences(self) -> Dict[str, Any]:
        """默认用户偏好"""
        return {
            "preferred_content_types": ["general"],
            "avg_click_position": 5.0,
            "preferred_content_length": "medium",
            "topic_interests": [],
            "interaction_patterns": {
                "click_through_rate": 0.5,
                "avg_session_length": 10.0,
                "bounce_rate": 0.4
            }
        }
    
    async def score_user_relevance(self, document: Dict[str, Any], user_prefs: Dict[str, Any]) -> float:
        """基于用户偏好计算文档相关性"""
        try:
            score = 0.0
            
            # 内容类型匹配
            doc_type = document.get("metadata", {}).get("content_type", "general")
            if doc_type in user_prefs.get("preferred_content_types", []):
                score += 0.3
            
            # 主题兴趣匹配
            doc_topics = document.get("metadata", {}).get("topics", [])
            user_interests = user_prefs.get("topic_interests", [])
            topic_overlap = len(set(doc_topics) & set(user_interests))
            if topic_overlap > 0:
                score += min(0.4, topic_overlap * 0.1)
            
            # 内容长度偏好
            doc_length = len(document.get("content", ""))
            preferred_length = user_prefs.get("preferred_content_length", "medium")
            
            if preferred_length == "short" and doc_length < 500:
                score += 0.2
            elif preferred_length == "medium" and 500 <= doc_length <= 2000:
                score += 0.2
            elif preferred_length == "long" and doc_length > 2000:
                score += 0.2
            
            # 历史交互模式
            interaction = user_prefs.get("interaction_patterns", {})
            if interaction.get("click_through_rate", 0) > 0.6:
                score += 0.1  # 高点击率用户给予额外权重
            
            return min(1.0, score)
            
        except Exception as e:
            logger.error(f"用户相关性评分失败: {e}")
            return 0.5


class FreshnessScorer:
    """时效性评分器"""
    
    def __init__(self):
        self.decay_factor = 0.1  # 衰减因子
    
    def score_freshness(self, timestamp: Optional[datetime]) -> float:
        """
        计算内容时效性分数
        
        Args:
            timestamp: 内容时间戳
            
        Returns:
            时效性分数 (0-1)
        """
        try:
            if not timestamp:
                return 0.5  # 无时间戳时返回中等分数
            
            now = datetime.now()
            age_days = (now - timestamp).days
            
            # 使用指数衰减函数
            freshness_score = np.exp(-self.decay_factor * age_days)
            
            return max(0.1, min(1.0, freshness_score))
            
        except Exception as e:
            logger.error(f"时效性评分失败: {e}")
            return 0.5


class AuthorityScorer:
    """权威性评分器"""
    
    def __init__(self):
        self.source_weights = {
            "official_doc": 1.0,
            "expert_blog": 0.8,
            "community_post": 0.6,
            "user_generated": 0.4,
            "unknown": 0.5
        }
    
    def score_authority(self, document: Dict[str, Any]) -> float:
        """
        计算文档权威性分数
        
        Args:
            document: 文档信息
            
        Returns:
            权威性分数 (0-1)
        """
        try:
            metadata = document.get("metadata", {})
            
            # 来源权威性
            source_type = metadata.get("source_type", "unknown")
            source_score = self.source_weights.get(source_type, 0.5)
            
            # 引用次数（如果有）
            citation_count = metadata.get("citation_count", 0)
            citation_score = min(0.3, citation_count * 0.01)  # 最多0.3分
            
            # 作者权威性（如果有）
            author_score = metadata.get("author_authority", 0.0)
            
            # 内容质量指标
            quality_indicators = metadata.get("quality_score", 0.5)
            
            # 综合权威性分数
            authority_score = (
                source_score * 0.4 +
                citation_score +
                author_score * 0.2 +
                quality_indicators * 0.1
            )
            
            return min(1.0, authority_score)
            
        except Exception as e:
            logger.error(f"权威性评分失败: {e}")
            return 0.5


class AdvancedReranker:
    """高级重排序器"""
    
    def __init__(self):
        self.config = get_retrieval_config()
        self.cross_encoder = CrossEncoderReranker()
        self.behavior_analyzer = UserBehaviorAnalyzer()
        self.freshness_scorer = FreshnessScorer()
        self.authority_scorer = AuthorityScorer()
        
        # 各因子权重
        self.factor_weights = self.config.get("rerank_factors", {
            "semantic": 0.4,
            "behavior": 0.2,
            "freshness": 0.2,
            "authority": 0.2
        })
    
    async def rerank(
        self,
        query: str,
        results: List[Dict[str, Any]],
        user_id: Optional[str] = None,
        top_k: Optional[int] = None
    ) -> List[RerankingResult]:
        """
        多因子重排序
        
        Args:
            query: 查询文本
            results: 原始检索结果
            user_id: 用户ID
            top_k: 返回结果数量
            
        Returns:
            重排序后的结果
        """
        try:
            logger.info(f"开始高级重排序，原始结果数: {len(results)}")
            
            if not results:
                return []
            
            # 获取用户偏好
            user_prefs = {}
            if user_id:
                user_prefs = await self.behavior_analyzer.get_user_preferences(user_id)
            
            # 并行计算各种分数
            tasks = [
                self._calculate_semantic_scores(query, results),
                self._calculate_behavior_scores(results, user_prefs),
                self._calculate_freshness_scores(results),
                self._calculate_authority_scores(results)
            ]
            
            semantic_scores, behavior_scores, freshness_scores, authority_scores = await asyncio.gather(*tasks)
            
            # 计算综合分数并重排序
            reranked_results = []
            
            for i, result in enumerate(results):
                # 各因子分数
                factors = {
                    "semantic": semantic_scores[i],
                    "behavior": behavior_scores[i],
                    "freshness": freshness_scores[i],
                    "authority": authority_scores[i]
                }
                
                # 计算加权综合分数
                final_score = sum(
                    factors[factor] * self.factor_weights[factor]
                    for factor in factors
                )
                
                reranked_result = RerankingResult(
                    id=result.get("id", ""),
                    content=result.get("content", ""),
                    original_score=result.get("score", 0.0),
                    rerank_score=final_score,
                    factors=factors,
                    metadata=result.get("metadata", {})
                )
                
                reranked_results.append(reranked_result)
            
            # 按重排序分数排序
            reranked_results.sort(key=lambda x: x.rerank_score, reverse=True)
            
            # 限制返回数量
            if top_k:
                reranked_results = reranked_results[:top_k]
            
            logger.info(f"重排序完成，返回 {len(reranked_results)} 个结果")
            return reranked_results
            
        except Exception as e:
            logger.error(f"重排序失败: {e}")
            # 失败时返回原始结果
            return [
                RerankingResult(
                    id=r.get("id", ""),
                    content=r.get("content", ""),
                    original_score=r.get("score", 0.0),
                    rerank_score=r.get("score", 0.0),
                    factors={},
                    metadata=r.get("metadata", {})
                )
                for r in results
            ]
    
    async def _calculate_semantic_scores(self, query: str, results: List[Dict]) -> List[float]:
        """计算语义相关性分数"""
        try:
            documents = [result.get("content", "") for result in results]
            return await self.cross_encoder.score_pairs(query, documents)
        except Exception as e:
            logger.error(f"语义分数计算失败: {e}")
            return [0.5] * len(results)
    
    async def _calculate_behavior_scores(self, results: List[Dict], user_prefs: Dict) -> List[float]:
        """计算用户行为分数"""
        try:
            scores = []
            for result in results:
                score = await self.behavior_analyzer.score_user_relevance(result, user_prefs)
                scores.append(score)
            return scores
        except Exception as e:
            logger.error(f"行为分数计算失败: {e}")
            return [0.5] * len(results)
    
    async def _calculate_freshness_scores(self, results: List[Dict]) -> List[float]:
        """计算时效性分数"""
        try:
            scores = []
            for result in results:
                timestamp_str = result.get("metadata", {}).get("timestamp")
                timestamp = None
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str)
                    except:
                        pass
                
                score = self.freshness_scorer.score_freshness(timestamp)
                scores.append(score)
            return scores
        except Exception as e:
            logger.error(f"时效性分数计算失败: {e}")
            return [0.5] * len(results)
    
    async def _calculate_authority_scores(self, results: List[Dict]) -> List[float]:
        """计算权威性分数"""
        try:
            scores = []
            for result in results:
                score = self.authority_scorer.score_authority(result)
                scores.append(score)
            return scores
        except Exception as e:
            logger.error(f"权威性分数计算失败: {e}")
            return [0.5] * len(results)


# 创建全局重排序器实例
advanced_reranker = AdvancedReranker()
