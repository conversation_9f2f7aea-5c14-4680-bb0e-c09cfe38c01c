"""
数据库管理工具
用于执行迁移、索引优化、性能监控等数据库管理任务
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from loguru import logger
from .database_client import database_client


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent.parent / "migrations"
        self.executed_migrations = set()
    
    async def run_migrations(self, target_version: Optional[str] = None) -> bool:
        """
        运行数据库迁移
        
        Args:
            target_version: 目标版本，None表示运行所有迁移
            
        Returns:
            是否成功
        """
        try:
            logger.info("开始执行数据库迁移...")
            
            # 确保迁移表存在
            await self._ensure_migration_table()
            
            # 获取已执行的迁移
            executed = await self._get_executed_migrations()
            self.executed_migrations = set(executed)
            
            # 获取待执行的迁移文件
            migration_files = self._get_migration_files()
            
            success_count = 0
            for migration_file in migration_files:
                if migration_file.stem in self.executed_migrations:
                    logger.debug(f"跳过已执行的迁移: {migration_file.name}")
                    continue
                
                if target_version and migration_file.stem > target_version:
                    logger.info(f"达到目标版本 {target_version}，停止迁移")
                    break
                
                success = await self._execute_migration(migration_file)
                if success:
                    success_count += 1
                    await self._record_migration(migration_file.stem)
                else:
                    logger.error(f"迁移失败: {migration_file.name}")
                    return False
            
            logger.info(f"数据库迁移完成，成功执行 {success_count} 个迁移")
            return True
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return False
    
    async def optimize_indexes(self) -> bool:
        """优化数据库索引"""
        try:
            logger.info("开始优化数据库索引...")
            
            # 重建统计信息
            await self._update_table_statistics()
            
            # 检查索引使用情况
            index_stats = await self._get_index_statistics()
            
            # 识别未使用的索引
            unused_indexes = await self._find_unused_indexes()
            
            # 识别缺失的索引
            missing_indexes = await self._suggest_missing_indexes()
            
            # 生成优化报告
            optimization_report = {
                "timestamp": datetime.now().isoformat(),
                "index_count": len(index_stats),
                "unused_indexes": unused_indexes,
                "missing_indexes": missing_indexes,
                "recommendations": []
            }
            
            # 添加优化建议
            if unused_indexes:
                optimization_report["recommendations"].append(
                    f"发现 {len(unused_indexes)} 个未使用的索引，建议删除以节省空间"
                )
            
            if missing_indexes:
                optimization_report["recommendations"].append(
                    f"建议创建 {len(missing_indexes)} 个索引以提升查询性能"
                )
            
            logger.info(f"索引优化分析完成: {optimization_report}")
            return True
            
        except Exception as e:
            logger.error(f"索引优化失败: {e}")
            return False
    
    async def get_database_health(self) -> Dict[str, Any]:
        """获取数据库健康状态"""
        try:
            health_info = {}
            
            # 连接状态
            health_info["connection_status"] = "healthy"
            
            # 表统计信息
            table_stats = await self._get_table_statistics()
            health_info["table_statistics"] = table_stats
            
            # 索引统计信息
            index_stats = await self._get_index_statistics()
            health_info["index_statistics"] = {
                "total_indexes": len(index_stats),
                "total_size_mb": sum(stat.get("size_mb", 0) for stat in index_stats)
            }
            
            # 性能指标
            performance_stats = await self._get_performance_statistics()
            health_info["performance"] = performance_stats
            
            # 磁盘使用情况
            disk_usage = await self._get_disk_usage()
            health_info["disk_usage"] = disk_usage
            
            return health_info
            
        except Exception as e:
            logger.error(f"获取数据库健康状态失败: {e}")
            return {"connection_status": "error", "error": str(e)}
    
    async def cleanup_old_data(self, days: int = 30) -> bool:
        """清理旧数据"""
        try:
            logger.info(f"开始清理 {days} 天前的旧数据...")
            
            cleanup_tasks = [
                ("query_history", "created_at"),
                ("user_behavior", "created_at"),
                ("performance_metrics", "created_at"),
                ("temporal_scores", "calculated_at"),
                ("chinese_segmentation_results", "created_at"),
                ("semantic_role_results", "created_at"),
                ("self_rag_evaluations", "created_at")
            ]
            
            total_deleted = 0
            
            for table_name, date_column in cleanup_tasks:
                deleted_count = await database_client.execute(f"""
                    DELETE FROM {table_name} 
                    WHERE {date_column} < NOW() - INTERVAL '{days} days'
                """)
                
                total_deleted += deleted_count
                logger.info(f"从 {table_name} 删除了 {deleted_count} 条记录")
            
            # 清理孤立的方面向量
            orphaned_vectors = await database_client.execute("""
                DELETE FROM aspect_vectors 
                WHERE document_id NOT IN (SELECT id FROM documents)
            """)
            
            total_deleted += orphaned_vectors
            logger.info(f"删除了 {orphaned_vectors} 个孤立的方面向量")
            
            # 更新表统计信息
            await self._update_table_statistics()
            
            logger.info(f"数据清理完成，总共删除 {total_deleted} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据清理失败: {e}")
            return False
    
    async def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            logger.info(f"开始备份数据库到: {backup_path}")
            
            # 这里应该调用pg_dump等工具进行备份
            # 简化实现，仅记录备份请求
            
            backup_info = {
                "timestamp": datetime.now().isoformat(),
                "backup_path": backup_path,
                "status": "completed"
            }
            
            logger.info(f"数据库备份完成: {backup_info}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    async def _ensure_migration_table(self):
        """确保迁移表存在"""
        await database_client.execute("""
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version VARCHAR(255) PRIMARY KEY,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    async def _get_executed_migrations(self) -> List[str]:
        """获取已执行的迁移"""
        try:
            results = await database_client.fetch(
                "SELECT version FROM schema_migrations ORDER BY version"
            )
            return [row['version'] for row in results]
        except:
            return []
    
    def _get_migration_files(self) -> List[Path]:
        """获取迁移文件列表"""
        if not self.migrations_dir.exists():
            return []
        
        migration_files = []
        for file_path in self.migrations_dir.glob("*.sql"):
            migration_files.append(file_path)
        
        return sorted(migration_files)
    
    async def _execute_migration(self, migration_file: Path) -> bool:
        """执行单个迁移文件"""
        try:
            logger.info(f"执行迁移: {migration_file.name}")
            
            with open(migration_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句并执行
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement:
                    await database_client.execute(statement)
            
            logger.info(f"迁移执行成功: {migration_file.name}")
            return True
            
        except Exception as e:
            logger.error(f"迁移执行失败 {migration_file.name}: {e}")
            return False
    
    async def _record_migration(self, version: str):
        """记录已执行的迁移"""
        await database_client.execute(
            "INSERT INTO schema_migrations (version) VALUES ($1) ON CONFLICT DO NOTHING",
            version
        )
    
    async def _update_table_statistics(self):
        """更新表统计信息"""
        try:
            # 获取所有表名
            tables = await database_client.fetch("""
                SELECT tablename FROM pg_tables 
                WHERE schemaname = 'public'
            """)
            
            # 更新每个表的统计信息
            for table in tables:
                table_name = table['tablename']
                await database_client.execute(f"ANALYZE {table_name}")
            
            logger.info("表统计信息更新完成")
            
        except Exception as e:
            logger.error(f"更新表统计信息失败: {e}")
    
    async def _get_table_statistics(self) -> List[Dict[str, Any]]:
        """获取表统计信息"""
        try:
            stats = await database_client.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    most_common_vals,
                    most_common_freqs,
                    histogram_bounds
                FROM pg_stats 
                WHERE schemaname = 'public'
                ORDER BY tablename, attname
            """)
            
            return [dict(stat) for stat in stats]
            
        except Exception as e:
            logger.error(f"获取表统计信息失败: {e}")
            return []
    
    async def _get_index_statistics(self) -> List[Dict[str, Any]]:
        """获取索引统计信息"""
        try:
            stats = await database_client.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_scan,
                    idx_tup_read,
                    idx_tup_fetch,
                    pg_size_pretty(pg_relation_size(indexrelid)) as size
                FROM pg_stat_user_indexes 
                ORDER BY idx_scan DESC
            """)
            
            return [dict(stat) for stat in stats]
            
        except Exception as e:
            logger.error(f"获取索引统计信息失败: {e}")
            return []
    
    async def _find_unused_indexes(self) -> List[str]:
        """查找未使用的索引"""
        try:
            unused = await database_client.fetch("""
                SELECT indexname
                FROM pg_stat_user_indexes 
                WHERE idx_scan = 0
                AND schemaname = 'public'
            """)
            
            return [row['indexname'] for row in unused]
            
        except Exception as e:
            logger.error(f"查找未使用索引失败: {e}")
            return []
    
    async def _suggest_missing_indexes(self) -> List[str]:
        """建议缺失的索引"""
        try:
            # 简化实现：基于查询模式建议索引
            suggestions = []
            
            # 检查是否需要复合索引
            high_frequency_queries = await database_client.fetch("""
                SELECT search_type, COUNT(*) as query_count
                FROM query_history 
                WHERE created_at >= NOW() - INTERVAL '7 days'
                GROUP BY search_type
                HAVING COUNT(*) > 100
            """)
            
            for query in high_frequency_queries:
                search_type = query['search_type']
                suggestions.append(f"考虑为 query_history(search_type, created_at) 创建复合索引")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"建议缺失索引失败: {e}")
            return []
    
    async def _get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        try:
            # 连接数
            connections = await database_client.fetchrow("""
                SELECT count(*) as active_connections
                FROM pg_stat_activity 
                WHERE state = 'active'
            """)
            
            # 缓存命中率
            cache_hit = await database_client.fetchrow("""
                SELECT 
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as cache_hit_ratio
                FROM pg_statio_user_tables
            """)
            
            # 锁等待
            locks = await database_client.fetchrow("""
                SELECT count(*) as waiting_locks
                FROM pg_locks 
                WHERE NOT granted
            """)
            
            return {
                "active_connections": connections['active_connections'] if connections else 0,
                "cache_hit_ratio": float(cache_hit['cache_hit_ratio']) if cache_hit and cache_hit['cache_hit_ratio'] else 0,
                "waiting_locks": locks['waiting_locks'] if locks else 0
            }
            
        except Exception as e:
            logger.error(f"获取性能统计信息失败: {e}")
            return {}
    
    async def _get_disk_usage(self) -> Dict[str, Any]:
        """获取磁盘使用情况"""
        try:
            usage = await database_client.fetchrow("""
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as database_size,
                    pg_size_pretty(sum(pg_total_relation_size(oid))) as total_table_size
                FROM pg_class 
                WHERE relkind = 'r'
            """)
            
            return {
                "database_size": usage['database_size'] if usage else "0 bytes",
                "total_table_size": usage['total_table_size'] if usage else "0 bytes"
            }
            
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败: {e}")
            return {}


# 创建全局实例
database_manager = DatabaseManager()
