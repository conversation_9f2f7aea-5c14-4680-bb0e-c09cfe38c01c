"""
Elasticsearch客户端
用于关键词搜索和全文检索
"""

from typing import List, Dict, Any, Optional
from elasticsearch import AsyncElasticsearch
from loguru import logger

from .config import settings, get_elasticsearch_config


class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self):
        self.client = None
        self.config = get_elasticsearch_config()
        self.index_name = self.config["index"]
    
    async def _get_client(self) -> AsyncElasticsearch:
        """获取Elasticsearch客户端"""
        if not self.client:
            try:
                client_config = {
                    "hosts": [self.config["url"]],
                    "timeout": 30,
                    "max_retries": 3,
                    "retry_on_timeout": True
                }
                
                # 添加认证信息
                if "auth" in self.config:
                    client_config["http_auth"] = self.config["auth"]
                
                self.client = AsyncElasticsearch(**client_config)
                
                # 测试连接
                await self.client.ping()
                logger.info("Elasticsearch客户端初始化成功")
                
            except Exception as e:
                logger.error(f"Elasticsearch客户端初始化失败: {e}")
                raise
        
        return self.client
    
    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.close()
            logger.info("Elasticsearch连接已关闭")
    
    async def search_documents(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 10,
        fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            client = await self._get_client()
            
            # 构建搜索查询
            search_body = self._build_search_query(query, filters, fields)
            
            # 执行搜索
            response = await client.search(
                index=self.index_name,
                body=search_body,
                size=top_k
            )
            
            # 格式化结果
            results = []
            for hit in response["hits"]["hits"]:
                result = {
                    "id": hit["_id"],
                    "score": hit["_score"],
                    "source": hit["_source"],
                    "content": hit["_source"].get("content", ""),
                    "metadata": hit["_source"].get("metadata", {}),
                    "document_id": hit["_source"].get("document_id"),
                    "chunk_id": hit["_source"].get("chunk_id")
                }
                
                # 添加高亮信息
                if "highlight" in hit:
                    result["highlights"] = hit["highlight"]
                
                results.append(result)
            
            logger.debug(f"Elasticsearch搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"Elasticsearch搜索失败: {e}")
            return []
    
    def _build_search_query(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """构建搜索查询"""
        
        # 默认搜索字段
        if not fields:
            fields = ["content^2", "title^3", "metadata.heading^2"]
        
        # 构建多字段查询
        query_clause = {
            "multi_match": {
                "query": query,
                "fields": fields,
                "type": "best_fields",
                "fuzziness": "AUTO",
                "operator": "or"
            }
        }
        
        # 构建完整查询
        search_body = {
            "query": query_clause,
            "highlight": {
                "fields": {
                    "content": {
                        "fragment_size": 150,
                        "number_of_fragments": 3
                    },
                    "title": {},
                    "metadata.heading": {}
                }
            },
            "sort": [
                {"_score": {"order": "desc"}},
                {"created_at": {"order": "desc"}}
            ]
        }
        
        # 添加过滤条件
        if filters:
            filter_clauses = []
            
            for key, value in filters.items():
                if isinstance(value, list):
                    # 多值过滤
                    filter_clauses.append({
                        "terms": {key: value}
                    })
                elif isinstance(value, dict):
                    # 范围过滤
                    if "gte" in value or "lte" in value or "gt" in value or "lt" in value:
                        filter_clauses.append({
                            "range": {key: value}
                        })
                else:
                    # 精确匹配
                    filter_clauses.append({
                        "term": {f"{key}.keyword": value}
                    })
            
            if filter_clauses:
                search_body["query"] = {
                    "bool": {
                        "must": [query_clause],
                        "filter": filter_clauses
                    }
                }
        
        return search_body
    
    async def search_by_document(
        self,
        document_id: str,
        query: str,
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """在特定文档中搜索"""
        filters = {"document_id": document_id}
        return await self.search_documents(query, filters, top_k)
    
    async def get_document_chunks(
        self,
        document_id: str,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取文档的所有块"""
        try:
            client = await self._get_client()
            
            search_body = {
                "query": {
                    "term": {
                        "document_id.keyword": document_id
                    }
                },
                "sort": [
                    {"metadata.chunk_index": {"order": "asc"}}
                ],
                "size": limit
            }
            
            response = await client.search(
                index=self.index_name,
                body=search_body
            )
            
            chunks = []
            for hit in response["hits"]["hits"]:
                chunk = {
                    "id": hit["_id"],
                    "chunk_id": hit["_source"].get("chunk_id"),
                    "content": hit["_source"].get("content", ""),
                    "metadata": hit["_source"].get("metadata", {}),
                    "created_at": hit["_source"].get("created_at")
                }
                chunks.append(chunk)
            
            logger.debug(f"获取文档块完成，返回 {len(chunks)} 个块")
            return chunks
            
        except Exception as e:
            logger.error(f"获取文档块失败: {e}")
            return []
    
    async def suggest_queries(
        self,
        partial_query: str,
        size: int = 5
    ) -> List[str]:
        """查询建议"""
        try:
            client = await self._get_client()
            
            suggest_body = {
                "suggest": {
                    "query_suggestion": {
                        "prefix": partial_query,
                        "completion": {
                            "field": "suggest",
                            "size": size
                        }
                    }
                }
            }
            
            response = await client.search(
                index=self.index_name,
                body=suggest_body
            )
            
            suggestions = []
            if "suggest" in response:
                for suggestion in response["suggest"]["query_suggestion"]:
                    for option in suggestion["options"]:
                        suggestions.append(option["text"])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"查询建议失败: {e}")
            return []
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            client = await self._get_client()
            
            # 获取索引统计
            stats_response = await client.indices.stats(index=self.index_name)
            
            # 获取文档数量
            count_response = await client.count(index=self.index_name)
            
            index_stats = stats_response["indices"][self.index_name]
            
            return {
                "document_count": count_response["count"],
                "index_size": index_stats["total"]["store"]["size_in_bytes"],
                "index_size_human": index_stats["total"]["store"]["size"],
                "search_total": index_stats["total"]["search"]["query_total"],
                "search_time": index_stats["total"]["search"]["query_time_in_millis"],
                "indexing_total": index_stats["total"]["indexing"]["index_total"],
                "indexing_time": index_stats["total"]["indexing"]["index_time_in_millis"]
            }
            
        except Exception as e:
            logger.error(f"获取索引统计失败: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            client = await self._get_client()
            
            # 检查集群健康状态
            health = await client.cluster.health()
            
            # 检查索引是否存在
            index_exists = await client.indices.exists(index=self.index_name)
            
            return {
                "cluster_status": health["status"],
                "cluster_name": health["cluster_name"],
                "number_of_nodes": health["number_of_nodes"],
                "active_primary_shards": health["active_primary_shards"],
                "active_shards": health["active_shards"],
                "index_exists": index_exists,
                "index_name": self.index_name
            }
            
        except Exception as e:
            logger.error(f"Elasticsearch健康检查失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }


# 创建全局Elasticsearch客户端
es_client = ElasticsearchClient()
