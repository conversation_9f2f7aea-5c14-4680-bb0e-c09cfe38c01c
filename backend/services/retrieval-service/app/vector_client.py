"""
向量数据库客户端
支持多种向量数据库的统一接口
"""

import uuid
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from loguru import logger

from .config import settings, get_vector_db_config


class VectorClient(ABC):
    """向量数据库客户端抽象基类"""
    
    @abstractmethod
    async def search_similar(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None,
        score_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        pass
    
    @abstractmethod
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        pass
    
    @abstractmethod
    async def list_collections(self) -> List[str]:
        """列出所有集合"""
        pass


class ChromaVectorClient(VectorClient):
    """ChromaDB向量客户端"""
    
    def __init__(self):
        self.client = None
        self.config = get_vector_db_config()
    
    async def _get_client(self):
        """获取ChromaDB客户端"""
        if not self.client:
            try:
                import chromadb
                from chromadb.config import Settings as ChromaSettings
                
                # 根据URL判断是否为远程连接
                if self.config["url"].startswith("http"):
                    self.client = chromadb.HttpClient(
                        host=self.config["url"].replace("http://", "").replace("https://", ""),
                        port=8000,
                        settings=ChromaSettings(allow_reset=True)
                    )
                else:
                    self.client = chromadb.PersistentClient(
                        path=self.config.get("path", "./chroma_db"),
                        settings=ChromaSettings(allow_reset=True)
                    )
                
                logger.info("ChromaDB客户端初始化成功")
            except Exception as e:
                logger.error(f"ChromaDB客户端初始化失败: {e}")
                raise
        
        return self.client
    
    async def search_similar(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None,
        score_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            results = collection.query(
                query_embeddings=[query_vector],
                n_results=top_k,
                where=filter_dict
            )
            
            # 格式化结果
            formatted_results = []
            if results["ids"] and len(results["ids"]) > 0:
                for i in range(len(results["ids"][0])):
                    score = 1 - results["distances"][0][i]  # 转换为相似度分数
                    
                    # 应用分数阈值过滤
                    if score_threshold and score < score_threshold:
                        continue
                    
                    result = {
                        "id": results["ids"][0][i],
                        "score": score,
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "content": results["documents"][0][i] if results["documents"] else ""
                    }
                    formatted_results.append(result)
            
            logger.debug(f"ChromaDB搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"ChromaDB搜索失败: {collection_name}, {e}")
            return []
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            count = collection.count()
            
            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata
            }
            
        except Exception as e:
            logger.error(f"获取ChromaDB集合信息失败: {collection_name}, {e}")
            return {}
    
    async def list_collections(self) -> List[str]:
        """列出所有集合"""
        try:
            client = await self._get_client()
            collections = client.list_collections()
            return [col.name for col in collections]
            
        except Exception as e:
            logger.error(f"列出ChromaDB集合失败: {e}")
            return []


class PineconeVectorClient(VectorClient):
    """Pinecone向量客户端"""
    
    def __init__(self):
        self.client = None
        self.config = get_vector_db_config()
    
    async def _get_client(self):
        """获取Pinecone客户端"""
        if not self.client:
            try:
                import pinecone
                
                pinecone.init(
                    api_key=self.config["api_key"],
                    environment=self.config["environment"]
                )
                
                self.client = pinecone
                logger.info("Pinecone客户端初始化成功")
            except Exception as e:
                logger.error(f"Pinecone客户端初始化失败: {e}")
                raise
        
        return self.client
    
    async def search_similar(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None,
        score_threshold: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            results = index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True
            )
            
            # 格式化结果
            formatted_results = []
            for match in results["matches"]:
                score = match["score"]
                
                # 应用分数阈值过滤
                if score_threshold and score < score_threshold:
                    continue
                
                result = {
                    "id": match["id"],
                    "score": score,
                    "metadata": match.get("metadata", {}),
                    "content": match.get("metadata", {}).get("content", "")
                }
                formatted_results.append(result)
            
            logger.debug(f"Pinecone搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Pinecone搜索失败: {collection_name}, {e}")
            return []
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取索引信息"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            stats = index.describe_index_stats()
            
            return {
                "name": collection_name,
                "count": stats.get("total_vector_count", 0),
                "dimension": stats.get("dimension", 0)
            }
            
        except Exception as e:
            logger.error(f"获取Pinecone索引信息失败: {collection_name}, {e}")
            return {}
    
    async def list_collections(self) -> List[str]:
        """列出所有索引"""
        try:
            client = await self._get_client()
            return client.list_indexes()
            
        except Exception as e:
            logger.error(f"列出Pinecone索引失败: {e}")
            return []


class VectorClientFactory:
    """向量客户端工厂"""
    
    @staticmethod
    def create_client() -> VectorClient:
        """根据配置创建向量客户端"""
        db_type = settings.VECTOR_DB_TYPE.lower()
        
        if db_type == "chroma":
            return ChromaVectorClient()
        elif db_type == "pinecone":
            return PineconeVectorClient()
        else:
            raise ValueError(f"不支持的向量数据库类型: {db_type}")


# 创建全局向量客户端
vector_client = VectorClientFactory.create_client()
