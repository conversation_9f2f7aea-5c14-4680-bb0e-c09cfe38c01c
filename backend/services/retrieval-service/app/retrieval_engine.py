"""
检索引擎核心模块
实现语义检索、关键词检索和混合检索
"""

import asyncio
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

from .config import settings, get_retrieval_config
from .vector_client import vector_client
from .elasticsearch_client import es_client
from .embedding_client import embedding_client
from .redis_client import retrieval_cache


class RetrievalEngine:
    """检索引擎"""
    
    def __init__(self):
        self.config = get_retrieval_config()
        self.semantic_weight = self.config["semantic_weight"]
        self.keyword_weight = self.config["keyword_weight"]
        self.enable_reranking = self.config["enable_reranking"]
        self.rerank_top_k = self.config["rerank_top_k"]
    
    async def semantic_search(
        self,
        query: str,
        collection_name: Optional[str] = None,
        document_id: Optional[str] = None,
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None,
        model: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """语义检索"""
        try:
            logger.info(f"开始语义检索: {query[:50]}...")
            
            # 获取查询向量
            query_embedding = await embedding_client.embed_text(query, model)
            if not query_embedding:
                logger.error("获取查询向量失败")
                return []
            
            # 确定集合名称
            if document_id:
                collection_name = f"document_{document_id}"
            elif not collection_name:
                collection_name = "default"
            
            # 执行向量搜索
            results = await vector_client.search_similar(
                collection_name=collection_name,
                query_vector=query_embedding,
                top_k=top_k,
                filter_dict=filters,
                score_threshold=score_threshold
            )
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_result = {
                    "rank": i + 1,
                    "id": result["id"],
                    "score": result["score"],
                    "content": result["content"],
                    "metadata": result["metadata"],
                    "source_type": "semantic",
                    "document_id": result["metadata"].get("document_id"),
                    "chunk_id": result["metadata"].get("chunk_id")
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"语义检索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"语义检索失败: {e}")
            return []
    
    async def keyword_search(
        self,
        query: str,
        document_id: Optional[str] = None,
        top_k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """关键词检索"""
        try:
            logger.info(f"开始关键词检索: {query[:50]}...")
            
            # 执行Elasticsearch搜索
            if document_id:
                results = await es_client.search_by_document(document_id, query, top_k)
            else:
                results = await es_client.search_documents(query, filters, top_k)
            
            # 格式化结果
            formatted_results = []
            for i, result in enumerate(results):
                formatted_result = {
                    "rank": i + 1,
                    "id": result["id"],
                    "score": result["score"],
                    "content": result["content"],
                    "metadata": result["metadata"],
                    "source_type": "keyword",
                    "document_id": result["document_id"],
                    "chunk_id": result["chunk_id"],
                    "highlights": result.get("highlights", {})
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"关键词检索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"关键词检索失败: {e}")
            return []
    
    async def hybrid_search(
        self,
        query: str,
        collection_name: Optional[str] = None,
        document_id: Optional[str] = None,
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None,
        model: Optional[str] = None,
        semantic_weight: Optional[float] = None,
        keyword_weight: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """混合检索（语义+关键词）"""
        try:
            logger.info(f"开始混合检索: {query[:50]}...")
            
            # 使用自定义权重或默认权重
            sem_weight = semantic_weight or self.semantic_weight
            key_weight = keyword_weight or self.keyword_weight
            
            # 并行执行语义检索和关键词检索
            semantic_task = self.semantic_search(
                query, collection_name, document_id, 
                self.rerank_top_k if self.enable_reranking else top_k,
                score_threshold, filters, model
            )
            
            keyword_task = self.keyword_search(
                query, document_id,
                self.rerank_top_k if self.enable_reranking else top_k,
                filters
            )
            
            semantic_results, keyword_results = await asyncio.gather(
                semantic_task, keyword_task, return_exceptions=True
            )
            
            # 处理异常结果
            if isinstance(semantic_results, Exception):
                logger.error(f"语义检索异常: {semantic_results}")
                semantic_results = []
            
            if isinstance(keyword_results, Exception):
                logger.error(f"关键词检索异常: {keyword_results}")
                keyword_results = []
            
            # 合并和重排序结果
            merged_results = self._merge_results(
                semantic_results, keyword_results, 
                sem_weight, key_weight
            )
            
            # 重排序（如果启用）
            if self.enable_reranking and len(merged_results) > top_k:
                merged_results = await self._rerank_results(query, merged_results, top_k)
            else:
                merged_results = merged_results[:top_k]
            
            # 更新排名
            for i, result in enumerate(merged_results):
                result["rank"] = i + 1
                result["source_type"] = "hybrid"
            
            logger.info(f"混合检索完成，返回 {len(merged_results)} 个结果")
            return merged_results
            
        except Exception as e:
            logger.error(f"混合检索失败: {e}")
            return []
    
    def _merge_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """合并语义检索和关键词检索结果"""
        
        # 创建结果字典，以chunk_id为键
        merged_dict = {}
        
        # 处理语义检索结果
        for result in semantic_results:
            chunk_id = result.get("chunk_id") or result.get("id")
            if chunk_id:
                result["semantic_score"] = result["score"]
                result["keyword_score"] = 0.0
                result["combined_score"] = result["score"] * semantic_weight
                merged_dict[chunk_id] = result
        
        # 处理关键词检索结果
        for result in keyword_results:
            chunk_id = result.get("chunk_id") or result.get("id")
            if chunk_id:
                if chunk_id in merged_dict:
                    # 更新现有结果
                    merged_dict[chunk_id]["keyword_score"] = result["score"]
                    merged_dict[chunk_id]["combined_score"] = (
                        merged_dict[chunk_id]["semantic_score"] * semantic_weight +
                        result["score"] * keyword_weight
                    )
                    # 合并高亮信息
                    if "highlights" in result:
                        merged_dict[chunk_id]["highlights"] = result["highlights"]
                else:
                    # 新增结果
                    result["semantic_score"] = 0.0
                    result["keyword_score"] = result["score"]
                    result["combined_score"] = result["score"] * keyword_weight
                    merged_dict[chunk_id] = result
        
        # 按组合分数排序
        merged_results = list(merged_dict.values())
        merged_results.sort(key=lambda x: x["combined_score"], reverse=True)
        
        # 更新最终分数
        for result in merged_results:
            result["score"] = result["combined_score"]
        
        return merged_results
    
    async def _rerank_results(
        self,
        query: str,
        results: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """重排序结果"""
        try:
            # 这里可以实现更复杂的重排序逻辑
            # 例如使用交叉编码器模型、基于用户行为的重排序等
            
            # 简单的重排序：基于内容与查询的相关性
            query_terms = set(query.lower().split())
            
            for result in results:
                content = result.get("content", "").lower()
                content_terms = set(content.split())
                
                # 计算词汇重叠度
                overlap = len(query_terms.intersection(content_terms))
                overlap_ratio = overlap / len(query_terms) if query_terms else 0
                
                # 调整分数
                result["score"] = result["score"] * (1 + overlap_ratio * 0.1)
            
            # 重新排序
            results.sort(key=lambda x: x["score"], reverse=True)
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"重排序失败: {e}")
            return results[:top_k]
    
    async def search(
        self,
        query: str,
        search_type: str = "hybrid",
        collection_name: Optional[str] = None,
        document_id: Optional[str] = None,
        top_k: int = 10,
        score_threshold: Optional[float] = None,
        filters: Optional[Dict[str, Any]] = None,
        model: Optional[str] = None,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """统一搜索接口"""
        try:
            # 检查缓存
            if use_cache:
                cached_results = await retrieval_cache.get_cached_query_result(query, filters)
                if cached_results:
                    logger.info("查询结果缓存命中")
                    return cached_results[:top_k]
            
            # 根据搜索类型执行相应的检索
            if search_type == "semantic":
                results = await self.semantic_search(
                    query, collection_name, document_id, top_k, 
                    score_threshold, filters, model
                )
            elif search_type == "keyword":
                results = await self.keyword_search(
                    query, document_id, top_k, filters
                )
            elif search_type == "hybrid":
                results = await self.hybrid_search(
                    query, collection_name, document_id, top_k,
                    score_threshold, filters, model
                )
            else:
                raise ValueError(f"不支持的搜索类型: {search_type}")
            
            # 缓存结果
            if use_cache and results:
                await retrieval_cache.cache_query_result(query, results, filters)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    async def get_similar_documents(
        self,
        document_id: str,
        top_k: int = 5,
        model: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取相似文档"""
        try:
            # 获取文档的代表性向量（可以是文档标题或摘要的向量）
            # 这里简化处理，使用文档ID作为查询
            
            # 实际实现中，应该获取文档的标题或摘要
            query = f"document_{document_id}"
            
            results = await self.semantic_search(
                query=query,
                top_k=top_k,
                model=model,
                filters={"document_id": {"$ne": document_id}}  # 排除自身
            )
            
            return results
            
        except Exception as e:
            logger.error(f"获取相似文档失败: {e}")
            return []


# 创建全局检索引擎
retrieval_engine = RetrievalEngine()
