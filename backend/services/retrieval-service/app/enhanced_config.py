"""
增强检索配置管理
包含HyDE、查询重写、重排序等功能的配置参数
"""

import os
from typing import Dict, Any, List
from pydantic import BaseSettings, Field


class EnhancedRetrievalSettings(BaseSettings):
    """增强检索配置"""
    
    # HyDE配置
    HYDE_ENABLED: bool = Field(True, description="是否启用HyDE")
    HYDE_TEMPERATURE: float = Field(0.7, description="HyDE生成温度")
    HYDE_MAX_TOKENS: int = Field(500, description="HyDE最大token数")
    HYDE_WEIGHT: float = Field(0.6, description="HyDE结果权重")
    HYDE_CACHE_TTL: int = Field(3600, description="HyDE缓存时间(秒)")
    
    # 查询重写配置
    QUERY_REWRITE_ENABLED: bool = Field(True, description="是否启用查询重写")
    MAX_QUERY_VARIANTS: int = Field(5, description="最大查询变体数")
    SYNONYM_EXPANSION_ENABLED: bool = Field(True, description="是否启用同义词扩展")
    CONTEXT_ENHANCEMENT_ENABLED: bool = Field(True, description="是否启用上下文增强")
    QUERY_SIMPLIFICATION_ENABLED: bool = Field(True, description="是否启用查询简化")
    
    # 重排序配置
    RERANKING_ENABLED: bool = Field(True, description="是否启用重排序")
    CROSS_ENCODER_MODEL: str = Field(
        "cross-encoder/ms-marco-MiniLM-L-6-v2", 
        description="交叉编码器模型名称"
    )
    RERANK_TOP_K: int = Field(50, description="重排序候选数量")
    RERANK_BATCH_SIZE: int = Field(16, description="重排序批处理大小")
    
    # 重排序因子权重
    SEMANTIC_WEIGHT: float = Field(0.4, description="语义相关性权重")
    BEHAVIOR_WEIGHT: float = Field(0.2, description="用户行为权重")
    FRESHNESS_WEIGHT: float = Field(0.2, description="时效性权重")
    AUTHORITY_WEIGHT: float = Field(0.2, description="权威性权重")
    
    # 多粒度分块配置
    MULTI_GRANULARITY_ENABLED: bool = Field(True, description="是否启用多粒度分块")
    CHUNK_TYPES: List[str] = Field(
        ["sentence", "semantic", "sliding", "structure", "paragraph"],
        description="启用的分块类型"
    )
    
    # 分块权重配置
    SENTENCE_CHUNK_WEIGHT: float = Field(1.0, description="句子级分块权重")
    SEMANTIC_CHUNK_WEIGHT: float = Field(1.1, description="语义级分块权重")
    SLIDING_CHUNK_WEIGHT: float = Field(0.9, description="滑动窗口分块权重")
    STRUCTURE_CHUNK_WEIGHT: float = Field(1.2, description="结构化分块权重")
    PARAGRAPH_CHUNK_WEIGHT: float = Field(1.05, description="段落级分块权重")
    
    # 缓存配置
    CACHE_ENABLED: bool = Field(True, description="是否启用缓存")
    CACHE_TTL: int = Field(3600, description="缓存时间(秒)")
    CACHE_MAX_SIZE: int = Field(10000, description="最大缓存条目数")
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(100, description="最大并发请求数")
    REQUEST_TIMEOUT: int = Field(30, description="请求超时时间(秒)")
    BATCH_PROCESSING_SIZE: int = Field(32, description="批处理大小")
    
    # 质量控制配置
    MIN_SIMILARITY_THRESHOLD: float = Field(0.1, description="最小相似度阈值")
    MAX_RESULT_COUNT: int = Field(100, description="最大结果数量")
    QUALITY_FILTER_ENABLED: bool = Field(True, description="是否启用质量过滤")
    
    # 用户行为分析配置
    USER_BEHAVIOR_ENABLED: bool = Field(True, description="是否启用用户行为分析")
    BEHAVIOR_CACHE_TTL: int = Field(86400, description="用户行为缓存时间(秒)")
    MIN_USER_INTERACTIONS: int = Field(5, description="最少用户交互次数")
    
    # 时效性配置
    FRESHNESS_DECAY_FACTOR: float = Field(0.1, description="时效性衰减因子")
    MAX_CONTENT_AGE_DAYS: int = Field(365, description="内容最大年龄(天)")
    
    # 权威性配置
    SOURCE_WEIGHTS: Dict[str, float] = Field(
        {
            "official_doc": 1.0,
            "expert_blog": 0.8,
            "community_post": 0.6,
            "user_generated": 0.4,
            "unknown": 0.5
        },
        description="来源权威性权重"
    )
    
    # A/B测试配置
    AB_TESTING_ENABLED: bool = Field(False, description="是否启用A/B测试")
    CONTROL_GROUP_RATIO: float = Field(0.3, description="对照组比例")
    TREATMENT_GROUP_RATIO: float = Field(0.7, description="实验组比例")
    
    # 监控配置
    METRICS_ENABLED: bool = Field(True, description="是否启用指标收集")
    DETAILED_LOGGING: bool = Field(False, description="是否启用详细日志")
    PERFORMANCE_TRACKING: bool = Field(True, description="是否启用性能跟踪")
    
    class Config:
        env_prefix = "ENHANCED_RETRIEVAL_"
        case_sensitive = True


# 全局配置实例
enhanced_settings = EnhancedRetrievalSettings()


def get_enhanced_retrieval_config() -> Dict[str, Any]:
    """获取增强检索配置字典"""
    return {
        # HyDE配置
        "hyde": {
            "enabled": enhanced_settings.HYDE_ENABLED,
            "temperature": enhanced_settings.HYDE_TEMPERATURE,
            "max_tokens": enhanced_settings.HYDE_MAX_TOKENS,
            "weight": enhanced_settings.HYDE_WEIGHT,
            "cache_ttl": enhanced_settings.HYDE_CACHE_TTL
        },
        
        # 查询重写配置
        "query_rewrite": {
            "enabled": enhanced_settings.QUERY_REWRITE_ENABLED,
            "max_variants": enhanced_settings.MAX_QUERY_VARIANTS,
            "synonym_expansion": enhanced_settings.SYNONYM_EXPANSION_ENABLED,
            "context_enhancement": enhanced_settings.CONTEXT_ENHANCEMENT_ENABLED,
            "simplification": enhanced_settings.QUERY_SIMPLIFICATION_ENABLED
        },
        
        # 重排序配置
        "reranking": {
            "enabled": enhanced_settings.RERANKING_ENABLED,
            "model": enhanced_settings.CROSS_ENCODER_MODEL,
            "top_k": enhanced_settings.RERANK_TOP_K,
            "batch_size": enhanced_settings.RERANK_BATCH_SIZE,
            "factors": {
                "semantic": enhanced_settings.SEMANTIC_WEIGHT,
                "behavior": enhanced_settings.BEHAVIOR_WEIGHT,
                "freshness": enhanced_settings.FRESHNESS_WEIGHT,
                "authority": enhanced_settings.AUTHORITY_WEIGHT
            }
        },
        
        # 多粒度分块配置
        "chunking": {
            "multi_granularity": enhanced_settings.MULTI_GRANULARITY_ENABLED,
            "types": enhanced_settings.CHUNK_TYPES,
            "weights": {
                "sentence": enhanced_settings.SENTENCE_CHUNK_WEIGHT,
                "semantic": enhanced_settings.SEMANTIC_CHUNK_WEIGHT,
                "sliding": enhanced_settings.SLIDING_CHUNK_WEIGHT,
                "structure": enhanced_settings.STRUCTURE_CHUNK_WEIGHT,
                "paragraph": enhanced_settings.PARAGRAPH_CHUNK_WEIGHT
            }
        },
        
        # 缓存配置
        "cache": {
            "enabled": enhanced_settings.CACHE_ENABLED,
            "ttl": enhanced_settings.CACHE_TTL,
            "max_size": enhanced_settings.CACHE_MAX_SIZE
        },
        
        # 性能配置
        "performance": {
            "max_concurrent": enhanced_settings.MAX_CONCURRENT_REQUESTS,
            "timeout": enhanced_settings.REQUEST_TIMEOUT,
            "batch_size": enhanced_settings.BATCH_PROCESSING_SIZE
        },
        
        # 质量控制配置
        "quality": {
            "min_similarity": enhanced_settings.MIN_SIMILARITY_THRESHOLD,
            "max_results": enhanced_settings.MAX_RESULT_COUNT,
            "filter_enabled": enhanced_settings.QUALITY_FILTER_ENABLED
        },
        
        # 用户行为配置
        "user_behavior": {
            "enabled": enhanced_settings.USER_BEHAVIOR_ENABLED,
            "cache_ttl": enhanced_settings.BEHAVIOR_CACHE_TTL,
            "min_interactions": enhanced_settings.MIN_USER_INTERACTIONS
        },
        
        # 时效性配置
        "freshness": {
            "decay_factor": enhanced_settings.FRESHNESS_DECAY_FACTOR,
            "max_age_days": enhanced_settings.MAX_CONTENT_AGE_DAYS
        },
        
        # 权威性配置
        "authority": {
            "source_weights": enhanced_settings.SOURCE_WEIGHTS
        },
        
        # A/B测试配置
        "ab_testing": {
            "enabled": enhanced_settings.AB_TESTING_ENABLED,
            "control_ratio": enhanced_settings.CONTROL_GROUP_RATIO,
            "treatment_ratio": enhanced_settings.TREATMENT_GROUP_RATIO
        },
        
        # 监控配置
        "monitoring": {
            "metrics_enabled": enhanced_settings.METRICS_ENABLED,
            "detailed_logging": enhanced_settings.DETAILED_LOGGING,
            "performance_tracking": enhanced_settings.PERFORMANCE_TRACKING
        }
    }


def update_config(updates: Dict[str, Any]) -> bool:
    """
    动态更新配置
    
    Args:
        updates: 配置更新字典
        
    Returns:
        是否更新成功
    """
    try:
        global enhanced_settings
        
        for key, value in updates.items():
            if hasattr(enhanced_settings, key.upper()):
                setattr(enhanced_settings, key.upper(), value)
            else:
                print(f"警告: 未知配置项 {key}")
        
        return True
        
    except Exception as e:
        print(f"配置更新失败: {e}")
        return False


def validate_config() -> List[str]:
    """
    验证配置有效性
    
    Returns:
        验证错误列表
    """
    errors = []
    
    # 权重和比例验证
    total_rerank_weight = (
        enhanced_settings.SEMANTIC_WEIGHT +
        enhanced_settings.BEHAVIOR_WEIGHT +
        enhanced_settings.FRESHNESS_WEIGHT +
        enhanced_settings.AUTHORITY_WEIGHT
    )
    
    if abs(total_rerank_weight - 1.0) > 0.01:
        errors.append(f"重排序因子权重总和应为1.0，当前为{total_rerank_weight}")
    
    # A/B测试比例验证
    total_ab_ratio = enhanced_settings.CONTROL_GROUP_RATIO + enhanced_settings.TREATMENT_GROUP_RATIO
    if abs(total_ab_ratio - 1.0) > 0.01:
        errors.append(f"A/B测试组比例总和应为1.0，当前为{total_ab_ratio}")
    
    # 数值范围验证
    if not 0.0 <= enhanced_settings.HYDE_TEMPERATURE <= 2.0:
        errors.append("HyDE温度应在0.0-2.0范围内")
    
    if enhanced_settings.MAX_QUERY_VARIANTS < 1:
        errors.append("最大查询变体数应大于0")
    
    if enhanced_settings.RERANK_TOP_K < 1:
        errors.append("重排序候选数量应大于0")
    
    # 模型名称验证
    if not enhanced_settings.CROSS_ENCODER_MODEL:
        errors.append("交叉编码器模型名称不能为空")
    
    return errors


def get_config_summary() -> Dict[str, Any]:
    """获取配置摘要"""
    return {
        "version": "1.0.0",
        "features": {
            "hyde": enhanced_settings.HYDE_ENABLED,
            "query_rewrite": enhanced_settings.QUERY_REWRITE_ENABLED,
            "reranking": enhanced_settings.RERANKING_ENABLED,
            "multi_granularity": enhanced_settings.MULTI_GRANULARITY_ENABLED,
            "user_behavior": enhanced_settings.USER_BEHAVIOR_ENABLED,
            "ab_testing": enhanced_settings.AB_TESTING_ENABLED
        },
        "performance": {
            "max_concurrent": enhanced_settings.MAX_CONCURRENT_REQUESTS,
            "timeout": enhanced_settings.REQUEST_TIMEOUT,
            "cache_enabled": enhanced_settings.CACHE_ENABLED
        },
        "quality": {
            "min_similarity": enhanced_settings.MIN_SIMILARITY_THRESHOLD,
            "max_results": enhanced_settings.MAX_RESULT_COUNT,
            "quality_filter": enhanced_settings.QUALITY_FILTER_ENABLED
        }
    }


# 配置验证（启动时执行）
def validate_startup_config():
    """启动时配置验证"""
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        raise ValueError("配置验证失败，请检查配置参数")
    else:
        print("配置验证通过")


# 导出配置获取函数
__all__ = [
    "enhanced_settings",
    "get_enhanced_retrieval_config",
    "update_config",
    "validate_config",
    "get_config_summary",
    "validate_startup_config"
]
