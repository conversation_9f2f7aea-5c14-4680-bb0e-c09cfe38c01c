"""
检索服务API路由
"""

import uuid
import time
import hashlib
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from loguru import logger

from .retrieval_engine import retrieval_engine
from .database import async_session, SearchQueryDAO, SearchResultDAO
from .redis_client import retrieval_cache, session_manager
from .config import settings

# 创建路由器
router = APIRouter()


# 请求模型
class SearchRequest(BaseModel):
    """搜索请求"""
    query: str = Field(..., description="搜索查询")
    search_type: str = Field(default="hybrid", description="搜索类型: semantic, keyword, hybrid")
    collection_name: Optional[str] = Field(None, description="集合名称")
    document_id: Optional[str] = Field(None, description="文档ID")
    top_k: int = Field(default=10, description="返回结果数量")
    score_threshold: Optional[float] = Field(None, description="分数阈值")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    model: Optional[str] = Field(None, description="嵌入模型")
    use_cache: bool = Field(default=True, description="是否使用缓存")
    session_id: Optional[str] = Field(None, description="会话ID")


class DocumentSearchRequest(BaseModel):
    """文档内搜索请求"""
    query: str = Field(..., description="搜索查询")
    document_id: str = Field(..., description="文档ID")
    search_type: str = Field(default="hybrid", description="搜索类型")
    top_k: int = Field(default=10, description="返回结果数量")
    score_threshold: Optional[float] = Field(None, description="分数阈值")
    model: Optional[str] = Field(None, description="嵌入模型")


class SimilarDocumentsRequest(BaseModel):
    """相似文档请求"""
    document_id: str = Field(..., description="文档ID")
    top_k: int = Field(default=5, description="返回结果数量")
    model: Optional[str] = Field(None, description="嵌入模型")


# 响应模型
class SearchResponse(BaseModel):
    """搜索响应"""
    success: bool
    data: Dict[str, Any]
    message: str
    query_id: Optional[str] = None


# API端点
@router.post("/search", response_model=SearchResponse)
async def search(request: SearchRequest):
    """通用搜索接口"""
    start_time = time.time()
    
    try:
        # 验证参数
        if request.top_k > settings.MAX_TOP_K:
            raise HTTPException(
                status_code=400, 
                detail=f"top_k不能超过{settings.MAX_TOP_K}"
            )
        
        # 执行搜索
        results = await retrieval_engine.search(
            query=request.query,
            search_type=request.search_type,
            collection_name=request.collection_name,
            document_id=request.document_id,
            top_k=request.top_k,
            score_threshold=request.score_threshold,
            filters=request.filters,
            model=request.model,
            use_cache=request.use_cache
        )
        
        response_time = int((time.time() - start_time) * 1000)
        
        # 记录搜索查询
        query_id = None
        try:
            async with async_session() as session:
                query_hash = hashlib.md5(request.query.encode()).hexdigest()
                search_query = await SearchQueryDAO.create(
                    session,
                    query_text=request.query,
                    query_hash=query_hash,
                    query_type=request.search_type,
                    top_k=request.top_k,
                    similarity_threshold=request.score_threshold,
                    filters=request.filters or {},
                    results_count=len(results),
                    response_time_ms=response_time
                )
                query_id = str(search_query.id)
                
                # 记录搜索结果
                if results:
                    result_records = []
                    for i, result in enumerate(results):
                        result_record = {
                            "query_id": search_query.id,
                            "document_id": uuid.UUID(result["document_id"]) if result.get("document_id") else None,
                            "chunk_id": uuid.UUID(result["chunk_id"]) if result.get("chunk_id") else None,
                            "rank": i + 1,
                            "score": result["score"],
                            "content": result["content"][:1000],  # 限制内容长度
                            "metadata": result.get("metadata", {}),
                            "source_type": result.get("source_type", request.search_type)
                        }
                        result_records.append(result_record)
                    
                    await SearchResultDAO.create_batch(session, result_records)
        
        except Exception as e:
            logger.error(f"记录搜索查询失败: {e}")
        
        # 更新会话历史
        if request.session_id:
            try:
                await session_manager.add_query_to_history(
                    request.session_id, 
                    request.query, 
                    len(results)
                )
            except Exception as e:
                logger.error(f"更新会话历史失败: {e}")
        
        return SearchResponse(
            success=True,
            data={
                "query": request.query,
                "search_type": request.search_type,
                "results": results,
                "total_results": len(results),
                "response_time_ms": response_time,
                "filters": request.filters
            },
            message=f"搜索完成，找到 {len(results)} 个结果",
            query_id=query_id
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.post("/search/document", response_model=SearchResponse)
async def search_document(request: DocumentSearchRequest):
    """文档内搜索"""
    start_time = time.time()
    
    try:
        results = await retrieval_engine.search(
            query=request.query,
            search_type=request.search_type,
            document_id=request.document_id,
            top_k=request.top_k,
            score_threshold=request.score_threshold,
            model=request.model
        )
        
        response_time = int((time.time() - start_time) * 1000)
        
        return SearchResponse(
            success=True,
            data={
                "query": request.query,
                "document_id": request.document_id,
                "search_type": request.search_type,
                "results": results,
                "total_results": len(results),
                "response_time_ms": response_time
            },
            message=f"文档内搜索完成，找到 {len(results)} 个结果"
        )
        
    except Exception as e:
        logger.error(f"文档内搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档内搜索失败: {str(e)}")


@router.post("/search/similar-documents", response_model=SearchResponse)
async def search_similar_documents(request: SimilarDocumentsRequest):
    """相似文档搜索"""
    start_time = time.time()
    
    try:
        results = await retrieval_engine.get_similar_documents(
            document_id=request.document_id,
            top_k=request.top_k,
            model=request.model
        )
        
        response_time = int((time.time() - start_time) * 1000)
        
        return SearchResponse(
            success=True,
            data={
                "document_id": request.document_id,
                "similar_documents": results,
                "total_results": len(results),
                "response_time_ms": response_time
            },
            message=f"相似文档搜索完成，找到 {len(results)} 个相似文档"
        )
        
    except Exception as e:
        logger.error(f"相似文档搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"相似文档搜索失败: {str(e)}")


@router.get("/search/suggestions")
async def get_search_suggestions(
    query: str = Query(..., description="部分查询文本"),
    limit: int = Query(default=5, description="建议数量")
):
    """获取搜索建议"""
    try:
        from .elasticsearch_client import es_client
        
        suggestions = await es_client.suggest_queries(query, limit)
        
        return {
            "success": True,
            "data": {
                "query": query,
                "suggestions": suggestions
            },
            "message": f"获取到 {len(suggestions)} 个搜索建议"
        }
        
    except Exception as e:
        logger.error(f"获取搜索建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")


@router.get("/query/{query_id}")
async def get_query_details(query_id: str):
    """获取查询详情"""
    try:
        async with async_session() as session:
            query_record = await SearchQueryDAO.get_by_id(session, uuid.UUID(query_id))
            
            if not query_record:
                raise HTTPException(status_code=404, detail="查询记录不存在")
            
            # 获取查询结果
            results = await SearchResultDAO.get_by_query_id(session, query_record.id)
            
            return {
                "success": True,
                "data": {
                    "query": {
                        "id": str(query_record.id),
                        "text": query_record.query_text,
                        "type": query_record.query_type,
                        "top_k": query_record.top_k,
                        "similarity_threshold": query_record.similarity_threshold,
                        "filters": query_record.filters,
                        "results_count": query_record.results_count,
                        "response_time_ms": query_record.response_time_ms,
                        "created_at": query_record.created_at.isoformat()
                    },
                    "results": [
                        {
                            "id": str(result.id),
                            "document_id": str(result.document_id) if result.document_id else None,
                            "chunk_id": str(result.chunk_id) if result.chunk_id else None,
                            "rank": result.rank,
                            "score": result.score,
                            "content": result.content,
                            "metadata": result.metadata,
                            "source_type": result.source_type
                        }
                        for result in results
                    ]
                },
                "message": "获取查询详情成功"
            }
            
    except Exception as e:
        logger.error(f"获取查询详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取查询详情失败: {str(e)}")


@router.get("/cache/stats")
async def get_cache_stats():
    """获取缓存统计"""
    try:
        stats = await retrieval_cache.get_cache_stats()
        
        return {
            "success": True,
            "data": stats,
            "message": "获取缓存统计成功"
        }
        
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@router.delete("/cache")
async def clear_cache(cache_type: str = Query(default="all", description="缓存类型")):
    """清除缓存"""
    try:
        success = await retrieval_cache.clear_cache(cache_type)
        
        if success:
            return {
                "success": True,
                "data": {"cache_type": cache_type},
                "message": f"清除 {cache_type} 缓存成功"
            }
        else:
            raise HTTPException(status_code=500, detail="清除缓存失败")
            
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查各组件状态
        from .vector_client import vector_client
        from .elasticsearch_client import es_client
        from .embedding_client import embedding_client
        
        # 并行检查各组件健康状态
        health_checks = await asyncio.gather(
            embedding_client.health_check(),
            es_client.health_check(),
            return_exceptions=True
        )
        
        embedding_health = health_checks[0] if not isinstance(health_checks[0], Exception) else {"status": "error"}
        es_health = health_checks[1] if not isinstance(health_checks[1], Exception) else {"status": "error"}
        
        # 检查缓存状态
        cache_stats = await retrieval_cache.get_cache_stats()
        
        overall_status = "healthy"
        if (embedding_health.get("status") != "healthy" or 
            es_health.get("status") not in ["green", "yellow"]):
            overall_status = "degraded"
        
        return {
            "success": True,
            "data": {
                "status": overall_status,
                "components": {
                    "embedding_service": embedding_health,
                    "elasticsearch": es_health,
                    "cache": {
                        "status": "healthy",
                        "stats": cache_stats
                    }
                },
                "config": {
                    "max_top_k": settings.MAX_TOP_K,
                    "default_top_k": settings.DEFAULT_TOP_K,
                    "cache_enabled": settings.ENABLE_QUERY_CACHE
                }
            },
            "message": "健康检查完成"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "data": {"error": str(e)},
            "message": "健康检查失败"
        }


# 导入asyncio
import asyncio
