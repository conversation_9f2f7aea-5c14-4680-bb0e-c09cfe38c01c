"""
嵌入服务客户端
与向量化服务通信获取文本嵌入向量
"""

import asyncio
from typing import List, Dict, Any, Optional
import aiohttp
from loguru import logger

from .config import settings, get_embedding_service_config
from .redis_client import retrieval_cache


class EmbeddingClient:
    """嵌入服务客户端"""
    
    def __init__(self):
        self.config = get_embedding_service_config()
        self.base_url = self.config["url"]
        self.timeout = self.config["timeout"]
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            logger.info("嵌入服务客户端连接已关闭")
    
    async def embed_text(
        self, 
        text: str, 
        model: Optional[str] = None,
        use_cache: bool = True
    ) -> Optional[List[float]]:
        """获取单个文本的嵌入向量"""
        try:
            # 检查缓存
            if use_cache:
                cached_embedding = await retrieval_cache.get_cached_embedding(text, model or "default")
                if cached_embedding:
                    logger.debug("嵌入向量缓存命中")
                    return cached_embedding
            
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/embed/text"
            
            payload = {"text": text}
            if model:
                payload["model"] = model
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        embedding = result["data"]["embedding"]
                        
                        # 缓存结果
                        if use_cache:
                            await retrieval_cache.cache_embedding(text, embedding, model or "default")
                        
                        logger.debug(f"获取嵌入向量成功，维度: {len(embedding)}")
                        return embedding
                    else:
                        logger.error(f"嵌入服务返回错误: {result.get('message')}")
                        return None
                else:
                    error_text = await response.text()
                    logger.error(f"嵌入服务请求失败: {response.status}, {error_text}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error("嵌入服务请求超时")
            return None
        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            return None
    
    async def embed_texts(
        self, 
        texts: List[str], 
        model: Optional[str] = None,
        use_cache: bool = True,
        batch_size: int = 10
    ) -> List[Optional[List[float]]]:
        """批量获取文本的嵌入向量"""
        try:
            all_embeddings = []
            
            # 分批处理
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                # 检查缓存
                batch_embeddings = []
                uncached_texts = []
                uncached_indices = []
                
                if use_cache:
                    for j, text in enumerate(batch_texts):
                        cached_embedding = await retrieval_cache.get_cached_embedding(text, model or "default")
                        if cached_embedding:
                            batch_embeddings.append(cached_embedding)
                        else:
                            batch_embeddings.append(None)
                            uncached_texts.append(text)
                            uncached_indices.append(j)
                else:
                    uncached_texts = batch_texts
                    uncached_indices = list(range(len(batch_texts)))
                    batch_embeddings = [None] * len(batch_texts)
                
                # 处理未缓存的文本
                if uncached_texts:
                    session = await self._get_session()
                    url = f"{self.base_url}/api/v1/embed/texts"
                    
                    payload = {"texts": uncached_texts}
                    if model:
                        payload["model"] = model
                    
                    async with session.post(url, json=payload) as response:
                        if response.status == 200:
                            result = await response.json()
                            if result.get("success"):
                                embeddings = result["data"]["embeddings"]
                                
                                # 填充结果并缓存
                                for k, embedding in enumerate(embeddings):
                                    original_index = uncached_indices[k]
                                    batch_embeddings[original_index] = embedding
                                    
                                    # 缓存结果
                                    if use_cache and embedding:
                                        await retrieval_cache.cache_embedding(
                                            uncached_texts[k], 
                                            embedding, 
                                            model or "default"
                                        )
                            else:
                                logger.error(f"批量嵌入服务返回错误: {result.get('message')}")
                                # 填充None值
                                for k in uncached_indices:
                                    batch_embeddings[k] = None
                        else:
                            error_text = await response.text()
                            logger.error(f"批量嵌入服务请求失败: {response.status}, {error_text}")
                            # 填充None值
                            for k in uncached_indices:
                                batch_embeddings[k] = None
                
                all_embeddings.extend(batch_embeddings)
            
            logger.debug(f"批量获取嵌入向量完成，处理 {len(texts)} 个文本")
            return all_embeddings
            
        except Exception as e:
            logger.error(f"批量获取嵌入向量失败: {e}")
            return [None] * len(texts)
    
    async def search_document(
        self,
        document_id: str,
        query: str,
        top_k: int = 10,
        model: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """在文档中搜索"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/search"
            
            payload = {
                "query": query,
                "document_id": document_id,
                "top_k": top_k
            }
            
            if model:
                payload["model"] = model
            if filter_dict:
                payload["filter"] = filter_dict
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        return result["data"]["results"]
                    else:
                        logger.error(f"文档搜索返回错误: {result.get('message')}")
                        return []
                else:
                    error_text = await response.text()
                    logger.error(f"文档搜索请求失败: {response.status}, {error_text}")
                    return []
                    
        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            return []
    
    async def get_embedding_models(self) -> List[Dict[str, Any]]:
        """获取可用的嵌入模型列表"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/models"
            
            async with session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        return result["data"]["loaded_models"]
                    else:
                        logger.error(f"获取模型列表返回错误: {result.get('message')}")
                        return []
                else:
                    error_text = await response.text()
                    logger.error(f"获取模型列表请求失败: {response.status}, {error_text}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """嵌入服务健康检查"""
        try:
            session = await self._get_session()
            url = f"{self.base_url}/api/v1/health"
            
            async with session.get(url) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "status": "healthy" if result.get("success") else "unhealthy",
                        "data": result.get("data", {}),
                        "message": result.get("message", "")
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": f"HTTP {response.status}",
                        "message": "嵌入服务不可用"
                    }
                    
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "message": "嵌入服务连接失败"
            }


# 创建全局嵌入客户端
embedding_client = EmbeddingClient()
