"""
增强检索引擎模块
实现HyDE、查询重写、多路召回等高级检索功能
"""

import asyncio
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import numpy as np
from dataclasses import dataclass

from .config import settings, get_retrieval_config
from .retrieval_engine import retrieval_engine
from .embedding_client import embedding_client
from .redis_client import retrieval_cache


@dataclass
class SearchResult:
    """检索结果数据类"""
    id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    source_type: str = "enhanced"
    rerank_score: Optional[float] = None


class QueryRewriter:
    """查询重写和扩展器"""
    
    def __init__(self):
        self.config = get_retrieval_config()
        self.synonym_cache = {}
        
    async def rewrite_query(self, query: str, context: Optional[Dict] = None) -> List[str]:
        """
        查询重写和扩展
        
        Args:
            query: 原始查询
            context: 上下文信息（对话历史、用户画像等）
            
        Returns:
            重写后的查询列表
        """
        try:
            logger.info(f"开始查询重写: {query[:50]}...")
            
            rewritten_queries = [query]  # 包含原始查询
            
            # 1. 同义词替换扩展
            synonym_queries = await self._expand_with_synonyms(query)
            rewritten_queries.extend(synonym_queries)
            
            # 2. 上下文补充
            if context and context.get('conversation_history'):
                context_queries = await self._enhance_with_context(query, context)
                rewritten_queries.extend(context_queries)
            
            # 3. 查询简化和复杂化
            simplified_query = await self._simplify_query(query)
            if simplified_query and simplified_query != query:
                rewritten_queries.append(simplified_query)
                
            detailed_query = await self._add_details(query)
            if detailed_query and detailed_query != query:
                rewritten_queries.append(detailed_query)
            
            # 4. 去重并限制数量
            unique_queries = list(dict.fromkeys(rewritten_queries))  # 保持顺序去重
            max_variants = self.config.get("max_query_variants", 5)
            
            logger.info(f"查询重写完成，生成 {len(unique_queries)} 个变体")
            return unique_queries[:max_variants]
            
        except Exception as e:
            logger.error(f"查询重写失败: {e}")
            return [query]  # 失败时返回原始查询
    
    async def _expand_with_synonyms(self, query: str) -> List[str]:
        """使用同义词扩展查询"""
        try:
            # 简单的同义词映射（实际应用中可以使用WordNet、ConceptNet等）
            synonym_map = {
                "问题": ["疑问", "难题", "困难"],
                "方法": ["方式", "途径", "手段"],
                "解决": ["处理", "解答", "应对"],
                "系统": ["体系", "平台", "框架"],
                "优化": ["改进", "提升", "完善"],
                "性能": ["效果", "表现", "效率"],
                "实现": ["完成", "达成", "执行"],
                "分析": ["研究", "探讨", "评估"]
            }
            
            expanded_queries = []
            words = query.split()
            
            for word in words:
                if word in synonym_map:
                    for synonym in synonym_map[word]:
                        new_query = query.replace(word, synonym)
                        if new_query != query:
                            expanded_queries.append(new_query)
            
            return expanded_queries[:3]  # 最多返回3个同义词变体
            
        except Exception as e:
            logger.error(f"同义词扩展失败: {e}")
            return []
    
    async def _enhance_with_context(self, query: str, context: Dict) -> List[str]:
        """基于上下文增强查询"""
        try:
            enhanced_queries = []
            conversation_history = context.get('conversation_history', [])
            
            if conversation_history:
                # 获取最近的对话内容
                recent_messages = conversation_history[-3:]  # 最近3条消息
                context_keywords = []
                
                for msg in recent_messages:
                    if msg.get('role') == 'user':
                        # 提取用户消息中的关键词
                        words = msg.get('content', '').split()
                        context_keywords.extend([w for w in words if len(w) > 2])
                
                # 将上下文关键词添加到查询中
                if context_keywords:
                    unique_keywords = list(set(context_keywords))[:3]  # 最多3个关键词
                    enhanced_query = f"{query} {' '.join(unique_keywords)}"
                    enhanced_queries.append(enhanced_query)
            
            return enhanced_queries
            
        except Exception as e:
            logger.error(f"上下文增强失败: {e}")
            return []
    
    async def _simplify_query(self, query: str) -> Optional[str]:
        """简化查询（移除修饰词、保留核心概念）"""
        try:
            # 移除常见的修饰词和停用词
            stop_words = {"的", "了", "在", "是", "有", "和", "与", "或", "但是", "然而", "因此", "所以"}
            words = query.split()
            simplified_words = [w for w in words if w not in stop_words and len(w) > 1]
            
            if len(simplified_words) < len(words) and simplified_words:
                return " ".join(simplified_words)
            
            return None
            
        except Exception as e:
            logger.error(f"查询简化失败: {e}")
            return None
    
    async def _add_details(self, query: str) -> Optional[str]:
        """为查询添加详细信息"""
        try:
            # 根据查询类型添加相关的详细描述
            detail_templates = {
                "如何": "具体步骤和方法",
                "什么": "定义和解释",
                "为什么": "原因和机制",
                "优化": "性能提升和改进方案",
                "实现": "技术实现和代码示例",
                "配置": "设置参数和配置方法"
            }
            
            for keyword, detail in detail_templates.items():
                if keyword in query:
                    return f"{query} {detail}"
            
            return None
            
        except Exception as e:
            logger.error(f"查询详化失败: {e}")
            return None


class HyDEGenerator:
    """假设性文档生成器（Hypothetical Document Embeddings）"""
    
    def __init__(self):
        self.config = get_retrieval_config()
        
    async def generate_hypothetical_document(self, query: str) -> str:
        """
        生成假设性文档
        
        Args:
            query: 用户查询
            
        Returns:
            假设性文档内容
        """
        try:
            logger.info(f"生成假设性文档: {query[:50]}...")
            
            # 构建提示词模板
            prompt = self._build_hyde_prompt(query)
            
            # 调用LLM生成假设性文档
            # 这里使用简化的实现，实际应该调用生成服务
            hypothetical_doc = await self._call_llm_for_hyde(prompt)
            
            logger.info(f"假设性文档生成完成，长度: {len(hypothetical_doc)}")
            return hypothetical_doc
            
        except Exception as e:
            logger.error(f"假设性文档生成失败: {e}")
            return query  # 失败时返回原始查询
    
    def _build_hyde_prompt(self, query: str) -> str:
        """构建HyDE提示词"""
        prompt = f"""
请基于以下查询生成一个详细的假设性答案。答案应该：
1. 直接回答查询问题
2. 包含相关的技术细节和具体信息
3. 使用专业术语和准确的表达
4. 长度在200-400字之间
5. 结构清晰，逻辑性强

查询：{query}

假设性答案：
"""
        return prompt
    
    async def _call_llm_for_hyde(self, prompt: str) -> str:
        """调用LLM生成假设性文档"""
        try:
            # 这里应该调用实际的LLM服务
            # 为了演示，使用简化的模板生成
            
            # 检查缓存
            cache_key = f"hyde:{hashlib.md5(prompt.encode()).hexdigest()}"
            cached_result = await retrieval_cache.get(cache_key)
            if cached_result:
                return cached_result
            
            # 简化的假设性文档生成（实际应该调用LLM API）
            hypothetical_doc = await self._generate_template_response(prompt)
            
            # 缓存结果
            await retrieval_cache.set(cache_key, hypothetical_doc, ttl=3600)
            
            return hypothetical_doc
            
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            return "这是一个关于查询问题的详细技术说明和解决方案。"
    
    async def _generate_template_response(self, prompt: str) -> str:
        """生成模板响应（简化实现）"""
        # 这是一个简化的实现，实际应该调用真正的LLM
        query_keywords = prompt.split("查询：")[1].split("\n")[0] if "查询：" in prompt else ""
        
        template_response = f"""
针对"{query_keywords}"这个问题，可以从以下几个方面来理解和解决：

首先，需要明确问题的核心需求和技术背景。在实际应用中，这类问题通常涉及到系统架构设计、性能优化、以及最佳实践的选择。

从技术实现角度来看，解决方案应该考虑以下关键要素：
1. 系统的可扩展性和稳定性
2. 性能优化和资源利用效率
3. 代码的可维护性和可读性
4. 安全性和数据保护

具体的实现步骤包括：分析需求、设计架构、编码实现、测试验证、部署上线等环节。每个环节都需要遵循相应的技术标准和最佳实践。

在实际操作过程中，还需要注意监控系统运行状态，及时发现和解决潜在问题，确保系统的长期稳定运行。
"""
        return template_response.strip()
    
    async def hyde_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        使用HyDE进行检索
        
        Args:
            query: 原始查询
            top_k: 返回结果数量
            
        Returns:
            检索结果列表
        """
        try:
            logger.info(f"开始HyDE检索: {query[:50]}...")
            
            # 1. 生成假设性文档
            hypothetical_doc = await self.generate_hypothetical_document(query)
            
            # 2. 对假设性文档进行向量化
            hyp_embedding = await embedding_client.embed_text(hypothetical_doc)
            if not hyp_embedding:
                logger.warning("假设性文档向量化失败，使用原始查询")
                return await retrieval_engine.search(query, top_k=top_k)
            
            # 3. 使用假设性文档向量进行检索
            hyp_results = await retrieval_engine.semantic_search(
                query=hypothetical_doc,
                top_k=top_k * 2  # 获取更多候选结果
            )
            
            # 4. 原始查询检索作为补充
            query_results = await retrieval_engine.semantic_search(
                query=query,
                top_k=top_k
            )
            
            # 5. 结果融合和去重
            merged_results = self._merge_hyde_results(hyp_results, query_results, top_k)
            
            logger.info(f"HyDE检索完成，返回 {len(merged_results)} 个结果")
            return merged_results
            
        except Exception as e:
            logger.error(f"HyDE检索失败: {e}")
            # 失败时回退到普通检索
            return await retrieval_engine.search(query, top_k=top_k)
    
    def _merge_hyde_results(self, hyp_results: List[Dict], query_results: List[Dict], top_k: int) -> List[SearchResult]:
        """融合HyDE结果和原始查询结果"""
        try:
            # 创建结果字典，以ID为键避免重复
            merged_dict = {}
            
            # 处理HyDE结果（权重0.6）
            for i, result in enumerate(hyp_results):
                result_id = result.get("id") or result.get("chunk_id")
                if result_id:
                    score = result["score"] * 0.6  # HyDE结果权重
                    merged_dict[result_id] = SearchResult(
                        id=result_id,
                        content=result["content"],
                        score=score,
                        metadata=result["metadata"],
                        source_type="hyde"
                    )
            
            # 处理原始查询结果（权重0.4）
            for i, result in enumerate(query_results):
                result_id = result.get("id") or result.get("chunk_id")
                if result_id:
                    if result_id in merged_dict:
                        # 如果已存在，更新分数（加权平均）
                        existing_score = merged_dict[result_id].score
                        new_score = result["score"] * 0.4
                        merged_dict[result_id].score = existing_score + new_score
                        merged_dict[result_id].source_type = "hybrid"
                    else:
                        # 新结果
                        score = result["score"] * 0.4
                        merged_dict[result_id] = SearchResult(
                            id=result_id,
                            content=result["content"],
                            score=score,
                            metadata=result["metadata"],
                            source_type="query"
                        )
            
            # 按分数排序并返回top-k
            merged_results = list(merged_dict.values())
            merged_results.sort(key=lambda x: x.score, reverse=True)
            
            return merged_results[:top_k]
            
        except Exception as e:
            logger.error(f"HyDE结果融合失败: {e}")
            return []


class EnhancedRetrievalEngine:
    """增强检索引擎"""
    
    def __init__(self):
        self.config = get_retrieval_config()
        self.query_rewriter = QueryRewriter()
        self.hyde_generator = HyDEGenerator()
        
        # 功能开关
        self.enable_query_rewrite = self.config.get("enable_query_rewrite", True)
        self.enable_hyde = self.config.get("enable_hyde", True)
        
    async def enhanced_search(
        self,
        query: str,
        search_type: str = "enhanced",
        top_k: int = 10,
        context: Optional[Dict] = None,
        **kwargs
    ) -> List[SearchResult]:
        """
        增强检索主入口
        
        Args:
            query: 用户查询
            search_type: 检索类型 ("enhanced", "hyde", "rewrite", "baseline")
            top_k: 返回结果数量
            context: 上下文信息
            **kwargs: 其他参数
            
        Returns:
            检索结果列表
        """
        try:
            logger.info(f"开始增强检索: {query[:50]}..., 类型: {search_type}")
            
            if search_type == "baseline":
                # 基线检索（不使用增强功能）
                return await self._baseline_search(query, top_k, **kwargs)
            
            elif search_type == "hyde":
                # 仅使用HyDE
                return await self.hyde_generator.hyde_search(query, top_k)
            
            elif search_type == "rewrite":
                # 仅使用查询重写
                return await self._rewrite_search(query, top_k, context, **kwargs)
            
            elif search_type == "enhanced":
                # 全功能增强检索
                return await self._full_enhanced_search(query, top_k, context, **kwargs)
            
            else:
                logger.warning(f"未知的检索类型: {search_type}，使用基线检索")
                return await self._baseline_search(query, top_k, **kwargs)
                
        except Exception as e:
            logger.error(f"增强检索失败: {e}")
            # 失败时回退到基线检索
            return await self._baseline_search(query, top_k, **kwargs)
    
    async def _baseline_search(self, query: str, top_k: int, **kwargs) -> List[SearchResult]:
        """基线检索（使用原有检索引擎）"""
        results = await retrieval_engine.search(query, top_k=top_k, **kwargs)
        return [SearchResult(
            id=r.get("id", ""),
            content=r.get("content", ""),
            score=r.get("score", 0.0),
            metadata=r.get("metadata", {}),
            source_type="baseline"
        ) for r in results]
    
    async def _rewrite_search(self, query: str, top_k: int, context: Optional[Dict], **kwargs) -> List[SearchResult]:
        """基于查询重写的检索"""
        if not self.enable_query_rewrite:
            return await self._baseline_search(query, top_k, **kwargs)
        
        # 生成查询变体
        query_variants = await self.query_rewriter.rewrite_query(query, context)
        
        # 并行检索所有查询变体
        all_results = []
        tasks = []
        
        for variant in query_variants:
            task = retrieval_engine.search(variant, top_k=top_k, **kwargs)
            tasks.append(task)
        
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        for i, results in enumerate(results_list):
            if isinstance(results, Exception):
                logger.error(f"查询变体 {i} 检索失败: {results}")
                continue
            
            for result in results:
                all_results.append(SearchResult(
                    id=result.get("id", ""),
                    content=result.get("content", ""),
                    score=result.get("score", 0.0),
                    metadata=result.get("metadata", {}),
                    source_type="rewrite"
                ))
        
        # 去重和排序
        return self._deduplicate_and_rank(all_results, top_k)
    
    async def _full_enhanced_search(self, query: str, top_k: int, context: Optional[Dict], **kwargs) -> List[SearchResult]:
        """全功能增强检索"""
        all_results = []
        
        # 1. HyDE检索
        if self.enable_hyde:
            hyde_results = await self.hyde_generator.hyde_search(query, top_k)
            all_results.extend(hyde_results)
        
        # 2. 查询重写检索
        if self.enable_query_rewrite:
            rewrite_results = await self._rewrite_search(query, top_k, context, **kwargs)
            all_results.extend(rewrite_results)
        
        # 3. 基线检索作为补充
        baseline_results = await self._baseline_search(query, top_k, **kwargs)
        all_results.extend(baseline_results)
        
        # 4. 结果融合和排序
        final_results = self._deduplicate_and_rank(all_results, top_k)
        
        logger.info(f"全功能增强检索完成，返回 {len(final_results)} 个结果")
        return final_results
    
    def _deduplicate_and_rank(self, results: List[SearchResult], top_k: int) -> List[SearchResult]:
        """去重和重新排序"""
        try:
            # 按ID去重，保留最高分数的结果
            unique_results = {}
            
            for result in results:
                if result.id in unique_results:
                    if result.score > unique_results[result.id].score:
                        unique_results[result.id] = result
                else:
                    unique_results[result.id] = result
            
            # 按分数排序
            sorted_results = list(unique_results.values())
            sorted_results.sort(key=lambda x: x.score, reverse=True)
            
            return sorted_results[:top_k]
            
        except Exception as e:
            logger.error(f"去重排序失败: {e}")
            return results[:top_k]


# 创建全局增强检索引擎实例
enhanced_retrieval_engine = EnhancedRetrievalEngine()
