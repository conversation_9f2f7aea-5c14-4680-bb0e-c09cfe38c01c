"""
Redis客户端模块
用于缓存查询结果和会话管理
"""

import json
import pickle
import hashlib
from typing import Any, Optional, List, Dict
import aioredis
from loguru import logger

from .config import settings

# 全局Redis客户端
redis_client: Optional[aioredis.Redis] = None


async def init_redis():
    """初始化Redis连接"""
    global redis_client
    
    try:
        redis_client = aioredis.from_url(
            settings.REDIS_URL,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD,
            encoding="utf-8",
            decode_responses=False,  # 保持二进制数据
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # 测试连接
        await redis_client.ping()
        logger.info("Redis连接成功")
        
    except Exception as e:
        logger.error(f"Redis连接失败: {e}")
        raise


async def close_redis():
    """关闭Redis连接"""
    global redis_client
    
    if redis_client:
        await redis_client.close()
        logger.info("Redis连接已关闭")


def get_redis_client() -> aioredis.Redis:
    """获取Redis客户端"""
    if not redis_client:
        raise RuntimeError("Redis未初始化")
    return redis_client


class RetrievalCache:
    """检索缓存操作类"""
    
    def __init__(self, prefix: str = "retrieval"):
        self.prefix = prefix
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.prefix}:{key}"
    
    def _generate_query_hash(self, query: str, filters: Dict[str, Any] = None) -> str:
        """生成查询哈希"""
        query_data = {
            "query": query.strip().lower(),
            "filters": filters or {}
        }
        query_str = json.dumps(query_data, sort_keys=True)
        return hashlib.md5(query_str.encode()).hexdigest()
    
    async def cache_query_result(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        filters: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """缓存查询结果"""
        try:
            client = get_redis_client()
            query_hash = self._generate_query_hash(query, filters)
            cache_key = self._make_key(f"query:{query_hash}")
            
            cache_data = {
                "query": query,
                "filters": filters or {},
                "results": results,
                "cached_at": int(time.time())
            }
            
            serialized_data = pickle.dumps(cache_data)
            cache_ttl = ttl or settings.CACHE_TTL
            
            await client.setex(cache_key, cache_ttl, serialized_data)
            logger.debug(f"查询结果已缓存: {query_hash}")
            return True
            
        except Exception as e:
            logger.error(f"缓存查询结果失败: {e}")
            return False
    
    async def get_cached_query_result(
        self, 
        query: str, 
        filters: Dict[str, Any] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """获取缓存的查询结果"""
        try:
            if not settings.ENABLE_QUERY_CACHE:
                return None
                
            client = get_redis_client()
            query_hash = self._generate_query_hash(query, filters)
            cache_key = self._make_key(f"query:{query_hash}")
            
            cached_data = await client.get(cache_key)
            if cached_data is None:
                logger.debug(f"查询缓存未命中: {query_hash}")
                return None
            
            cache_data = pickle.loads(cached_data)
            logger.debug(f"查询缓存命中: {query_hash}")
            return cache_data["results"]
            
        except Exception as e:
            logger.error(f"获取缓存查询结果失败: {e}")
            return None
    
    async def cache_document_chunks(
        self, 
        document_id: str, 
        chunks: List[Dict[str, Any]],
        ttl: Optional[int] = None
    ) -> bool:
        """缓存文档块"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(f"document:{document_id}:chunks")
            
            serialized_data = pickle.dumps(chunks)
            cache_ttl = ttl or settings.CACHE_TTL
            
            await client.setex(cache_key, cache_ttl, serialized_data)
            logger.debug(f"文档块已缓存: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"缓存文档块失败: {e}")
            return False
    
    async def get_cached_document_chunks(self, document_id: str) -> Optional[List[Dict[str, Any]]]:
        """获取缓存的文档块"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(f"document:{document_id}:chunks")
            
            cached_data = await client.get(cache_key)
            if cached_data is None:
                return None
            
            chunks = pickle.loads(cached_data)
            logger.debug(f"文档块缓存命中: {document_id}")
            return chunks
            
        except Exception as e:
            logger.error(f"获取缓存文档块失败: {e}")
            return None
    
    async def cache_embedding(
        self, 
        text: str, 
        embedding: List[float],
        model: str = "default",
        ttl: Optional[int] = None
    ) -> bool:
        """缓存文本嵌入向量"""
        try:
            client = get_redis_client()
            text_hash = hashlib.md5(text.encode()).hexdigest()
            cache_key = self._make_key(f"embedding:{model}:{text_hash}")
            
            cache_data = {
                "text": text,
                "embedding": embedding,
                "model": model,
                "cached_at": int(time.time())
            }
            
            serialized_data = pickle.dumps(cache_data)
            cache_ttl = ttl or (settings.CACHE_TTL * 2)  # 嵌入向量缓存更长时间
            
            await client.setex(cache_key, cache_ttl, serialized_data)
            return True
            
        except Exception as e:
            logger.error(f"缓存嵌入向量失败: {e}")
            return False
    
    async def get_cached_embedding(
        self, 
        text: str, 
        model: str = "default"
    ) -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        try:
            client = get_redis_client()
            text_hash = hashlib.md5(text.encode()).hexdigest()
            cache_key = self._make_key(f"embedding:{model}:{text_hash}")
            
            cached_data = await client.get(cache_key)
            if cached_data is None:
                return None
            
            cache_data = pickle.loads(cached_data)
            return cache_data["embedding"]
            
        except Exception as e:
            logger.error(f"获取缓存嵌入向量失败: {e}")
            return None
    
    async def invalidate_document_cache(self, document_id: str) -> bool:
        """使文档相关缓存失效"""
        try:
            client = get_redis_client()
            
            # 删除文档块缓存
            chunk_key = self._make_key(f"document:{document_id}:chunks")
            await client.delete(chunk_key)
            
            # 删除相关查询缓存（通过模式匹配）
            pattern = self._make_key("query:*")
            keys = await client.keys(pattern)
            
            if keys:
                # 检查每个查询缓存是否包含该文档
                for key in keys:
                    try:
                        cached_data = await client.get(key)
                        if cached_data:
                            cache_data = pickle.loads(cached_data)
                            results = cache_data.get("results", [])
                            
                            # 如果结果中包含该文档，删除缓存
                            for result in results:
                                if result.get("document_id") == document_id:
                                    await client.delete(key)
                                    break
                    except:
                        continue
            
            logger.info(f"文档缓存已失效: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"使文档缓存失效失败: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            client = get_redis_client()
            
            # 获取不同类型的缓存数量
            query_pattern = self._make_key("query:*")
            document_pattern = self._make_key("document:*")
            embedding_pattern = self._make_key("embedding:*")
            
            query_keys = await client.keys(query_pattern)
            document_keys = await client.keys(document_pattern)
            embedding_keys = await client.keys(embedding_pattern)
            
            # 获取内存使用情况
            info = await client.info("memory")
            
            return {
                "query_cache_count": len(query_keys),
                "document_cache_count": len(document_keys),
                "embedding_cache_count": len(embedding_keys),
                "total_cache_count": len(query_keys) + len(document_keys) + len(embedding_keys),
                "memory_used": info.get("used_memory_human", "unknown"),
                "memory_peak": info.get("used_memory_peak_human", "unknown")
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    async def clear_cache(self, cache_type: str = "all") -> bool:
        """清除缓存"""
        try:
            client = get_redis_client()
            
            if cache_type == "all":
                pattern = self._make_key("*")
            elif cache_type == "query":
                pattern = self._make_key("query:*")
            elif cache_type == "document":
                pattern = self._make_key("document:*")
            elif cache_type == "embedding":
                pattern = self._make_key("embedding:*")
            else:
                raise ValueError(f"不支持的缓存类型: {cache_type}")
            
            keys = await client.keys(pattern)
            if keys:
                await client.delete(*keys)
                logger.info(f"已清除 {len(keys)} 个 {cache_type} 缓存")
            
            return True
            
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            return False


class SessionManager:
    """会话管理器"""
    
    def __init__(self, prefix: str = "session"):
        self.prefix = prefix
        self.default_ttl = 3600  # 1小时
    
    def _make_key(self, session_id: str) -> str:
        """生成会话键"""
        return f"{self.prefix}:{session_id}"
    
    async def create_session(self, session_id: str, user_data: Dict[str, Any]) -> bool:
        """创建会话"""
        try:
            client = get_redis_client()
            session_key = self._make_key(session_id)
            
            session_data = {
                "user_data": user_data,
                "created_at": int(time.time()),
                "last_activity": int(time.time()),
                "query_history": []
            }
            
            serialized_data = pickle.dumps(session_data)
            await client.setex(session_key, self.default_ttl, serialized_data)
            
            logger.debug(f"会话已创建: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话数据"""
        try:
            client = get_redis_client()
            session_key = self._make_key(session_id)
            
            session_data = await client.get(session_key)
            if session_data is None:
                return None
            
            return pickle.loads(session_data)
            
        except Exception as e:
            logger.error(f"获取会话失败: {e}")
            return None
    
    async def update_session_activity(self, session_id: str) -> bool:
        """更新会话活动时间"""
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                return False
            
            session_data["last_activity"] = int(time.time())
            
            client = get_redis_client()
            session_key = self._make_key(session_id)
            serialized_data = pickle.dumps(session_data)
            
            await client.setex(session_key, self.default_ttl, serialized_data)
            return True
            
        except Exception as e:
            logger.error(f"更新会话活动失败: {e}")
            return False
    
    async def add_query_to_history(self, session_id: str, query: str, results_count: int) -> bool:
        """添加查询到历史记录"""
        try:
            session_data = await self.get_session(session_id)
            if not session_data:
                return False
            
            query_record = {
                "query": query,
                "results_count": results_count,
                "timestamp": int(time.time())
            }
            
            session_data["query_history"].append(query_record)
            
            # 保持最近50条查询记录
            if len(session_data["query_history"]) > 50:
                session_data["query_history"] = session_data["query_history"][-50:]
            
            session_data["last_activity"] = int(time.time())
            
            client = get_redis_client()
            session_key = self._make_key(session_id)
            serialized_data = pickle.dumps(session_data)
            
            await client.setex(session_key, self.default_ttl, serialized_data)
            return True
            
        except Exception as e:
            logger.error(f"添加查询历史失败: {e}")
            return False


# 导入time模块
import time

# 创建全局实例
retrieval_cache = RetrievalCache()
session_manager = SessionManager()
