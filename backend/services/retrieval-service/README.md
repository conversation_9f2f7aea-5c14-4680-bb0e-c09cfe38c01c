# 检索服务 (Retrieval Service)

RAG系统的检索服务，提供语义检索、关键词检索和混合检索功能，支持多种数据源和检索策略。

## 📋 功能特性

### 🔍 多种检索方式
- **语义检索**: 基于向量相似度的语义搜索
- **关键词检索**: 基于Elasticsearch的全文搜索
- **混合检索**: 结合语义和关键词检索的混合搜索
- **相似文档**: 基于文档内容的相似文档推荐

### 🎯 智能检索优化
- **结果重排序**: 基于多种信号的智能重排序
- **分数融合**: 语义和关键词分数的智能融合
- **阈值过滤**: 支持相似度阈值过滤
- **结果去重**: 智能去重和结果合并

### ⚡ 高性能缓存
- **查询缓存**: Redis缓存热门查询结果
- **嵌入缓存**: 缓存文本嵌入向量
- **会话管理**: 用户会话和查询历史管理
- **智能失效**: 文档更新时的缓存失效

### 📊 检索分析
- **查询统计**: 详细的查询和结果统计
- **性能监控**: 响应时间和成功率监控
- **热门查询**: 热门查询词分析
- **用户行为**: 用户搜索行为分析

## 🏗️ 技术架构

### 技术栈
- **框架**: FastAPI + Uvicorn
- **语言**: Python 3.11+
- **数据库**: PostgreSQL + Redis
- **搜索引擎**: Elasticsearch
- **向量库**: ChromaDB/Pinecone

### 核心组件
```
Retrieval Service
├── 检索引擎        # 多种检索策略实现
├── 向量客户端      # 向量数据库接口
├── ES客户端       # Elasticsearch接口
├── 嵌入客户端      # 向量化服务接口
├── 缓存管理       # Redis缓存和会话
├── 结果重排序      # 智能结果优化
└── 分析统计       # 检索效果分析
```

## 🚀 快速开始

### 环境要求
- Python >= 3.11
- PostgreSQL >= 14.0
- Redis >= 6.0
- Elasticsearch >= 8.0
- ChromaDB或Pinecone

### 安装依赖
```bash
pip install -r requirements.txt
```

### 环境配置
```bash
cp .env.example .env
```

主要配置项：
```env
# 服务配置
HOST=0.0.0.0
PORT=8002
DEBUG=false

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/retrieval_service

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# 向量数据库配置
VECTOR_DB_TYPE=chroma
CHROMA_URL=http://localhost:8000

# Elasticsearch配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=documents

# 嵌入服务配置
EMBEDDING_SERVICE_URL=http://localhost:8001

# 检索配置
DEFAULT_TOP_K=10
MAX_TOP_K=100
DEFAULT_SIMILARITY_THRESHOLD=0.7
SEMANTIC_WEIGHT=0.7
KEYWORD_WEIGHT=0.3

# 缓存配置
CACHE_TTL=3600
ENABLE_QUERY_CACHE=true
ENABLE_RESULT_CACHE=true
```

### 运行服务

#### 开发模式
```bash
python main.py
```

#### 生产模式
```bash
uvicorn main:app --host 0.0.0.0 --port 8002 --workers 4
```

#### 使用Docker
```bash
docker build -t retrieval-service .
docker run -p 8002:8002 retrieval-service
```

## 📡 API 接口

### 通用搜索
```http
POST /api/v1/search
Content-Type: application/json

{
  "query": "搜索查询文本",
  "search_type": "hybrid",
  "top_k": 10,
  "score_threshold": 0.7,
  "filters": {"document_type": "pdf"},
  "model": "text-embedding-ada-002",
  "use_cache": true,
  "session_id": "session-123"
}
```

### 文档内搜索
```http
POST /api/v1/search/document
Content-Type: application/json

{
  "query": "搜索查询文本",
  "document_id": "doc-123",
  "search_type": "hybrid",
  "top_k": 10,
  "score_threshold": 0.7
}
```

### 相似文档搜索
```http
POST /api/v1/search/similar-documents
Content-Type: application/json

{
  "document_id": "doc-123",
  "top_k": 5,
  "model": "text-embedding-ada-002"
}
```

### 搜索建议
```http
GET /api/v1/search/suggestions?query=部分查询&limit=5
```

### 查询详情
```http
GET /api/v1/query/{query_id}
```

### 缓存管理
```http
# 获取缓存统计
GET /api/v1/cache/stats

# 清除缓存
DELETE /api/v1/cache?cache_type=all
```

### 健康检查
```http
GET /api/v1/health
```

## 🔧 配置说明

### 检索策略配置
```python
RETRIEVAL_CONFIG = {
    "semantic_weight": 0.7,      # 语义检索权重
    "keyword_weight": 0.3,       # 关键词检索权重
    "enable_reranking": True,    # 启用重排序
    "rerank_top_k": 50,         # 重排序候选数量
    "similarity_threshold": 0.7  # 相似度阈值
}
```

### 缓存策略配置
```python
CACHE_CONFIG = {
    "ttl": 3600,                    # 缓存过期时间
    "enable_query_cache": True,     # 启用查询缓存
    "enable_result_cache": True,    # 启用结果缓存
    "enable_embedding_cache": True  # 启用嵌入缓存
}
```

### 性能配置
```python
PERFORMANCE_CONFIG = {
    "max_concurrent_requests": 100,  # 最大并发请求
    "request_timeout": 30,          # 请求超时时间
    "batch_size": 32,               # 批处理大小
    "max_top_k": 100               # 最大返回结果数
}
```

## 📊 数据模型

### 搜索查询表
```sql
CREATE TABLE search_queries (
    id UUID PRIMARY KEY,
    query_text TEXT NOT NULL,
    query_hash VARCHAR(64) NOT NULL,
    query_type VARCHAR(20) NOT NULL,
    user_id UUID,
    session_id VARCHAR(100),
    top_k INTEGER DEFAULT 10,
    similarity_threshold FLOAT DEFAULT 0.7,
    filters JSONB DEFAULT '{}',
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 搜索结果表
```sql
CREATE TABLE search_results (
    id UUID PRIMARY KEY,
    query_id UUID NOT NULL,
    document_id UUID NOT NULL,
    chunk_id UUID NOT NULL,
    rank INTEGER NOT NULL,
    score FLOAT NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    source_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
pytest tests/

# 集成测试
pytest tests/integration/

# 性能测试
pytest tests/performance/
```

### 测试搜索
```bash
curl -X POST http://localhost:8002/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "人工智能的发展历程",
    "search_type": "hybrid",
    "top_k": 10
  }'
```

### 测试文档内搜索
```bash
curl -X POST http://localhost:8002/api/v1/search/document \
  -H "Content-Type: application/json" \
  -d '{
    "query": "机器学习算法",
    "document_id": "doc-123",
    "top_k": 5
  }'
```

## 📈 性能优化

### 检索优化
1. **并行检索**: 语义和关键词检索并行执行
2. **结果缓存**: 热门查询结果缓存
3. **嵌入缓存**: 文本嵌入向量缓存
4. **批量处理**: 批量获取嵌入向量

### 缓存策略
1. **分层缓存**: 查询、结果、嵌入多层缓存
2. **智能失效**: 文档更新时相关缓存失效
3. **LRU策略**: 最近最少使用缓存淘汰
4. **压缩存储**: 大对象压缩存储

### 数据库优化
1. **索引优化**: 查询字段和时间字段索引
2. **分区表**: 按时间分区大表
3. **连接池**: 数据库连接池管理
4. **读写分离**: 读写分离提升性能

## 🚨 故障排查

### 常见问题

1. **检索结果为空**
   ```bash
   # 检查向量数据库连接
   curl http://localhost:8000/api/v1/heartbeat
   
   # 检查Elasticsearch状态
   curl http://localhost:9200/_cluster/health
   
   # 检查嵌入服务
   curl http://localhost:8001/api/v1/health
   ```

2. **检索速度慢**
   ```bash
   # 检查缓存命中率
   curl http://localhost:8002/api/v1/cache/stats
   
   # 查看性能日志
   tail -f logs/retrieval_service.log
   ```

3. **缓存问题**
   ```bash
   # 清除所有缓存
   curl -X DELETE http://localhost:8002/api/v1/cache?cache_type=all
   
   # 检查Redis状态
   redis-cli ping
   ```

### 监控指标
- 检索响应时间
- 缓存命中率
- 查询成功率
- 结果相关性
- 系统资源使用

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
