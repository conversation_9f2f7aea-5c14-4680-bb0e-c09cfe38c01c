# RAG检索系统运维手册

## 概述

本手册为RAG检索系统的运维人员提供日常运维、故障处理、性能优化等操作指南。

## 系统概览

### 服务组件

- **检索服务**: 核心API服务 (端口8000)
- **数据库**: PostgreSQL (端口5432)
- **缓存**: Redis (端口6379)
- **搜索引擎**: Elasticsearch (端口9200)
- **监控**: Prometheus (端口9090)
- **可视化**: Grafana (端口3000)

### 部署架构

```
负载均衡器 (Nginx)
    ↓
检索服务集群 (3-20个实例)
    ↓
数据层 (PostgreSQL + Redis + Elasticsearch)
    ↓
监控层 (Prometheus + Grafana)
```

## 日常运维

### 1. 服务状态检查

**健康检查**:
```bash
# 检查服务状态
curl -f http://localhost:8000/health

# 检查所有Pod状态
kubectl get pods -n retrieval-service

# 检查服务日志
kubectl logs -f deployment/retrieval-service -n retrieval-service
```

**数据库检查**:
```bash
# 连接数据库
psql -h localhost -U retrieval_user -d retrieval_db

# 检查连接数
SELECT count(*) FROM pg_stat_activity;

# 检查表大小
SELECT schemaname,tablename,pg_size_pretty(size) as size
FROM (
  SELECT schemaname,tablename,pg_total_relation_size(schemaname||'.'||tablename) as size
  FROM pg_tables WHERE schemaname='public'
) t ORDER BY size DESC;
```

**Redis检查**:
```bash
# 连接Redis
redis-cli -h localhost -p 6379

# 检查内存使用
INFO memory

# 检查键数量
DBSIZE

# 检查慢查询
SLOWLOG GET 10
```

### 2. 性能监控

**关键指标监控**:
- 响应时间: P95 < 2秒
- QPS: > 100 requests/second
- 错误率: < 1%
- CPU使用率: < 80%
- 内存使用率: < 85%

**Grafana面板**:
- 系统概览面板
- API性能面板
- 数据库性能面板
- 业务指标面板

**告警规则**:
```yaml
# 高响应时间告警
- alert: HighResponseTime
  expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
  for: 2m
  
# 高错误率告警
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
  for: 1m
  
# 低QPS告警
- alert: LowQPS
  expr: rate(http_requests_total[5m]) < 10
  for: 5m
```

### 3. 日志管理

**日志级别**:
- ERROR: 错误信息
- WARNING: 警告信息
- INFO: 一般信息
- DEBUG: 调试信息

**日志查看**:
```bash
# 查看实时日志
kubectl logs -f deployment/retrieval-service -n retrieval-service

# 查看错误日志
kubectl logs deployment/retrieval-service -n retrieval-service | grep ERROR

# 查看特定时间段日志
kubectl logs deployment/retrieval-service -n retrieval-service --since=1h
```

**日志分析**:
```bash
# 统计错误类型
grep ERROR /app/logs/retrieval.log | awk '{print $5}' | sort | uniq -c

# 分析慢查询
grep "slow_query" /app/logs/retrieval.log | tail -20

# 统计API调用量
grep "POST /search" /app/logs/access.log | wc -l
```

## 故障处理

### 1. 服务不可用

**症状**: 健康检查失败，API返回5xx错误

**排查步骤**:
```bash
# 1. 检查Pod状态
kubectl get pods -n retrieval-service

# 2. 查看Pod事件
kubectl describe pod <pod-name> -n retrieval-service

# 3. 检查服务日志
kubectl logs <pod-name> -n retrieval-service

# 4. 检查资源使用
kubectl top pods -n retrieval-service
```

**常见原因及解决方案**:
- **内存不足**: 增加内存限制或优化内存使用
- **数据库连接失败**: 检查数据库状态和连接配置
- **依赖服务不可用**: 检查Redis、Elasticsearch状态

### 2. 响应时间过长

**症状**: P95响应时间 > 2秒

**排查步骤**:
```bash
# 1. 检查数据库性能
SELECT query, mean_time, calls FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

# 2. 检查Redis性能
redis-cli --latency-history -h localhost -p 6379

# 3. 检查Elasticsearch性能
curl -X GET "localhost:9200/_cat/indices?v&s=store.size:desc"

# 4. 分析应用日志
grep "slow_query" /app/logs/retrieval.log
```

**优化措施**:
- 优化数据库查询
- 增加缓存命中率
- 调整向量搜索参数
- 增加服务实例数量

### 3. 内存泄漏

**症状**: 内存使用持续增长，最终导致OOM

**排查步骤**:
```bash
# 1. 监控内存趋势
kubectl top pods -n retrieval-service --sort-by=memory

# 2. 检查内存使用详情
kubectl exec -it <pod-name> -n retrieval-service -- cat /proc/meminfo

# 3. 分析Python内存使用
kubectl exec -it <pod-name> -n retrieval-service -- python -c "
import psutil
process = psutil.Process()
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"
```

**解决方案**:
- 重启服务释放内存
- 调整缓存大小
- 优化向量加载策略
- 增加内存限制

### 4. 数据库问题

**连接数过多**:
```sql
-- 查看当前连接
SELECT count(*) FROM pg_stat_activity;

-- 终止空闲连接
SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
WHERE state = 'idle' AND state_change < now() - interval '1 hour';
```

**慢查询优化**:
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000;
SELECT pg_reload_conf();

-- 查看慢查询
SELECT query, mean_time, calls FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;
```

**锁等待问题**:
```sql
-- 查看锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## 扩容和缩容

### 1. 水平扩容

**手动扩容**:
```bash
# 扩容到10个实例
kubectl scale deployment retrieval-service --replicas=10 -n retrieval-service

# 检查扩容状态
kubectl rollout status deployment/retrieval-service -n retrieval-service
```

**自动扩容配置**:
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: retrieval-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: retrieval-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### 2. 垂直扩容

**调整资源限制**:
```bash
kubectl patch deployment retrieval-service -n retrieval-service -p '
{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "retrieval-service",
          "resources": {
            "requests": {"memory": "2Gi", "cpu": "1000m"},
            "limits": {"memory": "4Gi", "cpu": "2000m"}
          }
        }]
      }
    }
  }
}'
```

## 备份和恢复

### 1. 数据库备份

**定期备份**:
```bash
# 创建备份
pg_dump -h localhost -U retrieval_user -d retrieval_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份
gzip backup_$(date +%Y%m%d_%H%M%S).sql

# 上传到对象存储
aws s3 cp backup_*.sql.gz s3://backup-bucket/database/
```

**自动备份脚本**:
```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/backup"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="retrieval_db_backup_${TIMESTAMP}.sql"

# 创建备份
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > ${BACKUP_DIR}/${BACKUP_FILE}

# 压缩备份
gzip ${BACKUP_DIR}/${BACKUP_FILE}

# 上传到云存储
aws s3 cp ${BACKUP_DIR}/${BACKUP_FILE}.gz s3://backup-bucket/database/

# 清理本地文件
find ${BACKUP_DIR} -name "*.sql.gz" -mtime +7 -delete
```

### 2. 配置备份

**备份Kubernetes配置**:
```bash
# 备份所有配置
kubectl get all,configmap,secret,pvc -n retrieval-service -o yaml > k8s_backup_$(date +%Y%m%d).yaml

# 备份特定资源
kubectl get deployment retrieval-service -n retrieval-service -o yaml > deployment_backup.yaml
kubectl get configmap retrieval-service-config -n retrieval-service -o yaml > configmap_backup.yaml
```

### 3. 数据恢复

**从备份恢复**:
```bash
# 停止服务
kubectl scale deployment retrieval-service --replicas=0 -n retrieval-service

# 恢复数据库
gunzip -c backup_20231201_120000.sql.gz | psql -h localhost -U retrieval_user -d retrieval_db

# 重启服务
kubectl scale deployment retrieval-service --replicas=3 -n retrieval-service
```

## 性能优化

### 1. 数据库优化

**索引优化**:
```sql
-- 分析表统计信息
ANALYZE;

-- 重建索引
REINDEX INDEX CONCURRENTLY idx_documents_embedding;

-- 检查未使用的索引
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats WHERE schemaname = 'public';
```

**连接池优化**:
```python
# 调整连接池参数
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600
}
```

### 2. 缓存优化

**Redis优化**:
```bash
# 调整内存策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# 设置过期策略
redis-cli CONFIG SET maxmemory 2gb

# 优化持久化
redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

**应用缓存优化**:
```python
# 调整缓存参数
CACHE_CONFIG = {
    "default_timeout": 3600,
    "key_prefix": "retrieval_service",
    "max_entries": 10000
}
```

### 3. 向量搜索优化

**Elasticsearch优化**:
```bash
# 调整JVM堆大小
export ES_JAVA_OPTS="-Xms4g -Xmx4g"

# 优化索引设置
curl -X PUT "localhost:9200/vectors/_settings" -H 'Content-Type: application/json' -d'
{
  "index": {
    "refresh_interval": "30s",
    "number_of_replicas": 1
  }
}'
```

## 安全管理

### 1. 访问控制

**API访问控制**:
```yaml
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: retrieval-service-netpol
spec:
  podSelector:
    matchLabels:
      app: retrieval-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
```

**RBAC配置**:
```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: retrieval-service-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
```

### 2. 密钥管理

**定期轮换密钥**:
```bash
# 更新数据库密码
kubectl create secret generic retrieval-service-secrets \
  --from-literal=database-password=new_password \
  --dry-run=client -o yaml | kubectl apply -f -

# 重启服务应用新密钥
kubectl rollout restart deployment/retrieval-service -n retrieval-service
```

### 3. 安全扫描

**容器镜像扫描**:
```bash
# 使用trivy扫描镜像
trivy image retrieval-service:latest

# 扫描Kubernetes配置
trivy config deploy/k8s/
```

## 应急响应

### 1. 紧急扩容

```bash
# 紧急扩容到最大实例数
kubectl scale deployment retrieval-service --replicas=20 -n retrieval-service

# 检查扩容状态
kubectl get pods -n retrieval-service -w
```

### 2. 紧急回滚

```bash
# 查看部署历史
kubectl rollout history deployment/retrieval-service -n retrieval-service

# 回滚到上一版本
kubectl rollout undo deployment/retrieval-service -n retrieval-service

# 回滚到指定版本
kubectl rollout undo deployment/retrieval-service --to-revision=2 -n retrieval-service
```

### 3. 流量切换

```bash
# 切换到备用服务
kubectl patch service retrieval-service -n retrieval-service -p '
{
  "spec": {
    "selector": {
      "app": "backup-retrieval-service"
    }
  }
}'
```

## 联系信息

### 运维团队

- **主要联系人**: <EMAIL>
- **紧急联系**: <EMAIL>
- **技术支持**: <EMAIL>

### 升级路径

1. **L1支持**: 运维工程师 (响应时间: 15分钟)
2. **L2支持**: 高级运维工程师 (响应时间: 30分钟)
3. **L3支持**: 开发团队 (响应时间: 1小时)

### 文档更新

本手册应定期更新，最后更新时间: 2023-12-01

如有问题或建议，请联系文档维护团队: <EMAIL>
