# 向量化服务依赖

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 机器学习和向量化
sentence-transformers==2.2.2  # 句子嵌入
transformers==4.35.2  # 预训练模型
torch==2.1.1  # PyTorch
torchvision==0.16.1  # 计算机视觉
torchaudio==2.1.1  # 音频处理
numpy==1.25.2  # 数值计算
scikit-learn==1.3.2  # 机器学习工具

# 模型优化
onnx==1.15.0  # 模型优化
onnxruntime==1.16.3  # ONNX运行时
optimum==1.14.1  # 模型优化工具

# 数据处理
pandas==2.1.3  # 数据分析
datasets==2.14.6  # 数据集处理

# 缓存和队列
redis==5.0.1  # Redis客户端
celery==5.3.4  # 任务队列
flower==2.0.1  # Celery监控

# 数据库
asyncpg==0.29.0  # PostgreSQL异步驱动
sqlalchemy==2.0.23  # ORM

# 配置和环境
pydantic==2.5.0  # 数据验证
python-dotenv==1.0.0  # 环境变量
pyyaml==6.0.1  # YAML配置

# 日志和监控
loguru==0.7.2  # 日志记录
prometheus-client==0.19.0  # 监控指标

# HTTP客户端
httpx==0.25.2  # 异步HTTP客户端
aiohttp==3.9.1  # 异步HTTP

# 工具库
tqdm==4.66.1  # 进度条
psutil==5.9.6  # 系统信息
GPUtil==1.4.0  # GPU监控

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# 开发工具
black==23.11.0  # 代码格式化
isort==5.12.0  # 导入排序
flake8==6.1.0  # 代码检查
mypy==1.7.1  # 类型检查
