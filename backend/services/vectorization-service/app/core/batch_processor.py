"""
批量向量化处理引擎
"""

import asyncio
import numpy as np
import time
import uuid
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from enum import Enum

from app.utils.logger import get_logger

logger = get_logger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchTask:
    """批处理任务"""
    task_id: str
    texts: List[str]
    model_name: str
    batch_size: int
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    callback: Optional[Callable] = None


class BatchVectorizationEngine:
    """批量向量化引擎"""

    def __init__(self, max_workers: int = 4, max_queue_size: int = 100):
        self.processing_queue = asyncio.Queue(maxsize=max_queue_size)
        self.active_tasks: Dict[str, BatchTask] = {}
        self.completed_tasks: Dict[str, BatchTask] = {}
        self.max_workers = max_workers
        self.workers: List[asyncio.Task] = []
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False

        # 性能统计
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_texts_processed": 0,
            "average_processing_time": 0.0
        }

    async def initialize(self):
        """初始化批处理引擎"""
        logger.info("初始化批量向量化引擎...")

        self.running = True

        # 启动工作线程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)

        logger.info(f"批量向量化引擎初始化完成，启动{self.max_workers}个工作线程")

    async def submit_task(self, texts: List[str], model_name: str,
                         batch_size: int = 32, callback: Optional[Callable] = None) -> str:
        """
        提交批处理任务

        Args:
            texts: 文本列表
            model_name: 模型名称
            batch_size: 批大小
            callback: 完成回调函数

        Returns:
            str: 任务ID
        """
        try:
            task_id = str(uuid.uuid4())

            task = BatchTask(
                task_id=task_id,
                texts=texts,
                model_name=model_name,
                batch_size=batch_size,
                status=TaskStatus.PENDING,
                created_at=datetime.utcnow(),
                callback=callback
            )

            self.active_tasks[task_id] = task
            await self.processing_queue.put(task)

            self.stats["total_tasks"] += 1

            logger.info(f"批处理任务已提交: {task_id}, 文本数: {len(texts)}")
            return task_id

        except Exception as e:
            logger.error(f"提交批处理任务失败: {e}")
            raise

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.active_tasks.get(task_id) or self.completed_tasks.get(task_id)
        if not task:
            return None

        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "total_texts": len(task.texts),
            "error": task.error
        }

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.active_tasks.get(task_id)
        if not task:
            return False

        if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            logger.info(f"任务已取消: {task_id}")
            return True

        return False

    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程启动: {worker_name}")

        while self.running:
            try:
                # 从队列获取任务
                task = await asyncio.wait_for(
                    self.processing_queue.get(),
                    timeout=1.0
                )

                if task.status == TaskStatus.CANCELLED:
                    continue

                # 处理任务
                await self._process_task(task, worker_name)

            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"工作线程错误 {worker_name}: {e}")

        logger.info(f"工作线程停止: {worker_name}")

    async def _process_task(self, task: BatchTask, worker_name: str):
        """处理单个任务"""
        try:
            logger.info(f"开始处理任务: {task.task_id} (工作线程: {worker_name})")

            task.status = TaskStatus.RUNNING
            task.started_at = datetime.utcnow()
            start_time = time.time()

            # 模拟批量向量化处理
            result = await self._simulate_vectorization(task)

            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.progress = 100.0
            task.result = result

            processing_time = time.time() - start_time

            # 更新统计信息
            self.stats["completed_tasks"] += 1
            self.stats["total_texts_processed"] += len(task.texts)
            self._update_average_processing_time(processing_time)

            # 移动到已完成任务
            self.completed_tasks[task.task_id] = task
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

            # 执行回调
            if task.callback:
                try:
                    await task.callback(task.result)
                except Exception as e:
                    logger.error(f"任务回调执行失败: {task.task_id}, 错误: {e}")

            logger.info(f"任务处理完成: {task.task_id}, 耗时: {processing_time:.2f}秒")

        except Exception as e:
            logger.error(f"任务处理失败: {task.task_id}, 错误: {e}")

            task.status = TaskStatus.FAILED
            task.completed_at = datetime.utcnow()
            task.error = str(e)

            self.stats["failed_tasks"] += 1

            # 移动到已完成任务
            self.completed_tasks[task.task_id] = task
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

    async def _simulate_vectorization(self, task: BatchTask) -> Dict[str, Any]:
        """模拟向量化处理"""
        texts = task.texts
        batch_size = task.batch_size

        results = []
        total_batches = (len(texts) + batch_size - 1) // batch_size

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            # 模拟处理时间
            await asyncio.sleep(0.1)

            # 模拟向量化结果
            batch_results = []
            for text in batch_texts:
                vector = np.random.rand(384).tolist()  # 模拟384维向量
                batch_results.append({
                    "text": text,
                    "vector": vector,
                    "dimension": 384
                })

            results.extend(batch_results)

            # 更新进度
            current_batch = (i // batch_size) + 1
            task.progress = (current_batch / total_batches) * 100

        return {
            "status": "success",
            "total_texts": len(texts),
            "results": results,
            "model_name": task.model_name,
            "processing_time": time.time()
        }

    def _update_average_processing_time(self, processing_time: float):
        """更新平均处理时间"""
        completed = self.stats["completed_tasks"]
        if completed == 1:
            self.stats["average_processing_time"] = processing_time
        else:
            current_avg = self.stats["average_processing_time"]
            self.stats["average_processing_time"] = (current_avg * (completed - 1) + processing_time) / completed

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "stats": self.stats.copy(),
            "queue_size": self.processing_queue.qsize(),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "workers": len(self.workers),
            "running": self.running
        }

    async def process_batch(self, texts: List[str], model_name: str,
                           batch_size: int = 32) -> Dict[str, Any]:
        """同步批量处理文本（兼容性方法）"""
        task_id = await self.submit_task(texts, model_name, batch_size)

        # 等待任务完成
        while True:
            status = await self.get_task_status(task_id)
            if status and status["status"] in ["completed", "failed", "cancelled"]:
                break
            await asyncio.sleep(0.1)

        task = self.completed_tasks.get(task_id)
        if task and task.result:
            return task.result
        else:
            return {
                "status": "failed",
                "error": task.error if task else "任务不存在"
            }

    async def cleanup(self):
        """清理资源"""
        logger.info("清理批量向量化引擎资源...")

        self.running = False

        # 等待所有工作线程完成
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)

        # 清理任务
        self.active_tasks.clear()
        self.completed_tasks.clear()

        logger.info("批量向量化引擎资源清理完成")
