"""
向量质量评估器
"""

import numpy as np
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from concurrent.futures import ThreadPoolExecutor
import warnings

from app.utils.logger import get_logger

logger = get_logger(__name__)
warnings.filterwarnings('ignore')


class VectorQualityAssessor:
    """向量质量评估器"""

    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.scaler = StandardScaler()

    async def initialize(self):
        """初始化质量评估器"""
        logger.info("向量质量评估器初始化完成")

    async def assess_quality(self, vectors: List[List[float]],
                           labels: Optional[List[str]] = None,
                           metrics: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        评估向量质量

        Args:
            vectors: 向量列表
            labels: 标签列表（可选）
            metrics: 评估指标列表

        Returns:
            Dict[str, Any]: 评估结果
        """
        try:
            logger.info(f"开始评估向量质量: {len(vectors)}个向量")

            if not vectors:
                return {"error": "向量列表为空"}

            # 转换为numpy数组
            vector_array = np.array(vectors)

            if len(vector_array.shape) != 2:
                return {"error": "向量格式错误"}

            # 异步执行评估
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._assess_quality_sync,
                vector_array,
                labels,
                metrics or ["silhouette_score", "dimension_utilization", "similarity_distribution"]
            )

            logger.info(f"向量质量评估完成")
            return result

        except Exception as e:
            logger.error(f"向量质量评估失败: {e}")
            return {"error": str(e)}

    def _assess_quality_sync(self, vectors: np.ndarray, labels: Optional[List[str]],
                           metrics: List[str]) -> Dict[str, Any]:
        """同步执行质量评估"""
        try:
            result = {
                "vector_count": len(vectors),
                "dimension": vectors.shape[1],
                "metrics": {},
                "summary": {},
                "recommendations": []
            }

            # 基础统计
            result["basic_stats"] = self._calculate_basic_stats(vectors)

            # 执行各种评估指标
            for metric in metrics:
                if metric == "silhouette_score":
                    result["metrics"]["silhouette_score"] = self._calculate_silhouette_score(vectors, labels)
                elif metric == "calinski_harabasz":
                    result["metrics"]["calinski_harabasz"] = self._calculate_calinski_harabasz(vectors, labels)
                elif metric == "davies_bouldin":
                    result["metrics"]["davies_bouldin"] = self._calculate_davies_bouldin(vectors, labels)
                elif metric == "dimension_utilization":
                    result["metrics"]["dimension_utilization"] = self._calculate_dimension_utilization(vectors)
                elif metric == "similarity_distribution":
                    result["metrics"]["similarity_distribution"] = self._calculate_similarity_distribution(vectors)

            # 生成总体质量评分
            result["overall_quality_score"] = self._calculate_overall_score(result["metrics"])

            # 生成建议
            result["recommendations"] = self._generate_recommendations(result)

            return result

        except Exception as e:
            logger.error(f"同步质量评估失败: {e}")
            return {"error": str(e)}

    def _calculate_basic_stats(self, vectors: np.ndarray) -> Dict[str, Any]:
        """计算基础统计信息"""
        try:
            return {
                "mean_norm": float(np.mean(np.linalg.norm(vectors, axis=1))),
                "std_norm": float(np.std(np.linalg.norm(vectors, axis=1))),
                "mean_values": vectors.mean(axis=0).tolist()[:10],  # 只返回前10维的均值
                "std_values": vectors.std(axis=0).tolist()[:10],   # 只返回前10维的标准差
                "min_value": float(vectors.min()),
                "max_value": float(vectors.max()),
                "sparsity": float(np.mean(vectors == 0))
            }
        except Exception as e:
            logger.error(f"基础统计计算失败: {e}")
            return {}

    def _calculate_silhouette_score(self, vectors: np.ndarray, labels: Optional[List[str]]) -> Dict[str, Any]:
        """计算轮廓系数"""
        try:
            if len(vectors) < 2:
                return {"score": 0.0, "note": "样本数量不足"}

            if labels:
                # 使用提供的标签
                unique_labels = list(set(labels))
                if len(unique_labels) < 2:
                    return {"score": 0.0, "note": "标签类别不足"}

                label_indices = [unique_labels.index(label) for label in labels]
                score = silhouette_score(vectors, label_indices)
            else:
                # 使用K-means聚类
                n_clusters = min(8, len(vectors) // 2)  # 自动确定聚类数
                if n_clusters < 2:
                    return {"score": 0.0, "note": "样本数量不足以聚类"}

                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(vectors)
                score = silhouette_score(vectors, cluster_labels)

            return {
                "score": float(score),
                "interpretation": self._interpret_silhouette_score(score)
            }

        except Exception as e:
            logger.error(f"轮廓系数计算失败: {e}")
            return {"score": 0.0, "error": str(e)}

    def _calculate_calinski_harabasz(self, vectors: np.ndarray, labels: Optional[List[str]]) -> Dict[str, Any]:
        """计算Calinski-Harabasz指数"""
        try:
            if len(vectors) < 2:
                return {"score": 0.0, "note": "样本数量不足"}

            if labels:
                unique_labels = list(set(labels))
                if len(unique_labels) < 2:
                    return {"score": 0.0, "note": "标签类别不足"}

                label_indices = [unique_labels.index(label) for label in labels]
                score = calinski_harabasz_score(vectors, label_indices)
            else:
                n_clusters = min(8, len(vectors) // 2)
                if n_clusters < 2:
                    return {"score": 0.0, "note": "样本数量不足以聚类"}

                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(vectors)
                score = calinski_harabasz_score(vectors, cluster_labels)

            return {
                "score": float(score),
                "interpretation": "分数越高表示聚类效果越好"
            }

        except Exception as e:
            logger.error(f"Calinski-Harabasz指数计算失败: {e}")
            return {"score": 0.0, "error": str(e)}

    def _calculate_davies_bouldin(self, vectors: np.ndarray, labels: Optional[List[str]]) -> Dict[str, Any]:
        """计算Davies-Bouldin指数"""
        try:
            if len(vectors) < 2:
                return {"score": 0.0, "note": "样本数量不足"}

            if labels:
                unique_labels = list(set(labels))
                if len(unique_labels) < 2:
                    return {"score": 0.0, "note": "标签类别不足"}

                label_indices = [unique_labels.index(label) for label in labels]
                score = davies_bouldin_score(vectors, label_indices)
            else:
                n_clusters = min(8, len(vectors) // 2)
                if n_clusters < 2:
                    return {"score": 0.0, "note": "样本数量不足以聚类"}

                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(vectors)
                score = davies_bouldin_score(vectors, cluster_labels)

            return {
                "score": float(score),
                "interpretation": "分数越低表示聚类效果越好"
            }

        except Exception as e:
            logger.error(f"Davies-Bouldin指数计算失败: {e}")
            return {"score": 0.0, "error": str(e)}

    def _calculate_dimension_utilization(self, vectors: np.ndarray) -> Dict[str, Any]:
        """计算维度利用率"""
        try:
            # 计算每个维度的方差
            variances = np.var(vectors, axis=0)

            # 计算有效维度（方差大于阈值的维度）
            variance_threshold = np.mean(variances) * 0.1
            effective_dims = np.sum(variances > variance_threshold)

            # 使用PCA分析主成分
            pca = PCA()
            pca.fit(vectors)

            # 计算解释95%方差所需的主成分数量
            cumsum_ratio = np.cumsum(pca.explained_variance_ratio_)
            n_components_95 = np.argmax(cumsum_ratio >= 0.95) + 1

            utilization_ratio = effective_dims / vectors.shape[1]

            return {
                "total_dimensions": vectors.shape[1],
                "effective_dimensions": int(effective_dims),
                "utilization_ratio": float(utilization_ratio),
                "pca_95_components": int(n_components_95),
                "explained_variance_ratio": pca.explained_variance_ratio_[:10].tolist(),  # 前10个主成分
                "interpretation": self._interpret_dimension_utilization(utilization_ratio)
            }

        except Exception as e:
            logger.error(f"维度利用率计算失败: {e}")
            return {"error": str(e)}

    def _calculate_similarity_distribution(self, vectors: np.ndarray) -> Dict[str, Any]:
        """计算相似度分布"""
        try:
            # 随机采样以提高效率
            if len(vectors) > 1000:
                indices = np.random.choice(len(vectors), 1000, replace=False)
                sample_vectors = vectors[indices]
            else:
                sample_vectors = vectors

            # 计算余弦相似度矩阵
            normalized_vectors = sample_vectors / np.linalg.norm(sample_vectors, axis=1, keepdims=True)
            similarity_matrix = np.dot(normalized_vectors, normalized_vectors.T)

            # 提取上三角矩阵（排除对角线）
            upper_triangle = np.triu(similarity_matrix, k=1)
            similarities = upper_triangle[upper_triangle != 0]

            return {
                "mean_similarity": float(np.mean(similarities)),
                "std_similarity": float(np.std(similarities)),
                "min_similarity": float(np.min(similarities)),
                "max_similarity": float(np.max(similarities)),
                "similarity_percentiles": {
                    "25th": float(np.percentile(similarities, 25)),
                    "50th": float(np.percentile(similarities, 50)),
                    "75th": float(np.percentile(similarities, 75)),
                    "90th": float(np.percentile(similarities, 90))
                },
                "interpretation": self._interpret_similarity_distribution(np.mean(similarities), np.std(similarities))
            }

        except Exception as e:
            logger.error(f"相似度分布计算失败: {e}")
            return {"error": str(e)}

    def _interpret_silhouette_score(self, score: float) -> str:
        """解释轮廓系数"""
        if score > 0.7:
            return "优秀：向量聚类结构非常清晰"
        elif score > 0.5:
            return "良好：向量聚类结构较为清晰"
        elif score > 0.25:
            return "一般：向量聚类结构不够清晰"
        else:
            return "较差：向量聚类结构混乱"

    def _interpret_dimension_utilization(self, ratio: float) -> str:
        """解释维度利用率"""
        if ratio > 0.8:
            return "优秀：维度利用率很高，信息密度大"
        elif ratio > 0.6:
            return "良好：维度利用率较高"
        elif ratio > 0.4:
            return "一般：维度利用率中等，可能存在冗余"
        else:
            return "较差：维度利用率低，存在大量冗余维度"

    def _interpret_similarity_distribution(self, mean_sim: float, std_sim: float) -> str:
        """解释相似度分布"""
        if std_sim > 0.3:
            return "良好：相似度分布分散，向量区分度高"
        elif std_sim > 0.2:
            return "一般：相似度分布中等"
        else:
            return "较差：相似度分布集中，向量区分度低"

    def _calculate_overall_score(self, metrics: Dict[str, Any]) -> float:
        """计算总体质量评分"""
        try:
            scores = []

            # 轮廓系数 (0-1, 越高越好)
            if "silhouette_score" in metrics and "score" in metrics["silhouette_score"]:
                silhouette = metrics["silhouette_score"]["score"]
                scores.append(max(0, silhouette))  # 确保非负

            # 维度利用率 (0-1, 越高越好)
            if "dimension_utilization" in metrics and "utilization_ratio" in metrics["dimension_utilization"]:
                dim_util = metrics["dimension_utilization"]["utilization_ratio"]
                scores.append(min(1, dim_util))  # 确保不超过1

            # 相似度分布标准差 (转换为0-1分数，标准差越大越好)
            if "similarity_distribution" in metrics and "std_similarity" in metrics["similarity_distribution"]:
                std_sim = metrics["similarity_distribution"]["std_similarity"]
                scores.append(min(1, std_sim / 0.5))  # 归一化到0-1

            if scores:
                return float(np.mean(scores))
            else:
                return 0.5  # 默认分数

        except Exception as e:
            logger.error(f"总体评分计算失败: {e}")
            return 0.5

    def _generate_recommendations(self, result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        try:
            overall_score = result.get("overall_quality_score", 0.5)

            if overall_score < 0.6:
                recommendations.append("总体质量偏低，建议检查数据预处理和模型选择")

            # 基于轮廓系数的建议
            silhouette = result.get("metrics", {}).get("silhouette_score", {})
            if silhouette.get("score", 0) < 0.3:
                recommendations.append("聚类效果较差，建议调整模型参数或使用更适合的嵌入模型")

            # 基于维度利用率的建议
            dim_util = result.get("metrics", {}).get("dimension_utilization", {})
            if dim_util.get("utilization_ratio", 1) < 0.5:
                recommendations.append("维度利用率低，建议考虑降维或使用更小维度的模型")

            # 基于相似度分布的建议
            sim_dist = result.get("metrics", {}).get("similarity_distribution", {})
            if sim_dist.get("std_similarity", 0) < 0.2:
                recommendations.append("向量区分度低，建议使用更强的嵌入模型或改进文本预处理")

            if not recommendations:
                recommendations.append("向量质量良好，可以继续使用当前配置")

        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            recommendations.append("无法生成具体建议，请检查评估结果")

        return recommendations

    async def cleanup(self):
        """清理资源"""
        logger.info("清理向量质量评估器资源...")

        if self.executor:
            self.executor.shutdown(wait=True)

        logger.info("向量质量评估器资源清理完成")
