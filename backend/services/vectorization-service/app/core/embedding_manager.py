"""
嵌入模型管理器
支持多种嵌入模型的加载、切换和优化
"""

import torch
import numpy as np
from sentence_transformers import SentenceTransformer
from typing import Dict, List, Optional, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor
import psutil
import GPUtil

from app.config.settings import get_settings
from app.models.vectorization import ModelInfo, VectorResult
from app.utils.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class EmbeddingModelManager:
    """嵌入模型管理器"""
    
    def __init__(self):
        self.models: Dict[str, SentenceTransformer] = {}
        self.current_model: Optional[str] = None
        self.device = self._get_optimal_device()
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.model_configs = self._get_model_configs()
    
    def _get_optimal_device(self) -> torch.device:
        """获取最优计算设备"""
        if torch.cuda.is_available():
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    # 选择显存最大的GPU
                    best_gpu = max(gpus, key=lambda x: x.memoryFree)
                    device = torch.device(f'cuda:{best_gpu.id}')
                    logger.info(f"使用GPU: {best_gpu.name} (显存: {best_gpu.memoryFree}MB)")
                    return device
            except Exception as e:
                logger.warning(f"GPU检测失败: {e}")
        
        logger.info("使用CPU进行计算")
        return torch.device('cpu')
    
    def _get_model_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取模型配置"""
        return {
            "paraphrase-multilingual-MiniLM-L12-v2": {
                "dimension": 384,
                "max_seq_length": 512,
                "languages": ["zh", "en", "multi"],
                "description": "多语言轻量级模型，适合中英文混合场景"
            },
            "all-MiniLM-L6-v2": {
                "dimension": 384,
                "max_seq_length": 512,
                "languages": ["en"],
                "description": "英文轻量级模型，速度快"
            },
            "paraphrase-multilingual-mpnet-base-v2": {
                "dimension": 768,
                "max_seq_length": 512,
                "languages": ["zh", "en", "multi"],
                "description": "多语言高质量模型，效果好"
            },
            "text2vec-base-chinese": {
                "dimension": 768,
                "max_seq_length": 512,
                "languages": ["zh"],
                "description": "中文专用模型，中文效果优秀"
            }
        }
    
    async def initialize(self):
        """初始化模型管理器"""
        try:
            logger.info("初始化嵌入模型管理器...")
            
            # 加载默认模型
            default_model = settings.DEFAULT_EMBEDDING_MODEL
            await self.load_model(default_model)
            self.current_model = default_model
            
            logger.info(f"默认模型加载完成: {default_model}")
            
        except Exception as e:
            logger.error(f"模型管理器初始化失败: {e}")
            raise
    
    async def load_model(self, model_name: str, cache: bool = True) -> SentenceTransformer:
        """
        加载嵌入模型
        
        Args:
            model_name: 模型名称
            cache: 是否缓存模型
        
        Returns:
            SentenceTransformer: 加载的模型
        """
        try:
            if model_name in self.models and cache:
                logger.info(f"从缓存加载模型: {model_name}")
                return self.models[model_name]
            
            logger.info(f"开始加载模型: {model_name}")
            
            # 异步加载模型
            loop = asyncio.get_event_loop()
            model = await loop.run_in_executor(
                self.executor,
                self._load_model_sync,
                model_name
            )
            
            if cache:
                self.models[model_name] = model
                logger.info(f"模型已缓存: {model_name}")
            
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {model_name}, 错误: {e}")
            raise
    
    def _load_model_sync(self, model_name: str) -> SentenceTransformer:
        """同步加载模型"""
        try:
            model = SentenceTransformer(model_name, device=self.device)
            
            # 模型优化
            if self.device.type == 'cuda':
                model = model.half()  # 半精度优化
                logger.info(f"启用半精度优化: {model_name}")
            
            return model
            
        except Exception as e:
            logger.error(f"同步模型加载失败: {model_name}, 错误: {e}")
            raise
    
    async def encode_texts(self, texts: List[str], model_name: Optional[str] = None,
                          batch_size: int = 32, normalize: bool = True) -> np.ndarray:
        """
        编码文本为向量
        
        Args:
            texts: 文本列表
            model_name: 模型名称
            batch_size: 批大小
            normalize: 是否归一化
        
        Returns:
            np.ndarray: 向量数组
        """
        try:
            model_name = model_name or self.current_model
            if not model_name:
                raise ValueError("未指定模型")
            
            model = await self.load_model(model_name)
            
            # 优化批大小
            optimal_batch_size = self._optimize_batch_size(len(texts), model_name)
            actual_batch_size = min(batch_size, optimal_batch_size)
            
            logger.info(f"编码文本: 数量={len(texts)}, 批大小={actual_batch_size}")
            
            # 异步编码
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                self.executor,
                self._encode_texts_sync,
                model, texts, actual_batch_size, normalize
            )
            
            return embeddings
            
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            raise
    
    def _encode_texts_sync(self, model: SentenceTransformer, texts: List[str],
                          batch_size: int, normalize: bool) -> np.ndarray:
        """同步编码文本"""
        try:
            embeddings = model.encode(
                texts,
                batch_size=batch_size,
                convert_to_numpy=True,
                normalize_embeddings=normalize,
                show_progress_bar=True
            )
            return embeddings
            
        except Exception as e:
            logger.error(f"同步文本编码失败: {e}")
            raise
    
    def _optimize_batch_size(self, num_texts: int, model_name: str) -> int:
        """动态优化批大小"""
        try:
            if self.device.type == 'cuda':
                # 基于GPU显存动态调整
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # 使用第一个GPU
                    free_memory_gb = gpu.memoryFree / 1024
                    
                    # 根据显存估算批大小
                    if free_memory_gb > 8:
                        base_batch_size = 64
                    elif free_memory_gb > 4:
                        base_batch_size = 32
                    elif free_memory_gb > 2:
                        base_batch_size = 16
                    else:
                        base_batch_size = 8
                    
                    return min(base_batch_size, num_texts)
            
            # CPU模式使用较小批大小
            return min(16, num_texts)
            
        except Exception as e:
            logger.warning(f"批大小优化失败: {e}")
            return min(32, num_texts)
    
    async def get_available_models(self) -> List[ModelInfo]:
        """获取可用模型列表"""
        try:
            models = []
            for name, config in self.model_configs.items():
                model_info = ModelInfo(
                    name=name,
                    dimension=config["dimension"],
                    max_seq_length=config["max_seq_length"],
                    languages=config["languages"],
                    description=config["description"],
                    is_loaded=name in self.models,
                    is_current=name == self.current_model
                )
                models.append(model_info)
            
            return models
            
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            raise
    
    async def switch_model(self, model_name: str) -> Dict[str, Any]:
        """切换当前模型"""
        try:
            if model_name not in self.model_configs:
                return {"success": False, "message": f"模型不存在: {model_name}"}
            
            # 加载新模型
            await self.load_model(model_name)
            old_model = self.current_model
            self.current_model = model_name
            
            logger.info(f"模型切换: {old_model} -> {model_name}")
            
            return {
                "success": True,
                "old_model": old_model,
                "new_model": model_name,
                "message": "模型切换成功"
            }
            
        except Exception as e:
            logger.error(f"模型切换失败: {model_name}, 错误: {e}")
            return {"success": False, "message": str(e)}
    
    async def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """获取模型信息"""
        try:
            if model_name not in self.model_configs:
                return None
            
            config = self.model_configs[model_name]
            return ModelInfo(
                name=model_name,
                dimension=config["dimension"],
                max_seq_length=config["max_seq_length"],
                languages=config["languages"],
                description=config["description"],
                is_loaded=model_name in self.models,
                is_current=model_name == self.current_model
            )
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {model_name}, 错误: {e}")
            return None
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理嵌入模型管理器资源...")
        
        # 清理模型
        for model_name in list(self.models.keys()):
            try:
                del self.models[model_name]
                logger.info(f"模型已卸载: {model_name}")
            except Exception as e:
                logger.error(f"模型卸载失败: {model_name}, 错误: {e}")
        
        self.models.clear()
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("GPU缓存已清理")
        
        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)
            logger.info("线程池已关闭")
        
        logger.info("嵌入模型管理器资源清理完成")
