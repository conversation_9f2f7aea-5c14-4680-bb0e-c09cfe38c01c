"""
批量处理配置管理
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from enum import Enum


class BatchStrategy(Enum):
    """批处理策略"""
    FIXED_SIZE = "fixed_size"           # 固定批大小
    DYNAMIC_SIZE = "dynamic_size"       # 动态批大小
    MEMORY_ADAPTIVE = "memory_adaptive" # 内存自适应
    GPU_ADAPTIVE = "gpu_adaptive"       # GPU自适应


class PriorityLevel(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class BatchConfig:
    """批处理配置"""
    strategy: BatchStrategy = BatchStrategy.DYNAMIC_SIZE
    base_batch_size: int = 32
    max_batch_size: int = 128
    min_batch_size: int = 8
    memory_threshold: float = 0.8  # 内存使用阈值
    gpu_memory_threshold: float = 0.9  # GPU内存使用阈值
    timeout_seconds: int = 300
    retry_attempts: int = 3
    priority: PriorityLevel = PriorityLevel.NORMAL
    
    # 性能优化配置
    enable_prefetch: bool = True
    prefetch_factor: int = 2
    enable_caching: bool = True
    cache_size: int = 1000
    
    # 质量控制配置
    enable_quality_check: bool = True
    quality_threshold: float = 0.7
    enable_deduplication: bool = True
    similarity_threshold: float = 0.95


@dataclass
class ModelConfig:
    """模型配置"""
    model_name: str
    max_sequence_length: int = 512
    embedding_dimension: int = 384
    batch_size_multiplier: float = 1.0  # 模型特定的批大小调整因子
    memory_usage_factor: float = 1.0    # 内存使用因子
    processing_time_factor: float = 1.0 # 处理时间因子


class BatchConfigManager:
    """批处理配置管理器"""
    
    def __init__(self):
        self.default_config = BatchConfig()
        self.model_configs: Dict[str, ModelConfig] = {}
        self._initialize_model_configs()
    
    def _initialize_model_configs(self):
        """初始化模型配置"""
        self.model_configs = {
            "paraphrase-multilingual-MiniLM-L12-v2": ModelConfig(
                model_name="paraphrase-multilingual-MiniLM-L12-v2",
                max_sequence_length=512,
                embedding_dimension=384,
                batch_size_multiplier=1.2,
                memory_usage_factor=0.8,
                processing_time_factor=0.9
            ),
            "all-MiniLM-L6-v2": ModelConfig(
                model_name="all-MiniLM-L6-v2",
                max_sequence_length=512,
                embedding_dimension=384,
                batch_size_multiplier=1.5,
                memory_usage_factor=0.6,
                processing_time_factor=0.7
            ),
            "paraphrase-multilingual-mpnet-base-v2": ModelConfig(
                model_name="paraphrase-multilingual-mpnet-base-v2",
                max_sequence_length=512,
                embedding_dimension=768,
                batch_size_multiplier=0.8,
                memory_usage_factor=1.2,
                processing_time_factor=1.3
            ),
            "text2vec-base-chinese": ModelConfig(
                model_name="text2vec-base-chinese",
                max_sequence_length=512,
                embedding_dimension=768,
                batch_size_multiplier=0.9,
                memory_usage_factor=1.1,
                processing_time_factor=1.1
            )
        }
    
    def get_config_for_model(self, model_name: str, 
                           custom_config: Optional[BatchConfig] = None) -> BatchConfig:
        """获取模型特定的配置"""
        base_config = custom_config or self.default_config
        model_config = self.model_configs.get(model_name)
        
        if not model_config:
            return base_config
        
        # 根据模型特性调整配置
        adjusted_config = BatchConfig(
            strategy=base_config.strategy,
            base_batch_size=int(base_config.base_batch_size * model_config.batch_size_multiplier),
            max_batch_size=int(base_config.max_batch_size * model_config.batch_size_multiplier),
            min_batch_size=max(1, int(base_config.min_batch_size * model_config.batch_size_multiplier)),
            memory_threshold=base_config.memory_threshold * model_config.memory_usage_factor,
            gpu_memory_threshold=base_config.gpu_memory_threshold,
            timeout_seconds=int(base_config.timeout_seconds * model_config.processing_time_factor),
            retry_attempts=base_config.retry_attempts,
            priority=base_config.priority,
            enable_prefetch=base_config.enable_prefetch,
            prefetch_factor=base_config.prefetch_factor,
            enable_caching=base_config.enable_caching,
            cache_size=base_config.cache_size,
            enable_quality_check=base_config.enable_quality_check,
            quality_threshold=base_config.quality_threshold,
            enable_deduplication=base_config.enable_deduplication,
            similarity_threshold=base_config.similarity_threshold
        )
        
        return adjusted_config
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self.model_configs.get(model_name)
    
    def add_model_config(self, model_config: ModelConfig):
        """添加模型配置"""
        self.model_configs[model_config.model_name] = model_config
    
    def update_default_config(self, **kwargs):
        """更新默认配置"""
        for key, value in kwargs.items():
            if hasattr(self.default_config, key):
                setattr(self.default_config, key, value)
    
    def get_optimal_batch_size(self, model_name: str, text_count: int, 
                             available_memory: float, gpu_memory: float) -> int:
        """获取最优批大小"""
        config = self.get_config_for_model(model_name)
        model_config = self.get_model_config(model_name)
        
        if config.strategy == BatchStrategy.FIXED_SIZE:
            return config.base_batch_size
        
        elif config.strategy == BatchStrategy.DYNAMIC_SIZE:
            # 基于文本数量动态调整
            if text_count < 100:
                return min(config.base_batch_size, text_count)
            elif text_count < 1000:
                return min(config.max_batch_size, text_count // 10)
            else:
                return config.max_batch_size
        
        elif config.strategy == BatchStrategy.MEMORY_ADAPTIVE:
            # 基于内存使用情况调整
            if available_memory > config.memory_threshold:
                return config.max_batch_size
            elif available_memory > 0.5:
                return config.base_batch_size
            else:
                return config.min_batch_size
        
        elif config.strategy == BatchStrategy.GPU_ADAPTIVE:
            # 基于GPU内存调整
            if gpu_memory > config.gpu_memory_threshold:
                return config.max_batch_size
            elif gpu_memory > 0.7:
                return config.base_batch_size
            else:
                return config.min_batch_size
        
        return config.base_batch_size
