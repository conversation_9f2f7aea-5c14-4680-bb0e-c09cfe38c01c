"""
高级批量向量化处理引擎
支持智能调度、优先级管理、资源优化
"""

import asyncio
import time
import uuid
import psutil
import numpy as np
from typing import List, Dict, Any, Optional, Callable, Tuple
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import heapq
import hashlib

from app.core.batch_config import BatchConfigManager, BatchConfig, PriorityLevel
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class BatchTask:
    """批处理任务"""
    task_id: str
    texts: List[str]
    model_name: str
    config: BatchConfig
    priority: PriorityLevel
    created_at: datetime
    callback: Optional[Callable] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __lt__(self, other):
        """优先级比较（用于优先队列）"""
        return self.priority.value > other.priority.value


@dataclass
class BatchResult:
    """批处理结果"""
    task_id: str
    status: str
    vectors: List[List[float]]
    processing_time: float
    batch_count: int
    success_count: int
    error_count: int
    errors: List[Dict[str, Any]]
    quality_score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ProcessingStats:
    """处理统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    total_texts: int = 0
    total_processing_time: float = 0.0
    average_batch_size: float = 0.0
    throughput: float = 0.0  # texts per second
    
    def update_completion(self, task: BatchTask, processing_time: float, success_count: int):
        """更新完成统计"""
        self.completed_tasks += 1
        self.total_texts += len(task.texts)
        self.total_processing_time += processing_time
        
        if self.completed_tasks > 0:
            self.average_batch_size = self.total_texts / self.completed_tasks
            self.throughput = self.total_texts / self.total_processing_time if self.total_processing_time > 0 else 0


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self):
        self.cpu_usage_history = deque(maxlen=60)  # 保留60秒历史
        self.memory_usage_history = deque(maxlen=60)
        self.gpu_memory_history = deque(maxlen=60)
    
    def update_metrics(self):
        """更新系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            self.cpu_usage_history.append(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_usage_history.append(memory_percent)
            
            # GPU内存（如果可用）
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu_memory_percent = (gpus[0].memoryUsed / gpus[0].memoryTotal) * 100
                    self.gpu_memory_history.append(gpu_memory_percent)
            except ImportError:
                pass
                
        except Exception as e:
            logger.error(f"资源监控更新失败: {e}")
    
    def get_current_metrics(self) -> Dict[str, float]:
        """获取当前指标"""
        return {
            "cpu_usage": self.cpu_usage_history[-1] if self.cpu_usage_history else 0.0,
            "memory_usage": self.memory_usage_history[-1] if self.memory_usage_history else 0.0,
            "gpu_memory_usage": self.gpu_memory_history[-1] if self.gpu_memory_history else 0.0,
            "available_memory": 100 - (self.memory_usage_history[-1] if self.memory_usage_history else 0.0)
        }
    
    def get_average_metrics(self, window_size: int = 10) -> Dict[str, float]:
        """获取平均指标"""
        def avg(history, size):
            if not history:
                return 0.0
            recent = list(history)[-size:]
            return sum(recent) / len(recent)
        
        return {
            "avg_cpu_usage": avg(self.cpu_usage_history, window_size),
            "avg_memory_usage": avg(self.memory_usage_history, window_size),
            "avg_gpu_memory_usage": avg(self.gpu_memory_history, window_size)
        }


class AdvancedBatchProcessor:
    """高级批量处理引擎"""
    
    def __init__(self, max_workers: int = 4, max_queue_size: int = 1000):
        self.config_manager = BatchConfigManager()
        self.resource_monitor = ResourceMonitor()
        
        # 任务队列和管理
        self.task_queue = []  # 优先队列
        self.active_tasks: Dict[str, BatchTask] = {}
        self.completed_tasks: Dict[str, BatchResult] = {}
        self.task_history = deque(maxlen=10000)  # 任务历史
        
        # 工作线程管理
        self.max_workers = max_workers
        self.workers: List[asyncio.Task] = []
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        
        # 缓存和去重
        self.text_cache: Dict[str, List[float]] = {}  # 文本向量缓存
        self.deduplication_cache: Dict[str, str] = {}  # 去重缓存
        
        # 统计信息
        self.stats = ProcessingStats()
        self.model_stats: Dict[str, ProcessingStats] = defaultdict(ProcessingStats)
        
        # 性能优化
        self.batch_size_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.processing_time_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
    
    async def initialize(self):
        """初始化处理引擎"""
        logger.info("初始化高级批量处理引擎...")
        
        self.running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        # 启动资源监控
        asyncio.create_task(self._resource_monitor_loop())
        
        # 启动性能优化任务
        asyncio.create_task(self._optimization_loop())
        
        logger.info(f"高级批量处理引擎初始化完成，启动{self.max_workers}个工作线程")
    
    async def submit_task(self, texts: List[str], model_name: str,
                         priority: PriorityLevel = PriorityLevel.NORMAL,
                         config: Optional[BatchConfig] = None,
                         callback: Optional[Callable] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> str:
        """提交批处理任务"""
        try:
            task_id = str(uuid.uuid4())
            
            # 获取模型特定配置
            task_config = self.config_manager.get_config_for_model(model_name, config)
            
            # 文本去重（如果启用）
            if task_config.enable_deduplication:
                texts = self._deduplicate_texts(texts)
            
            # 创建任务
            task = BatchTask(
                task_id=task_id,
                texts=texts,
                model_name=model_name,
                config=task_config,
                priority=priority,
                created_at=datetime.utcnow(),
                callback=callback,
                metadata=metadata
            )
            
            # 添加到优先队列
            heapq.heappush(self.task_queue, task)
            self.active_tasks[task_id] = task
            
            self.stats.total_tasks += 1
            
            logger.info(f"任务已提交: {task_id}, 文本数: {len(texts)}, 优先级: {priority.name}")
            return task_id
            
        except Exception as e:
            logger.error(f"提交任务失败: {e}")
            raise
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程启动: {worker_name}")
        
        while self.running:
            try:
                # 从优先队列获取任务
                if not self.task_queue:
                    await asyncio.sleep(0.1)
                    continue
                
                task = heapq.heappop(self.task_queue)
                
                if task.task_id not in self.active_tasks:
                    continue  # 任务已被取消
                
                # 处理任务
                await self._process_task(task, worker_name)
                
            except Exception as e:
                logger.error(f"工作线程错误 {worker_name}: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"工作线程停止: {worker_name}")
    
    async def _process_task(self, task: BatchTask, worker_name: str):
        """处理单个任务"""
        start_time = time.time()
        
        try:
            logger.info(f"开始处理任务: {task.task_id} (工作线程: {worker_name})")
            
            # 获取最优批大小
            metrics = self.resource_monitor.get_current_metrics()
            optimal_batch_size = self.config_manager.get_optimal_batch_size(
                task.model_name,
                len(task.texts),
                metrics["available_memory"] / 100,
                (100 - metrics["gpu_memory_usage"]) / 100
            )
            
            # 执行向量化
            vectors, errors = await self._vectorize_texts(
                task.texts, 
                task.model_name, 
                optimal_batch_size,
                task.config
            )
            
            processing_time = time.time() - start_time
            success_count = len(vectors)
            error_count = len(errors)
            
            # 质量评估（如果启用）
            quality_score = None
            if task.config.enable_quality_check and vectors:
                quality_score = await self._assess_quality(vectors)
            
            # 创建结果
            result = BatchResult(
                task_id=task.task_id,
                status="success" if error_count == 0 else "partial_success" if success_count > 0 else "failed",
                vectors=vectors,
                processing_time=processing_time,
                batch_count=len(task.texts) // optimal_batch_size + (1 if len(task.texts) % optimal_batch_size else 0),
                success_count=success_count,
                error_count=error_count,
                errors=errors,
                quality_score=quality_score,
                metadata=task.metadata
            )
            
            # 更新统计
            self.stats.update_completion(task, processing_time, success_count)
            self.model_stats[task.model_name].update_completion(task, processing_time, success_count)
            
            # 记录性能数据
            self.batch_size_history[task.model_name].append(optimal_batch_size)
            self.processing_time_history[task.model_name].append(processing_time)
            
            # 保存结果
            self.completed_tasks[task.task_id] = result
            self.task_history.append((task, result))
            
            # 清理活跃任务
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            # 执行回调
            if task.callback:
                try:
                    await task.callback(result)
                except Exception as e:
                    logger.error(f"任务回调执行失败: {task.task_id}, 错误: {e}")
            
            logger.info(f"任务处理完成: {task.task_id}, 成功: {success_count}, 失败: {error_count}, 耗时: {processing_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"任务处理失败: {task.task_id}, 错误: {e}")
            
            # 创建失败结果
            result = BatchResult(
                task_id=task.task_id,
                status="failed",
                vectors=[],
                processing_time=time.time() - start_time,
                batch_count=0,
                success_count=0,
                error_count=len(task.texts),
                errors=[{"error": str(e), "text_count": len(task.texts)}]
            )
            
            self.completed_tasks[task.task_id] = result
            self.stats.failed_tasks += 1
            
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]

    async def _vectorize_texts(self, texts: List[str], model_name: str,
                              batch_size: int, config: BatchConfig) -> Tuple[List[List[float]], List[Dict[str, Any]]]:
        """向量化文本"""
        vectors = []
        errors = []

        try:
            # 检查缓存
            cached_vectors = []
            uncached_texts = []
            uncached_indices = []

            if config.enable_caching:
                for i, text in enumerate(texts):
                    text_hash = self._hash_text(text)
                    if text_hash in self.text_cache:
                        cached_vectors.append((i, self.text_cache[text_hash]))
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i)
            else:
                uncached_texts = texts
                uncached_indices = list(range(len(texts)))

            # 处理未缓存的文本
            if uncached_texts:
                # 模拟向量化处理
                for i in range(0, len(uncached_texts), batch_size):
                    batch_texts = uncached_texts[i:i + batch_size]

                    try:
                        # 模拟处理时间
                        await asyncio.sleep(0.01 * len(batch_texts))

                        # 生成模拟向量
                        for j, text in enumerate(batch_texts):
                            vector = np.random.rand(384).tolist()  # 模拟384维向量
                            original_index = uncached_indices[i + j]
                            vectors.append((original_index, vector))

                            # 缓存结果
                            if config.enable_caching:
                                text_hash = self._hash_text(text)
                                self.text_cache[text_hash] = vector

                                # 限制缓存大小
                                if len(self.text_cache) > config.cache_size:
                                    # 删除最旧的缓存项
                                    oldest_key = next(iter(self.text_cache))
                                    del self.text_cache[oldest_key]

                    except Exception as e:
                        # 记录批次错误
                        for j in range(len(batch_texts)):
                            original_index = uncached_indices[i + j]
                            errors.append({
                                "index": original_index,
                                "text": batch_texts[j][:100],
                                "error": str(e)
                            })

            # 合并缓存和新计算的向量
            all_vectors = cached_vectors + vectors
            all_vectors.sort(key=lambda x: x[0])  # 按原始索引排序

            return [v[1] for v in all_vectors], errors

        except Exception as e:
            logger.error(f"向量化失败: {e}")
            return [], [{"error": str(e), "text_count": len(texts)}]

    def _hash_text(self, text: str) -> str:
        """计算文本哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _deduplicate_texts(self, texts: List[str]) -> List[str]:
        """文本去重"""
        seen = set()
        deduplicated = []

        for text in texts:
            text_hash = self._hash_text(text)
            if text_hash not in seen:
                seen.add(text_hash)
                deduplicated.append(text)

        if len(deduplicated) < len(texts):
            logger.info(f"去重完成: {len(texts)} -> {len(deduplicated)}")

        return deduplicated

    async def _assess_quality(self, vectors: List[List[float]]) -> float:
        """评估向量质量"""
        try:
            if len(vectors) < 2:
                return 1.0

            # 简单的质量评估：计算向量间的平均余弦相似度
            vectors_array = np.array(vectors)
            norms = np.linalg.norm(vectors_array, axis=1, keepdims=True)
            normalized_vectors = vectors_array / (norms + 1e-8)

            # 计算相似度矩阵
            similarity_matrix = np.dot(normalized_vectors, normalized_vectors.T)

            # 排除对角线元素
            mask = ~np.eye(similarity_matrix.shape[0], dtype=bool)
            similarities = similarity_matrix[mask]

            # 质量分数：1 - 平均相似度（相似度越低，质量越好）
            avg_similarity = np.mean(similarities)
            quality_score = max(0.0, 1.0 - avg_similarity)

            return float(quality_score)

        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return 0.5

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        # 检查活跃任务
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                "task_id": task_id,
                "status": "running",
                "created_at": task.created_at.isoformat(),
                "texts_count": len(task.texts),
                "model_name": task.model_name,
                "priority": task.priority.name
            }

        # 检查已完成任务
        if task_id in self.completed_tasks:
            result = self.completed_tasks[task_id]
            return {
                "task_id": task_id,
                "status": result.status,
                "processing_time": result.processing_time,
                "success_count": result.success_count,
                "error_count": result.error_count,
                "quality_score": result.quality_score
            }

        return None

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
            logger.info(f"任务已取消: {task_id}")
            return True
        return False

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        metrics = self.resource_monitor.get_current_metrics()
        avg_metrics = self.resource_monitor.get_average_metrics()

        return {
            "overall_stats": asdict(self.stats),
            "model_stats": {name: asdict(stats) for name, stats in self.model_stats.items()},
            "system_metrics": {
                "current": metrics,
                "average": avg_metrics
            },
            "queue_info": {
                "pending_tasks": len(self.task_queue),
                "active_tasks": len(self.active_tasks),
                "completed_tasks": len(self.completed_tasks)
            },
            "cache_info": {
                "cache_size": len(self.text_cache),
                "cache_hit_rate": self._calculate_cache_hit_rate()
            }
        }

    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        # 这里应该实现实际的缓存命中率计算
        # 暂时返回模拟值
        return 0.75

    async def cleanup(self):
        """清理资源"""
        logger.info("清理高级批量处理引擎资源...")

        self.running = False

        # 等待所有工作线程完成
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)

        # 清理缓存和任务
        self.text_cache.clear()
        self.deduplication_cache.clear()
        self.active_tasks.clear()
        self.completed_tasks.clear()
        self.task_queue.clear()

        logger.info("高级批量处理引擎资源清理完成")
