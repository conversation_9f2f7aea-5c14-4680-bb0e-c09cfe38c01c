"""
监控指标中间件
"""

from fastapi import FastAPI, Request, Response
from prometheus_client import Counter, Histogram, Gauge
import time
from typing import Callable

# Prometheus指标
REQUEST_COUNT = Counter(
    'vectorization_service_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'vectorization_service_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'vectorization_service_active_requests',
    'Number of active requests'
)

VECTORIZATION_COUNT = Counter(
    'vectorization_requests_total',
    'Total number of vectorization requests',
    ['model', 'type', 'status']
)

VECTORIZATION_DURATION = Histogram(
    'vectorization_duration_seconds',
    'Vectorization duration in seconds',
    ['model', 'type']
)


async def metrics_middleware(request: Request, call_next: Callable) -> Response:
    """监控指标中间件"""
    start_time = time.time()
    
    # 增加活跃请求计数
    ACTIVE_REQUESTS.inc()
    
    try:
        # 处理请求
        response = await call_next(request)
        
        # 记录指标
        duration = time.time() - start_time
        method = request.method
        endpoint = request.url.path
        status_code = response.status_code
        
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        return response
        
    except Exception as e:
        # 记录错误指标
        duration = time.time() - start_time
        method = request.method
        endpoint = request.url.path
        
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=500
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        raise
    
    finally:
        # 减少活跃请求计数
        ACTIVE_REQUESTS.dec()


def add_metrics_middleware(app: FastAPI):
    """添加监控中间件到FastAPI应用"""
    app.middleware("http")(metrics_middleware)


def record_vectorization_metrics(model: str, request_type: str, 
                                duration: float, status: str):
    """记录向量化指标"""
    VECTORIZATION_COUNT.labels(
        model=model,
        type=request_type,
        status=status
    ).inc()
    
    VECTORIZATION_DURATION.labels(
        model=model,
        type=request_type
    ).observe(duration)
