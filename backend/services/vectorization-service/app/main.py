"""
向量化服务主应用
提供高性能的文本向量化功能
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import List, Optional, Dict, Any
import asyncio
from contextlib import asynccontextmanager

from app.config.settings import get_settings
from app.core.embedding_manager import EmbeddingModelManager
from app.core.batch_processor import BatchVectorizationEngine
from app.core.quality_assessor import VectorQualityAssessor
from app.core.storage_optimizer import VectorStorageOptimizer
from app.models.vectorization import (
    VectorizationRequest, BatchVectorizationRequest,
    VectorizationResponse, BatchVectorizationResponse,
    ModelInfo, QualityAssessmentRequest
)
from app.services.vectorization_service import VectorizationService
from app.utils.logger import get_logger
from app.middleware.error_handler import add_error_handlers
from app.middleware.metrics import add_metrics_middleware

logger = get_logger(__name__)
settings = get_settings()

# 全局服务实例
vectorization_service: Optional[VectorizationService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global vectorization_service
    
    # 启动时初始化
    logger.info("🚀 启动向量化服务...")
    
    try:
        # 初始化核心组件
        embedding_manager = EmbeddingModelManager()
        batch_processor = BatchVectorizationEngine()
        quality_assessor = VectorQualityAssessor()
        storage_optimizer = VectorStorageOptimizer()
        
        # 初始化向量化服务
        vectorization_service = VectorizationService(
            embedding_manager=embedding_manager,
            batch_processor=batch_processor,
            quality_assessor=quality_assessor,
            storage_optimizer=storage_optimizer
        )
        
        await vectorization_service.initialize()
        logger.info("✅ 向量化服务初始化完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 向量化服务初始化失败: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("🔄 关闭向量化服务...")
        if vectorization_service:
            await vectorization_service.cleanup()
        logger.info("✅ 向量化服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="RAG向量化服务",
    description="提供高性能的文本向量化和质量评估功能",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加错误处理和监控中间件
add_error_handlers(app)
add_metrics_middleware(app)


def get_vectorization_service() -> VectorizationService:
    """获取向量化服务实例"""
    if vectorization_service is None:
        raise HTTPException(status_code=500, detail="向量化服务未初始化")
    return vectorization_service


@app.get("/")
async def root():
    """根路径健康检查"""
    return {"message": "RAG向量化服务运行正常", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        service = get_vectorization_service()
        health_status = await service.health_check()
        return {"status": "healthy", "details": health_status}
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="服务不可用")


@app.get("/api/v1/models", response_model=List[ModelInfo])
async def get_available_models(
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    获取可用的嵌入模型列表
    
    Returns:
        List[ModelInfo]: 模型信息列表
    """
    try:
        models = await service.get_available_models()
        return models
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@app.post("/api/v1/vectorize/single", response_model=VectorizationResponse)
async def vectorize_single_text(
    request: VectorizationRequest,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    单文本向量化
    
    Args:
        request: 向量化请求
        service: 向量化服务实例
    
    Returns:
        VectorizationResponse: 向量化结果
    """
    try:
        logger.info(f"单文本向量化: 模型={request.model_name}, 长度={len(request.text)}")
        
        result = await service.vectorize_single(
            text=request.text,
            model_name=request.model_name,
            normalize=request.normalize
        )
        
        logger.info(f"单文本向量化完成: 维度={len(result.vector)}")
        return result
        
    except Exception as e:
        logger.error(f"单文本向量化失败: {e}")
        raise HTTPException(status_code=400, detail=f"向量化失败: {str(e)}")


@app.post("/api/v1/vectorize/batch", response_model=BatchVectorizationResponse)
async def vectorize_batch_texts(
    request: BatchVectorizationRequest,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    批量文本向量化
    
    Args:
        request: 批量向量化请求
        service: 向量化服务实例
    
    Returns:
        BatchVectorizationResponse: 批量向量化结果
    """
    try:
        logger.info(f"批量向量化: 文本数={len(request.texts)}, 模型={request.model_name}")
        
        result = await service.vectorize_batch(
            texts=request.texts,
            model_name=request.model_name,
            batch_size=request.batch_size,
            normalize=request.normalize
        )
        
        logger.info(f"批量向量化完成: 成功={result.success_count}, 失败={result.error_count}")
        return result
        
    except Exception as e:
        logger.error(f"批量向量化失败: {e}")
        raise HTTPException(status_code=400, detail=f"批量向量化失败: {str(e)}")


@app.post("/api/v1/vectorize/async")
async def async_vectorize(
    request: BatchVectorizationRequest,
    callback_url: Optional[str] = None,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    异步批量向量化
    
    Args:
        request: 批量向量化请求
        callback_url: 回调URL
        service: 向量化服务实例
    
    Returns:
        dict: 任务信息
    """
    try:
        logger.info(f"异步向量化: 文本数={len(request.texts)}")
        
        task_info = await service.async_vectorize(
            texts=request.texts,
            model_name=request.model_name,
            callback_url=callback_url,
            batch_size=request.batch_size
        )
        
        logger.info(f"异步任务创建: {task_info['task_id']}")
        return task_info
        
    except Exception as e:
        logger.error(f"异步向量化失败: {e}")
        raise HTTPException(status_code=400, detail=f"异步向量化失败: {str(e)}")


@app.get("/api/v1/tasks/{task_id}")
async def get_task_status(
    task_id: str,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    获取异步任务状态
    
    Args:
        task_id: 任务ID
        service: 向量化服务实例
    
    Returns:
        dict: 任务状态
    """
    try:
        status = await service.get_task_status(task_id)
        return status
    except Exception as e:
        logger.error(f"获取任务状态失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")


@app.post("/api/v1/quality/assess")
async def assess_vector_quality(
    request: QualityAssessmentRequest,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    向量质量评估
    
    Args:
        request: 质量评估请求
        service: 向量化服务实例
    
    Returns:
        dict: 质量评估结果
    """
    try:
        logger.info(f"向量质量评估: 向量数={len(request.vectors)}")
        
        result = await service.assess_quality(
            vectors=request.vectors,
            labels=request.labels,
            metrics=request.metrics
        )
        
        logger.info(f"质量评估完成")
        return result
        
    except Exception as e:
        logger.error(f"向量质量评估失败: {e}")
        raise HTTPException(status_code=400, detail=f"质量评估失败: {str(e)}")


@app.post("/api/v1/models/{model_name}/switch")
async def switch_model(
    model_name: str,
    service: VectorizationService = Depends(get_vectorization_service)
):
    """
    切换默认模型
    
    Args:
        model_name: 模型名称
        service: 向量化服务实例
    
    Returns:
        dict: 切换结果
    """
    try:
        result = await service.switch_model(model_name)
        logger.info(f"模型切换: {model_name}")
        return result
    except Exception as e:
        logger.error(f"模型切换失败: {model_name}, 错误: {e}")
        raise HTTPException(status_code=400, detail=f"模型切换失败: {str(e)}")


@app.get("/metrics")
async def get_metrics():
    """获取Prometheus监控指标"""
    from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
    return JSONResponse(
        content=generate_latest().decode('utf-8'),
        media_type=CONTENT_TYPE_LATEST
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
