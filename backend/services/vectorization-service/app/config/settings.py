"""
向量化服务配置设置
"""

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

from pydantic import Field
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "RAG向量化服务"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8003, env="PORT")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/rag_db",
        env="DATABASE_URL"
    )
    
    # Redis配置
    REDIS_URL: str = Field(
        default="redis://localhost:6379",
        env="REDIS_URL"
    )
    
    # 模型配置
    DEFAULT_EMBEDDING_MODEL: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        env="DEFAULT_EMBEDDING_MODEL"
    )
    EMBEDDING_DIMENSION: int = Field(default=384, env="EMBEDDING_DIMENSION")
    DEVICE: str = Field(default="auto", env="DEVICE")  # auto, cpu, cuda
    MODEL_CACHE_DIR: str = Field(default="./models", env="MODEL_CACHE_DIR")
    
    # 批处理配置
    DEFAULT_BATCH_SIZE: int = Field(default=32, env="DEFAULT_BATCH_SIZE")
    MAX_BATCH_SIZE: int = Field(default=128, env="MAX_BATCH_SIZE")
    MAX_TEXT_LENGTH: int = Field(default=512, env="MAX_TEXT_LENGTH")
    
    # 性能配置
    MAX_WORKERS: int = Field(default=4, env="MAX_WORKERS")
    TIMEOUT: int = Field(default=300, env="TIMEOUT")
    ENABLE_GPU: bool = Field(default=True, env="ENABLE_GPU")
    MIXED_PRECISION: bool = Field(default=True, env="MIXED_PRECISION")
    
    # 任务队列配置
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/0",
        env="CELERY_BROKER_URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/0",
        env="CELERY_RESULT_BACKEND"
    )
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=8004, env="METRICS_PORT")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        env="LOG_FORMAT"
    )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 全局配置实例
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """获取应用配置"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
