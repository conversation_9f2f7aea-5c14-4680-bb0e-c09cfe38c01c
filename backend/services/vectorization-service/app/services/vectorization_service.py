"""
向量化服务主要业务逻辑
"""

import asyncio
import time
import uuid
from typing import List, Dict, Optional, Any
from datetime import datetime

from app.core.embedding_manager import EmbeddingModelManager
from app.core.batch_processor import BatchVectorizationEngine
from app.core.quality_assessor import VectorQualityAssessor
from app.core.storage_optimizer import VectorStorageOptimizer
from app.models.vectorization import (
    VectorizationResponse, BatchVectorizationResponse,
    ModelInfo, AsyncTaskInfo, TaskStatus
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


class VectorizationService:
    """向量化服务主类"""
    
    def __init__(self, embedding_manager: EmbeddingModelManager,
                 batch_processor: BatchVectorizationEngine,
                 quality_assessor: VectorQualityAssessor,
                 storage_optimizer: VectorStorageOptimizer):
        self.embedding_manager = embedding_manager
        self.batch_processor = batch_processor
        self.quality_assessor = quality_assessor
        self.storage_optimizer = storage_optimizer
        self.async_tasks = {}
    
    async def initialize(self):
        """初始化服务"""
        logger.info("初始化向量化服务...")
        
        await self.embedding_manager.initialize()
        await self.batch_processor.initialize()
        await self.quality_assessor.initialize()
        await self.storage_optimizer.initialize()
        
        logger.info("向量化服务初始化完成")
    
    async def vectorize_single(self, text: str, model_name: Optional[str] = None,
                              normalize: bool = True) -> VectorizationResponse:
        """单文本向量化"""
        start_time = time.time()
        
        try:
            # 编码文本
            embeddings = await self.embedding_manager.encode_texts(
                texts=[text],
                model_name=model_name,
                normalize=normalize
            )
            
            processing_time = time.time() - start_time
            vector = embeddings[0].tolist()
            
            return VectorizationResponse(
                text=text,
                vector=vector,
                dimension=len(vector),
                model_name=model_name or self.embedding_manager.current_model,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"单文本向量化失败: {e}")
            raise
    
    async def vectorize_batch(self, texts: List[str], model_name: Optional[str] = None,
                             batch_size: int = 32, normalize: bool = True) -> BatchVectorizationResponse:
        """批量文本向量化"""
        start_time = time.time()
        
        try:
            # 编码文本
            embeddings = await self.embedding_manager.encode_texts(
                texts=texts,
                model_name=model_name,
                batch_size=batch_size,
                normalize=normalize
            )
            
            # 构建结果
            results = []
            errors = []
            
            for i, (text, embedding) in enumerate(zip(texts, embeddings)):
                try:
                    vector = embedding.tolist()
                    result = VectorizationResponse(
                        text=text,
                        vector=vector,
                        dimension=len(vector),
                        model_name=model_name or self.embedding_manager.current_model,
                        processing_time=0.0  # 单个文本时间在批处理中不单独计算
                    )
                    results.append(result)
                except Exception as e:
                    errors.append({"index": i, "text": text[:50], "error": str(e)})
            
            total_processing_time = time.time() - start_time
            
            return BatchVectorizationResponse(
                total_texts=len(texts),
                success_count=len(results),
                error_count=len(errors),
                results=results,
                errors=errors,
                total_processing_time=total_processing_time,
                average_processing_time=total_processing_time / len(texts) if texts else 0,
                model_name=model_name or self.embedding_manager.current_model
            )
            
        except Exception as e:
            logger.error(f"批量向量化失败: {e}")
            raise
    
    async def async_vectorize(self, texts: List[str], model_name: Optional[str] = None,
                             callback_url: Optional[str] = None,
                             batch_size: int = 32) -> Dict[str, Any]:
        """异步批量向量化"""
        task_id = str(uuid.uuid4())
        
        # 创建任务信息
        task_info = AsyncTaskInfo(
            task_id=task_id,
            status=TaskStatus.PENDING,
            total_texts=len(texts)
        )
        
        self.async_tasks[task_id] = task_info
        
        # 启动异步任务
        asyncio.create_task(self._process_async_task(
            task_id, texts, model_name, callback_url, batch_size
        ))
        
        return {
            "task_id": task_id,
            "status": "pending",
            "total_texts": len(texts),
            "estimated_time": len(texts) * 0.01  # 简单估算
        }
    
    async def _process_async_task(self, task_id: str, texts: List[str],
                                 model_name: Optional[str], callback_url: Optional[str],
                                 batch_size: int):
        """处理异步任务"""
        try:
            task_info = self.async_tasks[task_id]
            task_info.status = TaskStatus.RUNNING
            task_info.started_at = datetime.utcnow()
            
            # 执行向量化
            result = await self.vectorize_batch(
                texts=texts,
                model_name=model_name,
                batch_size=batch_size
            )
            
            # 更新任务状态
            task_info.status = TaskStatus.SUCCESS
            task_info.completed_at = datetime.utcnow()
            task_info.success_count = result.success_count
            task_info.error_count = result.error_count
            task_info.progress = 100.0
            
            # 如果有回调URL，发送结果
            if callback_url:
                await self._send_callback(callback_url, result)
            
        except Exception as e:
            task_info = self.async_tasks.get(task_id)
            if task_info:
                task_info.status = TaskStatus.FAILED
                task_info.error_message = str(e)
                task_info.completed_at = datetime.utcnow()
            
            logger.error(f"异步任务失败: {task_id}, 错误: {e}")
    
    async def _send_callback(self, callback_url: str, result: Any):
        """发送回调"""
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                await client.post(callback_url, json=result.dict())
        except Exception as e:
            logger.error(f"回调发送失败: {callback_url}, 错误: {e}")
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        task_info = self.async_tasks.get(task_id)
        if not task_info:
            raise ValueError(f"任务不存在: {task_id}")
        
        return task_info.dict()
    
    async def get_available_models(self) -> List[ModelInfo]:
        """获取可用模型列表"""
        return await self.embedding_manager.get_available_models()
    
    async def switch_model(self, model_name: str) -> Dict[str, Any]:
        """切换模型"""
        return await self.embedding_manager.switch_model(model_name)
    
    async def assess_quality(self, vectors: List[List[float]],
                           labels: Optional[List[str]] = None,
                           metrics: Optional[List[str]] = None) -> Dict[str, Any]:
        """向量质量评估"""
        return await self.quality_assessor.assess_quality(vectors, labels)
    
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            components = {
                "embedding_manager": "healthy",
                "batch_processor": "healthy",
                "quality_assessor": "healthy",
                "storage_optimizer": "healthy"
            }
            
            # 检查当前模型状态
            if self.embedding_manager.current_model:
                components["current_model"] = self.embedding_manager.current_model
            
            return components
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理向量化服务资源...")
        
        await self.embedding_manager.cleanup()
        await self.batch_processor.cleanup()
        await self.quality_assessor.cleanup()
        await self.storage_optimizer.cleanup()
        
        self.async_tasks.clear()
        
        logger.info("向量化服务资源清理完成")
