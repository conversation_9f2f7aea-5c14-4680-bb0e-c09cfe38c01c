"""
向量化相关数据模型
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class VectorizationRequest(BaseModel):
    """单文本向量化请求"""
    text: str = Field(..., description="待向量化的文本")
    model_name: Optional[str] = Field(None, description="模型名称")
    normalize: bool = Field(True, description="是否归一化向量")


class BatchVectorizationRequest(BaseModel):
    """批量向量化请求"""
    texts: List[str] = Field(..., description="待向量化的文本列表")
    model_name: Optional[str] = Field(None, description="模型名称")
    batch_size: int = Field(32, description="批处理大小")
    normalize: bool = Field(True, description="是否归一化向量")


class VectorResult(BaseModel):
    """向量结果"""
    vector: List[float] = Field(..., description="向量数据")
    dimension: int = Field(..., description="向量维度")
    model_name: str = Field(..., description="使用的模型名称")
    processing_time: float = Field(..., description="处理时间（秒）")


class VectorizationResponse(BaseModel):
    """向量化响应"""
    text: str = Field(..., description="原始文本")
    vector: List[float] = Field(..., description="向量数据")
    dimension: int = Field(..., description="向量维度")
    model_name: str = Field(..., description="使用的模型名称")
    processing_time: float = Field(..., description="处理时间（秒）")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class BatchVectorizationResponse(BaseModel):
    """批量向量化响应"""
    total_texts: int = Field(..., description="总文本数")
    success_count: int = Field(..., description="成功数量")
    error_count: int = Field(..., description="失败数量")
    results: List[VectorizationResponse] = Field(..., description="向量化结果列表")
    errors: List[Dict[str, str]] = Field(default_factory=list, description="错误信息")
    total_processing_time: float = Field(..., description="总处理时间（秒）")
    average_processing_time: float = Field(..., description="平均处理时间（秒）")
    model_name: str = Field(..., description="使用的模型名称")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class ModelInfo(BaseModel):
    """模型信息"""
    name: str = Field(..., description="模型名称")
    dimension: int = Field(..., description="向量维度")
    max_seq_length: int = Field(..., description="最大序列长度")
    languages: List[str] = Field(..., description="支持的语言")
    description: str = Field(..., description="模型描述")
    is_loaded: bool = Field(False, description="是否已加载")
    is_current: bool = Field(False, description="是否为当前模型")


class QualityMetrics(str, Enum):
    """质量评估指标"""
    SILHOUETTE_SCORE = "silhouette_score"
    CALINSKI_HARABASZ = "calinski_harabasz"
    DAVIES_BOULDIN = "davies_bouldin"
    DIMENSION_UTILIZATION = "dimension_utilization"
    SIMILARITY_DISTRIBUTION = "similarity_distribution"


class QualityAssessmentRequest(BaseModel):
    """质量评估请求"""
    vectors: List[List[float]] = Field(..., description="向量列表")
    labels: Optional[List[str]] = Field(None, description="标签列表")
    metrics: List[QualityMetrics] = Field(
        default=[QualityMetrics.SILHOUETTE_SCORE],
        description="评估指标"
    )


class QualityAssessmentResponse(BaseModel):
    """质量评估响应"""
    vector_count: int = Field(..., description="向量数量")
    dimension: int = Field(..., description="向量维度")
    metrics: Dict[str, float] = Field(..., description="评估指标结果")
    summary: Dict[str, Any] = Field(..., description="评估摘要")
    created_at: datetime = Field(default_factory=datetime.utcnow)


class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AsyncTaskInfo(BaseModel):
    """异步任务信息"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    total_texts: int = Field(..., description="总文本数")
    processed_texts: int = Field(0, description="已处理文本数")
    success_count: int = Field(0, description="成功数量")
    error_count: int = Field(0, description="失败数量")
    progress: float = Field(0.0, description="进度百分比")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_url: Optional[str] = Field(None, description="结果URL")


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = Field(..., description="服务版本")
    components: Dict[str, str] = Field(..., description="组件状态")
    system_info: Dict[str, Any] = Field(..., description="系统信息")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    detail: Optional[str] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = Field(None, description="请求ID")


# 工具函数
def create_task_id() -> str:
    """生成任务ID"""
    import uuid
    return str(uuid.uuid4())


def calculate_progress(processed: int, total: int) -> float:
    """计算进度百分比"""
    if total == 0:
        return 100.0
    return min(100.0, (processed / total) * 100.0)


def estimate_processing_time(text_count: int, model_name: str) -> float:
    """估算处理时间（秒）"""
    # 基于模型和文本数量的简单估算
    base_time_per_text = {
        "paraphrase-multilingual-MiniLM-L12-v2": 0.01,
        "all-MiniLM-L6-v2": 0.008,
        "paraphrase-multilingual-mpnet-base-v2": 0.02,
        "text2vec-base-chinese": 0.015
    }
    
    time_per_text = base_time_per_text.get(model_name, 0.01)
    return text_count * time_per_text
