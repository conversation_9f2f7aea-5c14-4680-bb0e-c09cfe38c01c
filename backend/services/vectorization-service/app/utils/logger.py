"""
日志配置工具
"""

import sys
from loguru import logger
from app.config.settings import get_settings

settings = get_settings()

# 移除默认处理器
logger.remove()

# 添加控制台处理器
logger.add(
    sys.stdout,
    format=settings.LOG_FORMAT,
    level=settings.LOG_LEVEL,
    colorize=True,
    backtrace=True,
    diagnose=True
)

# 添加文件处理器
logger.add(
    "logs/vectorization_service.log",
    format=settings.LOG_FORMAT,
    level=settings.LOG_LEVEL,
    rotation="100 MB",
    retention="30 days",
    compression="zip",
    backtrace=True,
    diagnose=True
)


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger
