# 向量化服务 (Embedding Service)

RAG系统的向量化服务，负责将文本转换为向量表示，支持多种嵌入模型和向量数据库。

## 📋 功能特性

### 🤖 多模型支持
- **OpenAI模型**: text-embedding-ada-002, text-embedding-3-small/large
- **本地模型**: Sentence Transformers系列模型
- **Hugging Face**: 支持各种开源嵌入模型
- **模型缓存**: 智能缓存机制提升性能

### 📊 向量数据库
- **ChromaDB**: 本地向量数据库
- **Pinecone**: 云端向量数据库
- **Weaviate**: 开源向量数据库（扩展支持）
- **自动管理**: 集合创建、索引优化

### ⚡ 高性能处理
- **异步处理**: 基于FastAPI的异步架构
- **批量处理**: 支持大批量文本向量化
- **任务队列**: Redis队列管理长时间任务
- **并发控制**: 多工作进程并行处理

### 🔍 智能搜索
- **语义搜索**: 基于向量相似度的语义搜索
- **混合搜索**: 结合关键词和语义搜索
- **过滤搜索**: 支持元数据过滤
- **相似度排序**: 多种相似度计算方法

## 🏗️ 技术架构

### 技术栈
- **框架**: FastAPI + Uvicorn
- **语言**: Python 3.11+
- **数据库**: PostgreSQL + Redis
- **向量库**: ChromaDB/Pinecone
- **ML库**: sentence-transformers, openai

### 核心组件
```
Embedding Service
├── 嵌入模型管理器    # 多模型加载和管理
├── 向量数据库客户端  # 多数据库适配
├── 任务处理器       # 异步任务处理
├── Redis缓存       # 结果缓存和队列
├── API路由        # RESTful接口
└── 数据库模型      # 元数据存储
```

## 🚀 快速开始

### 环境要求
- Python >= 3.11
- PostgreSQL >= 14.0
- Redis >= 6.0
- ChromaDB或Pinecone账号

### 安装依赖
```bash
pip install -r requirements.txt
```

### 环境配置
```bash
cp .env.example .env
```

主要配置项：
```env
# 服务配置
HOST=0.0.0.0
PORT=8001
DEBUG=false

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/embedding_service

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# 向量数据库配置
VECTOR_DB_TYPE=chroma
CHROMA_URL=http://localhost:8000

# 或使用Pinecone
# VECTOR_DB_TYPE=pinecone
# PINECONE_API_KEY=your-api-key
# PINECONE_ENVIRONMENT=us-west1-gcp

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 默认模型
DEFAULT_EMBEDDING_MODEL=text-embedding-ada-002

# 任务处理配置
MAX_CONCURRENT_TASKS=5
BATCH_SIZE=10
```

### 运行服务

#### 开发模式
```bash
python main.py
```

#### 生产模式
```bash
uvicorn main:app --host 0.0.0.0 --port 8001 --workers 4
```

#### 使用Docker
```bash
docker build -t embedding-service .
docker run -p 8001:8001 embedding-service
```

## 📡 API 接口

### 单文本向量化
```http
POST /api/v1/embed/text
Content-Type: application/json

{
  "text": "这是要向量化的文本",
  "model": "text-embedding-ada-002"
}
```

### 批量文本向量化
```http
POST /api/v1/embed/texts
Content-Type: application/json

{
  "texts": ["文本1", "文本2", "文本3"],
  "model": "text-embedding-ada-002"
}
```

### 文档向量化
```http
POST /api/v1/embed/document
Content-Type: application/json

{
  "document_id": "doc-123",
  "chunks": [
    {
      "id": "chunk-1",
      "content": "文档块内容",
      "metadata": {"page": 1, "section": "introduction"}
    }
  ],
  "model": "text-embedding-ada-002",
  "async_process": true
}
```

### 向量搜索
```http
POST /api/v1/search
Content-Type: application/json

{
  "query": "搜索查询文本",
  "document_id": "doc-123",
  "top_k": 10,
  "model": "text-embedding-ada-002",
  "filter": {"page": 1}
}
```

### 任务状态查询
```http
GET /api/v1/job/{job_id}
```

### 获取文档向量
```http
GET /api/v1/document/{document_id}/embeddings
```

### 删除文档向量
```http
DELETE /api/v1/document/{document_id}/embeddings
```

### 获取模型列表
```http
GET /api/v1/models
```

### 健康检查
```http
GET /api/v1/health
```

## 🔧 配置说明

### 嵌入模型配置
```python
# OpenAI模型
{
  "name": "text-embedding-ada-002",
  "provider": "openai",
  "dimension": 1536,
  "max_tokens": 8191
}

# 本地模型
{
  "name": "all-MiniLM-L6-v2",
  "provider": "local",
  "dimension": 384,
  "max_tokens": 512
}
```

### 向量数据库配置
```python
# ChromaDB配置
VECTOR_DB_CONFIG = {
  "type": "chroma",
  "url": "http://localhost:8000",
  "path": "./chroma_db"  # 本地模式
}

# Pinecone配置
VECTOR_DB_CONFIG = {
  "type": "pinecone",
  "api_key": "your-api-key",
  "environment": "us-west1-gcp"
}
```

### 任务处理配置
```python
TASK_CONFIG = {
  "max_concurrent_tasks": 5,    # 最大并发任务数
  "batch_size": 10,            # 批处理大小
  "queue_timeout": 5,          # 队列超时时间
  "retry_attempts": 3          # 重试次数
}
```

## 📊 数据模型

### 向量记录表
```sql
CREATE TABLE embedding_records (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL,
    chunk_id UUID NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    embedding_model VARCHAR(100) NOT NULL,
    embedding_dimension INTEGER NOT NULL,
    embedding_vector FLOAT[],
    metadata JSONB DEFAULT '{}',
    language VARCHAR(10) DEFAULT 'zh',
    token_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 向量化任务表
```sql
CREATE TABLE embedding_jobs (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    total_chunks INTEGER DEFAULT 0,
    processed_chunks INTEGER DEFAULT 0,
    failed_chunks INTEGER DEFAULT 0,
    embedding_model VARCHAR(100) NOT NULL,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
pytest tests/

# 集成测试
pytest tests/integration/

# 性能测试
pytest tests/performance/
```

### 测试向量化
```bash
curl -X POST http://localhost:8001/api/v1/embed/text \
  -H "Content-Type: application/json" \
  -d '{"text": "测试文本", "model": "text-embedding-ada-002"}'
```

### 测试搜索
```bash
curl -X POST http://localhost:8001/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"query": "搜索测试", "collection_name": "test", "top_k": 5}'
```

## 📈 性能优化

### 缓存策略
1. **向量缓存**: Redis缓存计算结果
2. **模型缓存**: 内存缓存加载的模型
3. **查询缓存**: 缓存热门查询结果

### 批处理优化
1. **动态批大小**: 根据模型调整批大小
2. **并行处理**: 多进程并行向量化
3. **内存管理**: 及时释放大向量内存

### 数据库优化
1. **索引优化**: 向量和元数据索引
2. **分区表**: 按时间分区大表
3. **连接池**: 数据库连接池管理

## 🚨 故障排查

### 常见问题

1. **模型加载失败**
   ```bash
   # 检查模型路径和权限
   ls -la models/
   
   # 检查网络连接
   curl -I https://api.openai.com
   ```

2. **向量数据库连接失败**
   ```bash
   # 检查ChromaDB状态
   curl http://localhost:8000/api/v1/heartbeat
   
   # 检查Pinecone连接
   curl -H "Api-Key: your-key" https://controller.us-west1-gcp.pinecone.io/databases
   ```

3. **任务处理缓慢**
   ```bash
   # 检查队列状态
   curl http://localhost:8001/api/v1/health
   
   # 查看任务日志
   tail -f logs/embedding.log
   ```

### 监控指标
- 向量化吞吐量
- 任务队列长度
- 模型响应时间
- 缓存命中率
- 错误率统计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
