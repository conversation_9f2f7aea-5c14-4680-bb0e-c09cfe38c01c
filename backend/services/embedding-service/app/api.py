"""
API路由定义
"""

import uuid
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from loguru import logger

from .embedding_models import embedding_model_manager
from .vector_db import vector_db_client
from .redis_client import embedding_queue, embedding_cache
from .database import async_session, EmbeddingJobDAO, EmbeddingRecordDAO
from .task_processor import task_processor

# 创建路由器
router = APIRouter()


# 请求模型
class EmbedTextRequest(BaseModel):
    """文本向量化请求"""
    text: str = Field(..., description="要向量化的文本")
    model: Optional[str] = Field(None, description="使用的模型名称")


class EmbedTextsRequest(BaseModel):
    """批量文本向量化请求"""
    texts: List[str] = Field(..., description="要向量化的文本列表")
    model: Optional[str] = Field(None, description="使用的模型名称")


class DocumentChunk(BaseModel):
    """文档块"""
    id: str = Field(..., description="块ID")
    content: str = Field(..., description="块内容")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="块元数据")
    language: Optional[str] = Field("zh", description="语言")


class EmbedDocumentRequest(BaseModel):
    """文档向量化请求"""
    document_id: str = Field(..., description="文档ID")
    chunks: List[DocumentChunk] = Field(..., description="文档块列表")
    model: Optional[str] = Field(None, description="使用的模型名称")
    async_process: bool = Field(True, description="是否异步处理")


class SearchRequest(BaseModel):
    """向量搜索请求"""
    query: str = Field(..., description="查询文本")
    collection_name: Optional[str] = Field(None, description="集合名称")
    document_id: Optional[str] = Field(None, description="文档ID")
    top_k: int = Field(10, description="返回结果数量")
    model: Optional[str] = Field(None, description="使用的模型名称")
    filter: Optional[Dict[str, Any]] = Field(None, description="过滤条件")


# 响应模型
class EmbedResponse(BaseModel):
    """向量化响应"""
    success: bool
    data: Dict[str, Any]
    message: str


class SearchResponse(BaseModel):
    """搜索响应"""
    success: bool
    data: Dict[str, Any]
    message: str


# API端点
@router.post("/embed/text", response_model=EmbedResponse)
async def embed_text(request: EmbedTextRequest):
    """单个文本向量化"""
    try:
        # 生成向量
        embedding = await embedding_model_manager.encode_single_text(
            request.text, 
            request.model
        )
        
        return EmbedResponse(
            success=True,
            data={
                "embedding": embedding,
                "dimension": len(embedding),
                "model": request.model or "default",
                "text_length": len(request.text)
            },
            message="文本向量化成功"
        )
        
    except Exception as e:
        logger.error(f"文本向量化失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量化失败: {str(e)}")


@router.post("/embed/texts", response_model=EmbedResponse)
async def embed_texts(request: EmbedTextsRequest):
    """批量文本向量化"""
    try:
        if len(request.texts) > 100:
            raise HTTPException(status_code=400, detail="批量处理最多支持100个文本")
        
        # 生成向量
        embeddings = await embedding_model_manager.encode_texts(
            request.texts, 
            request.model
        )
        
        return EmbedResponse(
            success=True,
            data={
                "embeddings": embeddings,
                "count": len(embeddings),
                "dimension": len(embeddings[0]) if embeddings else 0,
                "model": request.model or "default"
            },
            message=f"成功向量化 {len(embeddings)} 个文本"
        )
        
    except Exception as e:
        logger.error(f"批量文本向量化失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量化失败: {str(e)}")


@router.post("/embed/document", response_model=EmbedResponse)
async def embed_document(request: EmbedDocumentRequest, background_tasks: BackgroundTasks):
    """文档向量化"""
    try:
        if request.async_process:
            # 异步处理
            task_data = {
                "type": "embed_document",
                "document_id": request.document_id,
                "chunks": [chunk.dict() for chunk in request.chunks],
                "model_name": request.model
            }
            
            # 添加到队列
            await embedding_queue.push(task_data)
            
            return EmbedResponse(
                success=True,
                data={
                    "document_id": request.document_id,
                    "chunks_count": len(request.chunks),
                    "status": "queued"
                },
                message="文档向量化任务已加入队列"
            )
        else:
            # 同步处理（小文档）
            if len(request.chunks) > 50:
                raise HTTPException(status_code=400, detail="同步处理最多支持50个块，请使用异步模式")
            
            # 直接处理
            texts = [chunk.content for chunk in request.chunks]
            embeddings = await embedding_model_manager.encode_texts(texts, request.model)
            
            return EmbedResponse(
                success=True,
                data={
                    "document_id": request.document_id,
                    "embeddings": embeddings,
                    "chunks_count": len(request.chunks),
                    "dimension": len(embeddings[0]) if embeddings else 0
                },
                message="文档向量化完成"
            )
            
    except Exception as e:
        logger.error(f"文档向量化失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量化失败: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_vectors(request: SearchRequest):
    """向量搜索"""
    try:
        # 生成查询向量
        query_embedding = await embedding_model_manager.encode_single_text(
            request.query, 
            request.model
        )
        
        # 确定集合名称
        if request.document_id:
            collection_name = f"document_{request.document_id}"
        elif request.collection_name:
            collection_name = request.collection_name
        else:
            raise HTTPException(status_code=400, detail="必须指定document_id或collection_name")
        
        # 执行搜索
        results = await vector_db_client.search_vectors(
            collection_name,
            query_embedding,
            request.top_k,
            request.filter
        )
        
        return SearchResponse(
            success=True,
            data={
                "query": request.query,
                "results": results,
                "count": len(results),
                "collection": collection_name
            },
            message=f"搜索完成，找到 {len(results)} 个结果"
        )
        
    except Exception as e:
        logger.error(f"向量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/job/{job_id}")
async def get_job_status(job_id: str):
    """获取任务状态"""
    try:
        async with async_session() as session:
            job = await EmbeddingJobDAO.get_by_id(session, uuid.UUID(job_id))
            
            if not job:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            return {
                "success": True,
                "data": {
                    "id": str(job.id),
                    "document_id": str(job.document_id),
                    "status": job.status,
                    "total_chunks": job.total_chunks,
                    "processed_chunks": job.processed_chunks,
                    "failed_chunks": job.failed_chunks,
                    "progress": (job.processed_chunks / job.total_chunks * 100) if job.total_chunks > 0 else 0,
                    "embedding_model": job.embedding_model,
                    "error_message": job.error_message,
                    "started_at": job.started_at.isoformat() if job.started_at else None,
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                    "created_at": job.created_at.isoformat()
                },
                "message": "获取任务状态成功"
            }
            
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/document/{document_id}/embeddings")
async def get_document_embeddings(document_id: str):
    """获取文档的向量记录"""
    try:
        async with async_session() as session:
            records = await EmbeddingRecordDAO.get_by_document_id(
                session, 
                uuid.UUID(document_id)
            )
            
            embeddings_data = []
            for record in records:
                embeddings_data.append({
                    "id": str(record.id),
                    "chunk_id": str(record.chunk_id),
                    "content_hash": record.content_hash,
                    "embedding_model": record.embedding_model,
                    "embedding_dimension": record.embedding_dimension,
                    "metadata": record.metadata,
                    "language": record.language,
                    "token_count": record.token_count,
                    "created_at": record.created_at.isoformat()
                })
            
            return {
                "success": True,
                "data": {
                    "document_id": document_id,
                    "embeddings": embeddings_data,
                    "count": len(embeddings_data)
                },
                "message": f"获取文档向量记录成功，共 {len(embeddings_data)} 条"
            }
            
    except Exception as e:
        logger.error(f"获取文档向量记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取向量记录失败: {str(e)}")


@router.delete("/document/{document_id}/embeddings")
async def delete_document_embeddings(document_id: str, background_tasks: BackgroundTasks):
    """删除文档的向量记录"""
    try:
        # 添加删除任务到队列
        task_data = {
            "type": "delete_embeddings",
            "document_id": document_id
        }
        
        await embedding_queue.push(task_data)
        
        return {
            "success": True,
            "data": {
                "document_id": document_id,
                "status": "queued"
            },
            "message": "文档向量删除任务已加入队列"
        }
        
    except Exception as e:
        logger.error(f"删除文档向量失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除向量失败: {str(e)}")


@router.get("/models")
async def get_models():
    """获取可用的嵌入模型"""
    try:
        loaded_models = embedding_model_manager.get_loaded_models()
        
        return {
            "success": True,
            "data": {
                "loaded_models": loaded_models,
                "default_model": embedding_model_manager.default_model_name
            },
            "message": "获取模型列表成功"
        }
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查各组件状态
        status = {
            "embedding_service": "healthy",
            "task_processor": "running" if task_processor.is_running else "stopped",
            "loaded_models": len(embedding_model_manager.models),
            "queue_size": await embedding_queue.size()
        }
        
        return {
            "success": True,
            "data": status,
            "message": "服务健康"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "data": {"error": str(e)},
            "message": "服务异常"
        }
