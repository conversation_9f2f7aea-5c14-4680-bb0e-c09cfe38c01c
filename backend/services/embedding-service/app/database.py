"""
数据库连接和操作模块
"""

import asyncio
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, String, Integer, DateTime, Text, JSON, Float, Boolean
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from loguru import logger
import uuid

from .config import settings

# 创建基础模型类
Base = declarative_base()

# 全局变量
engine = None
async_session = None


class EmbeddingRecord(Base):
    """向量记录表"""
    __tablename__ = "embedding_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    chunk_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    content = Column(Text, nullable=False)
    content_hash = Column(String(64), nullable=False, index=True)
    embedding_model = Column(String(100), nullable=False)
    embedding_dimension = Column(Integer, nullable=False)
    embedding_vector = Column(ARRAY(Float), nullable=True)  # 存储向量
    metadata = Column(JSON, default={})
    language = Column(String(10), default="zh")
    token_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<EmbeddingRecord(id={self.id}, document_id={self.document_id})>"


class EmbeddingJob(Base):
    """向量化任务表"""
    __tablename__ = "embedding_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    status = Column(String(20), nullable=False, default="pending")  # pending, processing, completed, failed
    total_chunks = Column(Integer, default=0)
    processed_chunks = Column(Integer, default=0)
    failed_chunks = Column(Integer, default=0)
    embedding_model = Column(String(100), nullable=False)
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<EmbeddingJob(id={self.id}, status={self.status})>"


class EmbeddingModel(Base):
    """嵌入模型表"""
    __tablename__ = "embedding_models"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    provider = Column(String(50), nullable=False)  # openai, local, huggingface
    model_path = Column(String(500), nullable=False)
    dimension = Column(Integer, nullable=False)
    max_tokens = Column(Integer, default=512)
    is_active = Column(Boolean, default=True)
    description = Column(Text, nullable=True)
    config = Column(JSON, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<EmbeddingModel(name={self.name}, provider={self.provider})>"


async def init_db():
    """初始化数据库连接"""
    global engine, async_session
    
    try:
        # 创建异步引擎
        engine = create_async_engine(
            settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
            echo=settings.DEBUG,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # 创建会话工厂
        async_session = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化成功")
        
        # 初始化默认嵌入模型
        await init_default_models()
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_db():
    """关闭数据库连接"""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("数据库连接已关闭")


async def get_db_session() -> AsyncSession:
    """获取数据库会话"""
    if not async_session:
        raise RuntimeError("数据库未初始化")
    
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()


async def init_default_models():
    """初始化默认嵌入模型"""
    try:
        async with async_session() as session:
            # 检查是否已有模型
            from sqlalchemy import select
            result = await session.execute(select(EmbeddingModel))
            existing_models = result.scalars().all()
            
            if not existing_models:
                # 添加默认模型
                default_models = [
                    {
                        "name": "text-embedding-ada-002",
                        "provider": "openai",
                        "model_path": "text-embedding-ada-002",
                        "dimension": 1536,
                        "max_tokens": 8191,
                        "description": "OpenAI的文本嵌入模型",
                        "config": {
                            "api_key_required": True,
                            "batch_size": 100
                        }
                    },
                    {
                        "name": "all-MiniLM-L6-v2",
                        "provider": "local",
                        "model_path": "sentence-transformers/all-MiniLM-L6-v2",
                        "dimension": 384,
                        "max_tokens": 512,
                        "description": "本地Sentence Transformers模型",
                        "config": {
                            "device": "cpu",
                            "batch_size": 32
                        }
                    },
                    {
                        "name": "text-embedding-3-small",
                        "provider": "openai",
                        "model_path": "text-embedding-3-small",
                        "dimension": 1536,
                        "max_tokens": 8191,
                        "description": "OpenAI的新一代小型嵌入模型",
                        "config": {
                            "api_key_required": True,
                            "batch_size": 100
                        }
                    }
                ]
                
                for model_data in default_models:
                    model = EmbeddingModel(**model_data)
                    session.add(model)
                
                await session.commit()
                logger.info(f"已初始化 {len(default_models)} 个默认嵌入模型")
    
    except Exception as e:
        logger.error(f"初始化默认模型失败: {e}")


# 数据库操作类
class EmbeddingRecordDAO:
    """向量记录数据访问对象"""
    
    @staticmethod
    async def create(session: AsyncSession, **kwargs) -> EmbeddingRecord:
        """创建向量记录"""
        record = EmbeddingRecord(**kwargs)
        session.add(record)
        await session.commit()
        await session.refresh(record)
        return record
    
    @staticmethod
    async def get_by_id(session: AsyncSession, record_id: uuid.UUID) -> Optional[EmbeddingRecord]:
        """根据ID获取向量记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingRecord).where(EmbeddingRecord.id == record_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_chunk_id(session: AsyncSession, chunk_id: uuid.UUID) -> Optional[EmbeddingRecord]:
        """根据块ID获取向量记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingRecord).where(EmbeddingRecord.chunk_id == chunk_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_document_id(session: AsyncSession, document_id: uuid.UUID) -> List[EmbeddingRecord]:
        """根据文档ID获取所有向量记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingRecord).where(EmbeddingRecord.document_id == document_id)
        )
        return result.scalars().all()
    
    @staticmethod
    async def update(session: AsyncSession, record_id: uuid.UUID, **kwargs) -> Optional[EmbeddingRecord]:
        """更新向量记录"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingRecord).where(EmbeddingRecord.id == record_id)
        )
        record = result.scalar_one_or_none()
        
        if record:
            for key, value in kwargs.items():
                setattr(record, key, value)
            await session.commit()
            await session.refresh(record)
        
        return record
    
    @staticmethod
    async def delete(session: AsyncSession, record_id: uuid.UUID) -> bool:
        """删除向量记录"""
        from sqlalchemy import select, delete
        result = await session.execute(
            select(EmbeddingRecord).where(EmbeddingRecord.id == record_id)
        )
        record = result.scalar_one_or_none()
        
        if record:
            await session.execute(
                delete(EmbeddingRecord).where(EmbeddingRecord.id == record_id)
            )
            await session.commit()
            return True
        
        return False


class EmbeddingJobDAO:
    """向量化任务数据访问对象"""
    
    @staticmethod
    async def create(session: AsyncSession, **kwargs) -> EmbeddingJob:
        """创建向量化任务"""
        job = EmbeddingJob(**kwargs)
        session.add(job)
        await session.commit()
        await session.refresh(job)
        return job
    
    @staticmethod
    async def get_by_id(session: AsyncSession, job_id: uuid.UUID) -> Optional[EmbeddingJob]:
        """根据ID获取任务"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingJob).where(EmbeddingJob.id == job_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_document_id(session: AsyncSession, document_id: uuid.UUID) -> Optional[EmbeddingJob]:
        """根据文档ID获取任务"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingJob).where(EmbeddingJob.document_id == document_id)
            .order_by(EmbeddingJob.created_at.desc())
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_status(
        session: AsyncSession, 
        job_id: uuid.UUID, 
        status: str, 
        **kwargs
    ) -> Optional[EmbeddingJob]:
        """更新任务状态"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingJob).where(EmbeddingJob.id == job_id)
        )
        job = result.scalar_one_or_none()
        
        if job:
            job.status = status
            for key, value in kwargs.items():
                setattr(job, key, value)
            await session.commit()
            await session.refresh(job)
        
        return job
    
    @staticmethod
    async def get_pending_jobs(session: AsyncSession, limit: int = 10) -> List[EmbeddingJob]:
        """获取待处理任务"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingJob)
            .where(EmbeddingJob.status == "pending")
            .order_by(EmbeddingJob.created_at)
            .limit(limit)
        )
        return result.scalars().all()


class EmbeddingModelDAO:
    """嵌入模型数据访问对象"""
    
    @staticmethod
    async def get_active_models(session: AsyncSession) -> List[EmbeddingModel]:
        """获取活跃的嵌入模型"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingModel).where(EmbeddingModel.is_active == True)
        )
        return result.scalars().all()
    
    @staticmethod
    async def get_by_name(session: AsyncSession, name: str) -> Optional[EmbeddingModel]:
        """根据名称获取模型"""
        from sqlalchemy import select
        result = await session.execute(
            select(EmbeddingModel).where(EmbeddingModel.name == name)
        )
        return result.scalar_one_or_none()
