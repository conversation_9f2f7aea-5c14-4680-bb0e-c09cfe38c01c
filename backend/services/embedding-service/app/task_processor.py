"""
任务处理器
处理向量化任务的核心逻辑
"""

import asyncio
import hashlib
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

from .database import async_session, EmbeddingRecord, EmbeddingJob, EmbeddingRecordDAO, EmbeddingJobDAO
from .embedding_models import embedding_model_manager
from .vector_db import vector_db_client
from .redis_client import embedding_queue, embedding_cache, RedisLock


class TaskProcessor:
    """任务处理器"""
    
    def __init__(self):
        self.is_running = False
        self.max_concurrent_tasks = 5
        self.batch_size = 10
    
    async def start(self):
        """启动任务处理器"""
        if self.is_running:
            logger.warning("任务处理器已在运行")
            return
        
        self.is_running = True
        logger.info("任务处理器启动")
        
        # 启动多个并发任务处理协程
        tasks = []
        for i in range(self.max_concurrent_tasks):
            task = asyncio.create_task(self._process_tasks_loop(f"worker-{i}"))
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
    
    async def stop(self):
        """停止任务处理器"""
        self.is_running = False
        logger.info("任务处理器停止")
    
    async def _process_tasks_loop(self, worker_name: str):
        """任务处理循环"""
        logger.info(f"工作进程 {worker_name} 启动")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_data = await embedding_queue.pop(timeout=5)
                
                if task_data:
                    await self._process_single_task(task_data, worker_name)
                else:
                    # 没有任务时短暂休眠
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"工作进程 {worker_name} 处理任务时出错: {e}")
                await asyncio.sleep(5)  # 出错时等待更长时间
        
        logger.info(f"工作进程 {worker_name} 停止")
    
    async def _process_single_task(self, task_data: Dict[str, Any], worker_name: str):
        """处理单个任务"""
        try:
            task_type = task_data.get("type")
            
            if task_type == "embed_document":
                await self._process_document_embedding(task_data, worker_name)
            elif task_type == "embed_chunks":
                await self._process_chunks_embedding(task_data, worker_name)
            elif task_type == "update_embeddings":
                await self._process_embedding_update(task_data, worker_name)
            elif task_type == "delete_embeddings":
                await self._process_embedding_deletion(task_data, worker_name)
            else:
                logger.warning(f"未知任务类型: {task_type}")
                
        except Exception as e:
            logger.error(f"处理任务失败: {e}, 任务数据: {task_data}")
    
    async def _process_document_embedding(self, task_data: Dict[str, Any], worker_name: str):
        """处理文档向量化任务"""
        document_id = task_data.get("document_id")
        chunks = task_data.get("chunks", [])
        model_name = task_data.get("model_name")
        
        logger.info(f"{worker_name} 开始处理文档 {document_id} 的向量化，共 {len(chunks)} 个块")
        
        # 创建或获取任务记录
        async with async_session() as session:
            job = await EmbeddingJobDAO.get_by_document_id(session, uuid.UUID(document_id))
            if not job:
                job = await EmbeddingJobDAO.create(
                    session,
                    document_id=uuid.UUID(document_id),
                    total_chunks=len(chunks),
                    embedding_model=model_name or "default"
                )
            
            # 更新任务状态
            await EmbeddingJobDAO.update_status(
                session,
                job.id,
                "processing",
                started_at=datetime.utcnow()
            )
        
        try:
            # 分批处理块
            processed_count = 0
            failed_count = 0
            
            for i in range(0, len(chunks), self.batch_size):
                batch_chunks = chunks[i:i + self.batch_size]
                
                success = await self._process_chunk_batch(
                    document_id,
                    batch_chunks,
                    model_name,
                    worker_name
                )
                
                if success:
                    processed_count += len(batch_chunks)
                else:
                    failed_count += len(batch_chunks)
                
                # 更新进度
                async with async_session() as session:
                    await EmbeddingJobDAO.update_status(
                        session,
                        job.id,
                        "processing",
                        processed_chunks=processed_count,
                        failed_chunks=failed_count
                    )
            
            # 完成任务
            final_status = "completed" if failed_count == 0 else "failed"
            async with async_session() as session:
                await EmbeddingJobDAO.update_status(
                    session,
                    job.id,
                    final_status,
                    completed_at=datetime.utcnow(),
                    processed_chunks=processed_count,
                    failed_chunks=failed_count
                )
            
            logger.info(f"{worker_name} 完成文档 {document_id} 向量化，成功: {processed_count}, 失败: {failed_count}")
            
        except Exception as e:
            # 任务失败
            async with async_session() as session:
                await EmbeddingJobDAO.update_status(
                    session,
                    job.id,
                    "failed",
                    error_message=str(e),
                    completed_at=datetime.utcnow()
                )
            
            logger.error(f"{worker_name} 处理文档 {document_id} 向量化失败: {e}")
    
    async def _process_chunk_batch(
        self,
        document_id: str,
        chunks: List[Dict[str, Any]],
        model_name: Optional[str],
        worker_name: str
    ) -> bool:
        """处理块批次"""
        try:
            # 提取文本内容
            texts = [chunk.get("content", "") for chunk in chunks]
            
            # 生成向量
            embeddings = await embedding_model_manager.encode_texts(texts, model_name)
            
            # 准备数据库记录
            records_to_create = []
            vectors_to_insert = []
            metadatas_to_insert = []
            vector_ids = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = chunk.get("id")
                content = chunk.get("content", "")
                content_hash = hashlib.md5(content.encode()).hexdigest()
                
                # 数据库记录
                record_data = {
                    "document_id": uuid.UUID(document_id),
                    "chunk_id": uuid.UUID(chunk_id),
                    "content": content,
                    "content_hash": content_hash,
                    "embedding_model": model_name or "default",
                    "embedding_dimension": len(embeddings[i]),
                    "embedding_vector": embeddings[i],
                    "metadata": chunk.get("metadata", {}),
                    "language": chunk.get("language", "zh"),
                    "token_count": len(content.split())
                }
                records_to_create.append(record_data)
                
                # 向量数据库记录
                vectors_to_insert.append(embeddings[i])
                vector_ids.append(chunk_id)
                
                metadata = {
                    "document_id": document_id,
                    "chunk_id": chunk_id,
                    "content": content,
                    "heading": chunk.get("metadata", {}).get("heading"),
                    "section": chunk.get("metadata", {}).get("section"),
                    "page_number": chunk.get("metadata", {}).get("page_number")
                }
                metadatas_to_insert.append(metadata)
            
            # 保存到数据库
            async with async_session() as session:
                for record_data in records_to_create:
                    await EmbeddingRecordDAO.create(session, **record_data)
            
            # 保存到向量数据库
            collection_name = f"document_{document_id}"
            
            # 确保集合存在
            model_dimension = embedding_model_manager.get_model_dimension(model_name)
            await vector_db_client.create_collection(collection_name, model_dimension)
            
            # 插入向量
            await vector_db_client.insert_vectors(
                collection_name,
                vectors_to_insert,
                metadatas_to_insert,
                vector_ids
            )
            
            logger.info(f"{worker_name} 成功处理 {len(chunks)} 个块")
            return True
            
        except Exception as e:
            logger.error(f"{worker_name} 处理块批次失败: {e}")
            return False
    
    async def _process_chunks_embedding(self, task_data: Dict[str, Any], worker_name: str):
        """处理块向量化任务"""
        chunks = task_data.get("chunks", [])
        model_name = task_data.get("model_name")
        collection_name = task_data.get("collection_name", "default")
        
        logger.info(f"{worker_name} 开始处理 {len(chunks)} 个块的向量化")
        
        try:
            # 分批处理
            for i in range(0, len(chunks), self.batch_size):
                batch_chunks = chunks[i:i + self.batch_size]
                
                # 提取文本
                texts = [chunk.get("content", "") for chunk in batch_chunks]
                
                # 生成向量
                embeddings = await embedding_model_manager.encode_texts(texts, model_name)
                
                # 准备向量数据库数据
                vectors_to_insert = embeddings
                metadatas_to_insert = []
                vector_ids = []
                
                for j, chunk in enumerate(batch_chunks):
                    chunk_id = chunk.get("id", str(uuid.uuid4()))
                    vector_ids.append(chunk_id)
                    
                    metadata = {
                        "content": chunk.get("content", ""),
                        "source": chunk.get("source", ""),
                        **chunk.get("metadata", {})
                    }
                    metadatas_to_insert.append(metadata)
                
                # 确保集合存在
                model_dimension = embedding_model_manager.get_model_dimension(model_name)
                await vector_db_client.create_collection(collection_name, model_dimension)
                
                # 插入向量
                await vector_db_client.insert_vectors(
                    collection_name,
                    vectors_to_insert,
                    metadatas_to_insert,
                    vector_ids
                )
            
            logger.info(f"{worker_name} 完成 {len(chunks)} 个块的向量化")
            
        except Exception as e:
            logger.error(f"{worker_name} 处理块向量化失败: {e}")
    
    async def _process_embedding_update(self, task_data: Dict[str, Any], worker_name: str):
        """处理向量更新任务"""
        document_id = task_data.get("document_id")
        chunks = task_data.get("chunks", [])
        model_name = task_data.get("model_name")
        
        logger.info(f"{worker_name} 开始更新文档 {document_id} 的向量")
        
        try:
            # 删除旧向量
            collection_name = f"document_{document_id}"
            
            # 获取现有向量ID
            async with async_session() as session:
                existing_records = await EmbeddingRecordDAO.get_by_document_id(
                    session, 
                    uuid.UUID(document_id)
                )
                
                existing_ids = [str(record.chunk_id) for record in existing_records]
                
                if existing_ids:
                    await vector_db_client.delete_vectors(collection_name, existing_ids)
                
                # 删除数据库记录
                for record in existing_records:
                    await EmbeddingRecordDAO.delete(session, record.id)
            
            # 重新处理向量化
            await self._process_chunk_batch(document_id, chunks, model_name, worker_name)
            
            logger.info(f"{worker_name} 完成文档 {document_id} 向量更新")
            
        except Exception as e:
            logger.error(f"{worker_name} 更新向量失败: {e}")
    
    async def _process_embedding_deletion(self, task_data: Dict[str, Any], worker_name: str):
        """处理向量删除任务"""
        document_id = task_data.get("document_id")
        
        logger.info(f"{worker_name} 开始删除文档 {document_id} 的向量")
        
        try:
            collection_name = f"document_{document_id}"
            
            # 获取向量ID
            async with async_session() as session:
                records = await EmbeddingRecordDAO.get_by_document_id(
                    session, 
                    uuid.UUID(document_id)
                )
                
                vector_ids = [str(record.chunk_id) for record in records]
                
                if vector_ids:
                    # 删除向量数据库中的向量
                    await vector_db_client.delete_vectors(collection_name, vector_ids)
                
                # 删除数据库记录
                for record in records:
                    await EmbeddingRecordDAO.delete(session, record.id)
            
            logger.info(f"{worker_name} 完成文档 {document_id} 向量删除")
            
        except Exception as e:
            logger.error(f"{worker_name} 删除向量失败: {e}")


# 创建全局任务处理器
task_processor = TaskProcessor()
