"""
向量化服务配置
"""

import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "RAG向量化服务"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://rag_user:rag_password@localhost:5432/rag_system"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_DB: int = 0
    
    # 向量数据库配置
    VECTOR_DB_TYPE: str = "chroma"  # chroma, pinecone, weaviate
    CHROMA_URL: str = "http://localhost:8000"
    PINECONE_API_KEY: str = ""
    PINECONE_ENVIRONMENT: str = ""
    WEAVIATE_URL: str = "http://localhost:8080"
    
    # OpenAI配置
    OPENAI_API_KEY: str = ""
    OPENAI_ORG_ID: str = ""
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-ada-002"
    OPENAI_EMBEDDING_DIMENSION: int = 1536
    
    # 本地模型配置
    LOCAL_EMBEDDING_MODEL: str = "sentence-transformers/all-MiniLM-L6-v2"
    USE_LOCAL_MODEL: bool = False
    
    # 文本处理配置
    MAX_CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    MAX_TOKENS_PER_CHUNK: int = 500
    
    # 批处理配置
    BATCH_SIZE: int = 100
    MAX_CONCURRENT_REQUESTS: int = 10
    PROCESSING_TIMEOUT: int = 300  # 5分钟
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    VECTOR_CACHE_TTL: int = 86400  # 24小时
    
    # 安全配置
    CORS_ORIGINS: List[str] = ["http://localhost:3100", "http://localhost:3000"]
    API_KEY: str = ""
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# 向量数据库配置映射
VECTOR_DB_CONFIGS = {
    "chroma": {
        "url": settings.CHROMA_URL,
        "collection_name": "rag_documents"
    },
    "pinecone": {
        "api_key": settings.PINECONE_API_KEY,
        "environment": settings.PINECONE_ENVIRONMENT,
        "index_name": "rag-documents"
    },
    "weaviate": {
        "url": settings.WEAVIATE_URL,
        "class_name": "DocumentChunk"
    }
}


# 嵌入模型配置
EMBEDDING_CONFIGS = {
    "openai": {
        "api_key": settings.OPENAI_API_KEY,
        "model": settings.OPENAI_EMBEDDING_MODEL,
        "dimension": settings.OPENAI_EMBEDDING_DIMENSION
    },
    "local": {
        "model": settings.LOCAL_EMBEDDING_MODEL,
        "dimension": 384  # all-MiniLM-L6-v2的维度
    }
}


# 文本分块配置
CHUNKING_CONFIG = {
    "max_chunk_size": settings.MAX_CHUNK_SIZE,
    "chunk_overlap": settings.CHUNK_OVERLAP,
    "max_tokens": settings.MAX_TOKENS_PER_CHUNK,
    "separators": ["\n\n", "\n", "。", "！", "？", ".", "!", "?", " "]
}


# 支持的语言配置
SUPPORTED_LANGUAGES = {
    "zh": "中文",
    "en": "英文",
    "ja": "日文",
    "ko": "韩文"
}


def get_vector_db_config():
    """获取向量数据库配置"""
    return VECTOR_DB_CONFIGS.get(settings.VECTOR_DB_TYPE, VECTOR_DB_CONFIGS["chroma"])


def get_embedding_config():
    """获取嵌入模型配置"""
    if settings.USE_LOCAL_MODEL:
        return EMBEDDING_CONFIGS["local"]
    else:
        return EMBEDDING_CONFIGS["openai"]
