"""
向量数据库客户端模块
支持多种向量数据库：ChromaDB、Pinecone、Weaviate
"""

import uuid
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from loguru import logger

from .config import settings, get_vector_db_config


class VectorDBClient(ABC):
    """向量数据库客户端抽象基类"""
    
    @abstractmethod
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """创建集合"""
        pass
    
    @abstractmethod
    async def insert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> bool:
        """插入向量"""
        pass
    
    @abstractmethod
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索向量"""
        pass
    
    @abstractmethod
    async def delete_vectors(
        self, 
        collection_name: str, 
        ids: List[str]
    ) -> bool:
        """删除向量"""
        pass
    
    @abstractmethod
    async def update_vectors(
        self, 
        collection_name: str, 
        ids: List[str],
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]]
    ) -> bool:
        """更新向量"""
        pass
    
    @abstractmethod
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        pass


class ChromaDBClient(VectorDBClient):
    """ChromaDB客户端"""
    
    def __init__(self):
        self.client = None
        self.config = get_vector_db_config()
    
    async def _get_client(self):
        """获取ChromaDB客户端"""
        if not self.client:
            try:
                import chromadb
                from chromadb.config import Settings as ChromaSettings
                
                # 根据URL判断是否为远程连接
                if self.config["url"].startswith("http"):
                    self.client = chromadb.HttpClient(
                        host=self.config["url"].replace("http://", "").replace("https://", ""),
                        port=8000,
                        settings=ChromaSettings(allow_reset=True)
                    )
                else:
                    self.client = chromadb.PersistentClient(
                        path=self.config.get("path", "./chroma_db"),
                        settings=ChromaSettings(allow_reset=True)
                    )
                
                logger.info("ChromaDB客户端初始化成功")
            except Exception as e:
                logger.error(f"ChromaDB客户端初始化失败: {e}")
                raise
        
        return self.client
    
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """创建集合"""
        try:
            client = await self._get_client()
            
            # 检查集合是否已存在
            try:
                collection = client.get_collection(collection_name)
                logger.info(f"集合 {collection_name} 已存在")
                return True
            except:
                # 集合不存在，创建新集合
                collection = client.create_collection(
                    name=collection_name,
                    metadata={"dimension": dimension}
                )
                logger.info(f"成功创建集合 {collection_name}")
                return True
                
        except Exception as e:
            logger.error(f"创建集合失败: {collection_name}, {e}")
            return False
    
    async def insert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> bool:
        """插入向量"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in range(len(vectors))]
            
            # 转换文档内容为字符串列表
            documents = [meta.get("content", "") for meta in metadatas]
            
            collection.add(
                embeddings=vectors,
                metadatas=metadatas,
                documents=documents,
                ids=ids
            )
            
            logger.info(f"成功插入 {len(vectors)} 个向量到集合 {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"插入向量失败: {collection_name}, {e}")
            return False
    
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索向量"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            results = collection.query(
                query_embeddings=[query_vector],
                n_results=top_k,
                where=filter_dict
            )
            
            # 格式化结果
            formatted_results = []
            if results["ids"] and len(results["ids"]) > 0:
                for i in range(len(results["ids"][0])):
                    result = {
                        "id": results["ids"][0][i],
                        "score": 1 - results["distances"][0][i],  # ChromaDB返回距离，转换为相似度
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "document": results["documents"][0][i] if results["documents"] else ""
                    }
                    formatted_results.append(result)
            
            logger.info(f"搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {collection_name}, {e}")
            return []
    
    async def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """删除向量"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            collection.delete(ids=ids)
            
            logger.info(f"成功删除 {len(ids)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"删除向量失败: {collection_name}, {e}")
            return False
    
    async def update_vectors(
        self, 
        collection_name: str, 
        ids: List[str],
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]]
    ) -> bool:
        """更新向量"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            documents = [meta.get("content", "") for meta in metadatas]
            
            collection.update(
                ids=ids,
                embeddings=vectors,
                metadatas=metadatas,
                documents=documents
            )
            
            logger.info(f"成功更新 {len(ids)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"更新向量失败: {collection_name}, {e}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            client = await self._get_client()
            collection = client.get_collection(collection_name)
            
            count = collection.count()
            
            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata
            }
            
        except Exception as e:
            logger.error(f"获取集合信息失败: {collection_name}, {e}")
            return {}


class PineconeClient(VectorDBClient):
    """Pinecone客户端"""
    
    def __init__(self):
        self.client = None
        self.config = get_vector_db_config()
    
    async def _get_client(self):
        """获取Pinecone客户端"""
        if not self.client:
            try:
                import pinecone
                
                pinecone.init(
                    api_key=self.config["api_key"],
                    environment=self.config["environment"]
                )
                
                self.client = pinecone
                logger.info("Pinecone客户端初始化成功")
            except Exception as e:
                logger.error(f"Pinecone客户端初始化失败: {e}")
                raise
        
        return self.client
    
    async def create_collection(self, collection_name: str, dimension: int) -> bool:
        """创建索引（Pinecone中的集合概念）"""
        try:
            client = await self._get_client()
            
            # 检查索引是否已存在
            if collection_name in client.list_indexes():
                logger.info(f"索引 {collection_name} 已存在")
                return True
            
            # 创建新索引
            client.create_index(
                name=collection_name,
                dimension=dimension,
                metric="cosine"
            )
            
            logger.info(f"成功创建索引 {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建索引失败: {collection_name}, {e}")
            return False
    
    async def insert_vectors(
        self, 
        collection_name: str, 
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]], 
        ids: Optional[List[str]] = None
    ) -> bool:
        """插入向量"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in range(len(vectors))]
            
            # 准备数据
            vectors_to_upsert = [
                (ids[i], vectors[i], metadatas[i])
                for i in range(len(vectors))
            ]
            
            # 批量插入
            index.upsert(vectors=vectors_to_upsert)
            
            logger.info(f"成功插入 {len(vectors)} 个向量到索引 {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"插入向量失败: {collection_name}, {e}")
            return False
    
    async def search_vectors(
        self, 
        collection_name: str, 
        query_vector: List[float], 
        top_k: int = 10,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索向量"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            results = index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True
            )
            
            # 格式化结果
            formatted_results = []
            for match in results["matches"]:
                result = {
                    "id": match["id"],
                    "score": match["score"],
                    "metadata": match.get("metadata", {}),
                    "document": match.get("metadata", {}).get("content", "")
                }
                formatted_results.append(result)
            
            logger.info(f"搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {collection_name}, {e}")
            return []
    
    async def delete_vectors(self, collection_name: str, ids: List[str]) -> bool:
        """删除向量"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            index.delete(ids=ids)
            
            logger.info(f"成功删除 {len(ids)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"删除向量失败: {collection_name}, {e}")
            return False
    
    async def update_vectors(
        self, 
        collection_name: str, 
        ids: List[str],
        vectors: List[List[float]], 
        metadatas: List[Dict[str, Any]]
    ) -> bool:
        """更新向量"""
        # Pinecone使用upsert操作来更新
        return await self.insert_vectors(collection_name, vectors, metadatas, ids)
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """获取索引信息"""
        try:
            client = await self._get_client()
            index = client.Index(collection_name)
            
            stats = index.describe_index_stats()
            
            return {
                "name": collection_name,
                "count": stats.get("total_vector_count", 0),
                "dimension": stats.get("dimension", 0)
            }
            
        except Exception as e:
            logger.error(f"获取索引信息失败: {collection_name}, {e}")
            return {}


# 向量数据库工厂
class VectorDBFactory:
    """向量数据库工厂类"""
    
    @staticmethod
    def create_client() -> VectorDBClient:
        """根据配置创建向量数据库客户端"""
        db_type = settings.VECTOR_DB_TYPE.lower()
        
        if db_type == "chroma":
            return ChromaDBClient()
        elif db_type == "pinecone":
            return PineconeClient()
        else:
            raise ValueError(f"不支持的向量数据库类型: {db_type}")


# 创建全局向量数据库客户端
vector_db_client = VectorDBFactory.create_client()
