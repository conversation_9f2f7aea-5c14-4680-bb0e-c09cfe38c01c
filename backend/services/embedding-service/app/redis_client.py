"""
Redis客户端模块
用于缓存和任务队列
"""

import json
import pickle
from typing import Any, Optional, List, Dict
import aioredis
from loguru import logger

from .config import settings

# 全局Redis客户端
redis_client: Optional[aioredis.Redis] = None


async def init_redis():
    """初始化Redis连接"""
    global redis_client
    
    try:
        redis_client = aioredis.from_url(
            settings.REDIS_URL,
            db=settings.REDIS_DB,
            encoding="utf-8",
            decode_responses=False,  # 保持二进制数据
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # 测试连接
        await redis_client.ping()
        logger.info("Redis连接成功")
        
    except Exception as e:
        logger.error(f"Redis连接失败: {e}")
        raise


async def close_redis():
    """关闭Redis连接"""
    global redis_client
    
    if redis_client:
        await redis_client.close()
        logger.info("Redis连接已关闭")


def get_redis_client() -> aioredis.Redis:
    """获取Redis客户端"""
    if not redis_client:
        raise RuntimeError("Redis未初始化")
    return redis_client


class RedisCache:
    """Redis缓存操作类"""
    
    def __init__(self, prefix: str = "embedding"):
        self.prefix = prefix
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.prefix}:{key}"
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        serialize: bool = True
    ) -> bool:
        """设置缓存"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            if serialize:
                # 使用pickle序列化，支持复杂对象
                serialized_value = pickle.dumps(value)
            else:
                serialized_value = value
            
            if ttl:
                await client.setex(cache_key, ttl, serialized_value)
            else:
                await client.set(cache_key, serialized_value)
            
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, {e}")
            return False
    
    async def get(self, key: str, deserialize: bool = True) -> Any:
        """获取缓存"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            value = await client.get(cache_key)
            if value is None:
                return None
            
            if deserialize:
                # 使用pickle反序列化
                return pickle.loads(value)
            else:
                return value
                
        except Exception as e:
            logger.error(f"获取缓存失败: {key}, {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            result = await client.delete(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"删除缓存失败: {key}, {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            result = await client.exists(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"检查缓存存在性失败: {key}, {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            result = await client.expire(cache_key, ttl)
            return result
            
        except Exception as e:
            logger.error(f"设置缓存过期时间失败: {key}, {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        try:
            client = get_redis_client()
            cache_key = self._make_key(key)
            
            return await client.ttl(cache_key)
            
        except Exception as e:
            logger.error(f"获取缓存TTL失败: {key}, {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        try:
            client = get_redis_client()
            cache_pattern = self._make_key(pattern)
            
            keys = await client.keys(cache_pattern)
            # 移除前缀
            return [key.decode('utf-8').replace(f"{self.prefix}:", "") for key in keys]
            
        except Exception as e:
            logger.error(f"获取键列表失败: {pattern}, {e}")
            return []
    
    async def clear_pattern(self, pattern: str = "*") -> int:
        """清除匹配模式的所有缓存"""
        try:
            client = get_redis_client()
            cache_pattern = self._make_key(pattern)
            
            keys = await client.keys(cache_pattern)
            if keys:
                return await client.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"清除缓存失败: {pattern}, {e}")
            return 0


class RedisQueue:
    """Redis队列操作类"""
    
    def __init__(self, queue_name: str):
        self.queue_name = f"queue:{queue_name}"
    
    async def push(self, item: Any, serialize: bool = True) -> bool:
        """推入队列"""
        try:
            client = get_redis_client()
            
            if serialize:
                serialized_item = pickle.dumps(item)
            else:
                serialized_item = item
            
            await client.lpush(self.queue_name, serialized_item)
            return True
            
        except Exception as e:
            logger.error(f"推入队列失败: {self.queue_name}, {e}")
            return False
    
    async def pop(self, timeout: int = 0, deserialize: bool = True) -> Any:
        """弹出队列项"""
        try:
            client = get_redis_client()
            
            if timeout > 0:
                # 阻塞弹出
                result = await client.brpop(self.queue_name, timeout=timeout)
                if result:
                    _, item = result
                else:
                    return None
            else:
                # 非阻塞弹出
                item = await client.rpop(self.queue_name)
                if item is None:
                    return None
            
            if deserialize:
                return pickle.loads(item)
            else:
                return item
                
        except Exception as e:
            logger.error(f"弹出队列失败: {self.queue_name}, {e}")
            return None
    
    async def size(self) -> int:
        """获取队列大小"""
        try:
            client = get_redis_client()
            return await client.llen(self.queue_name)
            
        except Exception as e:
            logger.error(f"获取队列大小失败: {self.queue_name}, {e}")
            return 0
    
    async def clear(self) -> bool:
        """清空队列"""
        try:
            client = get_redis_client()
            await client.delete(self.queue_name)
            return True
            
        except Exception as e:
            logger.error(f"清空队列失败: {self.queue_name}, {e}")
            return False


class RedisLock:
    """Redis分布式锁"""
    
    def __init__(self, key: str, timeout: int = 60):
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.token = None
    
    async def acquire(self) -> bool:
        """获取锁"""
        try:
            client = get_redis_client()
            import uuid
            self.token = str(uuid.uuid4())
            
            # 使用SET命令的NX和EX选项实现分布式锁
            result = await client.set(
                self.key, 
                self.token, 
                nx=True, 
                ex=self.timeout
            )
            
            return result is not None
            
        except Exception as e:
            logger.error(f"获取锁失败: {self.key}, {e}")
            return False
    
    async def release(self) -> bool:
        """释放锁"""
        try:
            client = get_redis_client()
            
            # 使用Lua脚本确保原子性
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """
            
            result = await client.eval(lua_script, 1, self.key, self.token)
            return result == 1
            
        except Exception as e:
            logger.error(f"释放锁失败: {self.key}, {e}")
            return False
    
    async def extend(self, additional_time: int) -> bool:
        """延长锁时间"""
        try:
            client = get_redis_client()
            
            # 使用Lua脚本确保原子性
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("expire", KEYS[1], ARGV[2])
            else
                return 0
            end
            """
            
            result = await client.eval(
                lua_script, 
                1, 
                self.key, 
                self.token, 
                self.timeout + additional_time
            )
            return result == 1
            
        except Exception as e:
            logger.error(f"延长锁时间失败: {self.key}, {e}")
            return False


# 创建全局缓存实例
embedding_cache = RedisCache("embedding")
vector_cache = RedisCache("vector")
model_cache = RedisCache("model")

# 创建全局队列实例
embedding_queue = RedisQueue("embedding_tasks")
priority_queue = RedisQueue("priority_tasks")
