"""
嵌入模型管理器
支持多种嵌入模型：OpenAI、本地模型、Hugging Face等
"""

import asyncio
import hashlib
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
import numpy as np
from loguru import logger

from .config import settings
from .redis_client import model_cache


class EmbeddingModel(ABC):
    """嵌入模型抽象基类"""
    
    def __init__(self, model_name: str, dimension: int, max_tokens: int = 512):
        self.model_name = model_name
        self.dimension = dimension
        self.max_tokens = max_tokens
        self.is_loaded = False
    
    @abstractmethod
    async def load_model(self) -> bool:
        """加载模型"""
        pass
    
    @abstractmethod
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """编码文本为向量"""
        pass
    
    @abstractmethod
    async def encode_single_text(self, text: str) -> List[float]:
        """编码单个文本为向量"""
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": self.model_name,
            "dimension": self.dimension,
            "max_tokens": self.max_tokens,
            "is_loaded": self.is_loaded
        }
    
    def _truncate_text(self, text: str) -> str:
        """截断文本到最大长度"""
        # 简单的字符截断，实际应该按token截断
        max_chars = self.max_tokens * 4  # 估算字符数
        if len(text) > max_chars:
            return text[:max_chars]
        return text
    
    def _generate_cache_key(self, text: str) -> str:
        """生成缓存键"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"{self.model_name}:{text_hash}"


class OpenAIEmbeddingModel(EmbeddingModel):
    """OpenAI嵌入模型"""
    
    def __init__(self, model_name: str = "text-embedding-ada-002"):
        # 根据模型名称设置维度
        dimension_map = {
            "text-embedding-ada-002": 1536,
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072
        }
        
        dimension = dimension_map.get(model_name, 1536)
        max_tokens = 8191
        
        super().__init__(model_name, dimension, max_tokens)
        self.client = None
    
    async def load_model(self) -> bool:
        """加载OpenAI客户端"""
        try:
            import openai
            
            self.client = openai.AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY,
                base_url=settings.OPENAI_BASE_URL
            )
            
            self.is_loaded = True
            logger.info(f"OpenAI模型 {self.model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"OpenAI模型加载失败: {e}")
            return False
    
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """批量编码文本"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            # 检查缓存
            cached_results = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                cache_key = self._generate_cache_key(text)
                cached_vector = await model_cache.get(cache_key)
                
                if cached_vector:
                    cached_results.append((i, cached_vector))
                else:
                    uncached_texts.append(self._truncate_text(text))
                    uncached_indices.append(i)
            
            # 初始化结果列表
            results = [None] * len(texts)
            
            # 填充缓存结果
            for index, vector in cached_results:
                results[index] = vector
            
            # 处理未缓存的文本
            if uncached_texts:
                response = await self.client.embeddings.create(
                    model=self.model_name,
                    input=uncached_texts
                )
                
                # 处理API响应
                for i, embedding_data in enumerate(response.data):
                    vector = embedding_data.embedding
                    original_index = uncached_indices[i]
                    results[original_index] = vector
                    
                    # 缓存结果
                    cache_key = self._generate_cache_key(texts[original_index])
                    await model_cache.set(cache_key, vector, ttl=3600)  # 缓存1小时
            
            logger.info(f"成功编码 {len(texts)} 个文本，缓存命中 {len(cached_results)} 个")
            return results
            
        except Exception as e:
            logger.error(f"OpenAI文本编码失败: {e}")
            raise
    
    async def encode_single_text(self, text: str) -> List[float]:
        """编码单个文本"""
        results = await self.encode_texts([text])
        return results[0]


class LocalEmbeddingModel(EmbeddingModel):
    """本地嵌入模型（使用sentence-transformers）"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        # 常见模型的维度映射
        dimension_map = {
            "all-MiniLM-L6-v2": 384,
            "all-mpnet-base-v2": 768,
            "paraphrase-multilingual-MiniLM-L12-v2": 384,
            "distiluse-base-multilingual-cased": 512
        }
        
        dimension = dimension_map.get(model_name, 384)
        max_tokens = 512
        
        super().__init__(model_name, dimension, max_tokens)
        self.model = None
    
    async def load_model(self) -> bool:
        """加载本地模型"""
        try:
            from sentence_transformers import SentenceTransformer
            
            # 在线程池中加载模型，避免阻塞
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None, 
                SentenceTransformer, 
                self.model_name
            )
            
            self.is_loaded = True
            logger.info(f"本地模型 {self.model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"本地模型加载失败: {e}")
            return False
    
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """批量编码文本"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            # 检查缓存
            cached_results = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                cache_key = self._generate_cache_key(text)
                cached_vector = await model_cache.get(cache_key)
                
                if cached_vector:
                    cached_results.append((i, cached_vector))
                else:
                    uncached_texts.append(self._truncate_text(text))
                    uncached_indices.append(i)
            
            # 初始化结果列表
            results = [None] * len(texts)
            
            # 填充缓存结果
            for index, vector in cached_results:
                results[index] = vector
            
            # 处理未缓存的文本
            if uncached_texts:
                # 在线程池中执行编码，避免阻塞
                loop = asyncio.get_event_loop()
                embeddings = await loop.run_in_executor(
                    None,
                    self.model.encode,
                    uncached_texts
                )
                
                # 转换为列表格式
                for i, embedding in enumerate(embeddings):
                    vector = embedding.tolist()
                    original_index = uncached_indices[i]
                    results[original_index] = vector
                    
                    # 缓存结果
                    cache_key = self._generate_cache_key(texts[original_index])
                    await model_cache.set(cache_key, vector, ttl=3600)  # 缓存1小时
            
            logger.info(f"成功编码 {len(texts)} 个文本，缓存命中 {len(cached_results)} 个")
            return results
            
        except Exception as e:
            logger.error(f"本地模型文本编码失败: {e}")
            raise
    
    async def encode_single_text(self, text: str) -> List[float]:
        """编码单个文本"""
        results = await self.encode_texts([text])
        return results[0]


class HuggingFaceEmbeddingModel(EmbeddingModel):
    """Hugging Face嵌入模型"""
    
    def __init__(self, model_name: str, dimension: int = 768):
        super().__init__(model_name, dimension)
        self.tokenizer = None
        self.model = None
    
    async def load_model(self) -> bool:
        """加载Hugging Face模型"""
        try:
            from transformers import AutoTokenizer, AutoModel
            import torch
            
            # 在线程池中加载模型
            loop = asyncio.get_event_loop()
            
            self.tokenizer = await loop.run_in_executor(
                None,
                AutoTokenizer.from_pretrained,
                self.model_name
            )
            
            self.model = await loop.run_in_executor(
                None,
                AutoModel.from_pretrained,
                self.model_name
            )
            
            self.is_loaded = True
            logger.info(f"Hugging Face模型 {self.model_name} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"Hugging Face模型加载失败: {e}")
            return False
    
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """批量编码文本"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            import torch
            
            # 检查缓存
            cached_results = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                cache_key = self._generate_cache_key(text)
                cached_vector = await model_cache.get(cache_key)
                
                if cached_vector:
                    cached_results.append((i, cached_vector))
                else:
                    uncached_texts.append(self._truncate_text(text))
                    uncached_indices.append(i)
            
            # 初始化结果列表
            results = [None] * len(texts)
            
            # 填充缓存结果
            for index, vector in cached_results:
                results[index] = vector
            
            # 处理未缓存的文本
            if uncached_texts:
                # 在线程池中执行编码
                loop = asyncio.get_event_loop()
                embeddings = await loop.run_in_executor(
                    None,
                    self._encode_with_model,
                    uncached_texts
                )
                
                # 处理结果
                for i, embedding in enumerate(embeddings):
                    vector = embedding.tolist()
                    original_index = uncached_indices[i]
                    results[original_index] = vector
                    
                    # 缓存结果
                    cache_key = self._generate_cache_key(texts[original_index])
                    await model_cache.set(cache_key, vector, ttl=3600)
            
            logger.info(f"成功编码 {len(texts)} 个文本，缓存命中 {len(cached_results)} 个")
            return results
            
        except Exception as e:
            logger.error(f"Hugging Face模型文本编码失败: {e}")
            raise
    
    def _encode_with_model(self, texts: List[str]):
        """使用模型编码文本（同步方法）"""
        import torch
        
        # 分词
        inputs = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=self.max_tokens,
            return_tensors="pt"
        )
        
        # 推理
        with torch.no_grad():
            outputs = self.model(**inputs)
            
        # 池化（使用CLS token或平均池化）
        embeddings = outputs.last_hidden_state.mean(dim=1)
        
        return embeddings.cpu().numpy()
    
    async def encode_single_text(self, text: str) -> List[float]:
        """编码单个文本"""
        results = await self.encode_texts([text])
        return results[0]


class EmbeddingModelManager:
    """嵌入模型管理器"""
    
    def __init__(self):
        self.models: Dict[str, EmbeddingModel] = {}
        self.default_model_name = settings.DEFAULT_EMBEDDING_MODEL
    
    async def load_model(self, model_name: str, model_type: str = "openai") -> bool:
        """加载指定模型"""
        try:
            if model_name in self.models:
                logger.info(f"模型 {model_name} 已加载")
                return True
            
            # 根据类型创建模型
            if model_type.lower() == "openai":
                model = OpenAIEmbeddingModel(model_name)
            elif model_type.lower() == "local":
                model = LocalEmbeddingModel(model_name)
            elif model_type.lower() == "huggingface":
                model = HuggingFaceEmbeddingModel(model_name)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            # 加载模型
            success = await model.load_model()
            if success:
                self.models[model_name] = model
                logger.info(f"模型 {model_name} 加载成功")
                return True
            else:
                logger.error(f"模型 {model_name} 加载失败")
                return False
                
        except Exception as e:
            logger.error(f"加载模型失败: {model_name}, {e}")
            return False
    
    async def get_model(self, model_name: Optional[str] = None) -> EmbeddingModel:
        """获取模型实例"""
        if model_name is None:
            model_name = self.default_model_name
        
        if model_name not in self.models:
            # 尝试加载默认模型
            await self.load_model(model_name, "openai")
        
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 未加载")
        
        return self.models[model_name]
    
    async def encode_texts(
        self, 
        texts: List[str], 
        model_name: Optional[str] = None
    ) -> List[List[float]]:
        """使用指定模型编码文本"""
        model = await self.get_model(model_name)
        return await model.encode_texts(texts)
    
    async def encode_single_text(
        self, 
        text: str, 
        model_name: Optional[str] = None
    ) -> List[float]:
        """使用指定模型编码单个文本"""
        model = await self.get_model(model_name)
        return await model.encode_single_text(text)
    
    def get_loaded_models(self) -> List[Dict[str, Any]]:
        """获取已加载的模型列表"""
        return [model.get_model_info() for model in self.models.values()]
    
    def get_model_dimension(self, model_name: Optional[str] = None) -> int:
        """获取模型维度"""
        if model_name is None:
            model_name = self.default_model_name
        
        if model_name in self.models:
            return self.models[model_name].dimension
        
        # 返回默认维度
        return 1536


# 创建全局模型管理器
embedding_model_manager = EmbeddingModelManager()
