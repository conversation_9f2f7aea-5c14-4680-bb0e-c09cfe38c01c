#!/usr/bin/env python3
"""
Redis Prometheus导出器
收集Redis指标并暴露给Prometheus
"""

import time
import json
import logging
from typing import Dict, List, Any
from prometheus_client import start_http_server, Gauge, Counter, Histogram, Info
import redis
import redis.sentinel
from redis.cluster import RedisCluster

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RedisExporter:
    """Redis Prometheus导出器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        
        # 定义指标
        self._define_metrics()
        
    def _define_metrics(self):
        """定义Prometheus指标"""
        
        # 基础指标
        self.redis_up = Gauge('redis_up', 'Redis实例是否可用', ['instance', 'role'])
        self.redis_info = Info('redis_info', 'Redis实例信息', ['instance'])
        
        # 连接指标
        self.redis_connected_clients = Gauge('redis_connected_clients', '当前连接的客户端数量', ['instance'])
        self.redis_blocked_clients = Gauge('redis_blocked_clients', '被阻塞的客户端数量', ['instance'])
        self.redis_tracking_clients = Gauge('redis_tracking_clients', '正在跟踪的客户端数量', ['instance'])
        
        # 内存指标
        self.redis_memory_used_bytes = Gauge('redis_memory_used_bytes', '已使用内存字节数', ['instance'])
        self.redis_memory_used_rss_bytes = Gauge('redis_memory_used_rss_bytes', 'RSS内存字节数', ['instance'])
        self.redis_memory_used_peak_bytes = Gauge('redis_memory_used_peak_bytes', '内存使用峰值字节数', ['instance'])
        self.redis_memory_used_overhead_bytes = Gauge('redis_memory_used_overhead_bytes', '内存开销字节数', ['instance'])
        self.redis_memory_used_dataset_bytes = Gauge('redis_memory_used_dataset_bytes', '数据集内存字节数', ['instance'])
        self.redis_memory_used_lua_bytes = Gauge('redis_memory_used_lua_bytes', 'Lua脚本内存字节数', ['instance'])
        self.redis_memory_fragmentation_ratio = Gauge('redis_memory_fragmentation_ratio', '内存碎片率', ['instance'])
        
        # 持久化指标
        self.redis_rdb_changes_since_last_save = Gauge('redis_rdb_changes_since_last_save', '上次保存后的变更数', ['instance'])
        self.redis_rdb_bgsave_in_progress = Gauge('redis_rdb_bgsave_in_progress', '后台保存是否进行中', ['instance'])
        self.redis_rdb_last_save_time = Gauge('redis_rdb_last_save_time', '上次保存时间戳', ['instance'])
        self.redis_rdb_last_bgsave_status = Gauge('redis_rdb_last_bgsave_status', '上次后台保存状态', ['instance'])
        self.redis_aof_enabled = Gauge('redis_aof_enabled', 'AOF是否启用', ['instance'])
        self.redis_aof_rewrite_in_progress = Gauge('redis_aof_rewrite_in_progress', 'AOF重写是否进行中', ['instance'])
        self.redis_aof_rewrite_scheduled = Gauge('redis_aof_rewrite_scheduled', 'AOF重写是否已调度', ['instance'])
        self.redis_aof_last_rewrite_time_sec = Gauge('redis_aof_last_rewrite_time_sec', 'AOF上次重写耗时', ['instance'])
        self.redis_aof_current_rewrite_time_sec = Gauge('redis_aof_current_rewrite_time_sec', 'AOF当前重写耗时', ['instance'])
        self.redis_aof_last_bgrewrite_status = Gauge('redis_aof_last_bgrewrite_status', 'AOF上次后台重写状态', ['instance'])
        self.redis_aof_last_write_status = Gauge('redis_aof_last_write_status', 'AOF上次写入状态', ['instance'])
        self.redis_aof_current_size_bytes = Gauge('redis_aof_current_size_bytes', 'AOF当前大小字节数', ['instance'])
        self.redis_aof_base_size_bytes = Gauge('redis_aof_base_size_bytes', 'AOF基础大小字节数', ['instance'])
        
        # 统计指标
        self.redis_total_connections_received = Counter('redis_total_connections_received_total', '总接收连接数', ['instance'])
        self.redis_total_commands_processed = Counter('redis_total_commands_processed_total', '总处理命令数', ['instance'])
        self.redis_instantaneous_ops_per_sec = Gauge('redis_instantaneous_ops_per_sec', '瞬时每秒操作数', ['instance'])
        self.redis_total_net_input_bytes = Counter('redis_total_net_input_bytes_total', '总网络输入字节数', ['instance'])
        self.redis_total_net_output_bytes = Counter('redis_total_net_output_bytes_total', '总网络输出字节数', ['instance'])
        self.redis_instantaneous_input_kbps = Gauge('redis_instantaneous_input_kbps', '瞬时输入KB/s', ['instance'])
        self.redis_instantaneous_output_kbps = Gauge('redis_instantaneous_output_kbps', '瞬时输出KB/s', ['instance'])
        self.redis_rejected_connections = Counter('redis_rejected_connections_total', '拒绝连接总数', ['instance'])
        self.redis_sync_full = Counter('redis_sync_full_total', '全量同步总数', ['instance'])
        self.redis_sync_partial_ok = Counter('redis_sync_partial_ok_total', '部分同步成功总数', ['instance'])
        self.redis_sync_partial_err = Counter('redis_sync_partial_err_total', '部分同步失败总数', ['instance'])
        self.redis_expired_keys = Counter('redis_expired_keys_total', '过期键总数', ['instance'])
        self.redis_evicted_keys = Counter('redis_evicted_keys_total', '驱逐键总数', ['instance'])
        self.redis_keyspace_hits = Counter('redis_keyspace_hits_total', '键空间命中总数', ['instance'])
        self.redis_keyspace_misses = Counter('redis_keyspace_misses_total', '键空间未命中总数', ['instance'])
        self.redis_pubsub_channels = Gauge('redis_pubsub_channels', '发布订阅频道数', ['instance'])
        self.redis_pubsub_patterns = Gauge('redis_pubsub_patterns', '发布订阅模式数', ['instance'])
        
        # 数据库指标
        self.redis_db_keys = Gauge('redis_db_keys', '数据库键数量', ['instance', 'db'])
        self.redis_db_keys_expiring = Gauge('redis_db_keys_expiring', '数据库过期键数量', ['instance', 'db'])
        self.redis_db_avg_ttl_seconds = Gauge('redis_db_avg_ttl_seconds', '数据库平均TTL秒数', ['instance', 'db'])
        
        # 复制指标
        self.redis_master_repl_offset = Gauge('redis_master_repl_offset', '主节点复制偏移量', ['instance'])
        self.redis_slave_repl_offset = Gauge('redis_slave_repl_offset', '从节点复制偏移量', ['instance'])
        self.redis_slave_lag = Gauge('redis_slave_lag', '从节点延迟', ['instance'])
        self.redis_slave_read_only = Gauge('redis_slave_read_only', '从节点是否只读', ['instance'])
        self.redis_connected_slaves = Gauge('redis_connected_slaves', '连接的从节点数', ['instance'])
        
        # 集群指标
        self.redis_cluster_enabled = Gauge('redis_cluster_enabled', '集群是否启用', ['instance'])
        self.redis_cluster_state = Gauge('redis_cluster_state', '集群状态', ['instance'])
        self.redis_cluster_slots_assigned = Gauge('redis_cluster_slots_assigned', '集群分配的槽位数', ['instance'])
        self.redis_cluster_slots_ok = Gauge('redis_cluster_slots_ok', '集群正常的槽位数', ['instance'])
        self.redis_cluster_slots_pfail = Gauge('redis_cluster_slots_pfail', '集群可能失败的槽位数', ['instance'])
        self.redis_cluster_slots_fail = Gauge('redis_cluster_slots_fail', '集群失败的槽位数', ['instance'])
        self.redis_cluster_known_nodes = Gauge('redis_cluster_known_nodes', '集群已知节点数', ['instance'])
        self.redis_cluster_size = Gauge('redis_cluster_size', '集群大小', ['instance'])
        self.redis_cluster_current_epoch = Gauge('redis_cluster_current_epoch', '集群当前纪元', ['instance'])
        self.redis_cluster_my_epoch = Gauge('redis_cluster_my_epoch', '集群我的纪元', ['instance'])
        
        # 慢日志指标
        self.redis_slowlog_length = Gauge('redis_slowlog_length', '慢日志长度', ['instance'])
        
        # 延迟指标
        self.redis_latency_percentiles_usec = Histogram('redis_latency_percentiles_usec', '延迟百分位数微秒', ['instance', 'command'])
        
    def get_redis_client(self, instance_config: Dict[str, Any]):
        """获取Redis客户端"""
        instance_name = instance_config['name']
        
        if instance_name not in self.clients:
            if instance_config['type'] == 'standalone':
                self.clients[instance_name] = redis.Redis(
                    host=instance_config['host'],
                    port=instance_config['port'],
                    password=instance_config.get('password'),
                    db=instance_config.get('db', 0),
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
            elif instance_config['type'] == 'sentinel':
                sentinel = redis.sentinel.Sentinel(
                    instance_config['sentinels'],
                    socket_timeout=5
                )
                self.clients[instance_name] = sentinel.master_for(
                    instance_config['service_name'],
                    password=instance_config.get('password')
                )
            elif instance_config['type'] == 'cluster':
                startup_nodes = [
                    {"host": node['host'], "port": node['port']}
                    for node in instance_config['nodes']
                ]
                self.clients[instance_name] = RedisCluster(
                    startup_nodes=startup_nodes,
                    password=instance_config.get('password'),
                    skip_full_coverage_check=True,
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
        
        return self.clients[instance_name]
    
    def collect_metrics(self, instance_config: Dict[str, Any]):
        """收集单个实例的指标"""
        instance_name = instance_config['name']
        
        try:
            client = self.get_redis_client(instance_config)
            
            # 测试连接
            client.ping()
            self.redis_up.labels(instance=instance_name, role='unknown').set(1)
            
            # 获取INFO信息
            info = client.info()
            
            # 设置实例信息
            self.redis_info.labels(instance=instance_name).info({
                'version': info.get('redis_version', ''),
                'mode': info.get('redis_mode', ''),
                'role': info.get('role', ''),
                'os': info.get('os', ''),
                'arch_bits': str(info.get('arch_bits', '')),
                'multiplexing_api': info.get('multiplexing_api', ''),
                'gcc_version': info.get('gcc_version', ''),
                'process_id': str(info.get('process_id', '')),
                'run_id': info.get('run_id', ''),
                'tcp_port': str(info.get('tcp_port', '')),
                'uptime_in_seconds': str(info.get('uptime_in_seconds', ''))
            })
            
            # 更新角色标签
            role = info.get('role', 'unknown')
            self.redis_up.labels(instance=instance_name, role=role).set(1)
            
            # 连接指标
            self.redis_connected_clients.labels(instance=instance_name).set(info.get('connected_clients', 0))
            self.redis_blocked_clients.labels(instance=instance_name).set(info.get('blocked_clients', 0))
            self.redis_tracking_clients.labels(instance=instance_name).set(info.get('tracking_clients', 0))
            
            # 内存指标
            self.redis_memory_used_bytes.labels(instance=instance_name).set(info.get('used_memory', 0))
            self.redis_memory_used_rss_bytes.labels(instance=instance_name).set(info.get('used_memory_rss', 0))
            self.redis_memory_used_peak_bytes.labels(instance=instance_name).set(info.get('used_memory_peak', 0))
            self.redis_memory_used_overhead_bytes.labels(instance=instance_name).set(info.get('used_memory_overhead', 0))
            self.redis_memory_used_dataset_bytes.labels(instance=instance_name).set(info.get('used_memory_dataset', 0))
            self.redis_memory_used_lua_bytes.labels(instance=instance_name).set(info.get('used_memory_lua', 0))
            self.redis_memory_fragmentation_ratio.labels(instance=instance_name).set(info.get('mem_fragmentation_ratio', 0))
            
            # 持久化指标
            self.redis_rdb_changes_since_last_save.labels(instance=instance_name).set(info.get('rdb_changes_since_last_save', 0))
            self.redis_rdb_bgsave_in_progress.labels(instance=instance_name).set(info.get('rdb_bgsave_in_progress', 0))
            self.redis_rdb_last_save_time.labels(instance=instance_name).set(info.get('rdb_last_save_time', 0))
            self.redis_rdb_last_bgsave_status.labels(instance=instance_name).set(1 if info.get('rdb_last_bgsave_status') == 'ok' else 0)
            
            self.redis_aof_enabled.labels(instance=instance_name).set(info.get('aof_enabled', 0))
            if info.get('aof_enabled'):
                self.redis_aof_rewrite_in_progress.labels(instance=instance_name).set(info.get('aof_rewrite_in_progress', 0))
                self.redis_aof_rewrite_scheduled.labels(instance=instance_name).set(info.get('aof_rewrite_scheduled', 0))
                self.redis_aof_last_rewrite_time_sec.labels(instance=instance_name).set(info.get('aof_last_rewrite_time_sec', 0))
                self.redis_aof_current_rewrite_time_sec.labels(instance=instance_name).set(info.get('aof_current_rewrite_time_sec', 0))
                self.redis_aof_last_bgrewrite_status.labels(instance=instance_name).set(1 if info.get('aof_last_bgrewrite_status') == 'ok' else 0)
                self.redis_aof_last_write_status.labels(instance=instance_name).set(1 if info.get('aof_last_write_status') == 'ok' else 0)
                self.redis_aof_current_size_bytes.labels(instance=instance_name).set(info.get('aof_current_size', 0))
                self.redis_aof_base_size_bytes.labels(instance=instance_name).set(info.get('aof_base_size', 0))
            
            # 统计指标
            self.redis_total_connections_received.labels(instance=instance_name)._value._value = info.get('total_connections_received', 0)
            self.redis_total_commands_processed.labels(instance=instance_name)._value._value = info.get('total_commands_processed', 0)
            self.redis_instantaneous_ops_per_sec.labels(instance=instance_name).set(info.get('instantaneous_ops_per_sec', 0))
            self.redis_total_net_input_bytes.labels(instance=instance_name)._value._value = info.get('total_net_input_bytes', 0)
            self.redis_total_net_output_bytes.labels(instance=instance_name)._value._value = info.get('total_net_output_bytes', 0)
            self.redis_instantaneous_input_kbps.labels(instance=instance_name).set(info.get('instantaneous_input_kbps', 0))
            self.redis_instantaneous_output_kbps.labels(instance=instance_name).set(info.get('instantaneous_output_kbps', 0))
            self.redis_rejected_connections.labels(instance=instance_name)._value._value = info.get('rejected_connections', 0)
            self.redis_sync_full.labels(instance=instance_name)._value._value = info.get('sync_full', 0)
            self.redis_sync_partial_ok.labels(instance=instance_name)._value._value = info.get('sync_partial_ok', 0)
            self.redis_sync_partial_err.labels(instance=instance_name)._value._value = info.get('sync_partial_err', 0)
            self.redis_expired_keys.labels(instance=instance_name)._value._value = info.get('expired_keys', 0)
            self.redis_evicted_keys.labels(instance=instance_name)._value._value = info.get('evicted_keys', 0)
            self.redis_keyspace_hits.labels(instance=instance_name)._value._value = info.get('keyspace_hits', 0)
            self.redis_keyspace_misses.labels(instance=instance_name)._value._value = info.get('keyspace_misses', 0)
            self.redis_pubsub_channels.labels(instance=instance_name).set(info.get('pubsub_channels', 0))
            self.redis_pubsub_patterns.labels(instance=instance_name).set(info.get('pubsub_patterns', 0))
            
            # 数据库指标
            for db_name, db_info in info.items():
                if db_name.startswith('db'):
                    db_num = db_name[2:]
                    if isinstance(db_info, dict):
                        self.redis_db_keys.labels(instance=instance_name, db=db_num).set(db_info.get('keys', 0))
                        self.redis_db_keys_expiring.labels(instance=instance_name, db=db_num).set(db_info.get('expires', 0))
                        self.redis_db_avg_ttl_seconds.labels(instance=instance_name, db=db_num).set(db_info.get('avg_ttl', 0) / 1000)
            
            # 复制指标
            if role == 'master':
                self.redis_master_repl_offset.labels(instance=instance_name).set(info.get('master_repl_offset', 0))
                self.redis_connected_slaves.labels(instance=instance_name).set(info.get('connected_slaves', 0))
            elif role == 'slave':
                self.redis_slave_repl_offset.labels(instance=instance_name).set(info.get('slave_repl_offset', 0))
                self.redis_slave_lag.labels(instance=instance_name).set(info.get('slave_lag', 0))
                self.redis_slave_read_only.labels(instance=instance_name).set(info.get('slave_read_only', 0))
            
            # 集群指标
            if info.get('cluster_enabled'):
                self.redis_cluster_enabled.labels(instance=instance_name).set(1)
                cluster_info = client.cluster_info()
                self.redis_cluster_state.labels(instance=instance_name).set(1 if cluster_info.get('cluster_state') == 'ok' else 0)
                self.redis_cluster_slots_assigned.labels(instance=instance_name).set(cluster_info.get('cluster_slots_assigned', 0))
                self.redis_cluster_slots_ok.labels(instance=instance_name).set(cluster_info.get('cluster_slots_ok', 0))
                self.redis_cluster_slots_pfail.labels(instance=instance_name).set(cluster_info.get('cluster_slots_pfail', 0))
                self.redis_cluster_slots_fail.labels(instance=instance_name).set(cluster_info.get('cluster_slots_fail', 0))
                self.redis_cluster_known_nodes.labels(instance=instance_name).set(cluster_info.get('cluster_known_nodes', 0))
                self.redis_cluster_size.labels(instance=instance_name).set(cluster_info.get('cluster_size', 0))
                self.redis_cluster_current_epoch.labels(instance=instance_name).set(cluster_info.get('cluster_current_epoch', 0))
                self.redis_cluster_my_epoch.labels(instance=instance_name).set(cluster_info.get('cluster_my_epoch', 0))
            else:
                self.redis_cluster_enabled.labels(instance=instance_name).set(0)
            
            # 慢日志指标
            slowlog_len = client.slowlog_len()
            self.redis_slowlog_length.labels(instance=instance_name).set(slowlog_len)
            
            logger.info(f"收集实例 {instance_name} 指标成功")
            
        except Exception as e:
            logger.error(f"收集实例 {instance_name} 指标失败: {e}")
            self.redis_up.labels(instance=instance_name, role='unknown').set(0)
    
    def run(self):
        """运行导出器"""
        logger.info("启动Redis Prometheus导出器")
        
        # 启动HTTP服务器
        start_http_server(self.config.get('port', 9121))
        logger.info(f"HTTP服务器启动在端口 {self.config.get('port', 9121)}")
        
        # 收集指标循环
        while True:
            try:
                for instance_config in self.config['instances']:
                    self.collect_metrics(instance_config)
                
                time.sleep(self.config.get('scrape_interval', 15))
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，正在退出...")
                break
            except Exception as e:
                logger.error(f"收集指标时发生错误: {e}")
                time.sleep(5)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Redis Prometheus导出器')
    parser.add_argument('--config', '-c', default='redis-exporter-config.json', help='配置文件路径')
    parser.add_argument('--port', '-p', type=int, default=9121, help='HTTP服务器端口')
    
    args = parser.parse_args()
    
    # 加载配置
    try:
        with open(args.config, 'r') as f:
            config = json.load(f)
    except FileNotFoundError:
        # 使用默认配置
        config = {
            "port": args.port,
            "scrape_interval": 15,
            "instances": [
                {
                    "name": "redis-standalone",
                    "type": "standalone",
                    "host": "localhost",
                    "port": 6379,
                    "password": None,
                    "db": 0
                }
            ]
        }
        logger.warning(f"配置文件 {args.config} 不存在，使用默认配置")
    
    # 创建并运行导出器
    exporter = RedisExporter(config)
    exporter.run()


if __name__ == "__main__":
    main()
