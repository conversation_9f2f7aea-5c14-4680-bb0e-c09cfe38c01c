# Redis单机模式配置文件
# 适用于开发环境和小规模部署

# 网络配置
bind 0.0.0.0
port 6379
protected-mode no
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 快照配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 复制配置
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# 安全配置
# requirepass foobared
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command EVAL ""
# rename-command DEBUG ""
# rename-command CONFIG ""

# 内存管理
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 惰性删除
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# 线程I/O
io-threads 4
io-threads-do-reads yes

# 追加文件配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua脚本
lua-time-limit 5000

# 慢日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 0

# 事件通知
notify-keyspace-events ""

# 高级配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# 频率
hz 10

# 动态HZ
dynamic-hz yes

# AOF重写增量fsync
aof-rewrite-incremental-fsync yes

# RDB保存增量fsync
rdb-save-incremental-fsync yes

# LFU配置
lfu-log-factor 10
lfu-decay-time 1

# 模块配置
# loadmodule /path/to/my_module.so
# loadmodule /path/to/other_module.so

# 包含其他配置文件
# include /path/to/local.conf
# include /path/to/other.conf
