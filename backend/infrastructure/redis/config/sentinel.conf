# Redis哨兵配置文件
# 用于监控Redis主从集群并实现自动故障转移

# 网络配置
bind 0.0.0.0
port 26379
protected-mode no

# 哨兵配置
sentinel announce-ip ""
sentinel announce-port 0

# 监控主节点
# sentinel monitor <master-name> <ip> <redis-port> <quorum>
sentinel monitor mymaster redis-master 6379 2

# 主节点密码
# sentinel auth-pass mymaster your_master_password

# 故障转移超时时间（毫秒）
sentinel down-after-milliseconds mymaster 30000

# 故障转移期间允许的并行同步从节点数量
sentinel parallel-syncs mymaster 1

# 故障转移超时时间（毫秒）
sentinel failover-timeout mymaster 180000

# 拒绝故障转移的条件
sentinel deny-scripts-reconfig yes

# 通知脚本
# sentinel notification-script mymaster /var/redis/notify.sh

# 客户端重新配置脚本
# sentinel client-reconfig-script mymaster /var/redis/reconfig.sh

# 日志配置
logfile ""
loglevel notice

# 工作目录
dir /tmp

# 哨兵运行时配置（自动生成，不要手动修改）
# Generated by CONFIG REWRITE
# sentinel known-replica mymaster ********** 6379
# sentinel known-replica mymaster ********** 6379
# sentinel known-sentinel mymaster ********** 26379 
# sentinel known-sentinel mymaster ********** 26379
# sentinel current-epoch 0
