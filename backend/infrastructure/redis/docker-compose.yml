# Redis缓存基础设施配置
# 包含Redis主从集群、哨兵、集群模式等多种部署方案

version: '3.8'

services:
  # Redis单机模式（开发环境）
  redis-standalone:
    image: redis:7.2-alpine
    container_name: rag-redis-standalone
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis主节点（主从模式）
  redis-master:
    image: redis:7.2-alpine
    container_name: rag-redis-master
    ports:
      - "6380:6379"
    volumes:
      - redis_master_data:/data
      - ./config/redis-master.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis从节点1
  redis-slave-1:
    image: redis:7.2-alpine
    container_name: rag-redis-slave-1
    ports:
      - "6381:6379"
    volumes:
      - redis_slave1_data:/data
      - ./config/redis-slave.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_MASTER_HOST=redis-master
      - REDIS_MASTER_PORT=6379
    depends_on:
      - redis-master
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis从节点2
  redis-slave-2:
    image: redis:7.2-alpine
    container_name: rag-redis-slave-2
    ports:
      - "6382:6379"
    volumes:
      - redis_slave2_data:/data
      - ./config/redis-slave.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_MASTER_HOST=redis-master
      - REDIS_MASTER_PORT=6379
    depends_on:
      - redis-master
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis哨兵1
  redis-sentinel-1:
    image: redis:7.2-alpine
    container_name: rag-redis-sentinel-1
    ports:
      - "26379:26379"
    volumes:
      - ./config/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      - rag-network
    restart: unless-stopped

  # Redis哨兵2
  redis-sentinel-2:
    image: redis:7.2-alpine
    container_name: rag-redis-sentinel-2
    ports:
      - "26380:26379"
    volumes:
      - ./config/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      - rag-network
    restart: unless-stopped

  # Redis哨兵3
  redis-sentinel-3:
    image: redis:7.2-alpine
    container_name: rag-redis-sentinel-3
    ports:
      - "26381:26379"
    volumes:
      - ./config/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave-1
      - redis-slave-2
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点1
  redis-cluster-1:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-1
    ports:
      - "7001:7001"
      - "17001:17001"
    volumes:
      - redis_cluster1_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7001
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点2
  redis-cluster-2:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-2
    ports:
      - "7002:7002"
      - "17002:17002"
    volumes:
      - redis_cluster2_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7002
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点3
  redis-cluster-3:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-3
    ports:
      - "7003:7003"
      - "17003:17003"
    volumes:
      - redis_cluster3_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7003
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点4
  redis-cluster-4:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-4
    ports:
      - "7004:7004"
      - "17004:17004"
    volumes:
      - redis_cluster4_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7004
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点5
  redis-cluster-5:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-5
    ports:
      - "7005:7005"
      - "17005:17005"
    volumes:
      - redis_cluster5_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7005
    networks:
      - rag-network
    restart: unless-stopped

  # Redis集群节点6
  redis-cluster-6:
    image: redis:7.2-alpine
    container_name: rag-redis-cluster-6
    ports:
      - "7006:7006"
      - "17006:17006"
    volumes:
      - redis_cluster6_data:/data
      - ./config/redis-cluster.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PORT=7006
    networks:
      - rag-network
    restart: unless-stopped

  # Redis Commander（Web管理界面）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: rag-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=standalone:redis-standalone:6379,master:redis-master:6380,slave1:redis-slave-1:6381,slave2:redis-slave-2:6382
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    depends_on:
      - redis-standalone
      - redis-master
    networks:
      - rag-network
    restart: unless-stopped

  # RedisInsight（官方管理工具）
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: rag-redis-insight
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    networks:
      - rag-network
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  redis_master_data:
    driver: local
  redis_slave1_data:
    driver: local
  redis_slave2_data:
    driver: local
  redis_cluster1_data:
    driver: local
  redis_cluster2_data:
    driver: local
  redis_cluster3_data:
    driver: local
  redis_cluster4_data:
    driver: local
  redis_cluster5_data:
    driver: local
  redis_cluster6_data:
    driver: local
  redis_insight_data:
    driver: local

networks:
  rag-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
