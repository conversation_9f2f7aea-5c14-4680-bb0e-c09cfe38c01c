#!/usr/bin/env python3
"""
Redis管理工具
提供Redis集群的管理、监控、备份等功能
"""

import os
import sys
import json
import time
import argparse
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
import redis
import redis.sentinel
from redis.cluster import RedisCluster

class RedisManager:
    """Redis管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.clients = {}
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "standalone": {
                "host": "localhost",
                "port": 6379,
                "password": None,
                "db": 0
            },
            "sentinel": {
                "sentinels": [
                    ("localhost", 26379),
                    ("localhost", 26380),
                    ("localhost", 26381)
                ],
                "service_name": "mymaster",
                "password": None
            },
            "cluster": {
                "nodes": [
                    {"host": "localhost", "port": 7001},
                    {"host": "localhost", "port": 7002},
                    {"host": "localhost", "port": 7003},
                    {"host": "localhost", "port": 7004},
                    {"host": "localhost", "port": 7005},
                    {"host": "localhost", "port": 7006}
                ],
                "password": None
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            # 合并默认配置
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            return config
        
        return default_config
    
    def get_standalone_client(self) -> redis.Redis:
        """获取单机Redis客户端"""
        if 'standalone' not in self.clients:
            config = self.config['standalone']
            self.clients['standalone'] = redis.Redis(
                host=config['host'],
                port=config['port'],
                password=config['password'],
                db=config['db'],
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
        return self.clients['standalone']
    
    def get_sentinel_client(self) -> redis.Redis:
        """获取哨兵Redis客户端"""
        if 'sentinel' not in self.clients:
            config = self.config['sentinel']
            sentinel = redis.sentinel.Sentinel(
                config['sentinels'],
                socket_timeout=5,
                socket_connect_timeout=5
            )
            self.clients['sentinel'] = sentinel.master_for(
                config['service_name'],
                password=config['password'],
                decode_responses=True
            )
        return self.clients['sentinel']
    
    def get_cluster_client(self) -> RedisCluster:
        """获取集群Redis客户端"""
        if 'cluster' not in self.clients:
            config = self.config['cluster']
            startup_nodes = [
                {"host": node['host'], "port": node['port']}
                for node in config['nodes']
            ]
            self.clients['cluster'] = RedisCluster(
                startup_nodes=startup_nodes,
                password=config['password'],
                decode_responses=True,
                skip_full_coverage_check=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
        return self.clients['cluster']
    
    def get_client(self, mode: str = 'standalone'):
        """根据模式获取Redis客户端"""
        if mode == 'standalone':
            return self.get_standalone_client()
        elif mode == 'sentinel':
            return self.get_sentinel_client()
        elif mode == 'cluster':
            return self.get_cluster_client()
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def ping(self, mode: str = 'standalone') -> bool:
        """测试Redis连接"""
        try:
            client = self.get_client(mode)
            result = client.ping()
            print(f"✅ {mode.upper()} 模式连接正常: {result}")
            return True
        except Exception as e:
            print(f"❌ {mode.upper()} 模式连接失败: {e}")
            return False
    
    def info(self, mode: str = 'standalone', section: str = None) -> Dict[str, Any]:
        """获取Redis信息"""
        try:
            client = self.get_client(mode)
            info = client.info(section)
            
            print(f"\n=== {mode.upper()} 模式信息 ===")
            if section:
                print(f"节: {section}")
            
            for key, value in info.items():
                print(f"{key}: {value}")
            
            return info
        except Exception as e:
            print(f"❌ 获取信息失败: {e}")
            return {}
    
    def monitor_stats(self, mode: str = 'standalone', duration: int = 60):
        """监控Redis统计信息"""
        try:
            client = self.get_client(mode)
            print(f"🔍 开始监控 {mode.upper()} 模式，持续 {duration} 秒...")
            
            start_time = time.time()
            while time.time() - start_time < duration:
                info = client.info()
                
                # 清屏
                os.system('clear' if os.name == 'posix' else 'cls')
                
                print(f"=== Redis {mode.upper()} 实时监控 ===")
                print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"运行时间: {info.get('uptime_in_seconds', 0)} 秒")
                print(f"连接数: {info.get('connected_clients', 0)}")
                print(f"使用内存: {info.get('used_memory_human', 'N/A')}")
                print(f"内存使用率: {info.get('used_memory_rss_human', 'N/A')}")
                print(f"键总数: {info.get('db0', {}).get('keys', 0) if 'db0' in info else 0}")
                print(f"命令处理数: {info.get('total_commands_processed', 0)}")
                print(f"网络输入: {info.get('total_net_input_bytes', 0)} 字节")
                print(f"网络输出: {info.get('total_net_output_bytes', 0)} 字节")
                
                if mode == 'cluster':
                    print(f"集群状态: {info.get('cluster_state', 'N/A')}")
                    print(f"集群节点数: {info.get('cluster_known_nodes', 0)}")
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
        except Exception as e:
            print(f"❌ 监控失败: {e}")
    
    def backup(self, mode: str = 'standalone', output_dir: str = './backups'):
        """备份Redis数据"""
        try:
            client = self.get_client(mode)
            
            # 创建备份目录
            os.makedirs(output_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(output_dir, f"redis_{mode}_{timestamp}.rdb")
            
            print(f"🔄 开始备份 {mode.upper()} 模式数据...")
            
            # 执行BGSAVE
            client.bgsave()
            
            # 等待备份完成
            while client.lastsave() == client.lastsave():
                time.sleep(1)
            
            print(f"✅ 备份完成: {backup_file}")
            return backup_file
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def flush_db(self, mode: str = 'standalone', confirm: bool = False):
        """清空数据库"""
        if not confirm:
            response = input(f"⚠️  确定要清空 {mode.upper()} 模式的所有数据吗？(yes/no): ")
            if response.lower() != 'yes':
                print("操作已取消")
                return
        
        try:
            client = self.get_client(mode)
            client.flushall()
            print(f"✅ {mode.upper()} 模式数据已清空")
        except Exception as e:
            print(f"❌ 清空数据失败: {e}")
    
    def set_key(self, mode: str, key: str, value: str, ttl: int = None):
        """设置键值"""
        try:
            client = self.get_client(mode)
            if ttl:
                client.setex(key, ttl, value)
                print(f"✅ 设置键 {key} = {value} (TTL: {ttl}s)")
            else:
                client.set(key, value)
                print(f"✅ 设置键 {key} = {value}")
        except Exception as e:
            print(f"❌ 设置键失败: {e}")
    
    def get_key(self, mode: str, key: str):
        """获取键值"""
        try:
            client = self.get_client(mode)
            value = client.get(key)
            if value is not None:
                print(f"✅ 键 {key} = {value}")
            else:
                print(f"❌ 键 {key} 不存在")
            return value
        except Exception as e:
            print(f"❌ 获取键失败: {e}")
            return None
    
    def delete_key(self, mode: str, key: str):
        """删除键"""
        try:
            client = self.get_client(mode)
            result = client.delete(key)
            if result:
                print(f"✅ 删除键 {key}")
            else:
                print(f"❌ 键 {key} 不存在")
            return result
        except Exception as e:
            print(f"❌ 删除键失败: {e}")
            return False
    
    def list_keys(self, mode: str, pattern: str = '*', limit: int = 100):
        """列出键"""
        try:
            client = self.get_client(mode)
            
            if mode == 'cluster':
                # 集群模式需要从所有节点获取键
                keys = []
                for node in client.get_nodes():
                    node_keys = node.keys(pattern)
                    keys.extend(node_keys[:limit])
                    if len(keys) >= limit:
                        break
                keys = keys[:limit]
            else:
                keys = client.keys(pattern)[:limit]
            
            print(f"📋 找到 {len(keys)} 个键 (模式: {pattern}):")
            for key in keys:
                print(f"  - {key}")
            
            return keys
        except Exception as e:
            print(f"❌ 列出键失败: {e}")
            return []
    
    def cluster_info(self):
        """获取集群信息"""
        try:
            client = self.get_cluster_client()
            
            print("=== Redis集群信息 ===")
            
            # 集群节点信息
            nodes = client.get_nodes()
            print(f"节点数量: {len(nodes)}")
            
            for i, node in enumerate(nodes):
                info = node.info()
                print(f"\n节点 {i+1}:")
                print(f"  地址: {node.host}:{node.port}")
                print(f"  角色: {'主节点' if info.get('role') == 'master' else '从节点'}")
                print(f"  内存使用: {info.get('used_memory_human', 'N/A')}")
                print(f"  连接数: {info.get('connected_clients', 0)}")
            
            # 集群状态
            cluster_info = client.cluster_info()
            print(f"\n集群状态: {cluster_info.get('cluster_state', 'N/A')}")
            print(f"集群槽位: {cluster_info.get('cluster_slots_assigned', 0)}/16384")
            
        except Exception as e:
            print(f"❌ 获取集群信息失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Redis管理工具')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--mode', '-m', choices=['standalone', 'sentinel', 'cluster'], 
                       default='standalone', help='Redis模式')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # ping命令
    subparsers.add_parser('ping', help='测试连接')
    
    # info命令
    info_parser = subparsers.add_parser('info', help='获取信息')
    info_parser.add_argument('--section', help='信息节')
    
    # monitor命令
    monitor_parser = subparsers.add_parser('monitor', help='监控统计')
    monitor_parser.add_argument('--duration', '-d', type=int, default=60, help='监控时长')
    
    # backup命令
    backup_parser = subparsers.add_parser('backup', help='备份数据')
    backup_parser.add_argument('--output', '-o', default='./backups', help='输出目录')
    
    # flush命令
    flush_parser = subparsers.add_parser('flush', help='清空数据')
    flush_parser.add_argument('--confirm', action='store_true', help='确认清空')
    
    # set命令
    set_parser = subparsers.add_parser('set', help='设置键值')
    set_parser.add_argument('key', help='键名')
    set_parser.add_argument('value', help='键值')
    set_parser.add_argument('--ttl', type=int, help='过期时间（秒）')
    
    # get命令
    get_parser = subparsers.add_parser('get', help='获取键值')
    get_parser.add_argument('key', help='键名')
    
    # delete命令
    del_parser = subparsers.add_parser('delete', help='删除键')
    del_parser.add_argument('key', help='键名')
    
    # keys命令
    keys_parser = subparsers.add_parser('keys', help='列出键')
    keys_parser.add_argument('--pattern', default='*', help='匹配模式')
    keys_parser.add_argument('--limit', type=int, default=100, help='限制数量')
    
    # cluster-info命令
    subparsers.add_parser('cluster-info', help='集群信息')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建管理器
    manager = RedisManager(args.config)
    
    try:
        if args.command == 'ping':
            manager.ping(args.mode)
        
        elif args.command == 'info':
            manager.info(args.mode, args.section)
        
        elif args.command == 'monitor':
            manager.monitor_stats(args.mode, args.duration)
        
        elif args.command == 'backup':
            manager.backup(args.mode, args.output)
        
        elif args.command == 'flush':
            manager.flush_db(args.mode, args.confirm)
        
        elif args.command == 'set':
            manager.set_key(args.mode, args.key, args.value, args.ttl)
        
        elif args.command == 'get':
            manager.get_key(args.mode, args.key)
        
        elif args.command == 'delete':
            manager.delete_key(args.mode, args.key)
        
        elif args.command == 'keys':
            manager.list_keys(args.mode, args.pattern, args.limit)
        
        elif args.command == 'cluster-info':
            manager.cluster_info()
    
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
