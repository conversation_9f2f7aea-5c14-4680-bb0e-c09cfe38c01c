#!/bin/bash

# Redis集群初始化脚本
# 自动创建和配置Redis集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
CLUSTER_NODES=(
    "127.0.0.1:7001"
    "127.0.0.1:7002"
    "127.0.0.1:7003"
    "127.0.0.1:7004"
    "127.0.0.1:7005"
    "127.0.0.1:7006"
)

REDIS_PASSWORD=""
REPLICAS=1

# 帮助信息
show_help() {
    cat << EOF
Redis集群初始化脚本

用法: $0 [选项]

选项:
    -h, --help              显示帮助信息
    -p, --password PASSWORD 设置Redis密码
    -r, --replicas NUM      设置每个主节点的副本数量 (默认: 1)
    -n, --nodes NODES       指定节点列表 (格式: host:port,host:port,...)
    --reset                 重置集群（删除所有数据）
    --status                显示集群状态
    --add-node HOST:PORT    添加节点到集群
    --remove-node NODE_ID   从集群移除节点

示例:
    $0                                          # 使用默认配置初始化集群
    $0 -p mypassword -r 2                      # 设置密码和副本数
    $0 --nodes "************:7001,************:7001,************:7001"
    $0 --status                                 # 显示集群状态
    $0 --reset                                  # 重置集群

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--password)
                REDIS_PASSWORD="$2"
                shift 2
                ;;
            -r|--replicas)
                REPLICAS="$2"
                shift 2
                ;;
            -n|--nodes)
                IFS=',' read -ra CLUSTER_NODES <<< "$2"
                shift 2
                ;;
            --reset)
                reset_cluster
                exit 0
                ;;
            --status)
                show_cluster_status
                exit 0
                ;;
            --add-node)
                add_node "$2"
                exit 0
                ;;
            --remove-node)
                remove_node "$2"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Redis CLI
check_redis_cli() {
    if ! command -v redis-cli &> /dev/null; then
        log_error "redis-cli 未找到，请安装Redis"
        exit 1
    fi
    log_success "redis-cli 已找到"
}

# 检查节点连接
check_nodes() {
    log_info "检查节点连接..."
    
    local failed_nodes=()
    
    for node in "${CLUSTER_NODES[@]}"; do
        local host=$(echo $node | cut -d: -f1)
        local port=$(echo $node | cut -d: -f2)
        
        if [[ -n "$REDIS_PASSWORD" ]]; then
            if ! redis-cli -h $host -p $port -a "$REDIS_PASSWORD" ping &> /dev/null; then
                failed_nodes+=("$node")
            fi
        else
            if ! redis-cli -h $host -p $port ping &> /dev/null; then
                failed_nodes+=("$node")
            fi
        fi
    done
    
    if [[ ${#failed_nodes[@]} -gt 0 ]]; then
        log_error "以下节点连接失败:"
        for node in "${failed_nodes[@]}"; do
            echo "  - $node"
        done
        exit 1
    fi
    
    log_success "所有节点连接正常"
}

# 清理节点
clean_nodes() {
    log_info "清理节点数据..."
    
    for node in "${CLUSTER_NODES[@]}"; do
        local host=$(echo $node | cut -d: -f1)
        local port=$(echo $node | cut -d: -f2)
        
        log_info "清理节点 $node"
        
        if [[ -n "$REDIS_PASSWORD" ]]; then
            redis-cli -h $host -p $port -a "$REDIS_PASSWORD" FLUSHALL
            redis-cli -h $host -p $port -a "$REDIS_PASSWORD" CLUSTER RESET
        else
            redis-cli -h $host -p $port FLUSHALL
            redis-cli -h $host -p $port CLUSTER RESET
        fi
    done
    
    log_success "节点清理完成"
}

# 创建集群
create_cluster() {
    log_info "创建Redis集群..."
    
    local nodes_str=$(IFS=' '; echo "${CLUSTER_NODES[*]}")
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        echo "yes" | redis-cli --cluster create $nodes_str \
            --cluster-replicas $REPLICAS \
            -a "$REDIS_PASSWORD"
    else
        echo "yes" | redis-cli --cluster create $nodes_str \
            --cluster-replicas $REPLICAS
    fi
    
    if [[ $? -eq 0 ]]; then
        log_success "Redis集群创建成功"
    else
        log_error "Redis集群创建失败"
        exit 1
    fi
}

# 验证集群
verify_cluster() {
    log_info "验证集群状态..."
    
    local first_node=$(echo ${CLUSTER_NODES[0]} | cut -d: -f1,2)
    local host=$(echo $first_node | cut -d: -f1)
    local port=$(echo $first_node | cut -d: -f2)
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        local cluster_info=$(redis-cli -h $host -p $port -a "$REDIS_PASSWORD" CLUSTER INFO)
    else
        local cluster_info=$(redis-cli -h $host -p $port CLUSTER INFO)
    fi
    
    if echo "$cluster_info" | grep -q "cluster_state:ok"; then
        log_success "集群状态正常"
        
        # 显示集群节点信息
        log_info "集群节点信息:"
        if [[ -n "$REDIS_PASSWORD" ]]; then
            redis-cli -h $host -p $port -a "$REDIS_PASSWORD" CLUSTER NODES
        else
            redis-cli -h $host -p $port CLUSTER NODES
        fi
    else
        log_error "集群状态异常"
        echo "$cluster_info"
        exit 1
    fi
}

# 显示集群状态
show_cluster_status() {
    log_info "获取集群状态..."
    
    local first_node=$(echo ${CLUSTER_NODES[0]} | cut -d: -f1,2)
    local host=$(echo $first_node | cut -d: -f1)
    local port=$(echo $first_node | cut -d: -f2)
    
    echo "=== 集群信息 ==="
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli -h $host -p $port -a "$REDIS_PASSWORD" CLUSTER INFO
    else
        redis-cli -h $host -p $port CLUSTER INFO
    fi
    
    echo -e "\n=== 节点信息 ==="
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli -h $host -p $port -a "$REDIS_PASSWORD" CLUSTER NODES
    else
        redis-cli -h $host -p $port CLUSTER NODES
    fi
}

# 重置集群
reset_cluster() {
    log_warning "这将删除所有集群数据，确定要继续吗？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        clean_nodes
        log_success "集群重置完成"
    else
        log_info "操作已取消"
    fi
}

# 添加节点
add_node() {
    local new_node="$1"
    
    if [[ -z "$new_node" ]]; then
        log_error "请指定要添加的节点 (格式: host:port)"
        exit 1
    fi
    
    local first_node=$(echo ${CLUSTER_NODES[0]} | cut -d: -f1,2)
    
    log_info "添加节点 $new_node 到集群..."
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli --cluster add-node $new_node $first_node -a "$REDIS_PASSWORD"
    else
        redis-cli --cluster add-node $new_node $first_node
    fi
    
    log_success "节点添加完成"
}

# 移除节点
remove_node() {
    local node_id="$1"
    
    if [[ -z "$node_id" ]]; then
        log_error "请指定要移除的节点ID"
        exit 1
    fi
    
    local first_node=$(echo ${CLUSTER_NODES[0]} | cut -d: -f1,2)
    
    log_info "从集群移除节点 $node_id..."
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli --cluster del-node $first_node $node_id -a "$REDIS_PASSWORD"
    else
        redis-cli --cluster del-node $first_node $node_id
    fi
    
    log_success "节点移除完成"
}

# 测试集群
test_cluster() {
    log_info "测试集群功能..."
    
    local first_node=$(echo ${CLUSTER_NODES[0]} | cut -d: -f1,2)
    local host=$(echo $first_node | cut -d: -f1)
    local port=$(echo $first_node | cut -d: -f2)
    
    # 设置测试键
    local test_key="cluster_test_$(date +%s)"
    local test_value="test_value_$(date +%s)"
    
    if [[ -n "$REDIS_PASSWORD" ]]; then
        redis-cli -h $host -p $port -a "$REDIS_PASSWORD" -c SET "$test_key" "$test_value"
        local retrieved_value=$(redis-cli -h $host -p $port -a "$REDIS_PASSWORD" -c GET "$test_key")
    else
        redis-cli -h $host -p $port -c SET "$test_key" "$test_value"
        local retrieved_value=$(redis-cli -h $host -p $port -c GET "$test_key")
    fi
    
    if [[ "$retrieved_value" == "$test_value" ]]; then
        log_success "集群读写测试通过"
        
        # 清理测试键
        if [[ -n "$REDIS_PASSWORD" ]]; then
            redis-cli -h $host -p $port -a "$REDIS_PASSWORD" -c DEL "$test_key"
        else
            redis-cli -h $host -p $port -c DEL "$test_key"
        fi
    else
        log_error "集群读写测试失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "Redis集群初始化开始..."
    
    # 解析参数
    parse_args "$@"
    
    # 检查依赖
    check_redis_cli
    
    # 检查节点
    check_nodes
    
    # 清理节点
    clean_nodes
    
    # 创建集群
    create_cluster
    
    # 验证集群
    verify_cluster
    
    # 测试集群
    test_cluster
    
    log_success "Redis集群初始化完成！"
    
    echo -e "\n${GREEN}集群信息:${NC}"
    echo "节点数量: ${#CLUSTER_NODES[@]}"
    echo "副本数量: $REPLICAS"
    echo "节点列表:"
    for node in "${CLUSTER_NODES[@]}"; do
        echo "  - $node"
    done
    
    echo -e "\n${BLUE}使用方法:${NC}"
    echo "连接集群: redis-cli -c -h <host> -p <port>"
    if [[ -n "$REDIS_PASSWORD" ]]; then
        echo "使用密码: redis-cli -c -h <host> -p <port> -a '$REDIS_PASSWORD'"
    fi
    echo "查看状态: $0 --status"
}

# 执行主函数
main "$@"
