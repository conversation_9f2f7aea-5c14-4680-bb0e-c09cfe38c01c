# 向量数据库告警规则
# 定义各种监控指标的告警条件

groups:
  # 向量数据库可用性告警
  - name: vector_db_availability
    rules:
      # ChromaDB服务不可用
      - alert: ChromaDBDown
        expr: up{job="chromadb"} == 0
        for: 1m
        labels:
          severity: critical
          service: chromadb
        annotations:
          summary: "ChromaDB服务不可用"
          description: "ChromaDB服务已停止响应超过1分钟"

      # Weaviate服务不可用
      - alert: WeaviateDown
        expr: up{job="weaviate"} == 0
        for: 1m
        labels:
          severity: critical
          service: weaviate
        annotations:
          summary: "Weaviate服务不可用"
          description: "Weaviate服务已停止响应超过1分钟"

      # Qdrant服务不可用
      - alert: QdrantDown
        expr: up{job="qdrant"} == 0
        for: 1m
        labels:
          severity: critical
          service: qdrant
        annotations:
          summary: "Qdrant服务不可用"
          description: "Qdrant服务已停止响应超过1分钟"

      # Milvus服务不可用
      - alert: MilvusDown
        expr: up{job="milvus"} == 0
        for: 1m
        labels:
          severity: critical
          service: milvus
        annotations:
          summary: "Milvus服务不可用"
          description: "Milvus服务已停止响应超过1分钟"

  # 性能告警
  - name: vector_db_performance
    rules:
      # 查询响应时间过长
      - alert: HighQueryLatency
        expr: histogram_quantile(0.95, rate(vector_db_query_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "向量数据库查询延迟过高"
          description: "95%的查询响应时间超过2秒，当前值: {{ $value }}秒"

      # 查询错误率过高
      - alert: HighQueryErrorRate
        expr: rate(vector_db_query_errors_total[5m]) / rate(vector_db_query_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "向量数据库查询错误率过高"
          description: "查询错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # QPS过高
      - alert: HighQueryRate
        expr: rate(vector_db_query_total[5m]) > 1000
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "向量数据库查询QPS过高"
          description: "查询QPS超过1000，当前值: {{ $value }}"

  # 资源使用告警
  - name: vector_db_resources
    rules:
      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (vector_db_memory_used_bytes / vector_db_memory_total_bytes) > 0.85
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "向量数据库内存使用率过高"
          description: "内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # 磁盘使用率过高
      - alert: HighDiskUsage
        expr: (vector_db_disk_used_bytes / vector_db_disk_total_bytes) > 0.80
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "向量数据库磁盘使用率过高"
          description: "磁盘使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # CPU使用率过高
      - alert: HighCPUUsage
        expr: rate(vector_db_cpu_seconds_total[5m]) > 0.80
        for: 5m
        labels:
          severity: warning
          category: resources
        annotations:
          summary: "向量数据库CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value | humanizePercentage }}"

  # 数据完整性告警
  - name: vector_db_data_integrity
    rules:
      # 向量数量异常下降
      - alert: VectorCountDrop
        expr: decrease(vector_db_vectors_total[1h]) < -1000
        for: 5m
        labels:
          severity: critical
          category: data_integrity
        annotations:
          summary: "向量数量异常下降"
          description: "1小时内向量数量下降超过1000个，下降量: {{ $value }}"

      # 集合数量异常变化
      - alert: CollectionCountChange
        expr: abs(delta(vector_db_collections_total[1h])) > 5
        for: 5m
        labels:
          severity: warning
          category: data_integrity
        annotations:
          summary: "集合数量异常变化"
          description: "1小时内集合数量变化超过5个，变化量: {{ $value }}"

  # 连接和网络告警
  - name: vector_db_connectivity
    rules:
      # 连接数过多
      - alert: TooManyConnections
        expr: vector_db_active_connections > 100
        for: 5m
        labels:
          severity: warning
          category: connectivity
        annotations:
          summary: "向量数据库连接数过多"
          description: "活跃连接数超过100，当前值: {{ $value }}"

      # 网络错误率过高
      - alert: HighNetworkErrorRate
        expr: rate(vector_db_network_errors_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: connectivity
        annotations:
          summary: "向量数据库网络错误率过高"
          description: "网络错误率超过10次/秒，当前值: {{ $value }}"

  # 业务指标告警
  - name: vector_db_business
    rules:
      # 索引构建失败
      - alert: IndexBuildFailure
        expr: increase(vector_db_index_build_failures_total[1h]) > 0
        for: 1m
        labels:
          severity: critical
          category: business
        annotations:
          summary: "向量索引构建失败"
          description: "1小时内有索引构建失败，失败次数: {{ $value }}"

      # 数据同步延迟
      - alert: DataSyncLag
        expr: vector_db_sync_lag_seconds > 300
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "数据同步延迟过高"
          description: "数据同步延迟超过5分钟，当前延迟: {{ $value }}秒"

      # 备份失败
      - alert: BackupFailure
        expr: increase(vector_db_backup_failures_total[24h]) > 0
        for: 1m
        labels:
          severity: critical
          category: business
        annotations:
          summary: "向量数据库备份失败"
          description: "24小时内有备份失败，失败次数: {{ $value }}"

  # 容量规划告警
  - name: vector_db_capacity
    rules:
      # 预计磁盘空间不足
      - alert: DiskSpaceRunningOut
        expr: predict_linear(vector_db_disk_free_bytes[6h], 24*3600) < 0
        for: 1h
        labels:
          severity: warning
          category: capacity
        annotations:
          summary: "预计磁盘空间将在24小时内耗尽"
          description: "基于当前增长趋势，磁盘空间将在24小时内耗尽"

      # 向量增长率过快
      - alert: HighVectorGrowthRate
        expr: rate(vector_db_vectors_total[1h]) > 10000
        for: 30m
        labels:
          severity: warning
          category: capacity
        annotations:
          summary: "向量增长率过快"
          description: "向量增长率超过10000个/小时，当前值: {{ $value }}"

  # 安全告警
  - name: vector_db_security
    rules:
      # 异常访问模式
      - alert: UnusualAccessPattern
        expr: rate(vector_db_requests_total[5m]) > 2 * rate(vector_db_requests_total[1h] offset 1h)
        for: 10m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "检测到异常访问模式"
          description: "当前访问频率是历史平均值的2倍以上"

      # 认证失败率过高
      - alert: HighAuthFailureRate
        expr: rate(vector_db_auth_failures_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "认证失败率过高"
          description: "认证失败率超过10次/秒，可能存在暴力破解攻击"
