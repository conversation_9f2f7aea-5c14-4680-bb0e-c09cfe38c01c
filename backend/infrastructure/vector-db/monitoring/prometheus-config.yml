# Prometheus配置文件
# 用于监控向量数据库的性能指标

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 规则文件
rule_files:
  - "vector-db-rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # ChromaDB监控
  - job_name: 'chromadb'
    static_configs:
      - targets: ['chromadb:8000']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Weaviate监控
  - job_name: 'weaviate'
    static_configs:
      - targets: ['weaviate:8080']
    metrics_path: '/v1/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Qdrant监控
  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Milvus监控
  - job_name: 'milvus'
    static_configs:
      - targets: ['milvus-standalone:9091']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # 向量数据库导出器
  - job_name: 'vector-db-exporter'
    static_configs:
      - targets: ['vector-db-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: vector-db-exporter:9100

  # 应用服务监控
  - job_name: 'embedding-service'
    static_configs:
      - targets: ['embedding-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'retrieval-service'
    static_configs:
      - targets: ['retrieval-service:8002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
