# 向量数据库基础设施

RAG系统的向量数据库基础设施，支持多种向量数据库的部署、管理和监控。

## 📋 支持的向量数据库

### 🔵 ChromaDB
- **类型**: 开源向量数据库
- **特点**: 轻量级、易部署、Python原生
- **适用场景**: 开发测试、小规模部署
- **端口**: 8000

### 🟢 Weaviate
- **类型**: 开源向量数据库
- **特点**: GraphQL API、丰富的模块生态
- **适用场景**: 复杂查询、知识图谱
- **端口**: 8080

### 🟡 Qdrant
- **类型**: 开源向量数据库
- **特点**: 高性能、Rust编写、丰富的过滤功能
- **适用场景**: 高性能检索、大规模部署
- **端口**: 6333 (HTTP), 6334 (gRPC)

### 🔴 Milvus
- **类型**: 开源向量数据库
- **特点**: 高性能、分布式、企业级
- **适用场景**: 大规模生产环境
- **端口**: 19530 (gRPC), 9091 (HTTP)

### 🟣 Pinecone
- **类型**: 云端向量数据库
- **特点**: 托管服务、高可用、自动扩展
- **适用场景**: 云端部署、无运维需求

## 🚀 快速开始

### 环境要求
- Docker >= 20.10
- Docker Compose >= 2.0
- Python >= 3.9
- 至少 4GB 内存

### 启动所有服务
```bash
# 启动所有向量数据库
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 启动特定数据库
```bash
# 仅启动ChromaDB
docker-compose up -d chromadb

# 仅启动Weaviate
docker-compose up -d weaviate

# 仅启动Qdrant
docker-compose up -d qdrant

# 仅启动Milvus（包含依赖）
docker-compose up -d etcd minio milvus-standalone
```

### 初始化数据库
```bash
# 初始化所有数据库
python scripts/init-vector-db.py

# 初始化特定数据库
python scripts/init-vector-db.py --db-type chromadb weaviate

# 健康检查
python scripts/init-vector-db.py --health-check
```

## 🔧 配置管理

### 配置文件结构
```
config/
├── vector-db-config.yaml    # 主配置文件
├── chromadb/               # ChromaDB配置
├── weaviate/              # Weaviate配置
├── qdrant/                # Qdrant配置
└── milvus/                # Milvus配置
```

### 主要配置项
```yaml
# 全局配置
global:
  default_provider: "chromadb"
  embedding_dimensions:
    openai_ada_002: 1536
    openai_3_large: 3072

# ChromaDB配置
chromadb:
  host: "localhost"
  port: 8000
  auth:
    enabled: true
    token: "test-token"

# 性能配置
performance:
  batch_size: 100
  max_concurrent_requests: 10
  request_timeout: 30
```

### 环境变量
```bash
# 通用配置
VECTOR_DB_TYPE=chromadb
VECTOR_DB_HOST=localhost
VECTOR_DB_PORT=8000

# ChromaDB
CHROMA_SERVER_AUTH_CREDENTIALS=test-token

# Pinecone
PINECONE_API_KEY=your-api-key
PINECONE_ENVIRONMENT=us-west1-gcp

# Weaviate
WEAVIATE_URL=http://localhost:8080

# Qdrant
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Milvus
MILVUS_HOST=localhost
MILVUS_PORT=19530
```

## 🛠️ 管理工具

### 数据库管理器
```bash
# 列出所有集合
python scripts/vector-db-manager.py --db-type chromadb list

# 获取集合统计
python scripts/vector-db-manager.py --db-type chromadb stats documents

# 删除集合
python scripts/vector-db-manager.py --db-type chromadb delete test_collection --confirm

# 备份集合
python scripts/vector-db-manager.py --db-type chromadb backup documents --output backup.json

# 恢复集合
python scripts/vector-db-manager.py restore backup.json

# 性能监控
python scripts/vector-db-manager.py --db-type chromadb monitor --duration 300
```

### 批量操作
```bash
# 批量初始化
for db in chromadb weaviate qdrant; do
  python scripts/init-vector-db.py --db-type $db
done

# 批量健康检查
python scripts/init-vector-db.py --health-check --db-type chromadb weaviate qdrant milvus

# 批量备份
for collection in documents chunks; do
  python scripts/vector-db-manager.py --db-type chromadb backup $collection --output backup_${collection}.json
done
```

## 📊 监控和告警

### Prometheus监控
```bash
# 启动监控栈
docker-compose -f monitoring/docker-compose.yml up -d

# 访问Prometheus
open http://localhost:9090

# 访问Grafana
open http://localhost:3000
# 默认账号: admin/admin
```

### 监控指标
- **可用性**: 服务状态、响应时间
- **性能**: QPS、延迟、错误率
- **资源**: CPU、内存、磁盘使用
- **业务**: 向量数量、集合数量、索引状态

### 告警规则
- 服务不可用 (>1分钟)
- 查询延迟过高 (>2秒)
- 错误率过高 (>5%)
- 资源使用率过高 (>85%)
- 数据异常变化

### Grafana仪表板
- 向量数据库概览
- 性能监控
- 资源使用情况
- 告警状态

## 🔒 安全配置

### 认证授权
```yaml
# ChromaDB认证
chromadb:
  auth:
    enabled: true
    token: "secure-token"

# Weaviate认证
weaviate:
  auth:
    enabled: true
    api_key: "your-api-key"

# 网络安全
security:
  allowed_hosts:
    - "localhost"
    - "*.rag-system.local"
  enable_encryption: true
```

### 网络隔离
```yaml
# Docker网络配置
networks:
  rag-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 数据加密
- 传输加密: TLS/SSL
- 存储加密: 磁盘加密
- 备份加密: GPG加密

## 📈 性能优化

### 硬件建议
```yaml
# 最小配置
minimum:
  cpu: 2 cores
  memory: 4GB
  disk: 50GB SSD

# 推荐配置
recommended:
  cpu: 8 cores
  memory: 16GB
  disk: 200GB NVMe SSD

# 生产配置
production:
  cpu: 16+ cores
  memory: 64GB+
  disk: 1TB+ NVMe SSD
```

### 调优参数
```yaml
# ChromaDB优化
chromadb:
  hnsw_space: "cosine"
  ef_construction: 200
  m: 16

# Qdrant优化
qdrant:
  optimizers_config:
    memmap_threshold: 20000
  hnsw_config:
    ef_construct: 100
    m: 16

# Milvus优化
milvus:
  index_params:
    index_type: "IVF_FLAT"
    nlist: 1024
```

### 批处理优化
- 批量插入: 100-1000条/批
- 并发控制: 10-20个并发
- 连接池: 20个连接

## 🔄 备份和恢复

### 自动备份
```bash
# 设置定时备份
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup-script.sh

# 备份脚本示例
#!/bin/bash
DATE=$(date +%Y%m%d)
for db in chromadb qdrant; do
  for collection in documents chunks; do
    python scripts/vector-db-manager.py --db-type $db backup $collection \
      --output backups/${db}_${collection}_${DATE}.json
  done
done
```

### 灾难恢复
```bash
# 恢复所有数据
for backup in backups/*.json; do
  python scripts/vector-db-manager.py restore $backup
done

# 验证恢复结果
python scripts/init-vector-db.py --health-check
```

## 🚨 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8000
   
   # 检查Docker状态
   docker-compose ps
   
   # 查看详细日志
   docker-compose logs chromadb
   ```

2. **连接超时**
   ```bash
   # 检查网络连通性
   curl -f http://localhost:8000/api/v1/heartbeat
   
   # 检查防火墙
   sudo ufw status
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   
   # 调整内存限制
   docker-compose up -d --scale chromadb=1 --memory=2g
   ```

4. **数据丢失**
   ```bash
   # 检查数据卷
   docker volume ls
   
   # 恢复备份
   python scripts/vector-db-manager.py restore latest_backup.json
   ```

### 日志分析
```bash
# 实时日志
docker-compose logs -f --tail=100

# 错误日志
docker-compose logs | grep ERROR

# 性能日志
docker-compose logs | grep -E "(slow|timeout|latency)"
```

### 性能诊断
```bash
# 监控资源使用
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# 网络诊断
docker exec chromadb netstat -tlnp

# 磁盘IO
docker exec chromadb iostat -x 1
```

## 📚 最佳实践

### 部署建议
1. **环境隔离**: 开发、测试、生产环境分离
2. **资源规划**: 根据数据量和QPS规划资源
3. **监控告警**: 完善的监控和告警体系
4. **备份策略**: 定期备份和恢复测试

### 运维建议
1. **版本管理**: 统一版本管理和升级策略
2. **配置管理**: 集中化配置管理
3. **日志管理**: 结构化日志和集中收集
4. **安全管理**: 定期安全审计和更新

### 开发建议
1. **连接池**: 使用连接池管理数据库连接
2. **批量操作**: 优先使用批量操作提升性能
3. **错误处理**: 完善的错误处理和重试机制
4. **测试覆盖**: 充分的单元测试和集成测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
