# 向量数据库配置文件
# 支持多种向量数据库的统一配置

# 全局配置
global:
  # 默认向量数据库类型
  default_provider: "chromadb"
  
  # 向量维度配置
  embedding_dimensions:
    openai_ada_002: 1536
    openai_3_small: 1536
    openai_3_large: 3072
    sentence_transformers_384: 384
    sentence_transformers_768: 768
  
  # 默认集合配置
  default_collection_config:
    distance_metric: "cosine"
    index_type: "hnsw"
    ef_construction: 200
    m: 16

# ChromaDB配置
chromadb:
  # 连接配置
  host: "localhost"
  port: 8000
  ssl: false
  
  # 认证配置
  auth:
    enabled: true
    token: "test-token"
  
  # 客户端配置
  client:
    timeout: 30
    max_retries: 3
    retry_delay: 1
  
  # 集合配置
  collections:
    default:
      name: "default_collection"
      metadata:
        description: "默认文档集合"
        hnsw_space: "cosine"
    
    documents:
      name: "documents"
      metadata:
        description: "文档向量集合"
        hnsw_space: "cosine"
    
    chunks:
      name: "chunks"
      metadata:
        description: "文档块向量集合"
        hnsw_space: "cosine"

# Weaviate配置
weaviate:
  # 连接配置
  url: "http://localhost:8080"
  timeout: 30
  
  # 认证配置
  auth:
    enabled: false
    api_key: ""
  
  # 模式配置
  schema:
    classes:
      - name: "Document"
        description: "文档类"
        vectorizer: "none"
        properties:
          - name: "content"
            dataType: ["text"]
            description: "文档内容"
          - name: "title"
            dataType: ["string"]
            description: "文档标题"
          - name: "document_id"
            dataType: ["string"]
            description: "文档ID"
          - name: "metadata"
            dataType: ["object"]
            description: "元数据"
        
      - name: "Chunk"
        description: "文档块类"
        vectorizer: "none"
        properties:
          - name: "content"
            dataType: ["text"]
            description: "块内容"
          - name: "chunk_id"
            dataType: ["string"]
            description: "块ID"
          - name: "document_id"
            dataType: ["string"]
            description: "所属文档ID"
          - name: "chunk_index"
            dataType: ["int"]
            description: "块索引"
          - name: "metadata"
            dataType: ["object"]
            description: "元数据"

# Qdrant配置
qdrant:
  # 连接配置
  host: "localhost"
  port: 6333
  grpc_port: 6334
  prefer_grpc: true
  timeout: 30
  
  # 认证配置
  auth:
    enabled: false
    api_key: ""
  
  # 集合配置
  collections:
    documents:
      name: "documents"
      vector_config:
        size: 1536
        distance: "Cosine"
      optimizers_config:
        default_segment_number: 2
        memmap_threshold: 20000
      hnsw_config:
        m: 16
        ef_construct: 100
        full_scan_threshold: 10000
    
    chunks:
      name: "chunks"
      vector_config:
        size: 1536
        distance: "Cosine"
      optimizers_config:
        default_segment_number: 2
        memmap_threshold: 20000

# Milvus配置
milvus:
  # 连接配置
  host: "localhost"
  port: 19530
  timeout: 30
  
  # 认证配置
  auth:
    enabled: false
    username: ""
    password: ""
  
  # 数据库配置
  database: "default"
  
  # 集合配置
  collections:
    documents:
      name: "documents"
      description: "文档向量集合"
      fields:
        - name: "id"
          dtype: "VarChar"
          max_length: 100
          is_primary: true
        - name: "document_id"
          dtype: "VarChar"
          max_length: 100
        - name: "content"
          dtype: "VarChar"
          max_length: 10000
        - name: "embedding"
          dtype: "FloatVector"
          dim: 1536
        - name: "metadata"
          dtype: "JSON"
      
      index_params:
        - field_name: "embedding"
          index_type: "IVF_FLAT"
          metric_type: "COSINE"
          params:
            nlist: 1024
    
    chunks:
      name: "chunks"
      description: "文档块向量集合"
      fields:
        - name: "id"
          dtype: "VarChar"
          max_length: 100
          is_primary: true
        - name: "chunk_id"
          dtype: "VarChar"
          max_length: 100
        - name: "document_id"
          dtype: "VarChar"
          max_length: 100
        - name: "content"
          dtype: "VarChar"
          max_length: 5000
        - name: "embedding"
          dtype: "FloatVector"
          dim: 1536
        - name: "metadata"
          dtype: "JSON"

# Pinecone配置（云服务）
pinecone:
  # 认证配置
  api_key: "${PINECONE_API_KEY}"
  environment: "us-west1-gcp"
  
  # 索引配置
  indexes:
    documents:
      name: "rag-documents"
      dimension: 1536
      metric: "cosine"
      pod_type: "p1.x1"
      replicas: 1
      shards: 1
      
    chunks:
      name: "rag-chunks"
      dimension: 1536
      metric: "cosine"
      pod_type: "p1.x1"
      replicas: 1
      shards: 1

# 性能配置
performance:
  # 批处理配置
  batch_size: 100
  max_batch_size: 1000
  
  # 并发配置
  max_concurrent_requests: 10
  request_timeout: 30
  
  # 连接池配置
  connection_pool_size: 20
  max_idle_connections: 5
  
  # 重试配置
  max_retries: 3
  retry_delay: 1
  backoff_factor: 2

# 监控配置
monitoring:
  # 指标收集
  enable_metrics: true
  metrics_port: 9090
  
  # 健康检查
  health_check_interval: 30
  health_check_timeout: 10
  
  # 日志配置
  log_level: "INFO"
  log_queries: true
  log_slow_queries: true
  slow_query_threshold: 1000  # 毫秒

# 安全配置
security:
  # 访问控制
  enable_auth: true
  auth_token_ttl: 3600
  
  # 数据加密
  enable_encryption: false
  encryption_key: "${VECTOR_DB_ENCRYPTION_KEY}"
  
  # 网络安全
  allowed_hosts:
    - "localhost"
    - "127.0.0.1"
    - "*.rag-system.local"
  
  # API限制
  rate_limit:
    enabled: true
    requests_per_minute: 1000
    burst_size: 100
