#!/usr/bin/env python3
"""
向量数据库管理工具
提供向量数据库的管理、监控、备份等功能
"""

import os
import sys
import json
import yaml
import asyncio
import argparse
from datetime import datetime
from typing import Dict, Any, List, Optional
from loguru import logger

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))


class VectorDBManager:
    """向量数据库管理器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    async def list_collections(self, db_type: str) -> List[Dict[str, Any]]:
        """列出所有集合"""
        try:
            collections = []
            
            if db_type == 'chromadb':
                import chromadb
                config = self.config['chromadb']
                client = chromadb.HttpClient(host=config['host'], port=config['port'])
                
                chroma_collections = client.list_collections()
                for collection in chroma_collections:
                    info = {
                        'name': collection.name,
                        'count': collection.count(),
                        'metadata': collection.metadata
                    }
                    collections.append(info)
                    
            elif db_type == 'weaviate':
                import weaviate
                config = self.config['weaviate']
                client = weaviate.Client(url=config['url'])
                
                schema = client.schema.get()
                for class_obj in schema.get('classes', []):
                    # 获取对象数量
                    result = client.query.aggregate(class_obj['class']).with_meta_count().do()
                    count = result['data']['Aggregate'][class_obj['class']][0]['meta']['count']
                    
                    info = {
                        'name': class_obj['class'],
                        'count': count,
                        'metadata': class_obj
                    }
                    collections.append(info)
                    
            elif db_type == 'qdrant':
                from qdrant_client import QdrantClient
                config = self.config['qdrant']
                client = QdrantClient(host=config['host'], port=config['port'])
                
                qdrant_collections = client.get_collections()
                for collection in qdrant_collections.collections:
                    collection_info = client.get_collection(collection.name)
                    info = {
                        'name': collection.name,
                        'count': collection_info.points_count,
                        'metadata': {
                            'status': collection_info.status,
                            'vectors_count': collection_info.vectors_count,
                            'config': collection_info.config
                        }
                    }
                    collections.append(info)
                    
            elif db_type == 'milvus':
                from pymilvus import connections, utility, Collection
                config = self.config['milvus']
                connections.connect(host=config['host'], port=config['port'])
                
                collection_names = utility.list_collections()
                for name in collection_names:
                    collection = Collection(name)
                    collection.load()
                    
                    info = {
                        'name': name,
                        'count': collection.num_entities,
                        'metadata': {
                            'schema': collection.schema,
                            'indexes': collection.indexes
                        }
                    }
                    collections.append(info)
                    
            elif db_type == 'pinecone':
                import pinecone
                config = self.config['pinecone']
                pinecone.init(api_key=config['api_key'], environment=config['environment'])
                
                index_names = pinecone.list_indexes()
                for name in index_names:
                    index = pinecone.Index(name)
                    stats = index.describe_index_stats()
                    
                    info = {
                        'name': name,
                        'count': stats.get('total_vector_count', 0),
                        'metadata': stats
                    }
                    collections.append(info)
            
            return collections
            
        except Exception as e:
            logger.error(f"列出集合失败 ({db_type}): {e}")
            return []
    
    async def get_collection_stats(self, db_type: str, collection_name: str) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            stats = {}
            
            if db_type == 'chromadb':
                import chromadb
                config = self.config['chromadb']
                client = chromadb.HttpClient(host=config['host'], port=config['port'])
                
                collection = client.get_collection(collection_name)
                stats = {
                    'name': collection_name,
                    'count': collection.count(),
                    'metadata': collection.metadata
                }
                
            elif db_type == 'weaviate':
                import weaviate
                config = self.config['weaviate']
                client = weaviate.Client(url=config['url'])
                
                # 获取类信息
                schema = client.schema.get(collection_name)
                
                # 获取对象数量
                result = client.query.aggregate(collection_name).with_meta_count().do()
                count = result['data']['Aggregate'][collection_name][0]['meta']['count']
                
                stats = {
                    'name': collection_name,
                    'count': count,
                    'schema': schema,
                    'properties': schema.get('properties', [])
                }
                
            elif db_type == 'qdrant':
                from qdrant_client import QdrantClient
                config = self.config['qdrant']
                client = QdrantClient(host=config['host'], port=config['port'])
                
                collection_info = client.get_collection(collection_name)
                stats = {
                    'name': collection_name,
                    'count': collection_info.points_count,
                    'vectors_count': collection_info.vectors_count,
                    'status': collection_info.status,
                    'config': collection_info.config
                }
                
            elif db_type == 'milvus':
                from pymilvus import connections, Collection
                config = self.config['milvus']
                connections.connect(host=config['host'], port=config['port'])
                
                collection = Collection(collection_name)
                collection.load()
                
                stats = {
                    'name': collection_name,
                    'count': collection.num_entities,
                    'schema': collection.schema,
                    'indexes': collection.indexes
                }
                
            elif db_type == 'pinecone':
                import pinecone
                config = self.config['pinecone']
                pinecone.init(api_key=config['api_key'], environment=config['environment'])
                
                index = pinecone.Index(collection_name)
                index_stats = index.describe_index_stats()
                
                stats = {
                    'name': collection_name,
                    'count': index_stats.get('total_vector_count', 0),
                    'dimension': index_stats.get('dimension', 0),
                    'index_fullness': index_stats.get('index_fullness', 0),
                    'namespaces': index_stats.get('namespaces', {})
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取集合统计失败 ({db_type}, {collection_name}): {e}")
            return {}
    
    async def delete_collection(self, db_type: str, collection_name: str) -> bool:
        """删除集合"""
        try:
            if db_type == 'chromadb':
                import chromadb
                config = self.config['chromadb']
                client = chromadb.HttpClient(host=config['host'], port=config['port'])
                client.delete_collection(collection_name)
                
            elif db_type == 'weaviate':
                import weaviate
                config = self.config['weaviate']
                client = weaviate.Client(url=config['url'])
                client.schema.delete_class(collection_name)
                
            elif db_type == 'qdrant':
                from qdrant_client import QdrantClient
                config = self.config['qdrant']
                client = QdrantClient(host=config['host'], port=config['port'])
                client.delete_collection(collection_name)
                
            elif db_type == 'milvus':
                from pymilvus import connections, utility
                config = self.config['milvus']
                connections.connect(host=config['host'], port=config['port'])
                utility.drop_collection(collection_name)
                
            elif db_type == 'pinecone':
                import pinecone
                config = self.config['pinecone']
                pinecone.init(api_key=config['api_key'], environment=config['environment'])
                pinecone.delete_index(collection_name)
            
            logger.info(f"删除集合成功: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除集合失败 ({db_type}, {collection_name}): {e}")
            return False
    
    async def backup_collection(self, db_type: str, collection_name: str, backup_path: str) -> bool:
        """备份集合"""
        try:
            backup_data = {
                'metadata': {
                    'db_type': db_type,
                    'collection_name': collection_name,
                    'backup_time': datetime.now().isoformat(),
                    'version': '1.0'
                },
                'data': []
            }
            
            if db_type == 'chromadb':
                import chromadb
                config = self.config['chromadb']
                client = chromadb.HttpClient(host=config['host'], port=config['port'])
                
                collection = client.get_collection(collection_name)
                results = collection.get(include=['embeddings', 'metadatas', 'documents'])
                
                backup_data['data'] = {
                    'ids': results['ids'],
                    'embeddings': results['embeddings'],
                    'metadatas': results['metadatas'],
                    'documents': results['documents']
                }
                
            elif db_type == 'qdrant':
                from qdrant_client import QdrantClient
                config = self.config['qdrant']
                client = QdrantClient(host=config['host'], port=config['port'])
                
                # 获取所有点
                points = client.scroll(collection_name, limit=10000)[0]
                
                backup_data['data'] = [
                    {
                        'id': point.id,
                        'vector': point.vector,
                        'payload': point.payload
                    }
                    for point in points
                ]
            
            # 保存备份文件
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"备份集合成功: {collection_name} -> {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"备份集合失败 ({db_type}, {collection_name}): {e}")
            return False
    
    async def restore_collection(self, backup_path: str) -> bool:
        """恢复集合"""
        try:
            # 读取备份文件
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            metadata = backup_data['metadata']
            db_type = metadata['db_type']
            collection_name = metadata['collection_name']
            data = backup_data['data']
            
            if db_type == 'chromadb':
                import chromadb
                config = self.config['chromadb']
                client = chromadb.HttpClient(host=config['host'], port=config['port'])
                
                # 创建或获取集合
                try:
                    collection = client.get_collection(collection_name)
                except:
                    collection = client.create_collection(collection_name)
                
                # 恢复数据
                if data['ids']:
                    collection.add(
                        ids=data['ids'],
                        embeddings=data['embeddings'],
                        metadatas=data['metadatas'],
                        documents=data['documents']
                    )
                    
            elif db_type == 'qdrant':
                from qdrant_client import QdrantClient
                from qdrant_client.models import PointStruct
                config = self.config['qdrant']
                client = QdrantClient(host=config['host'], port=config['port'])
                
                # 恢复数据
                points = [
                    PointStruct(
                        id=item['id'],
                        vector=item['vector'],
                        payload=item['payload']
                    )
                    for item in data
                ]
                
                if points:
                    client.upsert(collection_name, points)
            
            logger.info(f"恢复集合成功: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"恢复集合失败: {e}")
            return False
    
    async def monitor_performance(self, db_type: str, duration: int = 60) -> Dict[str, Any]:
        """监控性能"""
        try:
            start_time = datetime.now()
            metrics = {
                'db_type': db_type,
                'start_time': start_time.isoformat(),
                'duration': duration,
                'collections': [],
                'total_vectors': 0,
                'avg_response_time': 0
            }
            
            # 获取所有集合
            collections = await self.list_collections(db_type)
            
            total_vectors = 0
            response_times = []
            
            for collection in collections:
                collection_start = datetime.now()
                
                # 获取集合统计
                stats = await self.get_collection_stats(db_type, collection['name'])
                
                collection_end = datetime.now()
                response_time = (collection_end - collection_start).total_seconds() * 1000
                response_times.append(response_time)
                
                total_vectors += collection.get('count', 0)
                
                metrics['collections'].append({
                    'name': collection['name'],
                    'count': collection.get('count', 0),
                    'response_time_ms': response_time
                })
            
            metrics['total_vectors'] = total_vectors
            metrics['avg_response_time'] = sum(response_times) / len(response_times) if response_times else 0
            metrics['end_time'] = datetime.now().isoformat()
            
            return metrics
            
        except Exception as e:
            logger.error(f"性能监控失败 ({db_type}): {e}")
            return {}


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='向量数据库管理工具')
    parser.add_argument('--config', '-c', default='../config/vector-db-config.yaml', help='配置文件路径')
    parser.add_argument('--db-type', '-t', required=True, help='数据库类型')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出集合
    subparsers.add_parser('list', help='列出所有集合')
    
    # 集合统计
    stats_parser = subparsers.add_parser('stats', help='获取集合统计')
    stats_parser.add_argument('collection', help='集合名称')
    
    # 删除集合
    delete_parser = subparsers.add_parser('delete', help='删除集合')
    delete_parser.add_argument('collection', help='集合名称')
    delete_parser.add_argument('--confirm', action='store_true', help='确认删除')
    
    # 备份集合
    backup_parser = subparsers.add_parser('backup', help='备份集合')
    backup_parser.add_argument('collection', help='集合名称')
    backup_parser.add_argument('--output', '-o', required=True, help='备份文件路径')
    
    # 恢复集合
    restore_parser = subparsers.add_parser('restore', help='恢复集合')
    restore_parser.add_argument('backup_file', help='备份文件路径')
    
    # 性能监控
    monitor_parser = subparsers.add_parser('monitor', help='性能监控')
    monitor_parser.add_argument('--duration', '-d', type=int, default=60, help='监控时长（秒）')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
    
    # 创建管理器
    manager = VectorDBManager(args.config)
    
    try:
        if args.command == 'list':
            collections = await manager.list_collections(args.db_type)
            
            print(f"\n=== {args.db_type.upper()} 集合列表 ===")
            for collection in collections:
                print(f"名称: {collection['name']}")
                print(f"数量: {collection['count']}")
                print(f"元数据: {collection.get('metadata', {})}")
                print("-" * 50)
        
        elif args.command == 'stats':
            stats = await manager.get_collection_stats(args.db_type, args.collection)
            
            print(f"\n=== {args.collection} 统计信息 ===")
            print(json.dumps(stats, indent=2, ensure_ascii=False))
        
        elif args.command == 'delete':
            if not args.confirm:
                print("⚠️  删除操作需要确认，请添加 --confirm 参数")
                return
            
            success = await manager.delete_collection(args.db_type, args.collection)
            if success:
                print(f"✅ 集合 {args.collection} 删除成功")
            else:
                print(f"❌ 集合 {args.collection} 删除失败")
        
        elif args.command == 'backup':
            success = await manager.backup_collection(args.db_type, args.collection, args.output)
            if success:
                print(f"✅ 集合 {args.collection} 备份成功: {args.output}")
            else:
                print(f"❌ 集合 {args.collection} 备份失败")
        
        elif args.command == 'restore':
            success = await manager.restore_collection(args.backup_file)
            if success:
                print(f"✅ 集合恢复成功: {args.backup_file}")
            else:
                print(f"❌ 集合恢复失败: {args.backup_file}")
        
        elif args.command == 'monitor':
            print(f"🔍 开始监控 {args.db_type} 性能...")
            metrics = await manager.monitor_performance(args.db_type, args.duration)
            
            print(f"\n=== {args.db_type.upper()} 性能监控结果 ===")
            print(json.dumps(metrics, indent=2, ensure_ascii=False))
    
    except Exception as e:
        logger.error(f"命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
