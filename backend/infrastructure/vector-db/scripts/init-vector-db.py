#!/usr/bin/env python3
"""
向量数据库初始化脚本
支持多种向量数据库的自动初始化和配置
"""

import os
import sys
import yaml
import asyncio
import argparse
from typing import Dict, Any, List
from loguru import logger

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from services.embedding_service.app.vector_db import VectorDBFactory


class VectorDBInitializer:
    """向量数据库初始化器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        self.db_type = self.config['global']['default_provider']
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    async def init_chromadb(self) -> bool:
        """初始化ChromaDB"""
        try:
            logger.info("开始初始化ChromaDB...")
            
            import chromadb
            from chromadb.config import Settings as ChromaSettings
            
            config = self.config['chromadb']
            
            # 创建客户端
            if config.get('host') and config.get('port'):
                client = chromadb.HttpClient(
                    host=config['host'],
                    port=config['port'],
                    settings=ChromaSettings(allow_reset=True)
                )
            else:
                client = chromadb.PersistentClient(
                    path="./chroma_db",
                    settings=ChromaSettings(allow_reset=True)
                )
            
            # 创建集合
            collections_config = config.get('collections', {})
            for collection_key, collection_config in collections_config.items():
                collection_name = collection_config['name']
                metadata = collection_config.get('metadata', {})
                
                try:
                    # 检查集合是否存在
                    existing_collection = client.get_collection(collection_name)
                    logger.info(f"集合 {collection_name} 已存在")
                except:
                    # 创建新集合
                    collection = client.create_collection(
                        name=collection_name,
                        metadata=metadata
                    )
                    logger.info(f"创建集合成功: {collection_name}")
            
            logger.info("ChromaDB初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            return False
    
    async def init_weaviate(self) -> bool:
        """初始化Weaviate"""
        try:
            logger.info("开始初始化Weaviate...")
            
            import weaviate
            
            config = self.config['weaviate']
            
            # 创建客户端
            client = weaviate.Client(
                url=config['url'],
                timeout_config=(5, 15)
            )
            
            # 检查连接
            if not client.is_ready():
                logger.error("Weaviate服务未就绪")
                return False
            
            # 创建模式
            schema_config = config.get('schema', {})
            classes = schema_config.get('classes', [])
            
            for class_config in classes:
                class_name = class_config['name']
                
                # 检查类是否存在
                if client.schema.exists(class_name):
                    logger.info(f"类 {class_name} 已存在")
                    continue
                
                # 创建类
                client.schema.create_class(class_config)
                logger.info(f"创建类成功: {class_name}")
            
            logger.info("Weaviate初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"Weaviate初始化失败: {e}")
            return False
    
    async def init_qdrant(self) -> bool:
        """初始化Qdrant"""
        try:
            logger.info("开始初始化Qdrant...")
            
            from qdrant_client import QdrantClient
            from qdrant_client.models import Distance, VectorParams, CreateCollection
            
            config = self.config['qdrant']
            
            # 创建客户端
            client = QdrantClient(
                host=config['host'],
                port=config['port'],
                timeout=config.get('timeout', 30)
            )
            
            # 创建集合
            collections_config = config.get('collections', {})
            for collection_key, collection_config in collections_config.items():
                collection_name = collection_config['name']
                vector_config = collection_config['vector_config']
                
                # 检查集合是否存在
                try:
                    collection_info = client.get_collection(collection_name)
                    logger.info(f"集合 {collection_name} 已存在")
                    continue
                except:
                    pass
                
                # 创建集合
                client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=vector_config['size'],
                        distance=getattr(Distance, vector_config['distance'].upper())
                    )
                )
                logger.info(f"创建集合成功: {collection_name}")
            
            logger.info("Qdrant初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"Qdrant初始化失败: {e}")
            return False
    
    async def init_milvus(self) -> bool:
        """初始化Milvus"""
        try:
            logger.info("开始初始化Milvus...")
            
            from pymilvus import connections, Collection, CollectionSchema, FieldSchema, DataType, utility
            
            config = self.config['milvus']
            
            # 连接Milvus
            connections.connect(
                alias="default",
                host=config['host'],
                port=config['port']
            )
            
            # 创建集合
            collections_config = config.get('collections', {})
            for collection_key, collection_config in collections_config.items():
                collection_name = collection_config['name']
                
                # 检查集合是否存在
                if utility.has_collection(collection_name):
                    logger.info(f"集合 {collection_name} 已存在")
                    continue
                
                # 创建字段
                fields = []
                for field_config in collection_config['fields']:
                    field = FieldSchema(
                        name=field_config['name'],
                        dtype=getattr(DataType, field_config['dtype']),
                        is_primary=field_config.get('is_primary', False),
                        max_length=field_config.get('max_length'),
                        dim=field_config.get('dim')
                    )
                    fields.append(field)
                
                # 创建集合模式
                schema = CollectionSchema(
                    fields=fields,
                    description=collection_config.get('description', '')
                )
                
                # 创建集合
                collection = Collection(
                    name=collection_name,
                    schema=schema
                )
                
                # 创建索引
                index_params = collection_config.get('index_params', [])
                for index_param in index_params:
                    collection.create_index(
                        field_name=index_param['field_name'],
                        index_params={
                            "index_type": index_param['index_type'],
                            "metric_type": index_param['metric_type'],
                            "params": index_param.get('params', {})
                        }
                    )
                
                logger.info(f"创建集合成功: {collection_name}")
            
            logger.info("Milvus初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"Milvus初始化失败: {e}")
            return False
    
    async def init_pinecone(self) -> bool:
        """初始化Pinecone"""
        try:
            logger.info("开始初始化Pinecone...")
            
            import pinecone
            
            config = self.config['pinecone']
            
            # 初始化Pinecone
            pinecone.init(
                api_key=config['api_key'],
                environment=config['environment']
            )
            
            # 创建索引
            indexes_config = config.get('indexes', {})
            for index_key, index_config in indexes_config.items():
                index_name = index_config['name']
                
                # 检查索引是否存在
                if index_name in pinecone.list_indexes():
                    logger.info(f"索引 {index_name} 已存在")
                    continue
                
                # 创建索引
                pinecone.create_index(
                    name=index_name,
                    dimension=index_config['dimension'],
                    metric=index_config['metric'],
                    pod_type=index_config.get('pod_type', 'p1.x1'),
                    replicas=index_config.get('replicas', 1),
                    shards=index_config.get('shards', 1)
                )
                logger.info(f"创建索引成功: {index_name}")
            
            logger.info("Pinecone初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"Pinecone初始化失败: {e}")
            return False
    
    async def initialize(self, db_types: List[str] = None) -> bool:
        """初始化指定的向量数据库"""
        if db_types is None:
            db_types = [self.db_type]
        
        success_count = 0
        total_count = len(db_types)
        
        for db_type in db_types:
            logger.info(f"初始化 {db_type}...")
            
            try:
                if db_type == 'chromadb':
                    success = await self.init_chromadb()
                elif db_type == 'weaviate':
                    success = await self.init_weaviate()
                elif db_type == 'qdrant':
                    success = await self.init_qdrant()
                elif db_type == 'milvus':
                    success = await self.init_milvus()
                elif db_type == 'pinecone':
                    success = await self.init_pinecone()
                else:
                    logger.error(f"不支持的数据库类型: {db_type}")
                    success = False
                
                if success:
                    success_count += 1
                    logger.info(f"{db_type} 初始化成功")
                else:
                    logger.error(f"{db_type} 初始化失败")
                    
            except Exception as e:
                logger.error(f"{db_type} 初始化异常: {e}")
        
        logger.info(f"初始化完成: {success_count}/{total_count} 成功")
        return success_count == total_count
    
    async def health_check(self, db_types: List[str] = None) -> Dict[str, bool]:
        """健康检查"""
        if db_types is None:
            db_types = [self.db_type]
        
        results = {}
        
        for db_type in db_types:
            try:
                if db_type == 'chromadb':
                    import chromadb
                    config = self.config['chromadb']
                    client = chromadb.HttpClient(host=config['host'], port=config['port'])
                    client.heartbeat()
                    results[db_type] = True
                    
                elif db_type == 'weaviate':
                    import weaviate
                    config = self.config['weaviate']
                    client = weaviate.Client(url=config['url'])
                    results[db_type] = client.is_ready()
                    
                elif db_type == 'qdrant':
                    from qdrant_client import QdrantClient
                    config = self.config['qdrant']
                    client = QdrantClient(host=config['host'], port=config['port'])
                    client.get_collections()
                    results[db_type] = True
                    
                elif db_type == 'milvus':
                    from pymilvus import connections, utility
                    config = self.config['milvus']
                    connections.connect(host=config['host'], port=config['port'])
                    utility.get_server_version()
                    results[db_type] = True
                    
                elif db_type == 'pinecone':
                    import pinecone
                    config = self.config['pinecone']
                    pinecone.init(api_key=config['api_key'], environment=config['environment'])
                    pinecone.list_indexes()
                    results[db_type] = True
                    
                else:
                    results[db_type] = False
                    
            except Exception as e:
                logger.error(f"{db_type} 健康检查失败: {e}")
                results[db_type] = False
        
        return results


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='向量数据库初始化工具')
    parser.add_argument('--config', '-c', default='../config/vector-db-config.yaml', help='配置文件路径')
    parser.add_argument('--db-type', '-t', nargs='+', help='数据库类型 (chromadb, weaviate, qdrant, milvus, pinecone)')
    parser.add_argument('--health-check', action='store_true', help='仅执行健康检查')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = "DEBUG" if args.verbose else "INFO"
    logger.remove()
    logger.add(sys.stdout, level=log_level, format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
    
    # 初始化器
    initializer = VectorDBInitializer(args.config)
    
    if args.health_check:
        # 健康检查
        results = await initializer.health_check(args.db_type)
        
        print("\n=== 健康检查结果 ===")
        for db_type, status in results.items():
            status_text = "✅ 健康" if status else "❌ 异常"
            print(f"{db_type}: {status_text}")
        
        # 返回状态码
        all_healthy = all(results.values())
        sys.exit(0 if all_healthy else 1)
    
    else:
        # 初始化
        success = await initializer.initialize(args.db_type)
        
        if success:
            print("\n✅ 所有向量数据库初始化成功")
            sys.exit(0)
        else:
            print("\n❌ 部分向量数据库初始化失败")
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
