# 对象存储基础设施配置
# 包含MinIO、AWS S3兼容存储等

version: '3.8'

services:
  # MinIO对象存储服务
  minio:
    image: minio/minio:RELEASE.2024-01-16T16-07-38Z
    container_name: rag-minio
    ports:
      - "9000:9000"    # API端口
      - "9001:9001"    # Console端口
    volumes:
      - minio_data:/data
      - ./config/minio:/etc/minio
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin123}
      - MINIO_DOMAIN=localhost
      - MINIO_SERVER_URL=http://localhost:9000
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
    command: server /data --console-address ":9001"
    networks:
      - rag-storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # MinIO客户端工具
  minio-client:
    image: minio/mc:RELEASE.2024-01-13T08-44-48Z
    container_name: rag-minio-client
    depends_on:
      - minio
    volumes:
      - ./scripts:/scripts
    environment:
      - MINIO_ENDPOINT=http://minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_SECRET_KEY=${MINIO_ROOT_PASSWORD:-minioadmin123}
    networks:
      - rag-storage
    entrypoint: /bin/sh
    command: -c "sleep 10 && /scripts/init-buckets.sh"

  # Nginx文件服务器（用于静态资源）
  nginx-fileserver:
    image: nginx:1.25-alpine
    container_name: rag-nginx-fileserver
    ports:
      - "8080:80"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro
      - static_files:/usr/share/nginx/html/static
      - uploads:/usr/share/nginx/html/uploads
    networks:
      - rag-storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis（用于缓存文件元数据）
  redis-storage:
    image: redis:7.2-alpine
    container_name: rag-redis-storage
    ports:
      - "6380:6379"
    volumes:
      - redis_storage_data:/data
      - ./config/redis-storage.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - rag-storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 文件处理服务（图片压缩、格式转换等）
  imageproxy:
    image: willnorris/imageproxy:latest
    container_name: rag-imageproxy
    ports:
      - "8081:8080"
    environment:
      - IMAGEPROXY_CACHE=memory:100mb
      - IMAGEPROXY_CACHE_TTL=24h
    command: -addr 0.0.0.0:8080 -cache memory:100mb -verbose
    networks:
      - rag-storage
    restart: unless-stopped

  # 文件病毒扫描服务
  clamav:
    image: clamav/clamav:stable
    container_name: rag-clamav
    ports:
      - "3310:3310"
    volumes:
      - clamav_data:/var/lib/clamav
    environment:
      - CLAMAV_NO_FRESHCLAMD=false
      - CLAMAV_NO_CLAMD=false
    networks:
      - rag-storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/usr/local/bin/clamd", "--ping"]
      interval: 60s
      timeout: 30s
      retries: 3

  # 文件预览服务
  onlyoffice:
    image: onlyoffice/documentserver:7.5
    container_name: rag-onlyoffice
    ports:
      - "8082:80"
    volumes:
      - onlyoffice_data:/var/www/onlyoffice/Data
      - onlyoffice_logs:/var/log/onlyoffice
    environment:
      - JWT_ENABLED=false
      - JWT_SECRET=your-jwt-secret
    networks:
      - rag-storage
    restart: unless-stopped

  # 文件转换服务
  gotenberg:
    image: gotenberg/gotenberg:7
    container_name: rag-gotenberg
    ports:
      - "3000:3000"
    command:
      - "gotenberg"
      - "--chromium-disable-web-security"
      - "--chromium-allow-list=file:///*"
    networks:
      - rag-storage
    restart: unless-stopped

  # 文件同步服务（可选）
  rclone:
    image: rclone/rclone:latest
    container_name: rag-rclone
    volumes:
      - ./config/rclone.conf:/config/rclone/rclone.conf:ro
      - rclone_data:/data
      - minio_data:/minio:ro
    environment:
      - RCLONE_CONFIG=/config/rclone/rclone.conf
    networks:
      - rag-storage
    restart: unless-stopped
    command: serve webdav /data --addr :8083 --user admin --pass admin123

  # 存储监控
  minio-prometheus:
    image: prom/prometheus:v2.45.0
    container_name: rag-storage-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./config/prometheus-storage.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_storage_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
    networks:
      - rag-storage
    restart: unless-stopped

volumes:
  minio_data:
    driver: local
  static_files:
    driver: local
  uploads:
    driver: local
  redis_storage_data:
    driver: local
  clamav_data:
    driver: local
  onlyoffice_data:
    driver: local
  onlyoffice_logs:
    driver: local
  rclone_data:
    driver: local
  prometheus_storage_data:
    driver: local

networks:
  rag-storage:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
