#!/bin/bash

# MinIO存储桶初始化脚本
# 创建必要的存储桶和配置访问策略

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
MINIO_ENDPOINT=${MINIO_ENDPOINT:-http://minio:9000}
MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-minioadmin}
MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-minioadmin123}
MINIO_ALIAS="ragminio"

# 存储桶配置
BUCKETS=(
    "documents:文档存储桶"
    "images:图片存储桶"
    "videos:视频存储桶"
    "audio:音频存储桶"
    "archives:归档存储桶"
    "temp:临时文件存储桶"
    "backups:备份存储桶"
    "static:静态资源存储桶"
    "avatars:用户头像存储桶"
    "thumbnails:缩略图存储桶"
)

# 等待MinIO服务启动
wait_for_minio() {
    log_info "等待MinIO服务启动..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if mc ping $MINIO_ALIAS >/dev/null 2>&1; then
            log_success "MinIO服务已启动"
            return 0
        fi
        
        log_info "等待MinIO启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "MinIO服务启动超时"
    exit 1
}

# 配置MinIO客户端
configure_mc() {
    log_info "配置MinIO客户端..."
    
    # 添加MinIO服务器配置
    mc alias set $MINIO_ALIAS $MINIO_ENDPOINT $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    
    if [ $? -eq 0 ]; then
        log_success "MinIO客户端配置成功"
    else
        log_error "MinIO客户端配置失败"
        exit 1
    fi
}

# 创建存储桶
create_buckets() {
    log_info "创建存储桶..."
    
    for bucket_info in "${BUCKETS[@]}"; do
        local bucket_name=$(echo $bucket_info | cut -d: -f1)
        local bucket_desc=$(echo $bucket_info | cut -d: -f2)
        
        # 检查存储桶是否已存在
        if mc ls $MINIO_ALIAS/$bucket_name >/dev/null 2>&1; then
            log_warning "存储桶 $bucket_name 已存在"
        else
            # 创建存储桶
            mc mb $MINIO_ALIAS/$bucket_name
            
            if [ $? -eq 0 ]; then
                log_success "创建存储桶: $bucket_name ($bucket_desc)"
            else
                log_error "创建存储桶失败: $bucket_name"
                exit 1
            fi
        fi
    done
}

# 设置存储桶策略
set_bucket_policies() {
    log_info "设置存储桶访问策略..."
    
    # 公共读取策略（用于静态资源）
    local public_read_policy='{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {"AWS": ["*"]},
                "Action": ["s3:GetObject"],
                "Resource": ["arn:aws:s3:::BUCKET_NAME/*"]
            }
        ]
    }'
    
    # 私有策略（默认）
    local private_policy='{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Deny",
                "Principal": {"AWS": ["*"]},
                "Action": ["s3:*"],
                "Resource": ["arn:aws:s3:::BUCKET_NAME/*"]
            }
        ]
    }'
    
    # 公共存储桶列表
    local public_buckets=("static" "thumbnails")
    
    for bucket_info in "${BUCKETS[@]}"; do
        local bucket_name=$(echo $bucket_info | cut -d: -f1)
        local policy_file="/tmp/${bucket_name}_policy.json"
        
        # 检查是否为公共存储桶
        if [[ " ${public_buckets[@]} " =~ " ${bucket_name} " ]]; then
            # 设置公共读取策略
            echo "$public_read_policy" | sed "s/BUCKET_NAME/$bucket_name/g" > $policy_file
            mc policy set-json $policy_file $MINIO_ALIAS/$bucket_name
            log_success "设置公共读取策略: $bucket_name"
        else
            # 设置私有策略
            mc policy set private $MINIO_ALIAS/$bucket_name
            log_success "设置私有策略: $bucket_name"
        fi
        
        # 清理临时文件
        rm -f $policy_file
    done
}

# 设置存储桶版本控制
set_bucket_versioning() {
    log_info "设置存储桶版本控制..."
    
    # 需要版本控制的存储桶
    local versioned_buckets=("documents" "backups")
    
    for bucket_name in "${versioned_buckets[@]}"; do
        mc version enable $MINIO_ALIAS/$bucket_name
        
        if [ $? -eq 0 ]; then
            log_success "启用版本控制: $bucket_name"
        else
            log_warning "版本控制设置失败: $bucket_name"
        fi
    done
}

# 设置存储桶生命周期
set_bucket_lifecycle() {
    log_info "设置存储桶生命周期策略..."
    
    # 临时文件存储桶生命周期（7天后删除）
    local temp_lifecycle='{
        "Rules": [
            {
                "ID": "temp-files-cleanup",
                "Status": "Enabled",
                "Expiration": {
                    "Days": 7
                }
            }
        ]
    }'
    
    # 缩略图存储桶生命周期（30天后转为IA存储）
    local thumbnails_lifecycle='{
        "Rules": [
            {
                "ID": "thumbnails-transition",
                "Status": "Enabled",
                "Transitions": [
                    {
                        "Days": 30,
                        "StorageClass": "STANDARD_IA"
                    }
                ]
            }
        ]
    }'
    
    # 设置临时文件生命周期
    echo "$temp_lifecycle" > /tmp/temp_lifecycle.json
    mc ilm import $MINIO_ALIAS/temp < /tmp/temp_lifecycle.json
    log_success "设置临时文件生命周期策略"
    
    # 设置缩略图生命周期
    echo "$thumbnails_lifecycle" > /tmp/thumbnails_lifecycle.json
    mc ilm import $MINIO_ALIAS/thumbnails < /tmp/thumbnails_lifecycle.json
    log_success "设置缩略图生命周期策略"
    
    # 清理临时文件
    rm -f /tmp/temp_lifecycle.json /tmp/thumbnails_lifecycle.json
}

# 创建用户和访问密钥
create_users() {
    log_info "创建用户和访问密钥..."
    
    # 应用服务用户
    local app_user="rag-app"
    local app_password="rag-app-secret-2024"
    
    # 只读用户
    local readonly_user="rag-readonly"
    local readonly_password="rag-readonly-secret-2024"
    
    # 创建应用服务用户
    mc admin user add $MINIO_ALIAS $app_user $app_password
    if [ $? -eq 0 ]; then
        log_success "创建应用服务用户: $app_user"
    else
        log_warning "应用服务用户可能已存在: $app_user"
    fi
    
    # 创建只读用户
    mc admin user add $MINIO_ALIAS $readonly_user $readonly_password
    if [ $? -eq 0 ]; then
        log_success "创建只读用户: $readonly_user"
    else
        log_warning "只读用户可能已存在: $readonly_user"
    fi
    
    # 创建策略文件
    local app_policy='{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "s3:GetObject",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:ListBucket"
                ],
                "Resource": [
                    "arn:aws:s3:::documents/*",
                    "arn:aws:s3:::images/*",
                    "arn:aws:s3:::videos/*",
                    "arn:aws:s3:::audio/*",
                    "arn:aws:s3:::temp/*",
                    "arn:aws:s3:::static/*",
                    "arn:aws:s3:::avatars/*",
                    "arn:aws:s3:::thumbnails/*"
                ]
            }
        ]
    }'
    
    local readonly_policy='{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "s3:GetObject",
                    "s3:ListBucket"
                ],
                "Resource": [
                    "arn:aws:s3:::*"
                ]
            }
        ]
    }'
    
    # 创建并应用策略
    echo "$app_policy" > /tmp/app_policy.json
    echo "$readonly_policy" > /tmp/readonly_policy.json
    
    mc admin policy add $MINIO_ALIAS rag-app-policy /tmp/app_policy.json
    mc admin policy add $MINIO_ALIAS rag-readonly-policy /tmp/readonly_policy.json
    
    # 分配策略给用户
    mc admin policy set $MINIO_ALIAS rag-app-policy user=$app_user
    mc admin policy set $MINIO_ALIAS rag-readonly-policy user=$readonly_user
    
    log_success "用户策略配置完成"
    
    # 清理临时文件
    rm -f /tmp/app_policy.json /tmp/readonly_policy.json
}

# 显示配置信息
show_configuration() {
    log_info "MinIO配置信息:"
    echo "=================================="
    echo "MinIO端点: $MINIO_ENDPOINT"
    echo "管理控制台: http://localhost:9001"
    echo "根用户: $MINIO_ACCESS_KEY"
    echo "根密码: $MINIO_SECRET_KEY"
    echo ""
    echo "应用服务用户: rag-app"
    echo "应用服务密码: rag-app-secret-2024"
    echo ""
    echo "只读用户: rag-readonly"
    echo "只读密码: rag-readonly-secret-2024"
    echo ""
    echo "存储桶列表:"
    for bucket_info in "${BUCKETS[@]}"; do
        local bucket_name=$(echo $bucket_info | cut -d: -f1)
        local bucket_desc=$(echo $bucket_info | cut -d: -f2)
        echo "  - $bucket_name: $bucket_desc"
    done
    echo "=================================="
}

# 主函数
main() {
    log_info "开始初始化MinIO存储..."
    
    # 配置MinIO客户端
    configure_mc
    
    # 等待MinIO服务
    wait_for_minio
    
    # 创建存储桶
    create_buckets
    
    # 设置存储桶策略
    set_bucket_policies
    
    # 设置版本控制
    set_bucket_versioning
    
    # 设置生命周期
    set_bucket_lifecycle
    
    # 创建用户
    create_users
    
    # 显示配置信息
    show_configuration
    
    log_success "MinIO存储初始化完成！"
}

# 执行主函数
main "$@"
