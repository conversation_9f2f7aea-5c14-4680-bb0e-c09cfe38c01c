#!/usr/bin/env python3
"""
日志管理工具
提供日志查询、分析、清理、备份等功能
"""

import os
import sys
import json
import time
import argparse
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk

class LogManager:
    """日志管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.es_client = self._init_elasticsearch()
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "elasticsearch": {
                "hosts": ["http://localhost:9200"],
                "timeout": 30,
                "max_retries": 3
            },
            "kibana": {
                "url": "http://localhost:5601"
            },
            "logstash": {
                "url": "http://localhost:9600"
            },
            "indices": {
                "prefix": "rag-logs",
                "retention_days": 30,
                "max_size_gb": 100
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            # 合并默认配置
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            return config
        
        return default_config
    
    def _init_elasticsearch(self) -> Elasticsearch:
        """初始化Elasticsearch客户端"""
        return Elasticsearch(
            hosts=self.config['elasticsearch']['hosts'],
            timeout=self.config['elasticsearch']['timeout'],
            max_retries=self.config['elasticsearch']['max_retries']
        )
    
    def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        status = {}
        
        # 检查Elasticsearch
        try:
            health = self.es_client.cluster.health()
            status['elasticsearch'] = health['status'] in ['green', 'yellow']
            print(f"✅ Elasticsearch: {health['status']} ({health['number_of_nodes']} 节点)")
        except Exception as e:
            status['elasticsearch'] = False
            print(f"❌ Elasticsearch: 连接失败 - {e}")
        
        # 检查Kibana
        try:
            response = requests.get(f"{self.config['kibana']['url']}/api/status", timeout=5)
            status['kibana'] = response.status_code == 200
            print(f"✅ Kibana: 正常")
        except Exception as e:
            status['kibana'] = False
            print(f"❌ Kibana: 连接失败 - {e}")
        
        # 检查Logstash
        try:
            response = requests.get(f"{self.config['logstash']['url']}", timeout=5)
            status['logstash'] = response.status_code == 200
            print(f"✅ Logstash: 正常")
        except Exception as e:
            status['logstash'] = False
            print(f"❌ Logstash: 连接失败 - {e}")
        
        return status
    
    def list_indices(self, pattern: str = None) -> List[Dict[str, Any]]:
        """列出索引"""
        try:
            if pattern:
                indices = self.es_client.cat.indices(index=pattern, format='json')
            else:
                indices = self.es_client.cat.indices(format='json')
            
            # 排序并格式化
            indices = sorted(indices, key=lambda x: x['index'])
            
            print(f"📋 找到 {len(indices)} 个索引:")
            print(f"{'索引名称':<40} {'状态':<10} {'文档数':<15} {'大小':<10}")
            print("-" * 80)
            
            for idx in indices:
                print(f"{idx['index']:<40} {idx['status']:<10} {idx['docs.count']:<15} {idx['store.size']:<10}")
            
            return indices
        except Exception as e:
            print(f"❌ 列出索引失败: {e}")
            return []
    
    def search_logs(self, query: str, index: str = None, size: int = 10, 
                   start_time: str = None, end_time: str = None) -> List[Dict[str, Any]]:
        """搜索日志"""
        try:
            # 构建搜索体
            search_body = {
                "query": {
                    "bool": {
                        "must": []
                    }
                },
                "sort": [
                    {"@timestamp": {"order": "desc"}}
                ],
                "size": size
            }
            
            # 添加查询条件
            if query:
                search_body["query"]["bool"]["must"].append({
                    "query_string": {
                        "query": query,
                        "default_field": "message"
                    }
                })
            else:
                search_body["query"] = {"match_all": {}}
            
            # 添加时间范围
            if start_time or end_time:
                time_range = {}
                if start_time:
                    time_range["gte"] = start_time
                if end_time:
                    time_range["lte"] = end_time
                
                search_body["query"]["bool"]["must"].append({
                    "range": {
                        "@timestamp": time_range
                    }
                })
            
            # 执行搜索
            index_pattern = index or f"{self.config['indices']['prefix']}-*"
            result = self.es_client.search(index=index_pattern, body=search_body)
            
            hits = result['hits']['hits']
            total = result['hits']['total']['value']
            
            print(f"🔍 搜索结果: 找到 {total} 条记录，显示前 {len(hits)} 条")
            print("-" * 100)
            
            for hit in hits:
                source = hit['_source']
                timestamp = source.get('@timestamp', 'N/A')
                level = source.get('log_level', source.get('level', 'INFO'))
                service = source.get('service_name', 'unknown')
                message = source.get('message', '')[:100]
                
                print(f"{timestamp} [{level}] {service}: {message}")
            
            return hits
        except Exception as e:
            print(f"❌ 搜索日志失败: {e}")
            return []
    
    def get_log_stats(self, days: int = 7) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 按服务统计
            service_agg = {
                "aggs": {
                    "services": {
                        "terms": {
                            "field": "service_name.keyword",
                            "size": 20
                        }
                    }
                },
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": start_time.isoformat(),
                            "lte": end_time.isoformat()
                        }
                    }
                },
                "size": 0
            }
            
            # 按日志级别统计
            level_agg = {
                "aggs": {
                    "levels": {
                        "terms": {
                            "field": "log_level.keyword",
                            "size": 10
                        }
                    }
                },
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": start_time.isoformat(),
                            "lte": end_time.isoformat()
                        }
                    }
                },
                "size": 0
            }
            
            # 按时间统计
            time_agg = {
                "aggs": {
                    "timeline": {
                        "date_histogram": {
                            "field": "@timestamp",
                            "calendar_interval": "1h"
                        }
                    }
                },
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": start_time.isoformat(),
                            "lte": end_time.isoformat()
                        }
                    }
                },
                "size": 0
            }
            
            index_pattern = f"{self.config['indices']['prefix']}-*"
            
            # 执行聚合查询
            service_result = self.es_client.search(index=index_pattern, body=service_agg)
            level_result = self.es_client.search(index=index_pattern, body=level_agg)
            time_result = self.es_client.search(index=index_pattern, body=time_agg)
            
            stats = {
                "period": f"{days} 天",
                "total_logs": service_result['hits']['total']['value'],
                "services": {},
                "levels": {},
                "timeline": []
            }
            
            # 处理服务统计
            for bucket in service_result['aggregations']['services']['buckets']:
                stats['services'][bucket['key']] = bucket['doc_count']
            
            # 处理级别统计
            for bucket in level_result['aggregations']['levels']['buckets']:
                stats['levels'][bucket['key']] = bucket['doc_count']
            
            # 处理时间线统计
            for bucket in time_result['aggregations']['timeline']['buckets']:
                stats['timeline'].append({
                    'time': bucket['key_as_string'],
                    'count': bucket['doc_count']
                })
            
            # 显示统计信息
            print(f"📊 日志统计信息 (最近 {days} 天):")
            print(f"总日志数: {stats['total_logs']:,}")
            
            print("\n🏷️ 按服务分布:")
            for service, count in sorted(stats['services'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {service}: {count:,}")
            
            print("\n📈 按级别分布:")
            for level, count in sorted(stats['levels'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {level}: {count:,}")
            
            return stats
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {}
    
    def cleanup_old_indices(self, days: int = None, dry_run: bool = False) -> List[str]:
        """清理旧索引"""
        try:
            retention_days = days or self.config['indices']['retention_days']
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # 获取所有索引
            indices = self.es_client.cat.indices(
                index=f"{self.config['indices']['prefix']}-*", 
                format='json'
            )
            
            old_indices = []
            for idx in indices:
                index_name = idx['index']
                
                # 从索引名称提取日期
                try:
                    # 假设索引格式为: rag-logs-service-2024.01.15
                    date_part = index_name.split('-')[-3:]  # ['2024', '01', '15']
                    if len(date_part) == 3:
                        index_date = datetime.strptime('.'.join(date_part), '%Y.%m.%d')
                        if index_date < cutoff_date:
                            old_indices.append(index_name)
                except ValueError:
                    continue
            
            print(f"🗑️ 找到 {len(old_indices)} 个需要清理的索引 (超过 {retention_days} 天):")
            
            if dry_run:
                print("🔍 预演模式，不会实际删除:")
                for index_name in old_indices:
                    print(f"  - {index_name}")
            else:
                deleted_count = 0
                for index_name in old_indices:
                    try:
                        self.es_client.indices.delete(index=index_name)
                        print(f"  ✅ 已删除: {index_name}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"  ❌ 删除失败: {index_name} - {e}")
                
                print(f"\n✅ 清理完成，共删除 {deleted_count} 个索引")
            
            return old_indices
        except Exception as e:
            print(f"❌ 清理索引失败: {e}")
            return []
    
    def backup_indices(self, output_dir: str, indices: List[str] = None, 
                      compress: bool = True) -> List[str]:
        """备份索引"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if not indices:
                # 获取所有相关索引
                all_indices = self.es_client.cat.indices(
                    index=f"{self.config['indices']['prefix']}-*", 
                    format='json'
                )
                indices = [idx['index'] for idx in all_indices]
            
            backed_up = []
            
            for index_name in indices:
                print(f"💾 备份索引: {index_name}")
                
                backup_file = os.path.join(output_dir, f"{index_name}_{timestamp}.json")
                
                try:
                    # 使用scan获取所有文档
                    docs = scan(
                        self.es_client,
                        index=index_name,
                        query={"query": {"match_all": {}}},
                        scroll='5m',
                        size=1000
                    )
                    
                    doc_count = 0
                    with open(backup_file, 'w') as f:
                        for doc in docs:
                            f.write(json.dumps(doc) + '\n')
                            doc_count += 1
                    
                    # 压缩文件
                    if compress:
                        import gzip
                        with open(backup_file, 'rb') as f_in:
                            with gzip.open(f"{backup_file}.gz", 'wb') as f_out:
                                f_out.writelines(f_in)
                        os.remove(backup_file)
                        backup_file = f"{backup_file}.gz"
                    
                    backed_up.append(backup_file)
                    print(f"  ✅ 已备份 {doc_count} 个文档到: {backup_file}")
                    
                except Exception as e:
                    print(f"  ❌ 备份失败: {e}")
            
            print(f"\n✅ 备份完成，共备份 {len(backed_up)} 个索引")
            return backed_up
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return []
    
    def create_index_template(self, template_name: str = None):
        """创建索引模板"""
        try:
            template_name = template_name or "rag-logs-template"
            
            template = {
                "index_patterns": [f"{self.config['indices']['prefix']}-*"],
                "template": {
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0,
                        "index.refresh_interval": "5s",
                        "index.max_result_window": 50000
                    },
                    "mappings": {
                        "properties": {
                            "@timestamp": {
                                "type": "date"
                            },
                            "message": {
                                "type": "text",
                                "analyzer": "standard"
                            },
                            "log_level": {
                                "type": "keyword"
                            },
                            "service_name": {
                                "type": "keyword"
                            },
                            "trace_id": {
                                "type": "keyword"
                            },
                            "user_id": {
                                "type": "keyword"
                            },
                            "response_time": {
                                "type": "float"
                            },
                            "status_code": {
                                "type": "integer"
                            }
                        }
                    }
                }
            }
            
            self.es_client.indices.put_index_template(
                name=template_name,
                body=template
            )
            
            print(f"✅ 索引模板已创建: {template_name}")
            
        except Exception as e:
            print(f"❌ 创建索引模板失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='日志管理工具')
    parser.add_argument('--config', '-c', help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 健康检查
    subparsers.add_parser('health', help='健康检查')
    
    # 列出索引
    list_parser = subparsers.add_parser('list', help='列出索引')
    list_parser.add_argument('--pattern', '-p', help='索引模式')
    
    # 搜索日志
    search_parser = subparsers.add_parser('search', help='搜索日志')
    search_parser.add_argument('query', help='搜索查询')
    search_parser.add_argument('--index', '-i', help='索引名称')
    search_parser.add_argument('--size', '-s', type=int, default=10, help='结果数量')
    search_parser.add_argument('--start', help='开始时间')
    search_parser.add_argument('--end', help='结束时间')
    
    # 统计信息
    stats_parser = subparsers.add_parser('stats', help='获取统计信息')
    stats_parser.add_argument('--days', '-d', type=int, default=7, help='统计天数')
    
    # 清理索引
    cleanup_parser = subparsers.add_parser('cleanup', help='清理旧索引')
    cleanup_parser.add_argument('--days', '-d', type=int, help='保留天数')
    cleanup_parser.add_argument('--dry-run', action='store_true', help='预演模式')
    
    # 备份索引
    backup_parser = subparsers.add_parser('backup', help='备份索引')
    backup_parser.add_argument('--output', '-o', required=True, help='输出目录')
    backup_parser.add_argument('--indices', nargs='+', help='索引列表')
    backup_parser.add_argument('--no-compress', action='store_true', help='不压缩')
    
    # 创建模板
    template_parser = subparsers.add_parser('template', help='创建索引模板')
    template_parser.add_argument('--name', '-n', help='模板名称')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建管理器
    manager = LogManager(args.config)
    
    try:
        if args.command == 'health':
            manager.health_check()
        
        elif args.command == 'list':
            manager.list_indices(args.pattern)
        
        elif args.command == 'search':
            manager.search_logs(args.query, args.index, args.size, args.start, args.end)
        
        elif args.command == 'stats':
            manager.get_log_stats(args.days)
        
        elif args.command == 'cleanup':
            manager.cleanup_old_indices(args.days, args.dry_run)
        
        elif args.command == 'backup':
            manager.backup_indices(args.output, args.indices, not args.no_compress)
        
        elif args.command == 'template':
            manager.create_index_template(args.name)
    
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
