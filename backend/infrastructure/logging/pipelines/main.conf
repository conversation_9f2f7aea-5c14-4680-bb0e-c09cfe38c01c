# Logstash主管道配置
# 处理各种日志输入、过滤和输出

input {
  # Beats输入
  beats {
    port => 5044
    host => "0.0.0.0"
  }
  
  # TCP输入（用于应用程序直接发送日志）
  tcp {
    port => 5000
    codec => json_lines
  }
  
  # HTTP输入（用于Webhook）
  http {
    port => 8080
    codec => json
  }
  
  # Syslog输入
  syslog {
    port => 514
    host => "0.0.0.0"
  }
}

filter {
  # 添加处理时间戳
  mutate {
    add_field => { "[@metadata][processed_at]" => "%{+YYYY-MM-dd HH:mm:ss}" }
  }
  
  # 处理Docker容器日志
  if [container] {
    # 解析容器名称
    if [container][name] {
      mutate {
        add_field => { "service_name" => "%{[container][name]}" }
      }
      
      # 移除容器名称前缀
      mutate {
        gsub => [ "service_name", "^/", "" ]
        gsub => [ "service_name", "^rag-", "" ]
      }
    }
    
    # 解析容器标签
    if [container][labels] {
      if [container][labels][service] {
        mutate {
          add_field => { "service_type" => "%{[container][labels][service]}" }
        }
      }
      
      if [container][labels][environment] {
        mutate {
          add_field => { "environment" => "%{[container][labels][environment]}" }
        }
      }
    }
  }
  
  # 处理应用程序日志
  if [fields][log_type] == "application" {
    # 解析JSON格式的应用日志
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "app_log"
      }
      
      # 提取常用字段
      if [app_log][level] {
        mutate {
          add_field => { "log_level" => "%{[app_log][level]}" }
        }
      }
      
      if [app_log][timestamp] {
        date {
          match => [ "[app_log][timestamp]", "ISO8601" ]
          target => "@timestamp"
        }
      }
      
      if [app_log][service] {
        mutate {
          add_field => { "service_name" => "%{[app_log][service]}" }
        }
      }
      
      if [app_log][trace_id] {
        mutate {
          add_field => { "trace_id" => "%{[app_log][trace_id]}" }
        }
      }
      
      if [app_log][user_id] {
        mutate {
          add_field => { "user_id" => "%{[app_log][user_id]}" }
        }
      }
    }
  }
  
  # 处理Nginx访问日志
  if [fields][log_type] == "nginx_access" {
    grok {
      match => { 
        "message" => "%{COMBINEDAPACHELOG} %{QS:x_forwarded_for}"
      }
    }
    
    # 解析时间
    date {
      match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }
    
    # 转换响应码为数字
    mutate {
      convert => { "response" => "integer" }
      convert => { "bytes" => "integer" }
    }
    
    # 解析User-Agent
    useragent {
      source => "agent"
      target => "user_agent"
    }
    
    # 地理位置解析
    geoip {
      source => "clientip"
      target => "geoip"
    }
  }
  
  # 处理错误日志
  if [fields][log_type] == "error" {
    # 提取错误级别
    grok {
      match => { 
        "message" => "(?<error_level>FATAL|ERROR|WARN|WARNING|INFO|DEBUG|TRACE)"
      }
    }
    
    # 提取堆栈跟踪
    if [message] =~ /Exception|Error|Stack/ {
      mutate {
        add_field => { "has_stacktrace" => "true" }
      }
    }
  }
  
  # 处理数据库日志
  if [fields][log_type] == "database" {
    # PostgreSQL慢查询日志
    if [message] =~ /duration:/ {
      grok {
        match => { 
          "message" => "duration: %{NUMBER:query_duration:float} ms"
        }
      }
      
      if [query_duration] and [query_duration] > 1000 {
        mutate {
          add_field => { "slow_query" => "true" }
        }
      }
    }
  }
  
  # 添加通用字段
  mutate {
    add_field => { 
      "[@metadata][index_prefix]" => "rag-logs"
      "[@metadata][document_type]" => "_doc"
    }
  }
  
  # 根据日志类型设置索引
  if [fields][log_type] {
    mutate {
      add_field => { "[@metadata][index_suffix]" => "%{[fields][log_type]}" }
    }
  } else if [service_name] {
    mutate {
      add_field => { "[@metadata][index_suffix]" => "%{service_name}" }
    }
  } else {
    mutate {
      add_field => { "[@metadata][index_suffix]" => "general" }
    }
  }
  
  # 设置最终索引名称（按日期分割）
  mutate {
    add_field => { 
      "[@metadata][index_name]" => "%{[@metadata][index_prefix]}-%{[@metadata][index_suffix]}-%{+YYYY.MM.dd}"
    }
  }
  
  # 清理不需要的字段
  mutate {
    remove_field => [ "host", "agent", "ecs", "input", "log" ]
  }
}

output {
  # 输出到Elasticsearch
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "%{[@metadata][index_name]}"
    document_type => "%{[@metadata][document_type]}"
    
    # 模板配置
    template_name => "rag-logs"
    template_pattern => "rag-logs-*"
    template => "/usr/share/logstash/templates/rag-logs-template.json"
    template_overwrite => true
    
    # 性能配置
    workers => 2
    flush_size => 500
    idle_flush_time => 1
  }
  
  # 调试输出（开发环境）
  if [@metadata][debug] == "true" {
    stdout {
      codec => rubydebug
    }
  }
  
  # 错误日志单独处理
  if [log_level] == "ERROR" or [log_level] == "FATAL" or [error_level] == "ERROR" or [error_level] == "FATAL" {
    # 发送到告警系统
    http {
      url => "http://log-alerting:3012/webhook/error"
      http_method => "post"
      format => "json"
      headers => {
        "Content-Type" => "application/json"
      }
    }
  }
}
