# Logstash配置文件
# 配置节点、路径、监控等设置

# 节点配置
node.name: logstash-node-1

# 路径配置
path.data: /usr/share/logstash/data
path.logs: /usr/share/logstash/logs
path.settings: /usr/share/logstash/config

# 管道配置
pipeline.workers: 2
pipeline.batch.size: 125
pipeline.batch.delay: 50

# HTTP API配置
http.host: "0.0.0.0"
http.port: 9600

# 日志配置
log.level: info
log.format: plain

# 监控配置
monitoring.enabled: true
monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]

# 配置重新加载
config.reload.automatic: true
config.reload.interval: 3s

# 死信队列配置
dead_letter_queue.enable: true
dead_letter_queue.max_bytes: 1024mb

# 持久队列配置
queue.type: persisted
queue.max_bytes: 1gb
queue.checkpoint.writes: 1024

# X-Pack配置
xpack.monitoring.enabled: true
xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
