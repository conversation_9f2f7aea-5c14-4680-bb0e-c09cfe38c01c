# Elasticsearch配置文件
# 配置集群、节点、网络、安全等设置

# 集群配置
cluster.name: rag-cluster
node.name: elasticsearch-node-1

# 网络配置
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# 发现配置
discovery.type: single-node
cluster.initial_master_nodes: ["elasticsearch-node-1"]

# 路径配置
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# 内存配置
bootstrap.memory_lock: true

# 安全配置
xpack.security.enabled: false
xpack.security.enrollment.enabled: false
xpack.security.http.ssl.enabled: false
xpack.security.transport.ssl.enabled: false

# 监控配置
xpack.monitoring.collection.enabled: true

# 机器学习配置
xpack.ml.enabled: true

# 图形配置
xpack.graph.enabled: true

# 观察者配置
xpack.watcher.enabled: true

# SQL配置
xpack.sql.enabled: true

# 索引配置
action.auto_create_index: true
action.destructive_requires_name: true

# 搜索配置
search.max_buckets: 65536
search.default_search_timeout: 30s

# 索引模板配置
index.number_of_shards: 1
index.number_of_replicas: 0
index.refresh_interval: 1s

# 日志级别
logger.root: INFO
logger.org.elasticsearch.transport: WARN
logger.org.elasticsearch.discovery: WARN
logger.org.elasticsearch.cluster.service: DEBUG

# HTTP配置
http.max_content_length: 100mb
http.max_initial_line_length: 4kb
http.max_header_size: 8kb
http.compression: true
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, Content-Type, Content-Length, Authorization

# 线程池配置
thread_pool.write.queue_size: 1000
thread_pool.search.queue_size: 1000
thread_pool.get.queue_size: 1000

# 断路器配置
indices.breaker.total.limit: 70%
indices.breaker.fielddata.limit: 40%
indices.breaker.request.limit: 40%

# 缓存配置
indices.queries.cache.size: 10%
indices.fielddata.cache.size: 20%

# 恢复配置
cluster.routing.allocation.node_concurrent_recoveries: 2
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%

# 慢日志配置
index.search.slowlog.threshold.query.warn: 10s
index.search.slowlog.threshold.query.info: 5s
index.search.slowlog.threshold.query.debug: 2s
index.search.slowlog.threshold.query.trace: 500ms

index.search.slowlog.threshold.fetch.warn: 1s
index.search.slowlog.threshold.fetch.info: 800ms
index.search.slowlog.threshold.fetch.debug: 500ms
index.search.slowlog.threshold.fetch.trace: 200ms

index.indexing.slowlog.threshold.index.warn: 10s
index.indexing.slowlog.threshold.index.info: 5s
index.indexing.slowlog.threshold.index.debug: 2s
index.indexing.slowlog.threshold.index.trace: 500ms

# GC配置
indices.memory.index_buffer_size: 10%
indices.memory.min_index_buffer_size: 48mb

# 脚本配置
script.allowed_types: inline
script.allowed_contexts: search, update
