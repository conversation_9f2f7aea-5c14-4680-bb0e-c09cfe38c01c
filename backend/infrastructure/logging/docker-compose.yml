# ELK Stack日志服务基础设施配置
# 包含Elasticsearch、Logstash、Ki<PERSON>、Filebeat等组件

version: '3.8'

services:
  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: rag-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
      - ./config/jvm.options:/usr/share/elasticsearch/config/jvm.options:ro
    environment:
      - node.name=elasticsearch
      - cluster.name=rag-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    networks:
      - rag-logging
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Logstash日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: rag-logstash
    ports:
      - "5044:5044"  # Beats input
      - "5000:5000"  # TCP input
      - "9600:9600"  # HTTP API
    volumes:
      - ./config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ./config/pipelines.yml:/usr/share/logstash/config/pipelines.yml:ro
      - ./pipelines:/usr/share/logstash/pipeline:ro
      - logstash_data:/usr/share/logstash/data
    environment:
      - "LS_JAVA_OPTS=-Xms1g -Xmx1g"
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana可视化界面
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: rag-kibana
    ports:
      - "5601:5601"
    volumes:
      - ./config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
      - kibana_data:/usr/share/kibana/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=kibana_password
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Filebeat日志收集
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: rag-filebeat
    user: root
    volumes:
      - ./config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/log:/var/log:ro
      - filebeat_data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - LOGSTASH_HOSTS=logstash:5044
    command: filebeat -e -strict.perms=false
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - rag-logging
    restart: unless-stopped

  # Metricbeat系统指标收集
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.11.0
    container_name: rag-metricbeat
    user: root
    volumes:
      - ./config/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat_data:/usr/share/metricbeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    command: metricbeat -e -strict.perms=false -system.hostfs=/hostfs
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped

  # Heartbeat服务监控
  heartbeat:
    image: docker.elastic.co/beats/heartbeat:8.11.0
    container_name: rag-heartbeat
    volumes:
      - ./config/heartbeat.yml:/usr/share/heartbeat/heartbeat.yml:ro
      - heartbeat_data:/usr/share/heartbeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    command: heartbeat -e -strict.perms=false
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped

  # APM Server应用性能监控
  apm-server:
    image: docker.elastic.co/apm/apm-server:8.11.0
    container_name: rag-apm-server
    ports:
      - "8200:8200"
    volumes:
      - ./config/apm-server.yml:/usr/share/apm-server/apm-server.yml:ro
      - apm_data:/usr/share/apm-server/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    command: apm-server -e -strict.perms=false
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped

  # Curator索引管理
  curator:
    image: untergeek/curator:8.0.4
    container_name: rag-curator
    volumes:
      - ./config/curator.yml:/usr/share/curator/curator.yml:ro
      - ./config/curator_actions.yml:/usr/share/curator/curator_actions.yml:ro
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped
    command: >
      sh -c "
        while true; do
          curator --config /usr/share/curator/curator.yml /usr/share/curator/curator_actions.yml
          sleep 86400
        done
      "

  # Fluentd日志收集（备选方案）
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: rag-fluentd
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./config/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - fluentd_data:/fluentd/log
    environment:
      - FLUENTD_CONF=fluent.conf
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped

  # Grafana日志可视化
  grafana-logging:
    image: grafana/grafana:10.2.0
    container_name: rag-grafana-logging
    ports:
      - "3001:3000"
    volumes:
      - grafana_logging_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-elasticsearch-datasource
    networks:
      - rag-logging
    restart: unless-stopped

  # 日志分析服务
  log-analyzer:
    build:
      context: ../../services/log-analyzer
      dockerfile: Dockerfile
    container_name: rag-log-analyzer
    ports:
      - "3011:3011"
    volumes:
      - ./config/analyzer-config.yml:/app/config/analyzer-config.yml:ro
    environment:
      - NODE_ENV=production
      - PORT=3011
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - KIBANA_URL=http://kibana:5601
    depends_on:
      - elasticsearch
      - kibana
    networks:
      - rag-logging
    restart: unless-stopped

  # 日志告警服务
  log-alerting:
    build:
      context: ../../services/log-alerting
      dockerfile: Dockerfile
    container_name: rag-log-alerting
    volumes:
      - ./config/alerting-config.yml:/app/config/alerting-config.yml:ro
    environment:
      - NODE_ENV=production
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ALERT_WEBHOOK_URL=${ALERT_WEBHOOK_URL:-}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL:-}
    depends_on:
      - elasticsearch
    networks:
      - rag-logging
    restart: unless-stopped

volumes:
  elasticsearch_data:
    driver: local
  logstash_data:
    driver: local
  kibana_data:
    driver: local
  filebeat_data:
    driver: local
  metricbeat_data:
    driver: local
  heartbeat_data:
    driver: local
  apm_data:
    driver: local
  fluentd_data:
    driver: local
  grafana_logging_data:
    driver: local

networks:
  rag-logging:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
