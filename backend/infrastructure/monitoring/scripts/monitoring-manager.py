#!/usr/bin/env python3
"""
监控系统管理工具
提供监控系统的部署、配置、维护等功能
"""

import os
import sys
import json
import time
import argparse
import subprocess
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import yaml

class MonitoringManager:
    """监控系统管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.prometheus_url = self.config.get('prometheus_url', 'http://localhost:9090')
        self.grafana_url = self.config.get('grafana_url', 'http://localhost:3000')
        self.alertmanager_url = self.config.get('alertmanager_url', 'http://localhost:9093')
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "prometheus_url": "http://localhost:9090",
            "grafana_url": "http://localhost:3000",
            "alertmanager_url": "http://localhost:9093",
            "grafana_admin": {
                "username": "admin",
                "password": "admin123"
            },
            "notification": {
                "email": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "username": "<EMAIL>",
                    "password": "your-app-password"
                },
                "slack": {
                    "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
                }
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            # 合并默认配置
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            return config
        
        return default_config
    
    def check_services(self) -> Dict[str, bool]:
        """检查监控服务状态"""
        services = {
            'prometheus': self.prometheus_url,
            'grafana': self.grafana_url,
            'alertmanager': self.alertmanager_url
        }
        
        status = {}
        for service, url in services.items():
            try:
                response = requests.get(f"{url}/api/v1/status/config" if service == 'prometheus' 
                                      else f"{url}/api/health" if service == 'grafana'
                                      else f"{url}/api/v1/status", timeout=5)
                status[service] = response.status_code == 200
                print(f"✅ {service.capitalize()}: 正常 ({url})")
            except Exception as e:
                status[service] = False
                print(f"❌ {service.capitalize()}: 异常 ({url}) - {e}")
        
        return status
    
    def deploy_monitoring_stack(self):
        """部署监控技术栈"""
        print("🚀 开始部署监控技术栈...")
        
        try:
            # 启动Docker Compose
            result = subprocess.run([
                'docker-compose', 
                '-f', 'docker-compose.yml',
                'up', '-d'
            ], cwd=os.path.dirname(__file__), capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 监控技术栈部署成功")
                
                # 等待服务启动
                print("⏳ 等待服务启动...")
                time.sleep(30)
                
                # 检查服务状态
                self.check_services()
                
                # 配置Grafana
                self.setup_grafana()
                
            else:
                print(f"❌ 部署失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 部署异常: {e}")
    
    def setup_grafana(self):
        """配置Grafana"""
        print("🔧 配置Grafana...")
        
        try:
            # 等待Grafana启动
            for i in range(30):
                try:
                    response = requests.get(f"{self.grafana_url}/api/health", timeout=5)
                    if response.status_code == 200:
                        break
                except:
                    pass
                time.sleep(2)
            else:
                print("❌ Grafana启动超时")
                return
            
            # 获取认证信息
            auth = (
                self.config['grafana_admin']['username'],
                self.config['grafana_admin']['password']
            )
            
            # 添加Prometheus数据源
            datasource_config = {
                "name": "Prometheus",
                "type": "prometheus",
                "url": self.prometheus_url,
                "access": "proxy",
                "isDefault": True
            }
            
            response = requests.post(
                f"{self.grafana_url}/api/datasources",
                json=datasource_config,
                auth=auth
            )
            
            if response.status_code in [200, 409]:  # 200: 创建成功, 409: 已存在
                print("✅ Prometheus数据源配置成功")
            else:
                print(f"❌ 数据源配置失败: {response.text}")
            
            # 导入仪表板
            self.import_dashboards(auth)
            
        except Exception as e:
            print(f"❌ Grafana配置异常: {e}")
    
    def import_dashboards(self, auth):
        """导入Grafana仪表板"""
        dashboard_configs = [
            {
                "id": 1860,  # Node Exporter Full
                "title": "系统监控"
            },
            {
                "id": 893,   # Docker and system monitoring
                "title": "容器监控"
            },
            {
                "id": 7362,  # PostgreSQL Database
                "title": "PostgreSQL监控"
            },
            {
                "id": 763,   # Redis Dashboard
                "title": "Redis监控"
            }
        ]
        
        for dashboard in dashboard_configs:
            try:
                # 从Grafana官方获取仪表板配置
                response = requests.get(f"https://grafana.com/api/dashboards/{dashboard['id']}/revisions/latest/download")
                if response.status_code == 200:
                    dashboard_json = response.json()
                    
                    # 导入仪表板
                    import_data = {
                        "dashboard": dashboard_json,
                        "overwrite": True,
                        "inputs": [
                            {
                                "name": "DS_PROMETHEUS",
                                "type": "datasource",
                                "pluginId": "prometheus",
                                "value": "Prometheus"
                            }
                        ]
                    }
                    
                    import_response = requests.post(
                        f"{self.grafana_url}/api/dashboards/import",
                        json=import_data,
                        auth=auth
                    )
                    
                    if import_response.status_code == 200:
                        print(f"✅ 导入仪表板: {dashboard['title']}")
                    else:
                        print(f"❌ 导入仪表板失败: {dashboard['title']} - {import_response.text}")
                        
            except Exception as e:
                print(f"❌ 导入仪表板异常: {dashboard['title']} - {e}")
    
    def get_metrics(self, query: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """查询Prometheus指标"""
        try:
            params = {'query': query}
            
            if start_time and end_time:
                params.update({
                    'start': start_time,
                    'end': end_time,
                    'step': '15s'
                })
                endpoint = 'query_range'
            else:
                endpoint = 'query'
            
            response = requests.get(
                f"{self.prometheus_url}/api/v1/{endpoint}",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 查询失败: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            return {}
    
    def get_alerts(self) -> List[Dict[str, Any]]:
        """获取当前告警"""
        try:
            response = requests.get(f"{self.alertmanager_url}/api/v1/alerts", timeout=10)
            
            if response.status_code == 200:
                return response.json().get('data', [])
            else:
                print(f"❌ 获取告警失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ 获取告警异常: {e}")
            return []
    
    def silence_alert(self, matchers: List[Dict[str, str]], duration: str = "1h", comment: str = "Manual silence") -> bool:
        """静默告警"""
        try:
            silence_data = {
                "matchers": matchers,
                "startsAt": datetime.utcnow().isoformat() + "Z",
                "endsAt": (datetime.utcnow() + timedelta(hours=1)).isoformat() + "Z",
                "createdBy": "monitoring-manager",
                "comment": comment
            }
            
            response = requests.post(
                f"{self.alertmanager_url}/api/v1/silences",
                json=silence_data,
                timeout=10
            )
            
            if response.status_code == 200:
                silence_id = response.json().get('silenceID')
                print(f"✅ 告警已静默，ID: {silence_id}")
                return True
            else:
                print(f"❌ 静默失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 静默异常: {e}")
            return False
    
    def generate_report(self, output_file: str = None):
        """生成监控报告"""
        print("📊 生成监控报告...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "services": self.check_services(),
            "alerts": self.get_alerts(),
            "metrics": {}
        }
        
        # 收集关键指标
        key_metrics = [
            ("up", "服务可用性"),
            ("node_load1", "系统负载"),
            ("node_memory_MemAvailable_bytes", "可用内存"),
            ("node_filesystem_avail_bytes", "可用磁盘空间"),
            ("rate(http_requests_total[5m])", "HTTP请求率"),
            ("rate(http_requests_total{status=~\"5..\"}[5m])", "HTTP错误率")
        ]
        
        for query, description in key_metrics:
            result = self.get_metrics(query)
            if result.get('status') == 'success':
                report['metrics'][description] = result['data']['result']
        
        # 输出报告
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"✅ 报告已保存到: {output_file}")
        else:
            print(json.dumps(report, indent=2, ensure_ascii=False))
    
    def backup_config(self, backup_dir: str = "./backups"):
        """备份监控配置"""
        print("💾 备份监控配置...")
        
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 备份Prometheus配置
        try:
            response = requests.get(f"{self.prometheus_url}/api/v1/status/config")
            if response.status_code == 200:
                config_data = response.json()
                with open(f"{backup_dir}/prometheus_config_{timestamp}.yaml", 'w') as f:
                    f.write(config_data['data']['yaml'])
                print("✅ Prometheus配置已备份")
        except Exception as e:
            print(f"❌ Prometheus配置备份失败: {e}")
        
        # 备份AlertManager配置
        try:
            response = requests.get(f"{self.alertmanager_url}/api/v1/status")
            if response.status_code == 200:
                config_data = response.json()
                with open(f"{backup_dir}/alertmanager_config_{timestamp}.yaml", 'w') as f:
                    yaml.dump(config_data['data']['configYAML'], f)
                print("✅ AlertManager配置已备份")
        except Exception as e:
            print(f"❌ AlertManager配置备份失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        print("🔄 重新加载配置...")
        
        # 重新加载Prometheus配置
        try:
            response = requests.post(f"{self.prometheus_url}/-/reload")
            if response.status_code == 200:
                print("✅ Prometheus配置已重新加载")
            else:
                print(f"❌ Prometheus配置重新加载失败: {response.text}")
        except Exception as e:
            print(f"❌ Prometheus配置重新加载异常: {e}")
        
        # 重新加载AlertManager配置
        try:
            response = requests.post(f"{self.alertmanager_url}/-/reload")
            if response.status_code == 200:
                print("✅ AlertManager配置已重新加载")
            else:
                print(f"❌ AlertManager配置重新加载失败: {response.text}")
        except Exception as e:
            print(f"❌ AlertManager配置重新加载异常: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='监控系统管理工具')
    parser.add_argument('--config', '-c', help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 检查服务状态
    subparsers.add_parser('status', help='检查服务状态')
    
    # 部署监控栈
    subparsers.add_parser('deploy', help='部署监控技术栈')
    
    # 查询指标
    query_parser = subparsers.add_parser('query', help='查询指标')
    query_parser.add_argument('metric', help='指标查询语句')
    query_parser.add_argument('--start', help='开始时间')
    query_parser.add_argument('--end', help='结束时间')
    
    # 获取告警
    subparsers.add_parser('alerts', help='获取当前告警')
    
    # 静默告警
    silence_parser = subparsers.add_parser('silence', help='静默告警')
    silence_parser.add_argument('--alertname', required=True, help='告警名称')
    silence_parser.add_argument('--duration', default='1h', help='静默时长')
    silence_parser.add_argument('--comment', default='Manual silence', help='静默原因')
    
    # 生成报告
    report_parser = subparsers.add_parser('report', help='生成监控报告')
    report_parser.add_argument('--output', '-o', help='输出文件路径')
    
    # 备份配置
    backup_parser = subparsers.add_parser('backup', help='备份配置')
    backup_parser.add_argument('--dir', default='./backups', help='备份目录')
    
    # 重新加载配置
    subparsers.add_parser('reload', help='重新加载配置')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建管理器
    manager = MonitoringManager(args.config)
    
    try:
        if args.command == 'status':
            manager.check_services()
        
        elif args.command == 'deploy':
            manager.deploy_monitoring_stack()
        
        elif args.command == 'query':
            result = manager.get_metrics(args.metric, args.start, args.end)
            print(json.dumps(result, indent=2, ensure_ascii=False))
        
        elif args.command == 'alerts':
            alerts = manager.get_alerts()
            print(json.dumps(alerts, indent=2, ensure_ascii=False))
        
        elif args.command == 'silence':
            matchers = [{"name": "alertname", "value": args.alertname}]
            manager.silence_alert(matchers, args.duration, args.comment)
        
        elif args.command == 'report':
            manager.generate_report(args.output)
        
        elif args.command == 'backup':
            manager.backup_config(args.dir)
        
        elif args.command == 'reload':
            manager.reload_config()
    
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
