# 监控服务基础设施配置
# 包含Prometheus、<PERSON><PERSON>、<PERSON>ertManager等监控组件

version: '3.8'

services:
  # Prometheus监控服务
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./config/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--storage.tsdb.wal-compression'
    networks:
      - rag-monitoring
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana可视化服务
  grafana:
    image: grafana/grafana:10.0.0
    container_name: rag-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-worldmap-panel
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    networks:
      - rag-monitoring
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AlertManager告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: rag-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./config/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
      - '--cluster.advertise-address=0.0.0.0:9093'
    networks:
      - rag-monitoring
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9093/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node Exporter系统指标收集
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: rag-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - rag-monitoring
    restart: unless-stopped

  # cAdvisor容器指标收集
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: rag-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Blackbox Exporter端点监控
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: rag-blackbox-exporter
    ports:
      - "9115:9115"
    volumes:
      - ./config/blackbox.yml:/etc/blackbox_exporter/config.yml
    command:
      - '--config.file=/etc/blackbox_exporter/config.yml'
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Pushgateway指标推送网关
  pushgateway:
    image: prom/pushgateway:v1.6.0
    container_name: rag-pushgateway
    ports:
      - "9091:9091"
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Jaeger分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: rag-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
      - "6831:6831/udp"  # UDP agent
      - "6832:6832/udp"  # UDP agent
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Loki日志聚合
  loki:
    image: grafana/loki:2.8.0
    container_name: rag-loki
    ports:
      - "3100:3100"
    volumes:
      - ./config/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Promtail日志收集
  promtail:
    image: grafana/promtail:2.8.0
    container_name: rag-promtail
    volumes:
      - ./config/promtail.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Elasticsearch (用于日志存储)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: rag-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - rag-monitoring
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: rag-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      - elasticsearch
    networks:
      - rag-monitoring
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Filebeat (日志收集)
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: rag-filebeat
    user: root
    volumes:
      - ./config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    command: filebeat -e -strict.perms=false
    depends_on:
      - elasticsearch
    networks:
      - rag-monitoring
    restart: unless-stopped

  # Metricbeat (系统指标收集)
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.8.0
    container_name: rag-metricbeat
    user: root
    volumes:
      - ./config/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat_data:/usr/share/metricbeat/data
    command: metricbeat -e -strict.perms=false -system.hostfs=/hostfs
    depends_on:
      - elasticsearch
    networks:
      - rag-monitoring
    restart: unless-stopped

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  loki_data:
    driver: local
  elasticsearch_data:
    driver: local
  filebeat_data:
    driver: local
  metricbeat_data:
    driver: local

networks:
  rag-monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
