# AlertManager配置文件
# 定义告警路由、接收器和抑制规则

global:
  # SMTP配置
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-app-password'
  smtp_require_tls: true

  # Slack配置
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

  # 解决超时时间
  resolve_timeout: 5m

# 模板文件
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  # 默认接收器
  receiver: 'default'
  
  # 分组配置
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h

  # 子路由
  routes:
    # 严重告警立即通知
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      group_interval: 5m
      repeat_interval: 30m

    # 系统告警
    - match:
        category: system
      receiver: 'system-alerts'
      group_interval: 5m
      repeat_interval: 2h

    # 服务告警
    - match:
        category: service
      receiver: 'service-alerts'
      group_interval: 5m
      repeat_interval: 1h

    # 数据库告警
    - match:
        category: database
      receiver: 'database-alerts'
      group_interval: 5m
      repeat_interval: 1h

    # 应用告警
    - match:
        category: application
      receiver: 'application-alerts'
      group_interval: 10m
      repeat_interval: 2h

    # 业务告警
    - match:
        category: business
      receiver: 'business-alerts'
      group_interval: 30m
      repeat_interval: 4h

    # 信息级别告警
    - match:
        severity: info
      receiver: 'info-alerts'
      group_interval: 30m
      repeat_interval: 12h

# 抑制规则
inhibit_rules:
  # 如果有严重告警，抑制相同实例的警告告警
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # 如果服务不可用，抑制该服务的其他告警
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '.*'
    equal: ['job', 'instance']

  # 如果系统负载高，抑制CPU告警
  - source_match:
      alertname: 'HighSystemLoad'
    target_match:
      alertname: 'HighCPUUsage'
    equal: ['instance']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 默认告警'
        body: |
          告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt }}
          {{ end }}

  # 严重告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[RAG系统] 🚨 严重告警'
        body: |
          ⚠️ 严重告警触发！
          
          {{ range .Alerts }}
          🔥 告警名称: {{ .Annotations.summary }}
          📝 告警描述: {{ .Annotations.description }}
          🏷️ 告警标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          ⏰ 告警时间: {{ .StartsAt }}
          {{ if .EndsAt }}🏁 结束时间: {{ .EndsAt }}{{ end }}
          {{ end }}
          
          请立即处理！
    slack_configs:
      - channel: '#alerts-critical'
        title: '🚨 RAG系统严重告警'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *级别*: {{ .Labels.severity }}
          *服务*: {{ .Labels.job }}
          *实例*: {{ .Labels.instance }}
          {{ end }}
        send_resolved: true

  # 系统告警接收器
  - name: 'system-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 🖥️ 系统告警'
        body: |
          系统告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#alerts-system'
        title: '🖥️ 系统告警'
        send_resolved: true

  # 服务告警接收器
  - name: 'service-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 🔧 服务告警'
        body: |
          服务告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          服务: {{ .Labels.job }}
          实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#alerts-service'
        title: '🔧 服务告警'
        send_resolved: true

  # 数据库告警接收器
  - name: 'database-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 🗄️ 数据库告警'
        body: |
          数据库告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          数据库: {{ .Labels.job }}
          实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#alerts-database'
        title: '🗄️ 数据库告警'
        send_resolved: true

  # 应用告警接收器
  - name: 'application-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 📱 应用告警'
        body: |
          应用告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          应用: {{ .Labels.job }}
          实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#alerts-application'
        title: '📱 应用告警'
        send_resolved: true

  # 业务告警接收器
  - name: 'business-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG系统] 📊 业务告警'
        body: |
          业务告警详情:
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          告警时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - channel: '#alerts-business'
        title: '📊 业务告警'
        send_resolved: true

  # 信息告警接收器
  - name: 'info-alerts'
    slack_configs:
      - channel: '#alerts-info'
        title: 'ℹ️ 信息告警'
        send_resolved: true

# 时间窗口配置
time_intervals:
  - name: 'business-hours'
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '18:00'
        weekdays: ['monday:friday']

  - name: 'weekends'
    time_intervals:
      - weekdays: ['saturday', 'sunday']

  - name: 'nights'
    time_intervals:
      - times:
          - start_time: '22:00'
            end_time: '08:00'
