# 系统级别告警规则
# 包含CPU、内存、磁盘、网络等基础设施告警

groups:
  - name: system.rules
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高CPU使用率告警"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: CriticalCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "严重CPU使用率告警"
          description: "实例 {{ $labels.instance }} CPU使用率超过95%，当前值: {{ $value }}%"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高内存使用率告警"
          description: "实例 {{ $labels.instance }} 内存使用率超过80%，当前值: {{ $value }}%"

      - alert: CriticalMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "严重内存使用率告警"
          description: "实例 {{ $labels.instance }} 内存使用率超过95%，当前值: {{ $value }}%"

      # 磁盘使用率告警
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高磁盘使用率告警"
          description: "实例 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过80%，当前值: {{ $value }}%"

      - alert: CriticalDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 2m
        labels:
          severity: critical
          category: system
        annotations:
          summary: "严重磁盘使用率告警"
          description: "实例 {{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率超过95%，当前值: {{ $value }}%"

      # 磁盘IO告警
      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高磁盘IO等待告警"
          description: "实例 {{ $labels.instance }} 磁盘IO等待时间超过20%，当前值: {{ $value }}%"

      # 网络连接数告警
      - alert: HighNetworkConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高网络连接数告警"
          description: "实例 {{ $labels.instance }} TCP连接数超过1000，当前值: {{ $value }}"

      # 系统负载告警
      - alert: HighSystemLoad
        expr: node_load15 / count by(instance) (node_cpu_seconds_total{mode="idle"}) > 0.8
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高系统负载告警"
          description: "实例 {{ $labels.instance }} 15分钟平均负载超过CPU核心数的80%，当前值: {{ $value }}"

      # 文件描述符告警
      - alert: HighFileDescriptorUsage
        expr: (node_filefd_allocated / node_filefd_maximum) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "高文件描述符使用率告警"
          description: "实例 {{ $labels.instance }} 文件描述符使用率超过80%，当前值: {{ $value }}%"

      # 系统重启告警
      - alert: SystemReboot
        expr: node_time_seconds - node_boot_time_seconds < 300
        for: 0m
        labels:
          severity: info
          category: system
        annotations:
          summary: "系统重启告警"
          description: "实例 {{ $labels.instance }} 在5分钟内重启过"

      # 时钟偏移告警
      - alert: ClockSkew
        expr: abs(node_timex_offset_seconds) > 0.05
        for: 2m
        labels:
          severity: warning
          category: system
        annotations:
          summary: "系统时钟偏移告警"
          description: "实例 {{ $labels.instance }} 系统时钟偏移超过50ms，当前值: {{ $value }}s"

  - name: container.rules
    rules:
      # 容器CPU使用率告警
      - alert: HighContainerCPU
        expr: (rate(container_cpu_usage_seconds_total{name!=""}[5m]) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器高CPU使用率告警"
          description: "容器 {{ $labels.name }} CPU使用率超过80%，当前值: {{ $value }}%"

      # 容器内存使用率告警
      - alert: HighContainerMemory
        expr: (container_memory_usage_bytes{name!=""} / container_spec_memory_limit_bytes{name!=""}) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器高内存使用率告警"
          description: "容器 {{ $labels.name }} 内存使用率超过80%，当前值: {{ $value }}%"

      # 容器重启告警
      - alert: ContainerRestart
        expr: increase(container_start_time_seconds{name!=""}[1h]) > 0
        for: 0m
        labels:
          severity: warning
          category: container
        annotations:
          summary: "容器重启告警"
          description: "容器 {{ $labels.name }} 在过去1小时内重启过"

      # 容器退出告警
      - alert: ContainerKilled
        expr: time() - container_last_seen{name!=""} > 60
        for: 0m
        labels:
          severity: critical
          category: container
        annotations:
          summary: "容器退出告警"
          description: "容器 {{ $labels.name }} 已退出超过1分钟"

  - name: network.rules
    rules:
      # 网络接收错误告警
      - alert: HighNetworkReceiveErrors
        expr: rate(node_network_receive_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络接收错误告警"
          description: "实例 {{ $labels.instance }} 网卡 {{ $labels.device }} 接收错误率超过10/s，当前值: {{ $value }}"

      # 网络发送错误告警
      - alert: HighNetworkTransmitErrors
        expr: rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "网络发送错误告警"
          description: "实例 {{ $labels.instance }} 网卡 {{ $labels.device }} 发送错误率超过10/s，当前值: {{ $value }}"

      # 网络带宽使用率告警
      - alert: HighNetworkBandwidth
        expr: rate(node_network_receive_bytes_total[5m]) * 8 / 1024 / 1024 > 100
        for: 5m
        labels:
          severity: warning
          category: network
        annotations:
          summary: "高网络带宽使用告警"
          description: "实例 {{ $labels.instance }} 网卡 {{ $labels.device }} 接收带宽超过100Mbps，当前值: {{ $value }}Mbps"
