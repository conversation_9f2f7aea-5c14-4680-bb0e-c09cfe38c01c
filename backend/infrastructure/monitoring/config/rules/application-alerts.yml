# 应用级别告警规则
# 包含服务可用性、响应时间、错误率等应用指标告警

groups:
  - name: service.rules
    rules:
      # 服务不可用告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          category: service
        annotations:
          summary: "服务不可用告警"
          description: "服务 {{ $labels.job }} 实例 {{ $labels.instance }} 不可用"

      # HTTP服务响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          category: service
        annotations:
          summary: "高响应时间告警"
          description: "服务 {{ $labels.job }} 95%响应时间超过1秒，当前值: {{ $value }}s"

      - alert: CriticalResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          category: service
        annotations:
          summary: "严重响应时间告警"
          description: "服务 {{ $labels.job }} 95%响应时间超过5秒，当前值: {{ $value }}s"

      # HTTP错误率告警
      - alert: HighErrorRate
        expr: (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: service
        annotations:
          summary: "高错误率告警"
          description: "服务 {{ $labels.job }} 5xx错误率超过5%，当前值: {{ $value }}%"

      - alert: CriticalErrorRate
        expr: (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])) * 100 > 20
        for: 2m
        labels:
          severity: critical
          category: service
        annotations:
          summary: "严重错误率告警"
          description: "服务 {{ $labels.job }} 5xx错误率超过20%，当前值: {{ $value }}%"

      # 请求量异常告警
      - alert: LowRequestRate
        expr: rate(http_requests_total[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          category: service
        annotations:
          summary: "低请求量告警"
          description: "服务 {{ $labels.job }} 请求量异常低，当前值: {{ $value }}/s"

      - alert: HighRequestRate
        expr: rate(http_requests_total[5m]) > 1000
        for: 5m
        labels:
          severity: warning
          category: service
        annotations:
          summary: "高请求量告警"
          description: "服务 {{ $labels.job }} 请求量异常高，当前值: {{ $value }}/s"

  - name: database.rules
    rules:
      # PostgreSQL连接数告警
      - alert: HighPostgreSQLConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL高连接数告警"
          description: "PostgreSQL连接数使用率超过80%，当前值: {{ $value }}%"

      # PostgreSQL慢查询告警
      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL慢查询告警"
          description: "PostgreSQL查询效率低，返回行数/获取行数比例: {{ $value }}"

      # Redis内存使用告警
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis高内存使用告警"
          description: "Redis内存使用率超过80%，当前值: {{ $value }}%"

      # Redis连接数告警
      - alert: HighRedisConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis高连接数告警"
          description: "Redis连接数超过100，当前值: {{ $value }}"

      # Redis键过期告警
      - alert: HighRedisKeyExpiration
        expr: rate(redis_expired_keys_total[5m]) > 100
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "Redis高键过期率告警"
          description: "Redis键过期率超过100/s，当前值: {{ $value }}/s"

  - name: application.rules
    rules:
      # 文档处理失败率告警
      - alert: HighDocumentProcessingFailureRate
        expr: (rate(document_processing_failed_total[5m]) / rate(document_processing_total[5m])) * 100 > 10
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "文档处理失败率告警"
          description: "文档处理失败率超过10%，当前值: {{ $value }}%"

      # 向量化处理时间告警
      - alert: HighVectorizationTime
        expr: histogram_quantile(0.95, rate(vectorization_duration_seconds_bucket[5m])) > 30
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "向量化处理时间告警"
          description: "向量化处理95%时间超过30秒，当前值: {{ $value }}s"

      # 检索响应时间告警
      - alert: HighRetrievalTime
        expr: histogram_quantile(0.95, rate(retrieval_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "检索响应时间告警"
          description: "检索95%响应时间超过2秒，当前值: {{ $value }}s"

      # LLM调用失败率告警
      - alert: HighLLMFailureRate
        expr: (rate(llm_requests_failed_total[5m]) / rate(llm_requests_total[5m])) * 100 > 5
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "LLM调用失败率告警"
          description: "LLM调用失败率超过5%，当前值: {{ $value }}%"

      # 对话会话数告警
      - alert: HighActiveSessions
        expr: websocket_active_connections > 1000
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "高活跃会话数告警"
          description: "WebSocket活跃连接数超过1000，当前值: {{ $value }}"

      # 消息队列积压告警
      - alert: HighMessageQueueBacklog
        expr: message_queue_size > 1000
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "消息队列积压告警"
          description: "消息队列积压超过1000条，当前值: {{ $value }}"

  - name: business.rules
    rules:
      # 用户注册异常告警
      - alert: LowUserRegistration
        expr: rate(user_registrations_total[1h]) < 0.01
        for: 2h
        labels:
          severity: info
          category: business
        annotations:
          summary: "用户注册量低告警"
          description: "过去2小时用户注册量异常低，当前值: {{ $value }}/h"

      # 文档上传异常告警
      - alert: LowDocumentUploads
        expr: rate(document_uploads_total[1h]) < 0.1
        for: 2h
        labels:
          severity: info
          category: business
        annotations:
          summary: "文档上传量低告警"
          description: "过去2小时文档上传量异常低，当前值: {{ $value }}/h"

      # 对话创建异常告警
      - alert: LowConversationCreation
        expr: rate(conversations_created_total[1h]) < 1
        for: 2h
        labels:
          severity: info
          category: business
        annotations:
          summary: "对话创建量低告警"
          description: "过去2小时对话创建量异常低，当前值: {{ $value }}/h"

      # 存储空间告警
      - alert: HighStorageUsage
        expr: (storage_used_bytes / storage_total_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "存储空间使用率告警"
          description: "存储空间使用率超过80%，当前值: {{ $value }}%"

      # API配额告警
      - alert: HighAPIQuotaUsage
        expr: (api_quota_used / api_quota_limit) * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "API配额使用率告警"
          description: "API配额使用率超过80%，当前值: {{ $value }}%"
