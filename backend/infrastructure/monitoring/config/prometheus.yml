# Prometheus配置文件
# 定义监控目标、规则和告警配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'rag-system'
    environment: 'production'

# 告警规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 监控目标配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # Node Exporter系统指标
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # cAdvisor容器指标
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Blackbox Exporter端点监控
  - job_name: 'blackbox'
    static_configs:
      - targets: ['blackbox-exporter:9115']
    scrape_interval: 30s
    metrics_path: /metrics

  # Pushgateway批量指标
  - job_name: 'pushgateway'
    static_configs:
      - targets: ['pushgateway:9091']
    scrape_interval: 15s
    metrics_path: /metrics
    honor_labels: true

  # API网关监控
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['host.docker.internal:3001']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 用户服务监控
  - job_name: 'user-service'
    static_configs:
      - targets: ['host.docker.internal:3002']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 文档服务监控
  - job_name: 'document-service'
    static_configs:
      - targets: ['host.docker.internal:3003']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 向量化服务监控
  - job_name: 'vectorization-service'
    static_configs:
      - targets: ['host.docker.internal:3004']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 检索服务监控
  - job_name: 'retrieval-service'
    static_configs:
      - targets: ['host.docker.internal:3005']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 生成服务监控
  - job_name: 'generation-service'
    static_configs:
      - targets: ['host.docker.internal:3006']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 对话服务监控
  - job_name: 'conversation-service'
    static_configs:
      - targets: ['host.docker.internal:3007']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['host.docker.internal:9121']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['host.docker.internal:9187']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 向量数据库监控 (Pinecone/Weaviate/Chroma)
  - job_name: 'vector-db'
    static_configs:
      - targets: ['host.docker.internal:8000']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 15s

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['host.docker.internal:9113']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # 端点健康检查
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://host.docker.internal:3001/health  # API网关
        - http://host.docker.internal:3002/health  # 用户服务
        - http://host.docker.internal:3003/health  # 文档服务
        - http://host.docker.internal:3004/health  # 向量化服务
        - http://host.docker.internal:3005/health  # 检索服务
        - http://host.docker.internal:3006/health  # 生成服务
        - http://host.docker.internal:3007/health  # 对话服务
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # TCP端点检查
  - job_name: 'blackbox-tcp'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - host.docker.internal:5432  # PostgreSQL
        - host.docker.internal:6379  # Redis
        - host.docker.internal:8000  # 向量数据库
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # ICMP ping检查
  - job_name: 'blackbox-icmp'
    metrics_path: /probe
    params:
      module: [icmp]
    static_configs:
      - targets:
        - *******  # Google DNS
        - *******  # Cloudflare DNS
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # 服务发现配置 (可选)
  # - job_name: 'kubernetes-pods'
  #   kubernetes_sd_configs:
  #     - role: pod
  #   relabel_configs:
  #     - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
  #       action: keep
  #       regex: true
  #     - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
  #       action: replace
  #       target_label: __metrics_path__
  #       regex: (.+)

# 远程写入配置 (可选)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"
