# 配置中心基础设施配置
# 包含Consul、Etcd等配置管理服务

version: '3.8'

services:
  # Consul配置中心
  consul:
    image: consul:1.16.1
    container_name: rag-consul
    ports:
      - "8500:8500"    # HTTP API
      - "8600:8600/udp" # DNS
      - "8300:8300"    # Server RPC
      - "8301:8301"    # Serf LAN
      - "8302:8302"    # Serf WAN
    volumes:
      - consul_data:/consul/data
      - ./config/consul.hcl:/consul/config/consul.hcl:ro
      - ./scripts:/scripts:ro
    environment:
      - CONSUL_BIND_INTERFACE=eth0
      - CONSUL_CLIENT_INTERFACE=eth0
    command: >
      consul agent
      -config-file=/consul/config/consul.hcl
      -data-dir=/consul/data
      -ui
      -client=0.0.0.0
      -bind=0.0.0.0
      -bootstrap-expect=1
    networks:
      - rag-config
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "consul", "members"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Etcd配置中心（备选方案）
  etcd:
    image: quay.io/coreos/etcd:v3.5.9
    container_name: rag-etcd
    ports:
      - "2379:2379"  # Client port
      - "2380:2380"  # Peer port
    volumes:
      - etcd_data:/etcd-data
    environment:
      - ETCD_NAME=etcd-server
      - ETCD_DATA_DIR=/etcd-data
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
      - ETCD_ADVERTISE_CLIENT_URLS=http://localhost:2379
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://localhost:2380
      - ETCD_INITIAL_CLUSTER=etcd-server=http://localhost:2380
      - ETCD_INITIAL_CLUSTER_TOKEN=etcd-cluster-token
      - ETCD_INITIAL_CLUSTER_STATE=new
      - ETCD_AUTO_COMPACTION_RETENTION=1
      - ETCD_QUOTA_BACKEND_BYTES=**********
    networks:
      - rag-config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 配置中心服务
  config-service:
    build:
      context: ../../services/config-service
      dockerfile: Dockerfile
    container_name: rag-config-service
    ports:
      - "3009:3009"
    volumes:
      - ./config/app-config.yml:/app/config/app-config.yml:ro
      - config_backups:/app/backups
    environment:
      - NODE_ENV=production
      - PORT=3009
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - ETCD_ENDPOINTS=http://etcd:2379
      - CONFIG_BACKEND=consul
      - LOG_LEVEL=info
    depends_on:
      - consul
      - etcd
    networks:
      - rag-config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3009/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 配置管理界面
  consul-ui:
    image: nginx:1.25-alpine
    container_name: rag-consul-ui
    ports:
      - "8501:80"
    volumes:
      - ./ui/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ui/dist:/usr/share/nginx/html:ro
    depends_on:
      - consul
    networks:
      - rag-config
    restart: unless-stopped

  # 配置同步服务
  config-sync:
    build:
      context: ../../services/config-sync
      dockerfile: Dockerfile
    container_name: rag-config-sync
    volumes:
      - ./config/sync-config.yml:/app/config/sync-config.yml:ro
      - config_backups:/app/backups
    environment:
      - NODE_ENV=production
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - SYNC_INTERVAL=30
      - BACKUP_ENABLED=true
      - BACKUP_RETENTION_DAYS=30
    depends_on:
      - consul
      - config-service
    networks:
      - rag-config
    restart: unless-stopped

  # 配置验证服务
  config-validator:
    build:
      context: ../../services/config-validator
      dockerfile: Dockerfile
    container_name: rag-config-validator
    ports:
      - "3010:3010"
    volumes:
      - ./schemas:/app/schemas:ro
    environment:
      - NODE_ENV=production
      - PORT=3010
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
    depends_on:
      - consul
    networks:
      - rag-config
    restart: unless-stopped

  # 配置监控
  config-monitor:
    image: prom/prometheus:v2.45.0
    container_name: rag-config-monitor
    ports:
      - "9092:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_config_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    networks:
      - rag-config
    restart: unless-stopped

  # 配置告警
  config-alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: rag-config-alertmanager
    ports:
      - "9094:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_config_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9094'
    networks:
      - rag-config
    restart: unless-stopped

  # 配置备份服务
  config-backup:
    image: alpine:3.18
    container_name: rag-config-backup
    volumes:
      - config_backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    environment:
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
      - BACKUP_RETENTION_DAYS=30
      - S3_BUCKET=${BACKUP_S3_BUCKET:-}
      - S3_ACCESS_KEY=${BACKUP_S3_ACCESS_KEY:-}
      - S3_SECRET_KEY=${BACKUP_S3_SECRET_KEY:-}
    depends_on:
      - consul
    networks:
      - rag-config
    restart: unless-stopped
    command: >
      sh -c "
        apk add --no-cache curl jq aws-cli &&
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /backup.sh' | crontab - &&
        crond -f
      "

volumes:
  consul_data:
    driver: local
  etcd_data:
    driver: local
  config_backups:
    driver: local
  prometheus_config_data:
    driver: local
  alertmanager_config_data:
    driver: local

networks:
  rag-config:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
