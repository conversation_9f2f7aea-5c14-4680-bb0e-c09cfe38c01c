#!/usr/bin/env python3
"""
配置中心管理工具
提供配置的增删改查、备份恢复、监控等功能
"""

import os
import sys
import json
import yaml
import time
import argparse
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import consul
import etcd3

class ConfigManager:
    """配置中心管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.consul_client = None
        self.etcd_client = None
        self._init_clients()
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "backend": "consul",
            "consul": {
                "host": "localhost",
                "port": 8500,
                "token": None
            },
            "etcd": {
                "host": "localhost",
                "port": 2379
            },
            "service": {
                "url": "http://localhost:3009"
            }
        }
        
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    config = yaml.safe_load(f)
                else:
                    config = json.load(f)
            # 合并默认配置
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
            return config
        
        return default_config
    
    def _init_clients(self):
        """初始化客户端"""
        if self.config['backend'] == 'consul':
            self.consul_client = consul.Consul(
                host=self.config['consul']['host'],
                port=self.config['consul']['port'],
                token=self.config['consul']['token']
            )
        elif self.config['backend'] == 'etcd':
            self.etcd_client = etcd3.client(
                host=self.config['etcd']['host'],
                port=self.config['etcd']['port']
            )
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.config['backend'] == 'consul':
                # 检查Consul连接
                self.consul_client.agent.self()
                print("✅ Consul连接正常")
                return True
            elif self.config['backend'] == 'etcd':
                # 检查Etcd连接
                self.etcd_client.status()
                print("✅ Etcd连接正常")
                return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_config(self, key: str, namespace: str = 'default') -> Optional[Dict[str, Any]]:
        """获取配置"""
        try:
            if self.config['backend'] == 'consul':
                full_key = f"{namespace}/{key}"
                index, data = self.consul_client.kv.get(full_key)
                if data:
                    return {
                        'key': key,
                        'namespace': namespace,
                        'value': data['Value'].decode('utf-8'),
                        'modify_index': data['ModifyIndex'],
                        'create_index': data['CreateIndex']
                    }
            elif self.config['backend'] == 'etcd':
                full_key = f"/{namespace}/{key}"
                value, metadata = self.etcd_client.get(full_key)
                if value:
                    return {
                        'key': key,
                        'namespace': namespace,
                        'value': value.decode('utf-8'),
                        'version': metadata.version,
                        'create_revision': metadata.create_revision,
                        'mod_revision': metadata.mod_revision
                    }
            return None
        except Exception as e:
            print(f"❌ 获取配置失败: {e}")
            return None
    
    def set_config(self, key: str, value: str, namespace: str = 'default') -> bool:
        """设置配置"""
        try:
            if self.config['backend'] == 'consul':
                full_key = f"{namespace}/{key}"
                return self.consul_client.kv.put(full_key, value)
            elif self.config['backend'] == 'etcd':
                full_key = f"/{namespace}/{key}"
                self.etcd_client.put(full_key, value)
                return True
        except Exception as e:
            print(f"❌ 设置配置失败: {e}")
            return False
    
    def delete_config(self, key: str, namespace: str = 'default') -> bool:
        """删除配置"""
        try:
            if self.config['backend'] == 'consul':
                full_key = f"{namespace}/{key}"
                return self.consul_client.kv.delete(full_key)
            elif self.config['backend'] == 'etcd':
                full_key = f"/{namespace}/{key}"
                return self.etcd_client.delete(full_key)
        except Exception as e:
            print(f"❌ 删除配置失败: {e}")
            return False
    
    def list_configs(self, namespace: str = 'default', prefix: str = '') -> List[Dict[str, Any]]:
        """列出配置"""
        try:
            configs = []
            if self.config['backend'] == 'consul':
                search_key = f"{namespace}/{prefix}" if prefix else f"{namespace}/"
                index, data = self.consul_client.kv.get(search_key, recurse=True)
                if data:
                    for item in data:
                        key_parts = item['Key'].split('/', 1)
                        if len(key_parts) == 2:
                            configs.append({
                                'key': key_parts[1],
                                'namespace': key_parts[0],
                                'value': item['Value'].decode('utf-8'),
                                'modify_index': item['ModifyIndex'],
                                'create_index': item['CreateIndex']
                            })
            elif self.config['backend'] == 'etcd':
                search_key = f"/{namespace}/{prefix}" if prefix else f"/{namespace}/"
                for value, metadata in self.etcd_client.get_prefix(search_key):
                    key_parts = metadata.key.decode('utf-8').strip('/').split('/', 1)
                    if len(key_parts) == 2:
                        configs.append({
                            'key': key_parts[1],
                            'namespace': key_parts[0],
                            'value': value.decode('utf-8'),
                            'version': metadata.version,
                            'create_revision': metadata.create_revision,
                            'mod_revision': metadata.mod_revision
                        })
            return configs
        except Exception as e:
            print(f"❌ 列出配置失败: {e}")
            return []
    
    def watch_config(self, key: str, namespace: str = 'default', callback=None):
        """监听配置变更"""
        try:
            if self.config['backend'] == 'consul':
                full_key = f"{namespace}/{key}"
                index = None
                print(f"🔍 开始监听配置变更: {full_key}")
                
                while True:
                    try:
                        new_index, data = self.consul_client.kv.get(full_key, index=index)
                        if new_index != index:
                            index = new_index
                            if data:
                                value = data['Value'].decode('utf-8')
                                print(f"📝 配置已更新: {full_key} = {value}")
                                if callback:
                                    callback(key, namespace, value)
                            else:
                                print(f"🗑️ 配置已删除: {full_key}")
                                if callback:
                                    callback(key, namespace, None)
                        time.sleep(1)
                    except KeyboardInterrupt:
                        print("\n监听已停止")
                        break
                        
            elif self.config['backend'] == 'etcd':
                full_key = f"/{namespace}/{key}"
                print(f"🔍 开始监听配置变更: {full_key}")
                
                events_iterator, cancel = self.etcd_client.watch(full_key)
                try:
                    for event in events_iterator:
                        if event.type == etcd3.events.PutEvent:
                            value = event.value.decode('utf-8')
                            print(f"📝 配置已更新: {full_key} = {value}")
                            if callback:
                                callback(key, namespace, value)
                        elif event.type == etcd3.events.DeleteEvent:
                            print(f"🗑️ 配置已删除: {full_key}")
                            if callback:
                                callback(key, namespace, None)
                except KeyboardInterrupt:
                    print("\n监听已停止")
                    cancel()
                    
        except Exception as e:
            print(f"❌ 监听配置失败: {e}")
    
    def backup_configs(self, output_file: str, namespace: str = None):
        """备份配置"""
        try:
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'backend': self.config['backend'],
                'configs': {}
            }
            
            if namespace:
                namespaces = [namespace]
            else:
                # 获取所有命名空间
                namespaces = self._get_all_namespaces()
            
            for ns in namespaces:
                configs = self.list_configs(ns)
                backup_data['configs'][ns] = configs
            
            with open(output_file, 'w') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            total_configs = sum(len(configs) for configs in backup_data['configs'].values())
            print(f"✅ 备份完成: {output_file}")
            print(f"   命名空间: {len(backup_data['configs'])}")
            print(f"   配置项: {total_configs}")
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
    
    def restore_configs(self, backup_file: str, namespace: str = None, dry_run: bool = False):
        """恢复配置"""
        try:
            with open(backup_file, 'r') as f:
                backup_data = json.load(f)
            
            print(f"📂 加载备份文件: {backup_file}")
            print(f"   备份时间: {backup_data['timestamp']}")
            print(f"   后端类型: {backup_data['backend']}")
            
            if dry_run:
                print("🔍 预演模式，不会实际修改配置")
            
            restored_count = 0
            for ns, configs in backup_data['configs'].items():
                if namespace and ns != namespace:
                    continue
                
                print(f"\n📁 恢复命名空间: {ns}")
                for config in configs:
                    key = config['key']
                    value = config['value']
                    
                    if dry_run:
                        print(f"   [预演] {key} = {value}")
                    else:
                        if self.set_config(key, value, ns):
                            print(f"   ✅ {key}")
                            restored_count += 1
                        else:
                            print(f"   ❌ {key}")
            
            if not dry_run:
                print(f"\n✅ 恢复完成，共恢复 {restored_count} 个配置项")
            
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
    
    def _get_all_namespaces(self) -> List[str]:
        """获取所有命名空间"""
        namespaces = set()
        try:
            if self.config['backend'] == 'consul':
                index, data = self.consul_client.kv.get('', recurse=True)
                if data:
                    for item in data:
                        parts = item['Key'].split('/', 1)
                        if len(parts) >= 1:
                            namespaces.add(parts[0])
            elif self.config['backend'] == 'etcd':
                for value, metadata in self.etcd_client.get_prefix('/'):
                    parts = metadata.key.decode('utf-8').strip('/').split('/', 1)
                    if len(parts) >= 1:
                        namespaces.add(parts[0])
        except Exception as e:
            print(f"❌ 获取命名空间失败: {e}")
        
        return list(namespaces)
    
    def get_stats(self):
        """获取统计信息"""
        try:
            namespaces = self._get_all_namespaces()
            stats = {
                'namespaces': len(namespaces),
                'total_configs': 0,
                'namespace_details': {}
            }
            
            for ns in namespaces:
                configs = self.list_configs(ns)
                stats['namespace_details'][ns] = len(configs)
                stats['total_configs'] += len(configs)
            
            print("📊 配置统计信息:")
            print(f"   命名空间数量: {stats['namespaces']}")
            print(f"   配置项总数: {stats['total_configs']}")
            print("\n📁 各命名空间配置数量:")
            for ns, count in stats['namespace_details'].items():
                print(f"   {ns}: {count}")
            
            return stats
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='配置中心管理工具')
    parser.add_argument('--config', '-c', help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 健康检查
    subparsers.add_parser('health', help='健康检查')
    
    # 获取配置
    get_parser = subparsers.add_parser('get', help='获取配置')
    get_parser.add_argument('key', help='配置键')
    get_parser.add_argument('--namespace', '-n', default='default', help='命名空间')
    
    # 设置配置
    set_parser = subparsers.add_parser('set', help='设置配置')
    set_parser.add_argument('key', help='配置键')
    set_parser.add_argument('value', help='配置值')
    set_parser.add_argument('--namespace', '-n', default='default', help='命名空间')
    
    # 删除配置
    del_parser = subparsers.add_parser('delete', help='删除配置')
    del_parser.add_argument('key', help='配置键')
    del_parser.add_argument('--namespace', '-n', default='default', help='命名空间')
    
    # 列出配置
    list_parser = subparsers.add_parser('list', help='列出配置')
    list_parser.add_argument('--namespace', '-n', default='default', help='命名空间')
    list_parser.add_argument('--prefix', '-p', default='', help='键前缀')
    
    # 监听配置
    watch_parser = subparsers.add_parser('watch', help='监听配置变更')
    watch_parser.add_argument('key', help='配置键')
    watch_parser.add_argument('--namespace', '-n', default='default', help='命名空间')
    
    # 备份配置
    backup_parser = subparsers.add_parser('backup', help='备份配置')
    backup_parser.add_argument('--output', '-o', required=True, help='输出文件')
    backup_parser.add_argument('--namespace', '-n', help='命名空间（不指定则备份所有）')
    
    # 恢复配置
    restore_parser = subparsers.add_parser('restore', help='恢复配置')
    restore_parser.add_argument('--input', '-i', required=True, help='备份文件')
    restore_parser.add_argument('--namespace', '-n', help='命名空间（不指定则恢复所有）')
    restore_parser.add_argument('--dry-run', action='store_true', help='预演模式')
    
    # 统计信息
    subparsers.add_parser('stats', help='获取统计信息')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建管理器
    manager = ConfigManager(args.config)
    
    try:
        if args.command == 'health':
            manager.health_check()
        
        elif args.command == 'get':
            config = manager.get_config(args.key, args.namespace)
            if config:
                print(json.dumps(config, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 配置不存在: {args.namespace}/{args.key}")
        
        elif args.command == 'set':
            if manager.set_config(args.key, args.value, args.namespace):
                print(f"✅ 配置已设置: {args.namespace}/{args.key} = {args.value}")
            else:
                print(f"❌ 设置配置失败: {args.namespace}/{args.key}")
        
        elif args.command == 'delete':
            if manager.delete_config(args.key, args.namespace):
                print(f"✅ 配置已删除: {args.namespace}/{args.key}")
            else:
                print(f"❌ 删除配置失败: {args.namespace}/{args.key}")
        
        elif args.command == 'list':
            configs = manager.list_configs(args.namespace, args.prefix)
            if configs:
                print(json.dumps(configs, indent=2, ensure_ascii=False))
            else:
                print(f"❌ 未找到配置: {args.namespace}/{args.prefix}")
        
        elif args.command == 'watch':
            manager.watch_config(args.key, args.namespace)
        
        elif args.command == 'backup':
            manager.backup_configs(args.output, args.namespace)
        
        elif args.command == 'restore':
            manager.restore_configs(args.input, args.namespace, args.dry_run)
        
        elif args.command == 'stats':
            manager.get_stats()
    
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
