# Consul配置文件
# 配置Consul服务器的基本设置

# 数据中心名称
datacenter = "rag-dc1"

# 节点名称
node_name = "consul-server-1"

# 服务器模式
server = true

# 引导期望的服务器数量
bootstrap_expect = 1

# 数据目录
data_dir = "/consul/data"

# 日志级别
log_level = "INFO"

# 启用UI
ui_config {
  enabled = true
}

# 客户端地址
client_addr = "0.0.0.0"

# 绑定地址
bind_addr = "0.0.0.0"

# 连接配置
connect {
  enabled = true
}

# 端口配置
ports {
  grpc = 8502
  http = 8500
  https = -1
  dns = 8600
}

# ACL配置
acl = {
  enabled = true
  default_policy = "allow"
  enable_token_persistence = true
}

# 性能配置
performance {
  raft_multiplier = 1
}

# 限制配置
limits {
  http_max_conns_per_client = 200
  https_handshake_timeout = "5s"
  rpc_handshake_timeout = "5s"
  rpc_max_conns_per_client = 100
}

# 日志配置
logging {
  log_json = true
  enable_syslog = false
}

# 监控配置
telemetry {
  prometheus_retention_time = "60s"
  disable_hostname = false
  metrics_prefix = "consul"
}

# 自动重新加载配置
auto_reload_config = true

# 启用脚本检查
enable_script_checks = false

# 启用本地脚本检查
enable_local_script_checks = false

# 禁用远程执行
disable_remote_exec = true

# 禁用更新检查
disable_update_check = true

# 启用中央配置
enable_central_service_config = true

# 配置条目
config_entries {
  bootstrap = [
    {
      kind = "proxy-defaults"
      name = "global"
      config = {
        protocol = "http"
      }
    }
  ]
}

# 服务发现配置
services {
  name = "consul"
  tags = ["config-center", "service-discovery"]
  port = 8500
  check {
    http = "http://localhost:8500/v1/status/leader"
    interval = "10s"
  }
}

# 监控服务
services {
  name = "consul-metrics"
  tags = ["metrics", "monitoring"]
  port = 8500
  check {
    http = "http://localhost:8500/v1/agent/metrics?format=prometheus"
    interval = "30s"
  }
}
