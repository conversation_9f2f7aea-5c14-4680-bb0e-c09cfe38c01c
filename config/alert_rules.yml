# Prometheus告警规则 - RAG检索系统增强监控

groups:
  # 基础性能告警
  - name: basic_performance.rules
    rules:
      - alert: HighSearchLatency
        expr: histogram_quantile(0.95, rate(search_duration_seconds_bucket[5m])) > 2.0
        for: 2m
        labels:
          severity: warning
          service: rag-retrieval
        annotations:
          summary: "搜索延迟过高"
          description: "95%分位数搜索延迟超过2秒，当前值: {{ $value }}秒"

      - alert: HighErrorRate
        expr: rate(search_requests_total{status="error"}[5m]) / rate(search_requests_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
          service: rag-retrieval
        annotations:
          summary: "搜索错误率过高"
          description: "搜索错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      - alert: LowUserSatisfaction
        expr: avg_over_time(user_satisfaction_score[10m]) < 3.0
        for: 5m
        labels:
          severity: warning
          service: rag-retrieval
        annotations:
          summary: "用户满意度过低"
          description: "平均用户满意度低于3.0，当前值: {{ $value }}"

  # Self-RAG特定告警
  - name: self_rag.rules
    rules:
      - alert: SelfRAGHighIterations
        expr: histogram_quantile(0.90, rate(self_rag_iterations_bucket[5m])) > 4
        for: 3m
        labels:
          severity: warning
          technology: self-rag
        annotations:
          summary: "Self-RAG迭代次数过多"
          description: "90%的Self-RAG查询需要超过4次迭代，可能需要调整置信度阈值"

      - alert: SelfRAGLowConfidence
        expr: avg_over_time(self_rag_confidence[10m]) < 0.6
        for: 5m
        labels:
          severity: warning
          technology: self-rag
        annotations:
          summary: "Self-RAG置信度过低"
          description: "Self-RAG平均置信度低于0.6，当前值: {{ $value }}"

      - alert: SelfRAGHighLatency
        expr: histogram_quantile(0.95, rate(search_duration_seconds_bucket{search_type="self_rag_search"}[5m])) > 5.0
        for: 2m
        labels:
          severity: critical
          technology: self-rag
        annotations:
          summary: "Self-RAG检索延迟过高"
          description: "Self-RAG检索95%分位数延迟超过5秒，当前值: {{ $value }}秒"

  # 多向量检索告警
  - name: multi_vector.rules
    rules:
      - alert: MultiVectorLowDiversity
        expr: avg_over_time(multi_vector_fusion_score[10m]) < 0.5
        for: 5m
        labels:
          severity: warning
          technology: multi-vector
        annotations:
          summary: "多向量检索多样性过低"
          description: "多向量融合分数过低，可能影响检索多样性，当前值: {{ $value }}"

      - alert: AspectCoverageLow
        expr: min(aspect_coverage) < 0.3
        for: 5m
        labels:
          severity: warning
          technology: multi-vector
        annotations:
          summary: "方面覆盖率过低"
          description: "某些方面的覆盖率过低，方面: {{ $labels.aspect_name }}，覆盖率: {{ $value }}"

  # 中文优化告警
  - name: chinese_optimization.rules
    rules:
      - alert: ChineseSegmentationLowConfidence
        expr: avg_over_time(chinese_segmentation_confidence[10m]) < 0.7
        for: 5m
        labels:
          severity: warning
          technology: chinese-optimization
        annotations:
          summary: "中文分词置信度过低"
          description: "中文分词平均置信度低于0.7，分词器: {{ $labels.segmenter }}，当前值: {{ $value }}"

      - alert: ChineseSRLLowConfidence
        expr: avg_over_time(chinese_srl_confidence[10m]) < 0.6
        for: 5m
        labels:
          severity: warning
          technology: chinese-optimization
        annotations:
          summary: "中文语义角色标注置信度过低"
          description: "中文SRL平均置信度低于0.6，角色类型: {{ $labels.role_type }}，当前值: {{ $value }}"

  # 困难负样本挖掘告警
  - name: hard_negative_mining.rules
    rules:
      - alert: HardNegativeMiningHighLatency
        expr: histogram_quantile(0.95, rate(hard_negative_mining_duration_seconds_bucket[5m])) > 3.0
        for: 2m
        labels:
          severity: warning
          technology: hard-negative-mining
        annotations:
          summary: "困难负样本挖掘耗时过长"
          description: "困难负样本挖掘95%分位数耗时超过3秒，当前值: {{ $value }}秒"

      - alert: HardNegativeLowQuality
        expr: avg_over_time(hard_negative_quality[10m]) < 0.6
        for: 5m
        labels:
          severity: warning
          technology: hard-negative-mining
        annotations:
          summary: "困难负样本质量过低"
          description: "困难负样本平均质量分数低于0.6，当前值: {{ $value }}"

      - alert: ContrastiveLearningHighLoss
        expr: contrastive_training_loss > 2.0
        for: 3m
        labels:
          severity: warning
          technology: contrastive-learning
        annotations:
          summary: "对比学习训练损失过高"
          description: "对比学习训练损失超过2.0，可能需要调整学习率或模型参数"

  # 系统资源告警
  - name: system_resources.rules
    rules:
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
          component: system
        annotations:
          summary: "CPU使用率过高"
          description: "系统CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 85
        for: 3m
        labels:
          severity: critical
          component: system
        annotations:
          summary: "内存使用率过高"
          description: "系统内存使用率超过85%，当前值: {{ $value }}%"

      - alert: LowCacheHitRate
        expr: cache_hit_rate < 0.7
        for: 5m
        labels:
          severity: warning
          component: cache
        annotations:
          summary: "缓存命中率过低"
          description: "缓存命中率低于70%，缓存类型: {{ $labels.cache_type }}，当前值: {{ $value | humanizePercentage }}"

  # A/B测试告警
  - name: ab_testing.rules
    rules:
      - alert: ABTestLowSampleSize
        expr: increase(ab_test_assignments_total[1h]) < 50
        for: 30m
        labels:
          severity: warning
          component: ab-testing
        annotations:
          summary: "A/B测试样本量过小"
          description: "实验 {{ $labels.experiment }} 在过去1小时内分组数量少于50，可能影响统计显著性"

      - alert: ABTestHighVariance
        expr: |
          (
            rate(ab_test_conversion_total{group="treatment"}[1h]) - 
            rate(ab_test_conversion_total{group="control"}[1h])
          ) / rate(ab_test_conversion_total{group="control"}[1h]) > 0.5
        for: 15m
        labels:
          severity: info
          component: ab-testing
        annotations:
          summary: "A/B测试效果显著"
          description: "实验 {{ $labels.experiment }} 显示显著效果差异，建议关注"

  # 数据库告警
  - name: database.rules
    rules:
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends > 80
        for: 2m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接数超过80，当前值: {{ $value }}"

      - alert: VectorIndexPerformanceDegraded
        expr: rate(pg_stat_user_indexes_idx_scan[5m]) < 10
        for: 5m
        labels:
          severity: warning
          component: vector-index
        annotations:
          summary: "向量索引性能下降"
          description: "向量索引扫描频率过低，可能需要重建索引"

  # 业务指标告警
  - name: business_metrics.rules
    rules:
      - alert: SearchVolumeDropped
        expr: rate(search_requests_total[5m]) < 0.5
        for: 10m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "搜索量显著下降"
          description: "搜索请求频率低于每秒0.5次，可能存在业务问题"

      - alert: ZeroResultsHigh
        expr: rate(search_result_count_bucket{le="0"}[5m]) / rate(search_result_count_count[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          component: business
        annotations:
          summary: "零结果查询比例过高"
          description: "零结果查询比例超过10%，可能需要优化索引或查询处理"
