# Prometheus配置文件 - RAG检索系统增强监控

global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # RAG检索服务监控
  - job_name: 'rag-retrieval-service'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # 增强检索技术监控
  - job_name: 'enhanced-retrieval-metrics'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/advanced/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # 数据库监控
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['localhost:9187']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']
    scrape_interval: 30s

  # 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 30s

  # 应用程序监控
  - job_name: 'rag-application'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/health/metrics'
    scrape_interval: 20s
    honor_timestamps: true
    
    # 自定义标签
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:8000

  # A/B测试监控
  - job_name: 'ab-testing-metrics'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/advanced/ab-testing/metrics'
    scrape_interval: 60s
    
    # 实验特定标签
    metric_relabel_configs:
      - source_labels: [experiment]
        target_label: ab_experiment
      - source_labels: [group]
        target_label: ab_group

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-prometheus:9090/api/v1/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-prometheus:9090/api/v1/read"
