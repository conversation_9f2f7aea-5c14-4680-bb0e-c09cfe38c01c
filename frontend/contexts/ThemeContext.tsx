'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

// 主题类型
export type Theme = 'light' | 'dark' | 'system'

// 主题上下文接口
interface ThemeContextType {
  theme: Theme
  actualTheme: 'light' | 'dark'
  setTheme: (theme: Theme) => void
  toggleTheme: () => void
}

// 创建上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// 主题提供者组件
interface ThemeProviderProps {
  children: ReactNode
  defaultTheme?: Theme
}

export function ThemeProvider({ children, defaultTheme = 'system' }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme)
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light')

  // 获取系统主题
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window === 'undefined') return 'light'
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  // 计算实际主题
  const calculateActualTheme = (currentTheme: Theme): 'light' | 'dark' => {
    if (currentTheme === 'system') {
      return getSystemTheme()
    }
    return currentTheme
  }

  // 应用主题到DOM
  const applyTheme = (newActualTheme: 'light' | 'dark') => {
    const root = document.documentElement
    
    if (newActualTheme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    // 更新meta标签
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', newActualTheme === 'dark' ? '#1f2937' : '#3b82f6')
    }
  }

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme)
    
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme)
    }
    
    // 计算并应用实际主题
    const newActualTheme = calculateActualTheme(newTheme)
    setActualTheme(newActualTheme)
    applyTheme(newActualTheme)
  }

  // 切换主题
  const toggleTheme = () => {
    const newTheme = actualTheme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  // 初始化主题
  useEffect(() => {
    // 从localStorage读取保存的主题
    const savedTheme = localStorage.getItem('theme') as Theme
    const initialTheme = savedTheme || defaultTheme
    
    setThemeState(initialTheme)
    
    // 计算实际主题
    const initialActualTheme = calculateActualTheme(initialTheme)
    setActualTheme(initialActualTheme)
    applyTheme(initialActualTheme)
  }, [defaultTheme])

  // 监听系统主题变化
  useEffect(() => {
    if (theme !== 'system') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleChange = (e: MediaQueryListEvent) => {
      const newActualTheme = e.matches ? 'dark' : 'light'
      setActualTheme(newActualTheme)
      applyTheme(newActualTheme)
    }

    mediaQuery.addEventListener('change', handleChange)
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme])

  // 监听主题变化，更新实际主题
  useEffect(() => {
    const newActualTheme = calculateActualTheme(theme)
    if (newActualTheme !== actualTheme) {
      setActualTheme(newActualTheme)
      applyTheme(newActualTheme)
    }
  }, [theme, actualTheme])

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

// 使用主题上下文的Hook
export function useTheme() {
  const context = useContext(ThemeContext)
  
  if (context === undefined) {
    throw new Error('useTheme必须在ThemeProvider内部使用')
  }
  
  return context
}

// 主题切换Hook
export function useThemeToggle() {
  const { theme, actualTheme, toggleTheme } = useTheme()
  
  return {
    isDark: actualTheme === 'dark',
    isSystem: theme === 'system',
    toggle: toggleTheme,
  }
}
