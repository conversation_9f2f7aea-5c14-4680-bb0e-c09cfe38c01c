'use client'

import { createContext, useContext, useReducer, useEffect, ReactNode, useCallback } from 'react'
import { useSocket, useSocketEvent } from './SocketContext'
import { useAuth } from './AuthContext'
import { conversationApi } from '@/lib/api/conversation'
import toast from 'react-hot-toast'

// 消息接口
export interface Message {
  id: string
  conversationId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  contentType: 'text' | 'markdown' | 'code'
  metadata?: Record<string, any>
  parentMessageId?: string
  createdAt: string
  updatedAt: string
  isDeleted: boolean
  isLoading?: boolean
  error?: string
}

// 对话接口
export interface Conversation {
  id: string
  userId: string
  title: string
  status: 'active' | 'archived' | 'deleted'
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
  lastMessageAt?: string
  messageCount: number
  messages?: Message[]
}

// 对话状态接口
interface ConversationState {
  conversations: Conversation[]
  currentConversation: Conversation | null
  messages: Message[]
  isLoading: boolean
  isLoadingMessages: boolean
  isSending: boolean
  error: string | null
  typingUsers: string[]
}

// 对话动作类型
type ConversationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_LOADING_MESSAGES'; payload: boolean }
  | { type: 'SET_SENDING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'ADD_CONVERSATION'; payload: Conversation }
  | { type: 'UPDATE_CONVERSATION'; payload: Conversation }
  | { type: 'DELETE_CONVERSATION'; payload: string }
  | { type: 'SET_CURRENT_CONVERSATION'; payload: Conversation | null }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'UPDATE_MESSAGE'; payload: Message }
  | { type: 'DELETE_MESSAGE'; payload: string }
  | { type: 'SET_TYPING_USERS'; payload: string[] }
  | { type: 'ADD_TYPING_USER'; payload: string }
  | { type: 'REMOVE_TYPING_USER'; payload: string }

// 初始状态
const initialState: ConversationState = {
  conversations: [],
  currentConversation: null,
  messages: [],
  isLoading: false,
  isLoadingMessages: false,
  isSending: false,
  error: null,
  typingUsers: [],
}

// 状态reducer
function conversationReducer(state: ConversationState, action: ConversationAction): ConversationState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    
    case 'SET_LOADING_MESSAGES':
      return { ...state, isLoadingMessages: action.payload }
    
    case 'SET_SENDING':
      return { ...state, isSending: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload }
    
    case 'ADD_CONVERSATION':
      return {
        ...state,
        conversations: [action.payload, ...state.conversations],
      }
    
    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id ? action.payload : conv
        ),
        currentConversation: state.currentConversation?.id === action.payload.id
          ? action.payload
          : state.currentConversation,
      }
    
    case 'DELETE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.filter(conv => conv.id !== action.payload),
        currentConversation: state.currentConversation?.id === action.payload
          ? null
          : state.currentConversation,
      }
    
    case 'SET_CURRENT_CONVERSATION':
      return { ...state, currentConversation: action.payload }
    
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload }
    
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      }
    
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg =>
          msg.id === action.payload.id ? action.payload : msg
        ),
      }
    
    case 'DELETE_MESSAGE':
      return {
        ...state,
        messages: state.messages.filter(msg => msg.id !== action.payload),
      }
    
    case 'SET_TYPING_USERS':
      return { ...state, typingUsers: action.payload }
    
    case 'ADD_TYPING_USER':
      return {
        ...state,
        typingUsers: state.typingUsers.includes(action.payload)
          ? state.typingUsers
          : [...state.typingUsers, action.payload],
      }
    
    case 'REMOVE_TYPING_USER':
      return {
        ...state,
        typingUsers: state.typingUsers.filter(userId => userId !== action.payload),
      }
    
    default:
      return state
  }
}

// 对话上下文接口
interface ConversationContextType extends ConversationState {
  // 对话操作
  loadConversations: () => Promise<void>
  createConversation: (title?: string) => Promise<Conversation>
  updateConversation: (id: string, data: Partial<Conversation>) => Promise<void>
  deleteConversation: (id: string) => Promise<void>
  setCurrentConversation: (conversation: Conversation | null) => void
  
  // 消息操作
  loadMessages: (conversationId: string) => Promise<void>
  sendMessage: (content: string, type?: string) => Promise<void>
  updateMessage: (id: string, data: Partial<Message>) => Promise<void>
  deleteMessage: (id: string) => Promise<void>
  
  // 实时功能
  startTyping: () => void
  stopTyping: () => void
}

// 创建上下文
const ConversationContext = createContext<ConversationContextType | undefined>(undefined)

// 对话提供者组件
interface ConversationProviderProps {
  children: ReactNode
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  const [state, dispatch] = useReducer(conversationReducer, initialState)
  const { user, isAuthenticated } = useAuth()
  const { isConnected, joinConversation, leaveConversation, sendMessage: socketSendMessage, sendTyping } = useSocket()

  // 加载对话列表
  const loadConversations = useCallback(async () => {
    if (!isAuthenticated) return

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      const conversations = await conversationApi.getConversations()
      dispatch({ type: 'SET_CONVERSATIONS', payload: conversations })
    } catch (error: any) {
      const message = error.response?.data?.message || '加载对话失败'
      dispatch({ type: 'SET_ERROR', payload: message })
      toast.error(message)
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [isAuthenticated])

  // 创建对话
  const createConversation = useCallback(async (title?: string): Promise<Conversation> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null })
      
      const conversation = await conversationApi.createConversation({ title })
      dispatch({ type: 'ADD_CONVERSATION', payload: conversation })
      
      return conversation
    } catch (error: any) {
      const message = error.response?.data?.message || '创建对话失败'
      dispatch({ type: 'SET_ERROR', payload: message })
      toast.error(message)
      throw error
    }
  }, [])

  // 更新对话
  const updateConversation = useCallback(async (id: string, data: Partial<Conversation>) => {
    try {
      const conversation = await conversationApi.updateConversation(id, data)
      dispatch({ type: 'UPDATE_CONVERSATION', payload: conversation })
    } catch (error: any) {
      const message = error.response?.data?.message || '更新对话失败'
      toast.error(message)
      throw error
    }
  }, [])

  // 删除对话
  const deleteConversation = useCallback(async (id: string) => {
    try {
      await conversationApi.deleteConversation(id)
      dispatch({ type: 'DELETE_CONVERSATION', payload: id })
      toast.success('对话已删除')
    } catch (error: any) {
      const message = error.response?.data?.message || '删除对话失败'
      toast.error(message)
      throw error
    }
  }, [])

  // 设置当前对话
  const setCurrentConversation = useCallback((conversation: Conversation | null) => {
    // 离开之前的对话
    if (state.currentConversation && isConnected) {
      leaveConversation(state.currentConversation.id)
    }
    
    // 设置新对话
    dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: conversation })
    
    // 加入新对话
    if (conversation && isConnected) {
      joinConversation(conversation.id)
    }
    
    // 清空消息
    dispatch({ type: 'SET_MESSAGES', payload: [] })
  }, [state.currentConversation, isConnected, leaveConversation, joinConversation])

  // 加载消息
  const loadMessages = useCallback(async (conversationId: string) => {
    try {
      dispatch({ type: 'SET_LOADING_MESSAGES', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })
      
      const messages = await conversationApi.getMessages(conversationId)
      dispatch({ type: 'SET_MESSAGES', payload: messages })
    } catch (error: any) {
      const message = error.response?.data?.message || '加载消息失败'
      dispatch({ type: 'SET_ERROR', payload: message })
      toast.error(message)
    } finally {
      dispatch({ type: 'SET_LOADING_MESSAGES', payload: false })
    }
  }, [])

  // 发送消息
  const sendMessage = useCallback(async (content: string, type: string = 'text') => {
    if (!state.currentConversation) {
      toast.error('请先选择一个对话')
      return
    }

    try {
      dispatch({ type: 'SET_SENDING', payload: true })
      
      // 创建临时消息
      const tempMessage: Message = {
        id: `temp-${Date.now()}`,
        conversationId: state.currentConversation.id,
        role: 'user',
        content,
        contentType: type as any,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isDeleted: false,
        isLoading: true,
      }
      
      dispatch({ type: 'ADD_MESSAGE', payload: tempMessage })
      
      // 通过Socket发送消息
      if (isConnected) {
        socketSendMessage(state.currentConversation.id, content, type)
      } else {
        // 如果Socket未连接，使用HTTP API
        const message = await conversationApi.sendMessage(state.currentConversation.id, {
          content,
          contentType: type,
        })
        
        // 更新临时消息
        dispatch({ type: 'UPDATE_MESSAGE', payload: { ...message, isLoading: false } })
      }
    } catch (error: any) {
      const message = error.response?.data?.message || '发送消息失败'
      toast.error(message)
      
      // 移除临时消息
      dispatch({ type: 'DELETE_MESSAGE', payload: `temp-${Date.now()}` })
    } finally {
      dispatch({ type: 'SET_SENDING', payload: false })
    }
  }, [state.currentConversation, isConnected, socketSendMessage])

  // 更新消息
  const updateMessage = useCallback(async (id: string, data: Partial<Message>) => {
    try {
      const message = await conversationApi.updateMessage(id, data)
      dispatch({ type: 'UPDATE_MESSAGE', payload: message })
    } catch (error: any) {
      const message = error.response?.data?.message || '更新消息失败'
      toast.error(message)
      throw error
    }
  }, [])

  // 删除消息
  const deleteMessage = useCallback(async (id: string) => {
    try {
      await conversationApi.deleteMessage(id)
      dispatch({ type: 'DELETE_MESSAGE', payload: id })
      toast.success('消息已删除')
    } catch (error: any) {
      const message = error.response?.data?.message || '删除消息失败'
      toast.error(message)
      throw error
    }
  }, [])

  // 开始输入
  const startTyping = useCallback(() => {
    if (state.currentConversation && isConnected) {
      sendTyping(state.currentConversation.id, true)
    }
  }, [state.currentConversation, isConnected, sendTyping])

  // 停止输入
  const stopTyping = useCallback(() => {
    if (state.currentConversation && isConnected) {
      sendTyping(state.currentConversation.id, false)
    }
  }, [state.currentConversation, isConnected, sendTyping])

  // Socket事件监听
  useSocketEvent('message', (data) => {
    dispatch({ type: 'ADD_MESSAGE', payload: data })
  })

  useSocketEvent('message_sent', (data) => {
    dispatch({ type: 'UPDATE_MESSAGE', payload: data })
  })

  useSocketEvent('conversation_created', (data) => {
    dispatch({ type: 'ADD_CONVERSATION', payload: data })
  })

  useSocketEvent('conversation_updated', (data) => {
    dispatch({ type: 'UPDATE_CONVERSATION', payload: data })
  })

  useSocketEvent('conversation_deleted', (data) => {
    dispatch({ type: 'DELETE_CONVERSATION', payload: data.id })
  })

  useSocketEvent('user_typing', (data) => {
    if (data.userId !== user?.id) {
      dispatch({ type: 'ADD_TYPING_USER', payload: data.userId })
    }
  })

  useSocketEvent('user_stop_typing', (data) => {
    dispatch({ type: 'REMOVE_TYPING_USER', payload: data.userId })
  })

  // 初始化加载对话
  useEffect(() => {
    if (isAuthenticated) {
      loadConversations()
    }
  }, [isAuthenticated, loadConversations])

  // 当前对话变化时加载消息
  useEffect(() => {
    if (state.currentConversation) {
      loadMessages(state.currentConversation.id)
    }
  }, [state.currentConversation, loadMessages])

  const value: ConversationContextType = {
    ...state,
    loadConversations,
    createConversation,
    updateConversation,
    deleteConversation,
    setCurrentConversation,
    loadMessages,
    sendMessage,
    updateMessage,
    deleteMessage,
    startTyping,
    stopTyping,
  }

  return (
    <ConversationContext.Provider value={value}>
      {children}
    </ConversationContext.Provider>
  )
}

// 使用对话上下文的Hook
export function useConversation() {
  const context = useContext(ConversationContext)
  
  if (context === undefined) {
    throw new Error('useConversation必须在ConversationProvider内部使用')
  }
  
  return context
}
