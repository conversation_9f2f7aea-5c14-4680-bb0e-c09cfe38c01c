'use client'

import { create<PERSON>ontext, useContext, useEffect, useState, ReactNode, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuth } from './AuthContext'
import { tokenStorage } from '@/lib/utils/storage'
import toast from 'react-hot-toast'

// Socket事件类型
export interface SocketEvents {
  // 连接事件
  connect: () => void
  disconnect: (reason: string) => void
  connect_error: (error: Error) => void
  
  // 消息事件
  message: (data: any) => void
  message_sent: (data: any) => void
  message_error: (error: any) => void
  
  // 对话事件
  conversation_created: (data: any) => void
  conversation_updated: (data: any) => void
  conversation_deleted: (data: any) => void
  
  // 用户事件
  user_joined: (data: any) => void
  user_left: (data: any) => void
  user_typing: (data: any) => void
  user_stop_typing: (data: any) => void
  
  // 系统事件
  notification: (data: any) => void
  error: (error: any) => void
}

// Socket上下文接口
interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
  isConnecting: boolean
  connect: () => void
  disconnect: () => void
  emit: (event: string, data?: any) => void
  on: <K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) => void
  off: <K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) => void
  joinConversation: (conversationId: string) => void
  leaveConversation: (conversationId: string) => void
  sendMessage: (conversationId: string, content: string, type?: string) => void
  sendTyping: (conversationId: string, isTyping: boolean) => void
}

// 创建上下文
const SocketContext = createContext<SocketContextType | undefined>(undefined)

// Socket提供者组件
interface SocketProviderProps {
  children: ReactNode
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const { user, isAuthenticated } = useAuth()

  // 连接Socket
  const connect = useCallback(() => {
    if (socket?.connected || isConnecting || !isAuthenticated) {
      return
    }

    setIsConnecting(true)

    try {
      const token = tokenStorage.getToken()
      
      const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3002', {
        auth: {
          token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        retries: 3,
        retryDelay: 1000,
      })

      // 连接事件
      newSocket.on('connect', () => {
        console.log('Socket连接成功')
        setIsConnected(true)
        setIsConnecting(false)
        toast.success('连接成功', { duration: 2000 })
      })

      newSocket.on('disconnect', (reason) => {
        console.log('Socket连接断开:', reason)
        setIsConnected(false)
        setIsConnecting(false)
        
        if (reason === 'io server disconnect') {
          // 服务器主动断开，需要重新连接
          toast.error('连接已断开')
        }
      })

      newSocket.on('connect_error', (error) => {
        console.error('Socket连接错误:', error)
        setIsConnected(false)
        setIsConnecting(false)
        toast.error('连接失败')
      })

      // 认证错误
      newSocket.on('auth_error', (error) => {
        console.error('Socket认证错误:', error)
        toast.error('认证失败，请重新登录')
        disconnect()
      })

      // 系统通知
      newSocket.on('notification', (data) => {
        toast(data.message, {
          icon: data.type === 'success' ? '✅' : data.type === 'error' ? '❌' : 'ℹ️',
        })
      })

      // 错误处理
      newSocket.on('error', (error) => {
        console.error('Socket错误:', error)
        toast.error(error.message || '发生错误')
      })

      setSocket(newSocket)
    } catch (error) {
      console.error('创建Socket连接失败:', error)
      setIsConnecting(false)
      toast.error('连接失败')
    }
  }, [socket, isConnecting, isAuthenticated])

  // 断开Socket
  const disconnect = useCallback(() => {
    if (socket) {
      socket.disconnect()
      setSocket(null)
      setIsConnected(false)
      setIsConnecting(false)
    }
  }, [socket])

  // 发送事件
  const emit = useCallback((event: string, data?: any) => {
    if (socket?.connected) {
      socket.emit(event, data)
    } else {
      console.warn('Socket未连接，无法发送事件:', event)
    }
  }, [socket])

  // 监听事件
  const on = useCallback(<K extends keyof SocketEvents>(
    event: K, 
    callback: SocketEvents[K]
  ) => {
    if (socket) {
      socket.on(event as string, callback as any)
    }
  }, [socket])

  // 取消监听事件
  const off = useCallback(<K extends keyof SocketEvents>(
    event: K, 
    callback?: SocketEvents[K]
  ) => {
    if (socket) {
      if (callback) {
        socket.off(event as string, callback as any)
      } else {
        socket.off(event as string)
      }
    }
  }, [socket])

  // 加入对话
  const joinConversation = useCallback((conversationId: string) => {
    emit('join_conversation', { conversationId })
  }, [emit])

  // 离开对话
  const leaveConversation = useCallback((conversationId: string) => {
    emit('leave_conversation', { conversationId })
  }, [emit])

  // 发送消息
  const sendMessage = useCallback((
    conversationId: string, 
    content: string, 
    type: string = 'text'
  ) => {
    emit('send_message', {
      conversationId,
      content,
      type,
    })
  }, [emit])

  // 发送输入状态
  const sendTyping = useCallback((conversationId: string, isTyping: boolean) => {
    emit('typing', {
      conversationId,
      isTyping,
    })
  }, [emit])

  // 用户认证状态变化时处理连接
  useEffect(() => {
    if (isAuthenticated && user) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [isAuthenticated, user, connect, disconnect])

  // 页面可见性变化时处理连接
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时保持连接，但可以降低心跳频率
      } else {
        // 页面显示时确保连接正常
        if (isAuthenticated && !socket?.connected) {
          connect()
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isAuthenticated, socket, connect])

  // 网络状态变化时处理连接
  useEffect(() => {
    const handleOnline = () => {
      if (isAuthenticated && !socket?.connected) {
        connect()
      }
    }

    const handleOffline = () => {
      toast.error('网络连接已断开')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [isAuthenticated, socket, connect])

  const value: SocketContextType = {
    socket,
    isConnected,
    isConnecting,
    connect,
    disconnect,
    emit,
    on,
    off,
    joinConversation,
    leaveConversation,
    sendMessage,
    sendTyping,
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

// 使用Socket上下文的Hook
export function useSocket() {
  const context = useContext(SocketContext)
  
  if (context === undefined) {
    throw new Error('useSocket必须在SocketProvider内部使用')
  }
  
  return context
}

// Socket连接状态Hook
export function useSocketConnection() {
  const { isConnected, isConnecting, connect, disconnect } = useSocket()
  
  return {
    isConnected,
    isConnecting,
    isDisconnected: !isConnected && !isConnecting,
    connect,
    disconnect,
  }
}

// Socket事件监听Hook
export function useSocketEvent<K extends keyof SocketEvents>(
  event: K,
  callback: SocketEvents[K],
  deps: any[] = []
) {
  const { on, off } = useSocket()

  useEffect(() => {
    on(event, callback)
    
    return () => {
      off(event, callback)
    }
  }, [event, on, off, ...deps])
}
