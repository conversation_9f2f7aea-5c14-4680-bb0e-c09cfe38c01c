'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import { authApi } from '@/lib/api/auth'
import { tokenStorage } from '@/lib/utils/storage'

// 用户接口
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: string
  permissions: string[]
  createdAt: string
  updatedAt: string
}

// 认证上下文接口
interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
}

// 注册数据接口
interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

// 创建上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // 计算认证状态
  const isAuthenticated = !!user

  // 初始化认证状态
  useEffect(() => {
    initializeAuth()
  }, [])

  // 初始化认证
  const initializeAuth = async () => {
    try {
      const token = tokenStorage.getToken()
      
      if (!token) {
        setIsLoading(false)
        return
      }

      // 验证token并获取用户信息
      const userData = await authApi.getProfile()
      setUser(userData)
    } catch (error) {
      console.error('认证初始化失败:', error)
      // 清除无效token
      tokenStorage.removeToken()
    } finally {
      setIsLoading(false)
    }
  }

  // 登录
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true)
      
      const response = await authApi.login({ email, password })
      
      // 保存token
      tokenStorage.setToken(response.token)
      
      // 设置用户信息
      setUser(response.user)
      
      toast.success('登录成功')
      
      // 重定向到聊天页面
      router.push('/chat')
    } catch (error: any) {
      const message = error.response?.data?.message || '登录失败'
      toast.error(message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // 注册
  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true)
      
      // 验证密码确认
      if (data.password !== data.confirmPassword) {
        throw new Error('密码确认不匹配')
      }
      
      const response = await authApi.register({
        name: data.name,
        email: data.email,
        password: data.password,
      })
      
      // 保存token
      tokenStorage.setToken(response.token)
      
      // 设置用户信息
      setUser(response.user)
      
      toast.success('注册成功')
      
      // 重定向到聊天页面
      router.push('/chat')
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || '注册失败'
      toast.error(message)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // 登出
  const logout = () => {
    try {
      // 清除token
      tokenStorage.removeToken()
      
      // 清除用户信息
      setUser(null)
      
      toast.success('已退出登录')
      
      // 重定向到首页
      router.push('/')
    } catch (error) {
      console.error('登出失败:', error)
      toast.error('登出失败')
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const token = tokenStorage.getToken()
      
      if (!token) {
        throw new Error('没有有效的token')
      }
      
      const response = await authApi.refreshToken()
      
      // 更新token
      tokenStorage.setToken(response.token)
      
      // 更新用户信息
      if (response.user) {
        setUser(response.user)
      }
    } catch (error) {
      console.error('刷新token失败:', error)
      // 刷新失败，清除认证状态
      logout()
      throw error
    }
  }

  // 更新用户资料
  const updateProfile = async (data: Partial<User>) => {
    try {
      const updatedUser = await authApi.updateProfile(data)
      setUser(updatedUser)
      toast.success('资料更新成功')
    } catch (error: any) {
      const message = error.response?.data?.message || '更新失败'
      toast.error(message)
      throw error
    }
  }

  // 自动刷新token
  useEffect(() => {
    if (!user) return

    // 设置定时器，在token过期前刷新
    const interval = setInterval(async () => {
      try {
        await refreshToken()
      } catch (error) {
        console.error('自动刷新token失败:', error)
      }
    }, 15 * 60 * 1000) // 15分钟刷新一次

    return () => clearInterval(interval)
  }, [user])

  // 监听token变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token') {
        if (!e.newValue) {
          // token被删除，清除用户状态
          setUser(null)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// 使用认证上下文的Hook
export function useAuth() {
  const context = useContext(AuthContext)
  
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内部使用')
  }
  
  return context
}

// 认证守卫Hook
export function useAuthGuard(redirectTo: string = '/login') {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, router, redirectTo])

  return { isAuthenticated, isLoading }
}

// 访客守卫Hook（已登录用户重定向）
export function useGuestGuard(redirectTo: string = '/chat') {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, router, redirectTo])

  return { isAuthenticated, isLoading }
}
