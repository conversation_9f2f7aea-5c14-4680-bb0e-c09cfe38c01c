# RAG前端应用 - 中文说明文档

## 📋 概述

RAG前端应用是一个现代化的Web应用程序，基于Next.js 14构建，为RAG（检索增强生成）系统提供完整的用户界面。应用包含智能问答聊天、文档管理、用户认证和管理后台等功能模块。

## 🚀 主要功能

### 💬 智能问答聊天
- **实时对话**: 基于WebSocket的实时聊天体验
- **流式响应**: 支持AI回答的流式显示
- **多轮对话**: 保持上下文的连续对话
- **对话历史**: 完整的对话记录和管理
- **文档问答**: 基于上传文档的知识问答
- **输入状态**: 实时显示用户输入状态

### 📄 文档管理系统
- **文档上传**: 支持多种格式文档上传
- **文档浏览**: 在线文档预览和搜索
- **批量操作**: 支持文档的批量管理
- **标签管理**: 文档分类和标签系统
- **版本控制**: 文档版本历史管理
- **权限控制**: 基于角色的文档访问控制

### 👤 用户认证系统
- **登录注册**: 完整的用户认证流程
- **权限管理**: 基于RBAC的权限控制
- **个人设置**: 用户偏好和配置管理
- **安全保护**: JWT令牌和会话管理
- **密码安全**: 密码强度验证和重置

### 🛠️ 管理后台
- **用户管理**: 用户账户和权限管理
- **文档管理**: 系统文档的集中管理
- **数据分析**: 使用统计和性能分析
- **系统配置**: 系统参数和功能配置
- **监控面板**: 实时系统状态监控

### 🎨 用户体验
- **响应式设计**: 适配桌面和移动设备
- **主题切换**: 支持明暗主题切换
- **国际化**: 多语言支持（中英文）
- **无障碍**: 符合WCAG无障碍标准
- **性能优化**: 代码分割和懒加载

## 🏗️ 技术架构

### 技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Context + useReducer
- **数据获取**: TanStack Query (React Query)
- **实时通信**: Socket.IO Client
- **表单处理**: React Hook Form + Zod
- **UI组件**: Headless UI + Heroicons
- **通知系统**: React Hot Toast

### 项目结构
```
frontend/
├── app/                        # Next.js App Router
│   ├── (auth)/                # 认证相关页面
│   │   ├── login/             # 登录页面
│   │   ├── register/          # 注册页面
│   │   └── layout.tsx         # 认证布局
│   ├── admin/                 # 管理后台
│   │   ├── analytics/         # 数据分析
│   │   ├── documents/         # 文档管理
│   │   ├── users/             # 用户管理
│   │   └── layout.tsx         # 管理后台布局
│   ├── chat/                  # 聊天界面
│   │   ├── [id]/              # 具体对话页面
│   │   └── page.tsx           # 聊天主页
│   ├── documents/             # 文档管理
│   │   ├── [id]/              # 文档详情
│   │   └── page.tsx           # 文档列表
│   ├── globals.css            # 全局样式
│   ├── layout.tsx             # 根布局
│   ├── page.tsx               # 首页
│   └── providers.tsx          # 全局Provider
├── components/                # 组件库
│   ├── chat/                  # 聊天相关组件
│   │   ├── ChatInterface.tsx  # 聊天界面
│   │   ├── MessageList.tsx    # 消息列表
│   │   ├── MessageInput.tsx   # 消息输入
│   │   └── ConversationList.tsx # 对话列表
│   ├── documents/             # 文档相关组件
│   │   ├── DocumentList.tsx   # 文档列表
│   │   ├── DocumentUpload.tsx # 文档上传
│   │   └── DocumentViewer.tsx # 文档查看器
│   ├── layout/                # 布局组件
│   │   ├── Header.tsx         # 页头
│   │   ├── Sidebar.tsx        # 侧边栏
│   │   └── Footer.tsx         # 页脚
│   ├── ui/                    # 基础UI组件
│   │   ├── Button.tsx         # 按钮
│   │   ├── Input.tsx          # 输入框
│   │   ├── Modal.tsx          # 模态框
│   │   └── LoadingSpinner.tsx # 加载动画
│   └── admin/                 # 管理后台组件
├── contexts/                  # React Context
│   ├── AuthContext.tsx        # 认证上下文
│   ├── ConversationContext.tsx # 对话上下文
│   ├── SocketContext.tsx      # Socket通信上下文
│   └── ThemeContext.tsx       # 主题上下文
├── hooks/                     # 自定义Hooks
│   ├── useAuth.ts             # 认证Hook
│   ├── useSocket.ts           # Socket Hook
│   ├── useConversation.ts     # 对话Hook
│   └── useDocuments.ts        # 文档Hook
├── lib/                       # 工具库
│   ├── api.ts                 # API客户端
│   ├── auth.ts                # 认证工具
│   ├── socket.ts              # Socket配置
│   ├── utils.ts               # 通用工具
│   └── validations.ts         # 表单验证
├── types/                     # TypeScript类型定义
│   ├── auth.ts                # 认证类型
│   ├── conversation.ts        # 对话类型
│   ├── document.ts            # 文档类型
│   └── api.ts                 # API类型
└── public/                    # 静态资源
    ├── icons/                 # 图标
    ├── images/                # 图片
    └── favicon.ico            # 网站图标
```

## 📦 安装和部署

### 环境要求
- Node.js 18+
- npm 或 yarn
- 现代浏览器支持

### 开发环境启动
```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件

# 4. 启动开发服务器
npm run dev
```

### 生产构建
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

### Docker部署
```bash
# 构建镜像
docker build -t rag-frontend .

# 运行容器
docker run -d \
  --name rag-frontend \
  -p 3100:3000 \
  -e NEXT_PUBLIC_API_URL=http://api.example.com \
  rag-frontend
```

## 🔧 配置说明

### 环境变量
```bash
# API配置
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# 认证配置
NEXT_PUBLIC_JWT_SECRET=your-jwt-secret
NEXT_PUBLIC_SESSION_TIMEOUT=3600

# 功能开关
NEXT_PUBLIC_ENABLE_REGISTRATION=true
NEXT_PUBLIC_ENABLE_GUEST_MODE=false
NEXT_PUBLIC_ENABLE_FILE_UPLOAD=true

# 上传配置
NEXT_PUBLIC_MAX_FILE_SIZE=10485760  # 10MB
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,docx,txt,md

# 主题配置
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_ENABLE_THEME_SWITCH=true

# 分析配置
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

### Tailwind配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          900: '#111827',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

## 🧩 核心组件

### 聊天界面组件
```typescript
// components/chat/ChatInterface.tsx
interface ChatInterfaceProps {
  conversationId?: string;
  initialMessages?: Message[];
  onSendMessage?: (content: string) => void;
  isLoading?: boolean;
}

export function ChatInterface({
  conversationId,
  initialMessages = [],
  onSendMessage,
  isLoading = false
}: ChatInterfaceProps) {
  // 组件实现
}
```

### 文档上传组件
```typescript
// components/documents/DocumentUpload.tsx
interface DocumentUploadProps {
  onUploadComplete?: (document: Document) => void;
  onUploadError?: (error: string) => void;
  acceptedTypes?: string[];
  maxFileSize?: number;
  multiple?: boolean;
}

export function DocumentUpload({
  onUploadComplete,
  onUploadError,
  acceptedTypes = ['pdf', 'docx', 'txt'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  multiple = false
}: DocumentUploadProps) {
  // 组件实现
}
```

### 认证Hook
```typescript
// hooks/useAuth.ts
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  
  return {
    user: context.user,
    isAuthenticated: context.isAuthenticated,
    isLoading: context.isLoading,
    login: context.login,
    logout: context.logout,
    register: context.register,
  };
}
```

## 🎨 样式系统

### 设计令牌
```css
/* 颜色系统 */
:root {
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-900: #111827;
  
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
}

/* 间距系统 */
.space-xs { margin: 0.25rem; }
.space-sm { margin: 0.5rem; }
.space-md { margin: 1rem; }
.space-lg { margin: 1.5rem; }
.space-xl { margin: 2rem; }

/* 字体系统 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
```

### 组件样式
```css
/* 按钮组件 */
.btn {
  @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
  @apply disabled:opacity-50 disabled:pointer-events-none;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-900 hover:bg-gray-200;
}

/* 输入框组件 */
.input {
  @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2;
  @apply text-sm placeholder:text-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  @apply disabled:cursor-not-allowed disabled:opacity-50;
}

/* 卡片组件 */
.card {
  @apply rounded-lg border border-gray-200 bg-white p-6 shadow-sm;
}
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage

# E2E测试
npm run test:e2e
```

### 测试示例
```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## 📊 性能优化

### 代码分割
```typescript
// 动态导入组件
const DocumentViewer = dynamic(() => import('@/components/documents/DocumentViewer'), {
  loading: () => <LoadingSpinner />,
  ssr: false
});

// 路由级别的代码分割
const AdminPanel = dynamic(() => import('@/app/admin/page'), {
  loading: () => <div>Loading admin panel...</div>
});
```

### 图片优化
```typescript
// 使用Next.js Image组件
import Image from 'next/image';

<Image
  src="/images/hero.jpg"
  alt="Hero image"
  width={800}
  height={600}
  priority
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### 缓存策略
```typescript
// API缓存配置
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});
```

## 🔮 未来规划

### 短期计划 (1-2个月)
- [ ] 完善移动端适配
- [ ] 添加离线支持
- [ ] 优化加载性能
- [ ] 增强无障碍功能

### 中期计划 (3-6个月)
- [ ] 支持多语言国际化
- [ ] 添加PWA功能
- [ ] 实现主题定制
- [ ] 集成语音输入

### 长期计划 (6-12个月)
- [ ] 支持插件系统
- [ ] 添加协作功能
- [ ] 实现AI助手
- [ ] 移动应用开发

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: RAG开发团队
- **技术支持**: 通过GitHub Issues提交问题
- **文档更新**: 2025-08-28

---

*该文档将随着项目发展持续更新，确保信息的准确性和完整性。*
