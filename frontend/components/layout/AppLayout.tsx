'use client';

import React from 'react';
import Navigation, { MobileNavigation, Breadcrumb } from '../Navigation';

interface AppLayoutProps {
  children: React.ReactNode;
  showBreadcrumb?: boolean;
}

export default function AppLayout({ children, showBreadcrumb = true }: AppLayoutProps) {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* 桌面端侧边导航 */}
      <div className="hidden lg:block">
        <Navigation />
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 主内容 */}
        <main className="flex-1 overflow-auto">
          <div className="h-full">
            {showBreadcrumb && (
              <div className="bg-white border-b border-gray-200 px-6 py-4">
                <Breadcrumb />
              </div>
            )}
            {children}
          </div>
        </main>
      </div>

      {/* 移动端底部导航 */}
      <MobileNavigation />
    </div>
  );
}
