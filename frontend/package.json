{"name": "rag-frontend", "version": "1.0.0", "description": "RAG系统前端应用 - 用户界面、问答聊天、文档浏览", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "swr": "^2.2.4", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "react-syntax-highlighter": "^15.5.0", "react-dropzone": "^14.2.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "date-fns": "^2.30.0", "lodash": "^4.17.21", "js-cookie": "^3.0.5", "react-query": "^3.39.3", "zustand": "^4.4.7", "immer": "^10.0.3"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-window": "^1.8.8", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2"}, "engines": {"node": ">=18.0.0"}}