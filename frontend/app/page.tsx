'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Hero } from '@/components/home/<USER>'
import { Features } from '@/components/home/<USER>'
import { HowItWorks } from '@/components/home/<USER>'
import { CTA } from '@/components/home/<USER>'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'

export default function HomePage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // 如果用户已登录，重定向到聊天页面
    if (user && !isLoading) {
      router.push('/chat')
    }
  }, [user, isLoading, router])

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // 如果用户已登录，显示加载状态（即将重定向）
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">正在跳转到聊天页面...</p>
        </div>
      </div>
    )
  }

  // 未登录用户显示首页
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main>
        {/* 英雄区域 */}
        <Hero />
        
        {/* 功能特性 */}
        <Features />
        
        {/* 工作原理 */}
        <HowItWorks />
        
        {/* 行动号召 */}
        <CTA />
      </main>
      
      <Footer />
    </div>
  )
}
