/**
 * 管理后台仪表板页面
 * 显示系统概览、统计数据和快速操作
 */

'use client';

import { useState, useEffect } from 'react';
import {
  UsersIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  ServerIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

// 统计卡片数据类型
interface StatCard {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

// 模拟统计数据
const stats: StatCard[] = [
  {
    name: '总用户数',
    value: '1,234',
    change: '+12%',
    changeType: 'increase',
    icon: UsersIcon,
  },
  {
    name: '文档总数',
    value: '5,678',
    change: '+8%',
    changeType: 'increase',
    icon: DocumentTextIcon,
  },
  {
    name: '对话总数',
    value: '12,345',
    change: '+15%',
    changeType: 'increase',
    icon: ChatBubbleLeftRightIcon,
  },
  {
    name: '系统负载',
    value: '45%',
    change: '-3%',
    changeType: 'decrease',
    icon: ServerIcon,
  },
];

// 最近活动数据类型
interface Activity {
  id: string;
  type: 'user' | 'document' | 'conversation';
  message: string;
  time: string;
}

// 模拟最近活动数据
const recentActivities: Activity[] = [
  {
    id: '1',
    type: 'user',
    message: '新用户 张三 注册了账号',
    time: '2分钟前',
  },
  {
    id: '2',
    type: 'document',
    message: '用户 李四 上传了文档 "技术规范.pdf"',
    time: '5分钟前',
  },
  {
    id: '3',
    type: 'conversation',
    message: '用户 王五 开始了新的对话',
    time: '10分钟前',
  },
  {
    id: '4',
    type: 'document',
    message: '文档 "用户手册.docx" 处理完成',
    time: '15分钟前',
  },
];

export default function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模拟数据加载
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="mt-2 text-sm text-gray-700">
          系统概览和关键指标监控
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((item) => (
          <div
            key={item.name}
            className="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6 sm:py-6"
          >
            <dt>
              <div className="absolute rounded-md bg-primary-500 p-3">
                <item.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <p className="ml-16 truncate text-sm font-medium text-gray-500">
                {item.name}
              </p>
            </dt>
            <dd className="ml-16 flex items-baseline">
              <p className="text-2xl font-semibold text-gray-900">{item.value}</p>
              <p
                className={clsx(
                  'ml-2 flex items-baseline text-sm font-semibold',
                  item.changeType === 'increase'
                    ? 'text-green-600'
                    : 'text-red-600'
                )}
              >
                {item.changeType === 'increase' ? (
                  <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                )}
                <span className="sr-only">
                  {item.changeType === 'increase' ? '增长' : '下降'}
                </span>
                {item.change}
              </p>
            </dd>
          </div>
        ))}
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* 最近活动 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">最近活动</h3>
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivities.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivities.length - 1 ? (
                        <span
                          className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span
                            className={clsx(
                              'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white',
                              activity.type === 'user' && 'bg-blue-500',
                              activity.type === 'document' && 'bg-green-500',
                              activity.type === 'conversation' && 'bg-purple-500'
                            )}
                          >
                            {activity.type === 'user' && (
                              <UsersIcon className="h-4 w-4 text-white" />
                            )}
                            {activity.type === 'document' && (
                              <DocumentTextIcon className="h-4 w-4 text-white" />
                            )}
                            {activity.type === 'conversation' && (
                              <ChatBubbleLeftRightIcon className="h-4 w-4 text-white" />
                            )}
                          </span>
                        </div>
                        <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                          <div>
                            <p className="text-sm text-gray-500">{activity.message}</p>
                          </div>
                          <div className="whitespace-nowrap text-right text-sm text-gray-500">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">系统状态</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">API服务</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  正常
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">数据库</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  正常
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">向量数据库</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  正常
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">缓存服务</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  警告
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
              <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                添加用户
              </span>
            </button>
            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                批量导入文档
              </span>
            </button>
            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
              <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                系统配置
              </span>
            </button>
            <button className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <span className="mt-2 block text-sm font-medium text-gray-900">
                生成报告
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
