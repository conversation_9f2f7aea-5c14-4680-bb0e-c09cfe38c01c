/**
 * 文档管理页面
 * 管理系统中的所有文档，包括查看、编辑、删除、批量操作等功能
 */

'use client';

import { useState, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  CloudArrowUpIcon,
  FunnelIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

// 文档数据类型
interface Document {
  id: string;
  title: string;
  filename: string;
  type: 'pdf' | 'docx' | 'txt' | 'md' | 'xlsx' | 'pptx';
  size: number;
  status: 'processing' | 'completed' | 'failed' | 'pending';
  uploadedBy: string;
  uploadedAt: string;
  processedAt?: string;
  chunksCount: number;
  vectorsCount: number;
  downloadCount: number;
  tags: string[];
}

// 模拟文档数据
const mockDocuments: Document[] = [
  {
    id: '1',
    title: '技术规范文档',
    filename: 'tech-spec.pdf',
    type: 'pdf',
    size: 2048576, // 2MB
    status: 'completed',
    uploadedBy: '张三',
    uploadedAt: '2024-01-20',
    processedAt: '2024-01-20',
    chunksCount: 45,
    vectorsCount: 45,
    downloadCount: 12,
    tags: ['技术', '规范', '开发'],
  },
  {
    id: '2',
    title: '用户手册',
    filename: 'user-manual.docx',
    type: 'docx',
    size: 1536000, // 1.5MB
    status: 'processing',
    uploadedBy: '李四',
    uploadedAt: '2024-01-19',
    chunksCount: 0,
    vectorsCount: 0,
    downloadCount: 5,
    tags: ['用户', '手册', '帮助'],
  },
  {
    id: '3',
    title: '项目计划',
    filename: 'project-plan.xlsx',
    type: 'xlsx',
    size: 512000, // 512KB
    status: 'failed',
    uploadedBy: '王五',
    uploadedAt: '2024-01-18',
    chunksCount: 0,
    vectorsCount: 0,
    downloadCount: 2,
    tags: ['项目', '计划', '管理'],
  },
  {
    id: '4',
    title: 'API文档',
    filename: 'api-docs.md',
    type: 'md',
    size: 256000, // 256KB
    status: 'completed',
    uploadedBy: '赵六',
    uploadedAt: '2024-01-17',
    processedAt: '2024-01-17',
    chunksCount: 23,
    vectorsCount: 23,
    downloadCount: 18,
    tags: ['API', '文档', '开发'],
  },
];

// 文件类型图标
const typeIcons = {
  pdf: '📄',
  docx: '📝',
  txt: '📄',
  md: '📝',
  xlsx: '📊',
  pptx: '📊',
};

// 状态样式
const statusStyles = {
  processing: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  pending: 'bg-gray-100 text-gray-800',
};

// 状态文本
const statusText = {
  processing: '处理中',
  completed: '已完成',
  failed: '失败',
  pending: '等待中',
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>(mockDocuments);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.uploadedBy.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || doc.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // 选择/取消选择文档
  const handleSelectDocument = (docId: string) => {
    setSelectedDocuments(prev => 
      prev.includes(docId) 
        ? prev.filter(id => id !== docId)
        : [...prev, docId]
    );
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedDocuments.length === filteredDocuments.length) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(filteredDocuments.map(doc => doc.id));
    }
  };

  // 删除文档
  const handleDeleteDocument = (docId: string) => {
    if (confirm('确定要删除这个文档吗？')) {
      setDocuments(documents.filter(doc => doc.id !== docId));
      setSelectedDocuments(selectedDocuments.filter(id => id !== docId));
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedDocuments.length === 0) return;
    
    if (confirm(`确定要删除选中的 ${selectedDocuments.length} 个文档吗？`)) {
      setDocuments(documents.filter(doc => !selectedDocuments.includes(doc.id)));
      setSelectedDocuments([]);
    }
  };

  // 重新处理文档
  const handleReprocess = (docId: string) => {
    setDocuments(documents.map(doc => 
      doc.id === docId 
        ? { ...doc, status: 'processing' as const }
        : doc
    ));
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">文档管理</h1>
          <p className="mt-2 text-sm text-gray-700">
            管理系统中的所有文档，包括上传、处理状态和向量化信息
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {selectedDocuments.length > 0 && (
            <button
              type="button"
              className="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500"
              onClick={handleBatchDelete}
            >
              <TrashIcon className="-ml-0.5 mr-1.5 h-5 w-5" />
              删除选中 ({selectedDocuments.length})
            </button>
          )}
          <button
            type="button"
            className="inline-flex items-center rounded-md bg-primary-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-primary-500"
            onClick={() => setShowUploadModal(true)}
          >
            <CloudArrowUpIcon className="-ml-0.5 mr-1.5 h-5 w-5" />
            上传文档
          </button>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
            {/* 搜索框 */}
            <div className="sm:col-span-2">
              <label htmlFor="search" className="sr-only">搜索文档</label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="search"
                  name="search"
                  type="search"
                  placeholder="搜索文档标题、文件名或上传者..."
                  className="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* 文件类型过滤 */}
            <div>
              <label htmlFor="type-filter" className="sr-only">按类型过滤</label>
              <select
                id="type-filter"
                name="type-filter"
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="all">所有类型</option>
                <option value="pdf">PDF</option>
                <option value="docx">Word</option>
                <option value="txt">文本</option>
                <option value="md">Markdown</option>
                <option value="xlsx">Excel</option>
                <option value="pptx">PowerPoint</option>
              </select>
            </div>

            {/* 状态过滤 */}
            <div>
              <label htmlFor="status-filter" className="sr-only">按状态过滤</label>
              <select
                id="status-filter"
                name="status-filter"
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <option value="all">所有状态</option>
                <option value="completed">已完成</option>
                <option value="processing">处理中</option>
                <option value="failed">失败</option>
                <option value="pending">等待中</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* 文档列表 */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              文档列表 ({filteredDocuments.length})
            </h3>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                checked={selectedDocuments.length === filteredDocuments.length && filteredDocuments.length > 0}
                onChange={handleSelectAll}
              />
              <label className="text-sm text-gray-700">全选</label>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      选择
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      文档
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      处理信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      上传信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      统计
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={selectedDocuments.includes(doc.id)}
                          onChange={() => handleSelectDocument(doc.id)}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-2xl mr-3">
                            {typeIcons[doc.type]}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                            <div className="text-sm text-gray-500">{doc.filename}</div>
                            <div className="text-xs text-gray-400">{formatFileSize(doc.size)}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={clsx(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          statusStyles[doc.status]
                        )}>
                          {statusText[doc.status]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>分块: {doc.chunksCount}</div>
                        <div>向量: {doc.vectorsCount}</div>
                        {doc.processedAt && (
                          <div className="text-xs text-gray-400">
                            处理于: {doc.processedAt}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>上传者: {doc.uploadedBy}</div>
                        <div>上传于: {doc.uploadedAt}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>下载: {doc.downloadCount} 次</div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {doc.tags.slice(0, 2).map(tag => (
                            <span key={tag} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              {tag}
                            </span>
                          ))}
                          {doc.tags.length > 2 && (
                            <span className="text-xs text-gray-400">+{doc.tags.length - 2}</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            type="button"
                            className="text-primary-600 hover:text-primary-900"
                            title="查看详情"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            className="text-gray-600 hover:text-gray-900"
                            title="下载文档"
                          >
                            <ArrowDownTrayIcon className="h-4 w-4" />
                          </button>
                          {doc.status === 'failed' && (
                            <button
                              type="button"
                              className="text-yellow-600 hover:text-yellow-900"
                              title="重新处理"
                              onClick={() => handleReprocess(doc.id)}
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            type="button"
                            className="text-gray-600 hover:text-gray-900"
                            title="编辑文档"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            className="text-red-600 hover:text-red-900"
                            title="删除文档"
                            onClick={() => handleDeleteDocument(doc.id)}
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {filteredDocuments.length === 0 && (
                <div className="text-center py-12">
                  <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">没有找到文档</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    尝试调整搜索条件或上传新文档
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
