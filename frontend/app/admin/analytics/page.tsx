/**
 * 数据统计页面
 * 显示系统的各种统计数据和分析图表
 */

'use client';

import { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

// 统计数据类型
interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalDocuments: number;
    totalConversations: number;
    totalMessages: number;
    activeUsers: number;
    storageUsed: number;
  };
  trends: {
    userGrowth: number;
    documentGrowth: number;
    conversationGrowth: number;
    messageGrowth: number;
  };
  usage: {
    dailyActiveUsers: { date: string; count: number }[];
    documentUploads: { date: string; count: number }[];
    conversations: { date: string; count: number }[];
    topDocuments: { id: string; title: string; views: number; downloads: number }[];
    topUsers: { id: string; name: string; documents: number; conversations: number }[];
  };
  performance: {
    avgResponseTime: number;
    successRate: number;
    errorRate: number;
    uptime: number;
  };
}

// 模拟统计数据
const mockAnalytics: AnalyticsData = {
  overview: {
    totalUsers: 1234,
    totalDocuments: 5678,
    totalConversations: 12345,
    totalMessages: 45678,
    activeUsers: 456,
    storageUsed: 15.6, // GB
  },
  trends: {
    userGrowth: 12.5,
    documentGrowth: 8.3,
    conversationGrowth: 15.7,
    messageGrowth: 22.1,
  },
  usage: {
    dailyActiveUsers: [
      { date: '2024-01-14', count: 120 },
      { date: '2024-01-15', count: 135 },
      { date: '2024-01-16', count: 142 },
      { date: '2024-01-17', count: 158 },
      { date: '2024-01-18', count: 167 },
      { date: '2024-01-19', count: 189 },
      { date: '2024-01-20', count: 201 },
    ],
    documentUploads: [
      { date: '2024-01-14', count: 25 },
      { date: '2024-01-15', count: 32 },
      { date: '2024-01-16', count: 28 },
      { date: '2024-01-17', count: 41 },
      { date: '2024-01-18', count: 35 },
      { date: '2024-01-19', count: 47 },
      { date: '2024-01-20', count: 52 },
    ],
    conversations: [
      { date: '2024-01-14', count: 89 },
      { date: '2024-01-15', count: 95 },
      { date: '2024-01-16', count: 103 },
      { date: '2024-01-17', count: 118 },
      { date: '2024-01-18', count: 125 },
      { date: '2024-01-19', count: 142 },
      { date: '2024-01-20', count: 156 },
    ],
    topDocuments: [
      { id: '1', title: '技术规范文档', views: 245, downloads: 89 },
      { id: '2', title: 'API文档', views: 198, downloads: 67 },
      { id: '3', title: '用户手册', views: 156, downloads: 45 },
      { id: '4', title: '项目计划', views: 134, downloads: 32 },
      { id: '5', title: '设计指南', views: 112, downloads: 28 },
    ],
    topUsers: [
      { id: '1', name: '张三', documents: 25, conversations: 150 },
      { id: '2', name: '李四', documents: 18, conversations: 89 },
      { id: '3', name: '王五', documents: 15, conversations: 76 },
      { id: '4', name: '赵六', documents: 12, conversations: 65 },
      { id: '5', name: '钱七', documents: 10, conversations: 54 },
    ],
  },
  performance: {
    avgResponseTime: 245, // ms
    successRate: 99.2,
    errorRate: 0.8,
    uptime: 99.9,
  },
};

// 时间范围选项
const timeRanges = [
  { value: '7d', label: '最近7天' },
  { value: '30d', label: '最近30天' },
  { value: '90d', label: '最近90天' },
  { value: '1y', label: '最近1年' },
];

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData>(mockAnalytics);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(false);

  // 格式化数字
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 格式化百分比
  const formatPercentage = (num: number): string => {
    return num.toFixed(1) + '%';
  };

  // 格式化存储大小
  const formatStorage = (gb: number): string => {
    if (gb >= 1024) {
      return (gb / 1024).toFixed(1) + ' TB';
    }
    return gb.toFixed(1) + ' GB';
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和时间范围选择 */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据统计</h1>
          <p className="mt-2 text-sm text-gray-700">
            系统使用情况和性能指标分析
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            className="block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
          >
            {timeRanges.map((range) => (
              <option key={range.value} value={range.value}>
                {range.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 概览统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.totalUsers)}
                    </div>
                    <div className={clsx(
                      'ml-2 flex items-baseline text-sm font-semibold',
                      analytics.trends.userGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    )}>
                      {analytics.trends.userGrowth >= 0 ? (
                        <TrendingUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      )}
                      {formatPercentage(Math.abs(analytics.trends.userGrowth))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总文档数</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.totalDocuments)}
                    </div>
                    <div className={clsx(
                      'ml-2 flex items-baseline text-sm font-semibold',
                      analytics.trends.documentGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    )}>
                      {analytics.trends.documentGrowth >= 0 ? (
                        <TrendingUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      )}
                      {formatPercentage(Math.abs(analytics.trends.documentGrowth))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总对话数</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.totalConversations)}
                    </div>
                    <div className={clsx(
                      'ml-2 flex items-baseline text-sm font-semibold',
                      analytics.trends.conversationGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    )}>
                      {analytics.trends.conversationGrowth >= 0 ? (
                        <TrendingUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      ) : (
                        <TrendingDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      )}
                      {formatPercentage(Math.abs(analytics.trends.conversationGrowth))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatNumber(analytics.overview.activeUsers)}
                    </div>
                    <div className="ml-2 text-sm text-gray-500">
                      / {formatNumber(analytics.overview.totalUsers)}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* 用户活跃度趋势 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">用户活跃度趋势</h3>
            <div className="h-64 flex items-end justify-between space-x-2">
              {analytics.usage.dailyActiveUsers.map((data, index) => {
                const maxCount = Math.max(...analytics.usage.dailyActiveUsers.map(d => d.count));
                const height = (data.count / maxCount) * 100;
                return (
                  <div key={data.date} className="flex flex-col items-center flex-1">
                    <div
                      className="w-full bg-primary-500 rounded-t"
                      style={{ height: `${height}%` }}
                      title={`${data.date}: ${data.count} 用户`}
                    />
                    <div className="text-xs text-gray-500 mt-2 transform -rotate-45 origin-left">
                      {data.date.slice(5)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 文档上传趋势 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">文档上传趋势</h3>
            <div className="h-64 flex items-end justify-between space-x-2">
              {analytics.usage.documentUploads.map((data, index) => {
                const maxCount = Math.max(...analytics.usage.documentUploads.map(d => d.count));
                const height = (data.count / maxCount) * 100;
                return (
                  <div key={data.date} className="flex flex-col items-center flex-1">
                    <div
                      className="w-full bg-green-500 rounded-t"
                      style={{ height: `${height}%` }}
                      title={`${data.date}: ${data.count} 文档`}
                    />
                    <div className="text-xs text-gray-500 mt-2 transform -rotate-45 origin-left">
                      {data.date.slice(5)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 排行榜 */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* 热门文档 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">热门文档</h3>
            <div className="space-y-3">
              {analytics.usage.topDocuments.map((doc, index) => (
                <div key={doc.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-6 text-center">
                      <span className={clsx(
                        'text-sm font-medium',
                        index === 0 ? 'text-yellow-600' :
                        index === 1 ? 'text-gray-500' :
                        index === 2 ? 'text-orange-600' : 'text-gray-400'
                      )}>
                        {index + 1}
                      </span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                      <div className="text-sm text-gray-500">
                        {doc.views} 次查看 · {doc.downloads} 次下载
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 活跃用户 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">活跃用户</h3>
            <div className="space-y-3">
              {analytics.usage.topUsers.map((user, index) => (
                <div key={user.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-6 text-center">
                      <span className={clsx(
                        'text-sm font-medium',
                        index === 0 ? 'text-yellow-600' :
                        index === 1 ? 'text-gray-500' :
                        index === 2 ? 'text-orange-600' : 'text-gray-400'
                      )}>
                        {index + 1}
                      </span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">
                        {user.documents} 文档 · {user.conversations} 对话
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 性能指标 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统性能</h3>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-semibold text-gray-900">
                {analytics.performance.avgResponseTime}ms
              </div>
              <div className="text-sm text-gray-500">平均响应时间</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-green-600">
                {formatPercentage(analytics.performance.successRate)}
              </div>
              <div className="text-sm text-gray-500">成功率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-red-600">
                {formatPercentage(analytics.performance.errorRate)}
              </div>
              <div className="text-sm text-gray-500">错误率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-blue-600">
                {formatPercentage(analytics.performance.uptime)}
              </div>
              <div className="text-sm text-gray-500">系统可用性</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
