#!/usr/bin/env python3
"""
RAG检索系统数据库管理脚本
执行数据库扩展、索引优化、性能测试
"""

import asyncio
import asyncpg
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import yaml
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.connection_pool = None
        self.migration_file = Path(__file__).parent / "database_migration_enhanced.sql"
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载数据库配置"""
        if config_file and Path(config_file).exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        
        # 默认配置
        return {
            "database": {
                "host": os.getenv("DB_HOST", "localhost"),
                "port": int(os.getenv("DB_PORT", "5432")),
                "database": os.getenv("DB_NAME", "rag_system"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASSWORD", "password"),
                "min_size": 5,
                "max_size": 20
            }
        }
    
    async def create_connection_pool(self) -> bool:
        """创建数据库连接池"""
        try:
            db_config = self.config["database"]
            self.connection_pool = await asyncpg.create_pool(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["database"],
                user=db_config["user"],
                password=db_config["password"],
                min_size=db_config["min_size"],
                max_size=db_config["max_size"]
            )
            logger.info("数据库连接池创建成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    async def close_connection_pool(self):
        """关闭数据库连接池"""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("数据库连接池已关闭")
    
    async def execute_migration(self) -> bool:
        """执行数据库迁移"""
        logger.info("开始执行数据库迁移...")
        
        try:
            # 读取迁移脚本
            with open(self.migration_file, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            # 执行迁移
            async with self.connection_pool.acquire() as conn:
                await conn.execute(migration_sql)
            
            logger.info("数据库迁移执行成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return False
    
    async def verify_tables(self) -> bool:
        """验证表结构"""
        logger.info("验证数据库表结构...")
        
        expected_tables = [
            "documents",
            "document_chunks", 
            "aspect_vectors",
            "query_history",
            "user_behavior_analytics",
            "ab_experiments",
            "ab_user_assignments",
            "performance_metrics",
            "hard_negative_samples"
        ]
        
        try:
            async with self.connection_pool.acquire() as conn:
                # 查询所有表
                tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                """
                
                existing_tables = await conn.fetch(tables_query)
                existing_table_names = [row['table_name'] for row in existing_tables]
                
                # 检查必需的表
                missing_tables = []
                for table in expected_tables:
                    if table not in existing_table_names:
                        missing_tables.append(table)
                
                if missing_tables:
                    logger.error(f"缺少表: {missing_tables}")
                    return False
                
                logger.info("所有必需的表都存在")
                
                # 验证关键列
                await self._verify_table_columns(conn)
                
                return True
                
        except Exception as e:
            logger.error(f"表结构验证失败: {e}")
            return False
    
    async def _verify_table_columns(self, conn):
        """验证关键表的列结构"""
        # 验证aspect_vectors表
        aspect_vectors_columns = await conn.fetch("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'aspect_vectors'
        """)
        
        required_columns = ['id', 'document_id', 'aspect_name', 'vector', 'content', 'weight']
        existing_columns = [row['column_name'] for row in aspect_vectors_columns]
        
        for col in required_columns:
            if col not in existing_columns:
                raise Exception(f"aspect_vectors表缺少列: {col}")
        
        logger.info("aspect_vectors表结构验证通过")
    
    async def create_indexes(self) -> bool:
        """创建和优化索引"""
        logger.info("创建和优化索引...")
        
        try:
            async with self.connection_pool.acquire() as conn:
                # 检查向量扩展
                await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
                
                # 创建向量索引（如果不存在）
                vector_indexes = [
                    """
                    CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_cosine 
                    ON aspect_vectors USING ivfflat (vector vector_cosine_ops) 
                    WITH (lists = 100);
                    """,
                    """
                    CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_l2 
                    ON aspect_vectors USING ivfflat (vector vector_l2_ops) 
                    WITH (lists = 100);
                    """
                ]
                
                for index_sql in vector_indexes:
                    await conn.execute(index_sql)
                
                logger.info("向量索引创建完成")
                
                # 更新表统计信息
                await conn.execute("ANALYZE;")
                logger.info("表统计信息更新完成")
                
                return True
                
        except Exception as e:
            logger.error(f"索引创建失败: {e}")
            return False
    
    async def test_performance(self) -> Dict[str, float]:
        """测试数据库性能"""
        logger.info("开始数据库性能测试...")
        
        performance_results = {}
        
        try:
            async with self.connection_pool.acquire() as conn:
                # 测试基本查询性能
                start_time = time.time()
                await conn.fetch("SELECT COUNT(*) FROM documents;")
                performance_results["basic_query_ms"] = (time.time() - start_time) * 1000
                
                # 测试连接池性能
                start_time = time.time()
                tasks = []
                for _ in range(10):
                    task = self._test_concurrent_query(conn)
                    tasks.append(task)
                
                await asyncio.gather(*tasks)
                performance_results["concurrent_query_ms"] = (time.time() - start_time) * 1000 / 10
                
                # 测试向量查询性能（如果有数据）
                vector_count = await conn.fetchval("SELECT COUNT(*) FROM aspect_vectors;")
                if vector_count > 0:
                    start_time = time.time()
                    await conn.fetch("""
                        SELECT id, aspect_name, vector <-> '[0.1,0.2,0.3]'::vector as distance 
                        FROM aspect_vectors 
                        ORDER BY distance 
                        LIMIT 10;
                    """)
                    performance_results["vector_query_ms"] = (time.time() - start_time) * 1000
                
                logger.info("数据库性能测试完成")
                return performance_results
                
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            return {}
    
    async def _test_concurrent_query(self, conn):
        """并发查询测试"""
        await conn.fetch("SELECT 1;")
    
    async def insert_sample_data(self) -> bool:
        """插入示例数据"""
        logger.info("插入示例数据...")
        
        try:
            async with self.connection_pool.acquire() as conn:
                # 插入示例方面向量数据
                sample_vectors = [
                    {
                        "document_id": "550e8400-e29b-41d4-a716-446655440000",
                        "aspect_name": "semantic",
                        "vector": [0.1] * 1536,  # 示例向量
                        "content": "这是一个语义方面的示例内容",
                        "weight": 0.8,
                        "confidence": 0.9
                    },
                    {
                        "document_id": "550e8400-e29b-41d4-a716-446655440001", 
                        "aspect_name": "factual",
                        "vector": [0.2] * 1536,
                        "content": "这是一个事实方面的示例内容",
                        "weight": 0.7,
                        "confidence": 0.8
                    }
                ]
                
                for vector_data in sample_vectors:
                    await conn.execute("""
                        INSERT INTO aspect_vectors 
                        (document_id, aspect_name, vector, content, weight, confidence)
                        VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT DO NOTHING;
                    """, 
                    vector_data["document_id"],
                    vector_data["aspect_name"], 
                    vector_data["vector"],
                    vector_data["content"],
                    vector_data["weight"],
                    vector_data["confidence"]
                    )
                
                # 插入示例A/B测试实验
                await conn.execute("""
                    INSERT INTO ab_experiments 
                    (experiment_name, description, control_group, treatment_group, traffic_split)
                    VALUES 
                    ('self_rag_test', 'Self-RAG检索效果测试', 'enhanced_search', 'self_rag_search', 0.3),
                    ('multi_vector_test', '多向量检索效果测试', 'single_vector_search', 'multi_vector_search', 0.2)
                    ON CONFLICT (experiment_name) DO NOTHING;
                """)
                
                logger.info("示例数据插入完成")
                return True
                
        except Exception as e:
            logger.error(f"示例数据插入失败: {e}")
            return False
    
    async def generate_report(self) -> Dict[str, Any]:
        """生成数据库状态报告"""
        logger.info("生成数据库状态报告...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "tables": {},
            "indexes": {},
            "performance": {}
        }
        
        try:
            async with self.connection_pool.acquire() as conn:
                # 表统计信息
                tables_info = await conn.fetch("""
                    SELECT 
                        schemaname,
                        tablename,
                        attname as column_name,
                        n_distinct,
                        correlation
                    FROM pg_stats 
                    WHERE schemaname = 'public'
                    ORDER BY tablename, attname;
                """)
                
                for row in tables_info:
                    table_name = row['tablename']
                    if table_name not in report["tables"]:
                        report["tables"][table_name] = {}
                    
                    report["tables"][table_name][row['column_name']] = {
                        "n_distinct": row['n_distinct'],
                        "correlation": row['correlation']
                    }
                
                # 索引信息
                indexes_info = await conn.fetch("""
                    SELECT 
                        indexname,
                        tablename,
                        indexdef
                    FROM pg_indexes 
                    WHERE schemaname = 'public'
                    ORDER BY tablename, indexname;
                """)
                
                for row in indexes_info:
                    table_name = row['tablename']
                    if table_name not in report["indexes"]:
                        report["indexes"][table_name] = []
                    
                    report["indexes"][table_name].append({
                        "name": row['indexname'],
                        "definition": row['indexdef']
                    })
                
                # 性能测试
                report["performance"] = await self.test_performance()
                
                logger.info("数据库状态报告生成完成")
                return report
                
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            return report
    
    async def run_full_setup(self) -> bool:
        """执行完整的数据库设置"""
        logger.info("开始完整的数据库设置...")
        
        steps = [
            ("创建连接池", self.create_connection_pool),
            ("执行数据库迁移", self.execute_migration),
            ("验证表结构", self.verify_tables),
            ("创建索引", self.create_indexes),
            ("插入示例数据", self.insert_sample_data)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            
            if not await step_func():
                logger.error(f"步骤失败: {step_name}")
                return False
            
            logger.info(f"步骤完成: {step_name}")
        
        # 生成报告
        report = await self.generate_report()
        
        # 保存报告
        report_file = Path(__file__).parent / "database_setup_report.json"
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"数据库设置报告保存到: {report_file}")
        logger.info("🎉 数据库设置完成！")
        
        return True


async def main():
    """主函数"""
    manager = DatabaseManager()
    
    try:
        success = await manager.run_full_setup()
        
        if success:
            print("\n" + "="*50)
            print("🎉 数据库扩展和优化完成！")
            print("="*50)
            print("\n数据库功能:")
            print("✅ 多向量检索支持")
            print("✅ 用户行为分析")
            print("✅ A/B测试框架")
            print("✅ 性能监控")
            print("✅ 查询历史记录")
            print("\n下一步:")
            print("1. 检查 database_setup_report.json 报告")
            print("2. 验证向量索引性能")
            print("3. 配置应用程序数据库连接")
        else:
            print("\n❌ 数据库设置失败，请检查日志")
            
    except KeyboardInterrupt:
        print("\n用户中断设置过程")
    except Exception as e:
        logger.error(f"设置过程发生错误: {e}")
    finally:
        await manager.close_connection_pool()


if __name__ == "__main__":
    asyncio.run(main())
