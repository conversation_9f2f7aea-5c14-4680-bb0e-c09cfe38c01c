#!/usr/bin/env python3
"""
RAG检索系统增强技术环境配置脚本
自动安装依赖、下载模型、配置环境
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('setup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class EnvironmentSetup:
    """环境配置管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.requirements_file = self.project_root / "backend/services/retrieval-service/requirements-enhanced.txt"
        self.models_dir = self.project_root / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # 预训练模型列表
        self.models_to_download = [
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2", 
            "cross-encoder/ms-marco-MiniLM-L-6-v2",
            "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        ]
        
        # 中文处理库配置
        self.chinese_libs = ["jieba", "pkuseg", "thulac"]
    
    def check_python_version(self) -> bool:
        """检查Python版本"""
        logger.info("检查Python版本...")
        
        if sys.version_info < (3, 8):
            logger.error("需要Python 3.8或更高版本")
            return False
        
        logger.info(f"Python版本: {sys.version}")
        return True
    
    def install_dependencies(self) -> bool:
        """安装Python依赖包"""
        logger.info("开始安装Python依赖包...")
        
        try:
            # 升级pip
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ], check=True)
            
            # 安装依赖
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
            ], check=True)
            
            logger.info("Python依赖包安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"依赖包安装失败: {e}")
            return False
    
    def download_models(self) -> bool:
        """下载预训练模型"""
        logger.info("开始下载预训练模型...")
        
        try:
            from sentence_transformers import SentenceTransformer
            from transformers import AutoTokenizer, AutoModel
            
            for model_name in self.models_to_download:
                logger.info(f"下载模型: {model_name}")
                
                try:
                    if "cross-encoder" in model_name:
                        # 下载交叉编码器
                        AutoTokenizer.from_pretrained(model_name)
                        AutoModel.from_pretrained(model_name)
                    else:
                        # 下载句子变换器
                        SentenceTransformer(model_name)
                    
                    logger.info(f"模型 {model_name} 下载完成")
                    
                except Exception as e:
                    logger.warning(f"模型 {model_name} 下载失败: {e}")
                    continue
            
            logger.info("预训练模型下载完成")
            return True
            
        except ImportError as e:
            logger.error(f"模型下载失败，缺少依赖: {e}")
            return False
    
    def setup_chinese_libs(self) -> bool:
        """配置中文处理库"""
        logger.info("配置中文处理库...")
        
        try:
            # 配置jieba
            import jieba
            jieba.initialize()
            logger.info("jieba初始化完成")
            
            # 配置pkuseg
            try:
                import pkuseg
                seg = pkuseg.pkuseg()
                logger.info("pkuseg初始化完成")
            except ImportError:
                logger.warning("pkuseg未安装")
            
            # 配置thulac
            try:
                import thulac
                thu = thulac.thulac(seg_only=True)
                logger.info("thulac初始化完成")
            except ImportError:
                logger.warning("thulac未安装")
            
            logger.info("中文处理库配置完成")
            return True
            
        except Exception as e:
            logger.error(f"中文处理库配置失败: {e}")
            return False
    
    def create_config_files(self) -> bool:
        """创建配置文件"""
        logger.info("创建配置文件...")
        
        try:
            # 创建环境变量文件
            env_file = self.project_root / ".env.enhanced"
            env_content = """# RAG检索系统增强技术环境变量

# 启用的技术列表
ENHANCED_RETRIEVAL_ENABLED_TECHNIQUES=self_rag,hard_negative_mining,multi_vector_retrieval,chinese_optimization,temporal_aware_retrieval

# Self-RAG配置
ENHANCED_RETRIEVAL_SELF_RAG_ENABLED=true
ENHANCED_RETRIEVAL_SELF_RAG_MAX_ITERATIONS=3
ENHANCED_RETRIEVAL_SELF_RAG_CONFIDENCE_THRESHOLD=0.8

# 困难负样本挖掘配置
ENHANCED_RETRIEVAL_HARD_NEGATIVE_ENABLED=true
ENHANCED_RETRIEVAL_SIMILARITY_THRESHOLD_LOW=0.6
ENHANCED_RETRIEVAL_SIMILARITY_THRESHOLD_HIGH=0.85

# 多向量检索配置
ENHANCED_RETRIEVAL_MULTI_VECTOR_ENABLED=true
ENHANCED_RETRIEVAL_ASPECT_WEIGHTS='{"semantic": 0.3, "factual": 0.25, "temporal": 0.15, "entity": 0.2, "procedural": 0.1}'

# 中文优化配置
ENHANCED_RETRIEVAL_CHINESE_OPTIMIZATION_ENABLED=true
ENHANCED_RETRIEVAL_CHINESE_ENSEMBLE_WEIGHTS='{"jieba": 0.4, "pkuseg": 0.3, "thulac": 0.3}'

# 时效性检索配置
ENHANCED_RETRIEVAL_TEMPORAL_AWARE_ENABLED=true
ENHANCED_RETRIEVAL_FRESHNESS_DECAY_FACTOR=0.1

# 性能配置
ENHANCED_RETRIEVAL_MAX_CONCURRENT_REQUESTS=200
ENHANCED_RETRIEVAL_REQUEST_TIMEOUT=30
ENHANCED_RETRIEVAL_CACHE_ENABLED=true
ENHANCED_RETRIEVAL_CACHE_TTL=3600

# A/B测试配置
ENHANCED_RETRIEVAL_AB_TESTING_ENABLED=true

# 监控配置
ENHANCED_RETRIEVAL_METRICS_ENABLED=true
ENHANCED_RETRIEVAL_DETAILED_LOGGING=false
"""
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
            
            logger.info(f"环境变量文件创建: {env_file}")
            
            # 创建模型配置文件
            model_config_file = self.project_root / "config/models.yaml"
            model_config_file.parent.mkdir(exist_ok=True)
            
            model_config = """# 预训练模型配置
models:
  embedding:
    default: "sentence-transformers/all-MiniLM-L6-v2"
    multilingual: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    large: "sentence-transformers/all-mpnet-base-v2"
  
  cross_encoder:
    default: "cross-encoder/ms-marco-MiniLM-L-6-v2"
  
  chinese:
    segmentation:
      - "jieba"
      - "pkuseg" 
      - "thulac"

# 模型缓存配置
cache:
  model_cache_dir: "./models"
  max_cache_size: "10GB"
  auto_cleanup: true
"""
            
            with open(model_config_file, 'w', encoding='utf-8') as f:
                f.write(model_config)
            
            logger.info(f"模型配置文件创建: {model_config_file}")
            return True
            
        except Exception as e:
            logger.error(f"配置文件创建失败: {e}")
            return False
    
    def verify_installation(self) -> bool:
        """验证安装结果"""
        logger.info("验证安装结果...")
        
        verification_results = {}
        
        # 验证核心库
        core_libs = [
            "torch", "transformers", "sentence_transformers",
            "faiss", "numpy", "scipy", "sklearn",
            "fastapi", "uvicorn", "redis", "asyncpg"
        ]
        
        for lib in core_libs:
            try:
                __import__(lib)
                verification_results[lib] = "✅ 成功"
            except ImportError:
                verification_results[lib] = "❌ 失败"
        
        # 验证中文处理库
        for lib in self.chinese_libs:
            try:
                __import__(lib)
                verification_results[lib] = "✅ 成功"
            except ImportError:
                verification_results[lib] = "❌ 失败"
        
        # 验证模型下载
        try:
            from sentence_transformers import SentenceTransformer
            model = SentenceTransformer('all-MiniLM-L6-v2')
            verification_results["模型加载"] = "✅ 成功"
        except Exception:
            verification_results["模型加载"] = "❌ 失败"
        
        # 输出验证结果
        logger.info("=== 安装验证结果 ===")
        for item, status in verification_results.items():
            logger.info(f"{item}: {status}")
        
        # 检查是否有失败项
        failed_items = [item for item, status in verification_results.items() if "❌" in status]
        
        if failed_items:
            logger.warning(f"以下项目验证失败: {failed_items}")
            return False
        else:
            logger.info("所有项目验证成功！")
            return True
    
    def run_setup(self) -> bool:
        """执行完整的环境配置"""
        logger.info("开始RAG检索系统增强技术环境配置...")
        
        steps = [
            ("检查Python版本", self.check_python_version),
            ("安装Python依赖", self.install_dependencies),
            ("下载预训练模型", self.download_models),
            ("配置中文处理库", self.setup_chinese_libs),
            ("创建配置文件", self.create_config_files),
            ("验证安装结果", self.verify_installation)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            
            if not step_func():
                logger.error(f"步骤失败: {step_name}")
                return False
            
            logger.info(f"步骤完成: {step_name}")
        
        logger.info("🎉 环境配置完成！")
        return True


def main():
    """主函数"""
    setup = EnvironmentSetup()
    
    try:
        success = setup.run_setup()
        
        if success:
            print("\n" + "="*50)
            print("🎉 RAG检索系统增强技术环境配置成功！")
            print("="*50)
            print("\n下一步:")
            print("1. 检查 .env.enhanced 文件中的配置")
            print("2. 运行测试验证环境: python -m pytest tests/")
            print("3. 启动开发服务器: uvicorn app.main:app --reload")
            sys.exit(0)
        else:
            print("\n" + "="*50)
            print("❌ 环境配置失败，请检查日志文件 setup.log")
            print("="*50)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断配置过程")
        sys.exit(1)
    except Exception as e:
        logger.error(f"配置过程发生未预期错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
