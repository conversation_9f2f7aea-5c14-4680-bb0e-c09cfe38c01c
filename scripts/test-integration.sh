#!/bin/bash

# RAG系统集成测试脚本
# 测试各个服务之间的集成和端到端功能

set -e

echo "🚀 开始RAG系统集成测试..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_retries=30
    local retry_count=0

    log_info "检查 $service_name 服务健康状态..."
    
    while [ $retry_count -lt $max_retries ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            log_success "$service_name 服务健康检查通过"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        log_warning "$service_name 服务未就绪，等待中... ($retry_count/$max_retries)"
        sleep 2
    done
    
    log_error "$service_name 服务健康检查失败"
    return 1
}

# 测试API接口
test_api_endpoint() {
    local endpoint_name=$1
    local method=$2
    local url=$3
    local expected_status=$4
    local data=$5

    log_info "测试 $endpoint_name API..."
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$url")
    fi
    
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        log_success "$endpoint_name API测试通过 (状态码: $status_code)"
        return 0
    else
        log_error "$endpoint_name API测试失败 (期望: $expected_status, 实际: $status_code)"
        echo "响应内容: $response_body"
        return 1
    fi
}

# 主测试流程
main() {
    local base_url="http://localhost:3000"
    local test_failed=0

    echo "=========================================="
    echo "🔍 RAG系统集成测试开始"
    echo "=========================================="

    # 1. 检查所有服务健康状态
    log_info "第一步：检查服务健康状态"
    
    services=(
        "API网关:$base_url/health"
        "用户服务:http://localhost:3001/api/v1/health"
        "文档服务:http://localhost:3002/api/v1/health"
        "向量化服务:http://localhost:8001/health"
        "检索服务:http://localhost:8002/health"
        "生成服务:http://localhost:3003/api/v1/health"
        "对话服务:http://localhost:3004/api/v1/health"
    )

    for service in "${services[@]}"; do
        IFS=':' read -r name url <<< "$service"
        if ! check_service_health "$name" "$url"; then
            test_failed=1
        fi
    done

    # 2. 测试用户认证流程
    log_info "第二步：测试用户认证流程"
    
    # 用户注册
    register_data='{
        "email": "<EMAIL>",
        "password": "Test123456",
        "name": "测试用户",
        "confirmPassword": "Test123456"
    }'
    
    if ! test_api_endpoint "用户注册" "POST" "$base_url/api/v1/auth/register" "201" "$register_data"; then
        test_failed=1
    fi
    
    # 用户登录
    login_data='{
        "email": "<EMAIL>",
        "password": "Test123456"
    }'
    
    if test_api_endpoint "用户登录" "POST" "$base_url/api/v1/auth/login" "200" "$login_data"; then
        # 提取访问令牌
        access_token=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$login_data" \
            "$base_url/api/v1/auth/login" | \
            grep -o '"accessToken":"[^"]*"' | \
            cut -d'"' -f4)
        
        log_success "获取访问令牌成功"
    else
        test_failed=1
    fi

    # 3. 测试文档管理流程
    log_info "第三步：测试文档管理流程"
    
    if [ -n "$access_token" ]; then
        # 创建测试文档
        test_document='{
            "title": "测试文档",
            "content": "这是一个用于测试RAG系统的示例文档。它包含了一些基本的文本内容，用于验证文档上传、解析和向量化功能是否正常工作。",
            "category": "测试",
            "tags": ["测试", "示例"]
        }'
        
        # 上传文档
        if test_api_endpoint "文档上传" "POST" "$base_url/api/v1/documents" "201" "$test_document"; then
            log_success "文档上传测试通过"
        else
            test_failed=1
        fi
        
        # 获取文档列表
        if test_api_endpoint "文档列表" "GET" "$base_url/api/v1/documents" "200"; then
            log_success "文档列表获取测试通过"
        else
            test_failed=1
        fi
    else
        log_error "跳过文档管理测试（缺少访问令牌）"
        test_failed=1
    fi

    # 4. 测试问答流程
    log_info "第四步：测试问答流程"
    
    if [ -n "$access_token" ]; then
        # 创建对话
        conversation_data='{
            "title": "测试对话",
            "description": "用于测试的对话"
        }'
        
        if test_api_endpoint "创建对话" "POST" "$base_url/api/v1/conversations" "201" "$conversation_data"; then
            log_success "对话创建测试通过"
        else
            test_failed=1
        fi
        
        # 发送消息
        message_data='{
            "content": "请介绍一下RAG系统的基本功能",
            "conversationId": "test-conversation-id"
        }'
        
        if test_api_endpoint "发送消息" "POST" "$base_url/api/v1/conversations/messages" "200" "$message_data"; then
            log_success "消息发送测试通过"
        else
            test_failed=1
        fi
    else
        log_error "跳过问答流程测试（缺少访问令牌）"
        test_failed=1
    fi

    # 5. 测试系统监控
    log_info "第五步：测试系统监控"
    
    # 检查系统状态
    if test_api_endpoint "系统状态" "GET" "$base_url/api/v1/system/status" "200"; then
        log_success "系统状态检查通过"
    else
        test_failed=1
    fi
    
    # 检查系统指标
    if test_api_endpoint "系统指标" "GET" "$base_url/api/v1/system/metrics" "200"; then
        log_success "系统指标检查通过"
    else
        test_failed=1
    fi

    # 6. 性能测试
    log_info "第六步：基础性能测试"
    
    # 并发用户测试
    log_info "执行并发用户测试..."
    for i in {1..10}; do
        test_api_endpoint "并发测试-$i" "GET" "$base_url/health" "200" &
    done
    wait
    log_success "并发用户测试完成"

    # 7. 清理测试数据
    log_info "第七步：清理测试数据"
    
    if [ -n "$access_token" ]; then
        # 删除测试用户（如果支持）
        log_info "清理测试数据..."
        # 这里可以添加清理逻辑
        log_success "测试数据清理完成"
    fi

    # 测试结果汇总
    echo "=========================================="
    if [ $test_failed -eq 0 ]; then
        log_success "🎉 所有集成测试通过！"
        echo "系统集成测试完成，所有功能正常工作。"
    else
        log_error "❌ 部分测试失败！"
        echo "请检查失败的测试项目并修复相关问题。"
        exit 1
    fi
    echo "=========================================="
}

# 执行主函数
main "$@"
