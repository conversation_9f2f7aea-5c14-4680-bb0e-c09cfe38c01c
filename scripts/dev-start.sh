#!/bin/bash

# RAG系统开发环境快速启动脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f .env ]; then
        log_error ".env 文件不存在，请先运行 scripts/init-project.sh"
        exit 1
    fi
    
    # 检查关键配置
    if ! grep -q "OPENAI_API_KEY=sk-" .env 2>/dev/null; then
        log_warning "OPENAI_API_KEY 未配置或格式不正确"
        log_warning "请在 .env 文件中设置正确的 OpenAI API 密钥"
    fi
    
    log_success "环境配置检查完成"
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库、缓存、对象存储等基础服务
    docker-compose up -d postgres redis minio chroma
    
    # 等待服务启动
    log_info "等待基础设施服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps postgres redis minio chroma | grep -q "Up"; then
        log_success "基础设施服务启动成功"
    else
        log_error "基础设施服务启动失败"
        docker-compose logs postgres redis minio chroma
        exit 1
    fi
}

# 启动后端服务
start_backend_services() {
    log_info "启动后端服务..."
    
    # 按依赖顺序启动服务
    log_info "启动用户服务..."
    docker-compose up -d user-service
    sleep 5
    
    log_info "启动文档服务..."
    docker-compose up -d document-service
    sleep 5
    
    log_info "启动向量化服务..."
    docker-compose up -d embedding-service
    sleep 5
    
    log_info "启动检索服务..."
    docker-compose up -d retrieval-service
    sleep 5
    
    log_info "启动生成服务..."
    docker-compose up -d generation-service
    sleep 5
    
    log_info "启动对话服务..."
    docker-compose up -d conversation-service
    sleep 5
    
    log_info "启动API网关..."
    docker-compose up -d api-gateway
    sleep 5
    
    log_success "后端服务启动完成"
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    docker-compose up -d frontend
    sleep 5
    
    log_success "前端服务启动完成"
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    # 定义服务和端口
    declare -A services=(
        ["PostgreSQL"]="5432"
        ["Redis"]="6379"
        ["MinIO"]="9000"
        ["Chroma"]="8000"
        ["用户服务"]="3001"
        ["文档服务"]="3002"
        ["生成服务"]="3003"
        ["对话服务"]="3004"
        ["向量化服务"]="8001"
        ["检索服务"]="8002"
        ["API网关"]="3000"
        ["前端"]="3100"
    )
    
    echo ""
    echo "服务健康检查结果："
    echo "===================="
    
    for service in "${!services[@]}"; do
        port=${services[$service]}
        if nc -z localhost $port 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $service (端口 $port) - 运行正常"
        else
            echo -e "${RED}✗${NC} $service (端口 $port) - 无法访问"
        fi
    done
    
    echo ""
}

# 显示服务信息
show_service_info() {
    log_info "服务访问信息："
    echo ""
    echo "前端应用:"
    echo "  URL: http://localhost:3100"
    echo ""
    echo "API 网关:"
    echo "  URL: http://localhost:3000"
    echo "  健康检查: http://localhost:3000/health"
    echo ""
    echo "MinIO 控制台:"
    echo "  URL: http://localhost:9001"
    echo "  用户名: minioadmin"
    echo "  密码: minioadmin123"
    echo ""
    echo "Chroma 向量数据库:"
    echo "  URL: http://localhost:8000"
    echo ""
    echo "数据库连接:"
    echo "  Host: localhost"
    echo "  Port: 5432"
    echo "  Database: rag_system"
    echo "  Username: rag_user"
    echo ""
    echo "Redis 缓存:"
    echo "  URL: redis://localhost:6379"
    echo ""
}

# 显示有用的命令
show_useful_commands() {
    log_info "有用的开发命令："
    echo ""
    echo "查看所有服务状态:"
    echo "  docker-compose ps"
    echo ""
    echo "查看服务日志:"
    echo "  docker-compose logs -f [service-name]"
    echo "  例如: docker-compose logs -f api-gateway"
    echo ""
    echo "重启特定服务:"
    echo "  docker-compose restart [service-name]"
    echo ""
    echo "停止所有服务:"
    echo "  docker-compose down"
    echo ""
    echo "重新构建并启动:"
    echo "  docker-compose up -d --build"
    echo ""
    echo "进入服务容器:"
    echo "  docker-compose exec [service-name] /bin/bash"
    echo ""
    echo "查看数据库:"
    echo "  docker-compose exec postgres psql -U rag_user -d rag_system"
    echo ""
    echo "查看 Redis:"
    echo "  docker-compose exec redis redis-cli"
    echo ""
}

# 主函数
main() {
    log_info "启动 RAG 系统开发环境..."
    echo ""
    
    check_environment
    start_infrastructure
    start_backend_services
    start_frontend
    
    echo ""
    log_success "所有服务启动完成！"
    echo ""
    
    check_health
    show_service_info
    show_useful_commands
    
    log_success "开发环境已就绪！"
}

# 处理中断信号
trap 'log_warning "启动过程被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
