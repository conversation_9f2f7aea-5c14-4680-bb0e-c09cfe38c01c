#!/bin/bash

# RAG系统项目初始化脚本
# 用于初始化开发环境和项目依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js (版本 >= 18)"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python3 (版本 >= 3.8)"
        exit 1
    fi
    
    log_success "所有必要工具检查通过"
}

# 创建环境配置文件
setup_environment() {
    log_info "设置环境配置..."
    
    if [ ! -f .env ]; then
        log_info "复制环境配置文件..."
        cp .env.example .env
        log_warning "请编辑 .env 文件，填入正确的配置值"
        log_warning "特别是 OPENAI_API_KEY 和 ANTHROPIC_API_KEY"
    else
        log_info ".env 文件已存在，跳过复制"
    fi
    
    log_success "环境配置设置完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装根目录依赖
    log_info "安装根目录依赖..."
    npm install
    
    # 安装前端依赖
    if [ -d "frontend" ]; then
        log_info "安装前端依赖..."
        cd frontend
        npm install
        cd ..
    fi
    
    # 安装各个服务的依赖
    for service in backend/services/*/; do
        if [ -f "${service}package.json" ]; then
            service_name=$(basename "$service")
            log_info "安装 ${service_name} 服务依赖..."
            cd "$service"
            npm install
            cd - > /dev/null
        fi
    done
    
    # 安装 API 网关依赖
    if [ -f "backend/api-gateway/package.json" ]; then
        log_info "安装 API 网关依赖..."
        cd backend/api-gateway
        npm install
        cd - > /dev/null
    fi
    
    log_success "所有依赖安装完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 创建临时文件目录
    mkdir -p temp
    
    # 创建上传目录
    mkdir -p uploads
    
    # 创建备份目录
    mkdir -p backups
    
    log_success "目录创建完成"
}

# 初始化 Git hooks
setup_git_hooks() {
    log_info "设置 Git hooks..."
    
    if [ -d ".git" ]; then
        # 安装 husky
        npx husky install
        
        # 设置 pre-commit hook
        npx husky add .husky/pre-commit "npm run lint-staged"
        
        # 设置 commit-msg hook
        npx husky add .husky/commit-msg "npx commitlint --edit \$1"
        
        log_success "Git hooks 设置完成"
    else
        log_warning "不是 Git 仓库，跳过 Git hooks 设置"
    fi
}

# 构建 Docker 镜像
build_docker_images() {
    log_info "构建 Docker 镜像..."
    
    # 构建所有服务镜像
    docker-compose build
    
    log_success "Docker 镜像构建完成"
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库、缓存、对象存储等基础服务
    docker-compose up -d postgres redis minio chroma
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "基础设施服务启动成功"
    else
        log_error "基础设施服务启动失败"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待 PostgreSQL 完全启动
    log_info "等待 PostgreSQL 启动..."
    sleep 5
    
    # 运行数据库迁移
    log_info "运行数据库初始化脚本..."
    
    # 检查数据库连接
    if docker-compose exec -T postgres psql -U rag_user -d rag_system -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败"
        exit 1
    fi
    
    log_success "数据库初始化完成"
}

# 创建 MinIO 存储桶
setup_minio() {
    log_info "设置 MinIO 存储桶..."
    
    # 等待 MinIO 启动
    sleep 5
    
    # 创建存储桶（这里可以添加 mc 命令来创建桶）
    log_info "MinIO 存储桶将在首次使用时自动创建"
    
    log_success "MinIO 设置完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 检查所有服务状态
    log_info "检查服务状态..."
    docker-compose ps
    
    # 检查端口是否可访问
    services=(
        "PostgreSQL:5432"
        "Redis:6379"
        "MinIO:9000"
        "Chroma:8000"
    )
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        if nc -z localhost $port 2>/dev/null; then
            log_success "$name 服务可访问 (端口 $port)"
        else
            log_warning "$name 服务不可访问 (端口 $port)"
        fi
    done
    
    log_success "安装验证完成"
}

# 显示下一步操作
show_next_steps() {
    log_info "项目初始化完成！"
    echo ""
    echo "下一步操作："
    echo "1. 编辑 .env 文件，填入正确的 API 密钥"
    echo "2. 启动所有服务: docker-compose up -d"
    echo "3. 查看服务状态: docker-compose ps"
    echo "4. 查看日志: docker-compose logs -f [service-name]"
    echo "5. 访问前端: http://localhost:3100"
    echo "6. 访问 API: http://localhost:3000"
    echo "7. 访问 MinIO 控制台: http://localhost:9001"
    echo ""
    echo "开发命令："
    echo "- npm run dev: 启动开发模式"
    echo "- npm run build: 构建项目"
    echo "- npm run test: 运行测试"
    echo "- npm run lint: 代码检查"
    echo ""
}

# 主函数
main() {
    log_info "开始初始化 RAG 系统项目..."
    echo ""
    
    check_prerequisites
    setup_environment
    create_directories
    install_dependencies
    setup_git_hooks
    build_docker_images
    start_infrastructure
    init_database
    setup_minio
    verify_installation
    show_next_steps
    
    log_success "项目初始化完成！"
}

# 运行主函数
main "$@"
