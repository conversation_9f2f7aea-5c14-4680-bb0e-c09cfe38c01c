#!/bin/bash

# RAG系统数据库管理脚本
# 用于数据库的创建、迁移、备份等操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载环境变量
load_env() {
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    else
        log_error ".env 文件不存在"
        exit 1
    fi
}

# 检查数据库连接
check_db_connection() {
    log_info "检查数据库连接..."
    
    if docker-compose exec -T postgres pg_isready -U ${POSTGRES_USER} > /dev/null 2>&1; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库..."
    
    # 启动 PostgreSQL 容器
    docker-compose up -d postgres
    sleep 5
    
    # 检查数据库是否已存在
    if docker-compose exec -T postgres psql -U ${POSTGRES_USER} -lqt | cut -d \| -f 1 | grep -qw ${POSTGRES_DB}; then
        log_warning "数据库 ${POSTGRES_DB} 已存在"
    else
        # 创建数据库
        docker-compose exec -T postgres createdb -U ${POSTGRES_USER} ${POSTGRES_DB}
        log_success "数据库 ${POSTGRES_DB} 创建成功"
    fi
}

# 删除数据库
drop_database() {
    log_warning "即将删除数据库 ${POSTGRES_DB}"
    read -p "确认删除？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose exec -T postgres dropdb -U ${POSTGRES_USER} ${POSTGRES_DB} --if-exists
        log_success "数据库 ${POSTGRES_DB} 删除成功"
    else
        log_info "操作已取消"
    fi
}

# 运行迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    if ! check_db_connection; then
        log_error "数据库连接失败，无法运行迁移"
        exit 1
    fi
    
    # 运行初始化脚本
    if [ -f "database/init/01-init.sql" ]; then
        log_info "运行初始化脚本..."
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -f /docker-entrypoint-initdb.d/01-init.sql
        log_success "初始化脚本执行完成"
    fi
}

# 插入种子数据
seed_database() {
    log_info "插入种子数据..."
    
    if ! check_db_connection; then
        log_error "数据库连接失败，无法插入种子数据"
        exit 1
    fi
    
    # 插入种子数据
    if [ -f "database/init/02-seed.sql" ]; then
        log_info "插入种子数据..."
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -f /docker-entrypoint-initdb.d/02-seed.sql
        log_success "种子数据插入完成"
    fi
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    if ! check_db_connection; then
        log_error "数据库连接失败，无法备份"
        exit 1
    fi
    
    # 创建备份目录
    mkdir -p backups
    
    # 生成备份文件名
    BACKUP_FILE="backups/rag_system_$(date +%Y%m%d_%H%M%S).sql"
    
    # 执行备份
    docker-compose exec -T postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > ${BACKUP_FILE}
    
    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: ${BACKUP_FILE}"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    if [ -z "$1" ]; then
        log_error "请指定备份文件路径"
        echo "用法: $0 restore <backup_file>"
        exit 1
    fi
    
    BACKUP_FILE="$1"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        log_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    log_warning "即将恢复数据库，这将覆盖现有数据"
    read -p "确认恢复？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复数据库..."
        
        # 删除现有数据库
        docker-compose exec -T postgres dropdb -U ${POSTGRES_USER} ${POSTGRES_DB} --if-exists
        
        # 创建新数据库
        docker-compose exec -T postgres createdb -U ${POSTGRES_USER} ${POSTGRES_DB}
        
        # 恢复数据
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} < ${BACKUP_FILE}
        
        log_success "数据库恢复完成"
    else
        log_info "操作已取消"
    fi
}

# 重置数据库
reset_database() {
    log_warning "即将重置数据库，这将删除所有数据"
    read -p "确认重置？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "重置数据库..."
        
        # 删除数据库
        docker-compose exec -T postgres dropdb -U ${POSTGRES_USER} ${POSTGRES_DB} --if-exists
        
        # 创建数据库
        create_database
        
        # 运行迁移
        run_migrations
        
        # 插入种子数据
        seed_database
        
        log_success "数据库重置完成"
    else
        log_info "操作已取消"
    fi
}

# 连接数据库
connect_database() {
    log_info "连接到数据库..."
    docker-compose exec postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB}
}

# 查看数据库状态
show_status() {
    log_info "数据库状态信息:"
    echo ""
    
    if check_db_connection; then
        # 显示数据库信息
        echo "数据库连接: ✓"
        echo "数据库名称: ${POSTGRES_DB}"
        echo "用户名: ${POSTGRES_USER}"
        echo ""
        
        # 显示表信息
        echo "数据表信息:"
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "
            SELECT 
                schemaname as 模式,
                tablename as 表名,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as 大小
            FROM pg_tables 
            WHERE schemaname = 'public' 
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
        "
        
        echo ""
        echo "数据库大小:"
        docker-compose exec -T postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "
            SELECT pg_size_pretty(pg_database_size('${POSTGRES_DB}')) as 数据库大小;
        "
    else
        echo "数据库连接: ✗"
    fi
}

# 显示帮助信息
show_help() {
    echo "RAG系统数据库管理脚本"
    echo ""
    echo "用法: $0 <command> [options]"
    echo ""
    echo "命令:"
    echo "  create      创建数据库"
    echo "  drop        删除数据库"
    echo "  migrate     运行数据库迁移"
    echo "  seed        插入种子数据"
    echo "  backup      备份数据库"
    echo "  restore     恢复数据库 (需要指定备份文件)"
    echo "  reset       重置数据库 (删除并重新创建)"
    echo "  connect     连接到数据库"
    echo "  status      显示数据库状态"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 create                           # 创建数据库"
    echo "  $0 migrate                          # 运行迁移"
    echo "  $0 backup                           # 备份数据库"
    echo "  $0 restore backups/backup.sql       # 恢复数据库"
    echo "  $0 reset                            # 重置数据库"
    echo ""
}

# 主函数
main() {
    # 加载环境变量
    load_env
    
    case "${1:-help}" in
        "create")
            create_database
            ;;
        "drop")
            drop_database
            ;;
        "migrate")
            run_migrations
            ;;
        "seed")
            seed_database
            ;;
        "backup")
            backup_database
            ;;
        "restore")
            restore_database "$2"
            ;;
        "reset")
            reset_database
            ;;
        "connect")
            connect_database
            ;;
        "status")
            show_status
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
