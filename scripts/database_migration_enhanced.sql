-- RAG检索系统增强技术数据库扩展脚本
-- 支持多向量检索、用户行为分析、查询历史等功能

-- 开始事务
BEGIN;

-- 1. 扩展现有文档块表
ALTER TABLE document_chunks 
ADD COLUMN IF NOT EXISTS chunk_type VARCHAR(50) DEFAULT 'default',
ADD COLUMN IF NOT EXISTS processed_text TEXT,
ADD COLUMN IF NOT EXISTS synonyms TEXT[],
ADD COLUMN IF NOT EXISTS hypothetical_questions TEXT[],
ADD COLUMN IF NOT EXISTS chunk_weight FLOAT DEFAULT 1.0,
ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.5,
ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT NOW();

-- 添加注释
COMMENT ON COLUMN document_chunks.chunk_type IS '分块类型：sentence, semantic, sliding, structure, paragraph';
COMMENT ON COLUMN document_chunks.processed_text IS '处理后的文本内容';
COMMENT ON COLUMN document_chunks.synonyms IS '同义词列表';
COMMENT ON COLUMN document_chunks.hypothetical_questions IS '假设性问题列表';
COMMENT ON COLUMN document_chunks.chunk_weight IS '分块权重';
COMMENT ON COLUMN document_chunks.quality_score IS '质量评分';

-- 2. 创建方面向量表
CREATE TABLE IF NOT EXISTS aspect_vectors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL,
    chunk_id UUID,
    aspect_name VARCHAR(50) NOT NULL,
    vector VECTOR(1536),
    content TEXT NOT NULL,
    weight FLOAT DEFAULT 1.0,
    confidence FLOAT DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT fk_aspect_vectors_document 
        FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    CONSTRAINT fk_aspect_vectors_chunk 
        FOREIGN KEY (chunk_id) REFERENCES document_chunks(id) ON DELETE CASCADE
);

-- 添加注释
COMMENT ON TABLE aspect_vectors IS '多方面向量存储表';
COMMENT ON COLUMN aspect_vectors.aspect_name IS '方面名称：semantic, factual, temporal, entity, procedural';
COMMENT ON COLUMN aspect_vectors.vector IS '方面特定的向量表示';
COMMENT ON COLUMN aspect_vectors.content IS '方面相关的内容';
COMMENT ON COLUMN aspect_vectors.weight IS '方面权重';
COMMENT ON COLUMN aspect_vectors.confidence IS '置信度';

-- 3. 创建查询历史表
CREATE TABLE IF NOT EXISTS query_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    session_id UUID,
    original_query TEXT NOT NULL,
    rewritten_queries TEXT[],
    search_type VARCHAR(50) DEFAULT 'enhanced',
    search_results JSONB,
    user_feedback INTEGER CHECK (user_feedback >= 1 AND user_feedback <= 5),
    click_positions INTEGER[],
    response_time_ms INTEGER,
    result_count INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT valid_search_type 
        CHECK (search_type IN ('baseline', 'hyde', 'rewrite', 'enhanced', 'self_rag', 'multi_vector'))
);

-- 添加注释
COMMENT ON TABLE query_history IS '查询历史记录表';
COMMENT ON COLUMN query_history.search_type IS '检索类型';
COMMENT ON COLUMN query_history.search_results IS '检索结果JSON';
COMMENT ON COLUMN query_history.user_feedback IS '用户反馈评分(1-5)';
COMMENT ON COLUMN query_history.click_positions IS '用户点击位置数组';
COMMENT ON COLUMN query_history.response_time_ms IS '响应时间(毫秒)';

-- 4. 创建用户行为分析表
CREATE TABLE IF NOT EXISTS user_behavior_analytics (
    user_id UUID PRIMARY KEY,
    preferred_content_types TEXT[],
    preferred_search_types TEXT[],
    avg_session_length INTERVAL,
    total_queries INTEGER DEFAULT 0,
    successful_queries INTEGER DEFAULT 0,
    common_query_patterns TEXT[],
    click_through_rate FLOAT DEFAULT 0.0,
    satisfaction_score FLOAT DEFAULT 0.0,
    last_active TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE user_behavior_analytics IS '用户行为分析表';
COMMENT ON COLUMN user_behavior_analytics.preferred_content_types IS '偏好的内容类型';
COMMENT ON COLUMN user_behavior_analytics.preferred_search_types IS '偏好的检索类型';
COMMENT ON COLUMN user_behavior_analytics.common_query_patterns IS '常见查询模式';
COMMENT ON COLUMN user_behavior_analytics.click_through_rate IS '点击率';
COMMENT ON COLUMN user_behavior_analytics.satisfaction_score IS '满意度评分';

-- 5. 创建A/B测试实验表
CREATE TABLE IF NOT EXISTS ab_experiments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    control_group VARCHAR(50) NOT NULL,
    treatment_group VARCHAR(50) NOT NULL,
    traffic_split FLOAT DEFAULT 0.5 CHECK (traffic_split > 0 AND traffic_split < 1),
    start_date TIMESTAMP DEFAULT NOW(),
    end_date TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed')),
    metrics JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 添加注释
COMMENT ON TABLE ab_experiments IS 'A/B测试实验配置表';

-- 6. 创建A/B测试用户分组表
CREATE TABLE IF NOT EXISTS ab_user_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    experiment_id UUID NOT NULL,
    user_id UUID NOT NULL,
    group_name VARCHAR(50) NOT NULL,
    assigned_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT fk_ab_user_experiment 
        FOREIGN KEY (experiment_id) REFERENCES ab_experiments(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_experiment 
        UNIQUE (experiment_id, user_id)
);

-- 添加注释
COMMENT ON TABLE ab_user_assignments IS 'A/B测试用户分组表';

-- 7. 创建性能指标表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value FLOAT NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    tags JSONB,
    timestamp TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT valid_metric_type 
        CHECK (metric_type IN ('latency', 'accuracy', 'recall', 'precision', 'qps', 'error_rate'))
);

-- 添加注释
COMMENT ON TABLE performance_metrics IS '性能指标记录表';

-- 8. 创建困难负样本表
CREATE TABLE IF NOT EXISTS hard_negative_samples (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query TEXT NOT NULL,
    positive_doc_id UUID NOT NULL,
    negative_doc_id UUID NOT NULL,
    similarity_score FLOAT NOT NULL,
    relevance_score FLOAT NOT NULL,
    difficulty_level VARCHAR(20) DEFAULT 'hard' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
    created_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT fk_hard_negative_positive 
        FOREIGN KEY (positive_doc_id) REFERENCES documents(id) ON DELETE CASCADE,
    CONSTRAINT fk_hard_negative_negative 
        FOREIGN KEY (negative_doc_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- 添加注释
COMMENT ON TABLE hard_negative_samples IS '困难负样本表';

-- 9. 创建索引

-- 文档块表索引
CREATE INDEX IF NOT EXISTS idx_document_chunks_type ON document_chunks(chunk_type);
CREATE INDEX IF NOT EXISTS idx_document_chunks_weight ON document_chunks(chunk_weight DESC);
CREATE INDEX IF NOT EXISTS idx_document_chunks_quality ON document_chunks(quality_score DESC);
CREATE INDEX IF NOT EXISTS idx_document_chunks_updated ON document_chunks(last_updated DESC);

-- 方面向量表索引
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_aspect ON aspect_vectors(aspect_name);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_document ON aspect_vectors(document_id);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_chunk ON aspect_vectors(chunk_id);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_weight ON aspect_vectors(weight DESC);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_confidence ON aspect_vectors(confidence DESC);

-- 向量相似度索引（使用ivfflat）
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_cosine 
    ON aspect_vectors USING ivfflat (vector vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_aspect_vectors_vector_l2 
    ON aspect_vectors USING ivfflat (vector vector_l2_ops) WITH (lists = 100);

-- 查询历史表索引
CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_query_history_session ON query_history(session_id);
CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_query_history_search_type ON query_history(search_type);
CREATE INDEX IF NOT EXISTS idx_query_history_feedback ON query_history(user_feedback);
CREATE INDEX IF NOT EXISTS idx_query_history_response_time ON query_history(response_time_ms);

-- 用户行为分析表索引
CREATE INDEX IF NOT EXISTS idx_user_behavior_last_active ON user_behavior_analytics(last_active DESC);
CREATE INDEX IF NOT EXISTS idx_user_behavior_satisfaction ON user_behavior_analytics(satisfaction_score DESC);
CREATE INDEX IF NOT EXISTS idx_user_behavior_ctr ON user_behavior_analytics(click_through_rate DESC);

-- A/B测试表索引
CREATE INDEX IF NOT EXISTS idx_ab_experiments_status ON ab_experiments(status);
CREATE INDEX IF NOT EXISTS idx_ab_experiments_dates ON ab_experiments(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_ab_user_assignments_user ON ab_user_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_ab_user_assignments_experiment ON ab_user_assignments(experiment_id);

-- 性能指标表索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp DESC);

-- 困难负样本表索引
CREATE INDEX IF NOT EXISTS idx_hard_negative_query ON hard_negative_samples(query);
CREATE INDEX IF NOT EXISTS idx_hard_negative_similarity ON hard_negative_samples(similarity_score DESC);
CREATE INDEX IF NOT EXISTS idx_hard_negative_relevance ON hard_negative_samples(relevance_score);
CREATE INDEX IF NOT EXISTS idx_hard_negative_difficulty ON hard_negative_samples(difficulty_level);

-- 10. 创建触发器函数

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间戳触发器
CREATE TRIGGER update_aspect_vectors_updated_at 
    BEFORE UPDATE ON aspect_vectors 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_behavior_updated_at 
    BEFORE UPDATE ON user_behavior_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ab_experiments_updated_at 
    BEFORE UPDATE ON ab_experiments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. 创建视图

-- 查询统计视图
CREATE OR REPLACE VIEW query_statistics AS
SELECT 
    DATE_TRUNC('day', created_at) as date,
    search_type,
    COUNT(*) as query_count,
    AVG(response_time_ms) as avg_response_time,
    AVG(user_feedback) as avg_feedback,
    AVG(result_count) as avg_result_count
FROM query_history 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at), search_type
ORDER BY date DESC, search_type;

-- 用户活跃度视图
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT 
    user_id,
    total_queries,
    successful_queries,
    CASE 
        WHEN total_queries > 0 THEN (successful_queries::FLOAT / total_queries) * 100
        ELSE 0 
    END as success_rate,
    click_through_rate * 100 as ctr_percentage,
    satisfaction_score,
    last_active,
    CASE 
        WHEN last_active >= NOW() - INTERVAL '7 days' THEN 'active'
        WHEN last_active >= NOW() - INTERVAL '30 days' THEN 'inactive'
        ELSE 'dormant'
    END as activity_status
FROM user_behavior_analytics
ORDER BY last_active DESC;

-- 方面向量统计视图
CREATE OR REPLACE VIEW aspect_vector_stats AS
SELECT 
    aspect_name,
    COUNT(*) as vector_count,
    AVG(weight) as avg_weight,
    AVG(confidence) as avg_confidence,
    MAX(created_at) as last_created
FROM aspect_vectors
GROUP BY aspect_name
ORDER BY vector_count DESC;

-- 提交事务
COMMIT;

-- 输出完成信息
DO $$
BEGIN
    RAISE NOTICE '数据库扩展完成！';
    RAISE NOTICE '已创建表：aspect_vectors, query_history, user_behavior_analytics, ab_experiments, ab_user_assignments, performance_metrics, hard_negative_samples';
    RAISE NOTICE '已创建索引：向量索引、查询索引、用户行为索引等';
    RAISE NOTICE '已创建视图：query_statistics, user_activity_summary, aspect_vector_stats';
    RAISE NOTICE '已创建触发器：自动更新时间戳';
END $$;
