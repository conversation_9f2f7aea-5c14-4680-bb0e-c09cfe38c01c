# RAG系统持续集成工作流
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'

jobs:
  # 代码质量检查
  lint-and-format:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: ESLint 检查
        run: npm run lint

      - name: Prettier 格式检查
        run: npm run format:check

      - name: TypeScript 类型检查
        run: npm run type-check

  # 前端测试
  frontend-tests:
    name: 前端测试
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装根目录依赖
        run: npm ci

      - name: 安装前端依赖
        working-directory: ./frontend
        run: npm ci

      - name: 运行前端单元测试
        working-directory: ./frontend
        run: npm run test:unit

      - name: 运行前端集成测试
        working-directory: ./frontend
        run: npm run test:integration

      - name: 构建前端
        working-directory: ./frontend
        run: npm run build

  # 后端服务测试
  backend-tests:
    name: 后端服务测试
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: rag_system_test
          POSTGRES_USER: rag_user
          POSTGRES_PASSWORD: rag_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 设置 Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: 安装根目录依赖
        run: npm ci

      - name: 测试用户服务
        working-directory: ./backend/services/user-service
        env:
          DATABASE_URL: postgresql://rag_user:rag_password@localhost:5432/rag_system_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
        run: |
          npm ci
          npm run test

      - name: 测试文档服务
        working-directory: ./backend/services/document-service
        env:
          DATABASE_URL: postgresql://rag_user:rag_password@localhost:5432/rag_system_test
          REDIS_URL: redis://localhost:6379
        run: |
          npm ci
          npm run test

      - name: 测试生成服务
        working-directory: ./backend/services/generation-service
        env:
          REDIS_URL: redis://localhost:6379
          OPENAI_API_KEY: test-key
        run: |
          npm ci
          npm run test

      - name: 测试对话服务
        working-directory: ./backend/services/conversation-service
        env:
          DATABASE_URL: postgresql://rag_user:rag_password@localhost:5432/rag_system_test
          REDIS_URL: redis://localhost:6379
        run: |
          npm ci
          npm run test

      - name: 测试API网关
        working-directory: ./backend/api-gateway
        env:
          REDIS_URL: redis://localhost:6379
        run: |
          npm ci
          npm run test

  # Python 服务测试
  python-tests:
    name: Python 服务测试
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: 安装 Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: 测试向量化服务
        working-directory: ./backend/services/embedding-service
        run: |
          poetry install
          poetry run pytest

      - name: 测试检索服务
        working-directory: ./backend/services/retrieval-service
        run: |
          poetry install
          poetry run pytest

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 运行 Trivy 漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传 Trivy 扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Docker 镜像构建
  docker-build:
    name: Docker 镜像构建
    runs-on: ubuntu-latest
    needs: [lint-and-format, frontend-tests, backend-tests, python-tests]
    if: github.event_name == 'push'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录 Docker Hub
        if: github.ref == 'refs/heads/main'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: 构建并推送前端镜像
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: ${{ github.ref == 'refs/heads/main' }}
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/rag-frontend:latest
            ${{ secrets.DOCKER_USERNAME }}/rag-frontend:${{ github.sha }}

      - name: 构建并推送用户服务镜像
        uses: docker/build-push-action@v5
        with:
          context: ./backend/services/user-service
          push: ${{ github.ref == 'refs/heads/main' }}
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/rag-user-service:latest
            ${{ secrets.DOCKER_USERNAME }}/rag-user-service:${{ github.sha }}

      - name: 构建并推送文档服务镜像
        uses: docker/build-push-action@v5
        with:
          context: ./backend/services/document-service
          push: ${{ github.ref == 'refs/heads/main' }}
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/rag-document-service:latest
            ${{ secrets.DOCKER_USERNAME }}/rag-document-service:${{ github.sha }}

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: 部署到测试环境
        run: |
          echo "部署到测试环境"
          # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: 部署到生产环境
        run: |
          echo "部署到生产环境"
          # 这里添加实际的部署脚本
